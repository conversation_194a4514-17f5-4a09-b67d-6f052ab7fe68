package repository

import (
	"context"
	"cx/domain"

	"gorm.io/gorm"
)

type SalaryItemRepository interface {
	List(ctx context.Context) ([]domain.SalaryItem, error)
	Create(ctx context.Context, payload *domain.SalaryItemCreatePayload) error
}

type salaryItemRepository struct {
	db *gorm.DB
}

func NewSalaryItemRepository(db *gorm.DB) SalaryItemRepository {
	return &salaryItemRepository{db}
}

func (r *salaryItemRepository) List(ctx context.Context) ([]domain.SalaryItem, error) {
	var salaryItems []domain.SalaryItem

	tx := r.db.WithContext(ctx).Model(&domain.SalaryItem{})

	err := tx.Order("type ASC").Order("sort_order ASC").
		Where("is_active = TRUE").
		Find(&salaryItems).Error
	if err != nil {
		return nil, err
	}
	return salaryItems, nil
}

func (r *salaryItemRepository) Create(ctx context.Context, payload *domain.SalaryItemCreatePayload) error {
	return r.db.WithContext(ctx).Model(&domain.SalaryItem{}).
		Create(payload).Error
}
