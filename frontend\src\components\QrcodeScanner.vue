<template>
  <q-dialog
    v-model="open"
    persistent
    no-refocus
    position="top"
    class="card-dialog"
  >
    <q-card class="q-mx-sm q-mt-md">
      <!-- close -->
      <q-card-section>
        <q-space />
        <q-btn
          type="button"
          icon="close"
          flat
          round
          dense
          @click="open = false"
        />
      </q-card-section>
      <!-- reader -->
      <q-card-section id="reader"></q-card-section>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from 'vue';
import { Html5QrcodeScanner } from 'html5-qrcode';
import { handleError } from '@/utils/error-handler';

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
});

const emit = defineEmits(['update:modelValue', 'update:result']);

const open = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

onMounted(() => {
  if (open.value) {
    openScanner();
  }
});

watch(
  () => props.modelValue,
  (value) => {
    if (value) {
      openScanner();
    } else {
      closeScanner();
    }
  }
);

const scanner = ref<Html5QrcodeScanner | null>(null);
// Html5QrcodeScannerConfig
const config = {
  fps: 10,
  qrbox: { width: 250, height: 250 },
  aspectRatio: 1.0,
  facingMode: 'environment',
};
const openScanner = async () => {
  try {
    // 檢查相機權限
    const stream = await navigator.mediaDevices.getUserMedia({ video: true });
    stream.getTracks().forEach((track) => track.stop());

    if (scanner.value) {
      scanner.value.clear();
    }

    // 初始化掃描器
    initScanner();
  } catch (error) {
    console.error('相機初始化失敗:', error);
    handleError(error);
  }
};

const closeScanner = () => {
  scanner.value?.clear();
};

const initScanner = () => {
  scanner.value = new Html5QrcodeScanner('reader', config, false);

  scanner.value.render(
    (decodedText) => {
      emit('update:modelValue', false);
      emit('update:result', decodedText);
      console.log('掃描成功:', decodedText);
    },
    () => {
      // console.warn('掃描錯誤');
    }
  );
};
</script>
reader
