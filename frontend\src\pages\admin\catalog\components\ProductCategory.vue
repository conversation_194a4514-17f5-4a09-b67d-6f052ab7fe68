<template>
  <q-form
    ref="formRef"
    @submit.prevent="onSubmit"
    greedy
    autocomplete="off"
    class="column q-px-md-xl"
  >
    <!-- fields -->
    <div class="col-11">
      <!-- name -->
      <div class="row q-mb-md">
        <div class="col-12 col-sm-2 text-subtitle1 text-md-center">
          {{ t('category') }}
        </div>
        <div class="col-12 col-sm-10">
          <q-input
            type="text"
            v-model="localCategory.name"
            maxlength="50"
            outlined
            dense
            hide-bottom-space
            :rules="[(val: string) => !!val || t('error.required')]"
            lazy-rules
          />
        </div>
      </div>

      <!-- image -->
      <div class="row q-mb-md">
        <div class="col-12 col-sm-2 text-subtitle1 text-md-center">
          {{ t('image') }}
        </div>
        <div class="col-12 col-sm-10">
          <q-img
            :src="`/api/${localCategory.image.image_path}`"
            ratio="1"
            width="150px"
            fit="fill"
            class="item"
            v-if="localCategory.image.uuid"
          >
            <div
              class="absolute-top-right"
              style="background: none; padding: 8px 8px"
            >
              <q-btn
                type="button"
                round
                color="negative"
                icon="delete"
                size="sm"
                @click="confirmDeleteImage"
                :loading="isSubmit"
              />
            </div>
          </q-img>
          <!-- temp image for create -->
          <q-img
            :src="tempImage.url"
            ratio="1"
            width="150px"
            fit="fill"
            class="item"
            v-else-if="tempImage?.url"
          >
            <div
              class="absolute-top-right"
              style="background: none; padding: 8px 8px"
            >
              <q-btn
                type="button"
                round
                color="negative"
                icon="delete"
                size="sm"
                @click="deleteTempImage"
              />
            </div>
          </q-img>

          <q-btn
            round
            icon="add"
            color="positive"
            size="sm"
            class="q-ma-sm"
            @click="triggerFileInput"
            :loading="isSubmit"
          />
          <input
            type="file"
            ref="fileInput"
            accept="image/*"
            @change="handleFileUpload"
            style="display: none"
          />

          <!-- 圖片確認刪除對話框 -->
          <q-dialog v-model="deleteImageDialog" no-refocus>
            <q-card>
              <q-card-section class="row items-center">
                <q-avatar icon="warning" color="negative" text-color="white" />
                <span class="q-ml-sm">
                  {{ t('confirmDelete') }}
                </span>
              </q-card-section>

              <q-card-actions align="right">
                <q-btn
                  flat
                  :label="t('cancel')"
                  color="primary"
                  v-close-popup
                />
                <q-btn
                  flat
                  :label="t('delete')"
                  color="negative"
                  @click="deleteImage"
                  v-close-popup
                />
              </q-card-actions>
            </q-card>
          </q-dialog>
        </div>
      </div>
    </div>

    <!-- actions -->
    <div class="col-1">
      <div class="row">
        <div class="offset-7 offset-md-10">
          <q-btn
            type="submit"
            :label="t('submit')"
            color="submit"
            class="q-mt-md"
            :loading="isSubmit"
          />
        </div>
      </div>
    </div>
  </q-form>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { QForm, Notify } from 'quasar';
import { ProductCategoryApi, ProductCategory } from '@/api/productCategory';

const { t } = useI18n();

const props = defineProps<{
  category: ProductCategory | null;
}>();

const emit = defineEmits(['dataUpdated', 'close']);

const isSubmit = ref(false);
const formRef = ref<InstanceType<typeof QForm> | null>(null);
const localCategory = ref<ProductCategory>({
  id: 0,
  name: '',
  image: {
    uuid: '',
    image_path: '',
  },
});

// 觸發檔案選擇
const fileInput = ref();
const triggerFileInput = () => {
  if (!fileInput.value) return;

  fileInput.value.click();
};

// 處理檔案上傳
const tempImage = ref<{
  file: File;
  url: string;
}>();
const isUpload = ref(false);

const handleFileUpload = (event: Event | null) => {
  if (!event?.target) return;

  const input = event.target as HTMLInputElement;
  if (!input.files || input.files?.length === 0) return;

  const file = input.files[0];

  if (!file) return;

  // 遍歷選擇的檔案並添加到圖片陣列
  const reader = new FileReader();

  reader.onload = async (e: ProgressEvent<FileReader>) => {
    if (!e.target?.result) return;

    if (!localCategory.value.id) {
      // for create
      tempImage.value = {
        file: file,
        url: e.target.result as string, // 這是 DataURL
      };
    } else {
      // upload
      try {
        isUpload.value = true;
        await ProductCategoryApi.uploadImage(localCategory.value.id, file);
      } finally {
        isUpload.value = false;
        getData();
      }
    }
  };

  reader.readAsDataURL(file);

  // 清空 input，確保同一檔案能夠再次上傳
  input.value = '';
};

const deleteTempImage = () => {
  tempImage.value = undefined;
};

const deleteImageDialog = ref(false);
const deletedImageID = ref('');
const confirmDeleteImage = () => {
  deletedImageID.value = localCategory.value.image.uuid;
  deleteImageDialog.value = true;
};

const deleteImage = async () => {
  try {
    isSubmit.value = true;

    await ProductCategoryApi.deleteImage(
      localCategory.value.id,
      deletedImageID.value
    );
  } finally {
    isSubmit.value = false;
    deleteImageDialog.value = false;
    getData();
  }
};

const getData = async () => {
  const response = await ProductCategoryApi.get(localCategory.value.id);
  localCategory.value = response.result;
};

onMounted(() => {
  if (props.category?.id) {
    localCategory.value = { ...props.category };
    getData();
  }
});

watch(
  () => props.category,
  (newVal) => {
    formRef.value?.resetValidation();
    tempImage.value = undefined;

    if (newVal) {
      localCategory.value = { ...newVal };
      getData();
    } else {
      localCategory.value = {
        id: 0,
        name: '',
        image: {
          uuid: '',
          image_path: '',
        },
      };
    }
  }
);

const onSubmit = async () => {
  try {
    isSubmit.value = true;
    if (props.category?.id) {
      // update
      await ProductCategoryApi.update(localCategory.value);
    } else {
      // create
      const response = await ProductCategoryApi.create(localCategory.value);

      localCategory.value.id = response.result.id;

      if (tempImage.value) {
        await ProductCategoryApi.uploadImage(
          localCategory.value.id,
          tempImage.value.file
        );
      }
    }

    Notify.create({
      message: t('success'),
      position: 'top',
      color: 'positive',
    });

    emit('dataUpdated');
    emit('close');
  } finally {
    isSubmit.value = false;
    tempImage.value = undefined;
    getData();
  }
};
</script>

<style lang="scss" scope>
.image-container {
  position: relative;

  &:hover {
    .image-overlay {
      opacity: 1;
    }
  }
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
