package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type XeroController struct {
	xeroService service.XeroService
}

func NewXeroController(r *gin.RouterGroup, xeroService service.XeroService, tokenAuthMiddleware gin.HandlerFunc) {
	xeroController := XeroController{xeroService}

	v1 := r.Group("/v1/xero")
	{
		// OAuth2認證相關
		v1.POST("/callback", xeroController.HandleCallback)

		v1.Use(tokenAuthMiddleware)

		// 設定相關
		v1.POST("/config", xeroController.SaveConfig)
		v1.GET("/config", xeroController.GetConfig)

		// OAuth2認證相關
		v1.GET("/auth-url", xeroController.GetAuthURL)

		// 連接管理
		v1.GET("/status", xeroController.GetConnectionStatus)
		v1.POST("/validate-connection", xeroController.ValidateConnection)
		v1.POST("/refresh", xeroController.RefreshToken)
		v1.DELETE("/disconnect", xeroController.DisconnectXero)

		// Invoice 相關
		v1.GET("/invoices", xeroController.GetInvoices)
		v1.GET("/invoices/:invoiceId", xeroController.GetInvoice)
		v1.GET("/invoices/:invoiceId/pdf", xeroController.GetInvoicePDF)
		v1.POST("/invoices/:invoiceId/email", xeroController.SendInvoiceEmail)
		v1.GET("/email-status", xeroController.CheckEmailLimitStatus)

		// Accounts 相關
		v1.GET("/accounts", xeroController.GetAccounts)
		v1.GET("/accounts/cash", xeroController.GetCashAccount)

		// Order 同步相關
		v1.POST("/sync-order/:orderUUID", xeroController.SyncOrderToXero)
	}
}

// SaveConfig 儲存Xero設定
func (ctr *XeroController) SaveConfig(c *gin.Context) {
	var req domain.XeroAuthRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	config, err := ctr.xeroService.SaveConfig(c, &req)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to save config")
		return
	}

	utils.HandleSuccess(c, config)
}

// GetConfig 取得Xero設定
func (ctr *XeroController) GetConfig(c *gin.Context) {
	config, err := ctr.xeroService.GetConfig(c)
	if err != nil {
		utils.HandleError(c, http.StatusNotFound, err, "Config not found")
		return
	}

	// 隱藏敏感資訊
	// config.ClientSecret = "****"

	utils.HandleSuccess(c, config)
}

// GetAuthURL 取得OAuth2認證URL
func (ctr *XeroController) GetAuthURL(c *gin.Context) {
	authURL, state, err := ctr.xeroService.GenerateAuthURL(c)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to generate auth URL")
		return
	}

	// 將state儲存到session或Redis中
	// ctr.saveState(ctx, userID, state)

	utils.HandleSuccess(c, gin.H{
		"auth_url": authURL,
		"state":    state,
	})
}

// HandleCallback 處理OAuth2回調
func (ctr *XeroController) HandleCallback(c *gin.Context) {
	var req domain.XeroCallbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	// 驗證state參數
	// if !c.validateState(c, req.State) {
	// utils.HandleError(c, http.StatusBadRequest, nil, "Invalid state")
	// 	return
	// }

	token, err := ctr.xeroService.HandleCallback(c, req.Code)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to handle callback")
		return
	}

	// 清除state
	// c.clearState(ctx, userID)

	utils.HandleSuccess(c, gin.H{
		"tenant_name": token.TenantName,
		"tenant_id":   token.TenantID,
	})
}

// GetConnectionStatus 取得連接狀態
func (ctr *XeroController) GetConnectionStatus(c *gin.Context) {
	token, err := ctr.xeroService.GetValidToken(c)
	if err != nil {
		utils.HandleSuccess(c, gin.H{
			"connected":   false,
			"tenant_name": "",
			"tenant_id":   "",
		})
		return
	}

	utils.HandleSuccess(c, gin.H{
		"connected":   true,
		"tenant_name": token.TenantName,
		"tenant_id":   token.TenantID,
		"expires_at":  token.ExpiresAt,
	})
}

// RefreshToken 刷新Token
func (ctr *XeroController) RefreshToken(c *gin.Context) {
	token, err := ctr.xeroService.RefreshToken(c)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to refresh token")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"expires_at": token.ExpiresAt,
	})
}

// ValidateConnection 驗證 Xero 連接
func (ctr *XeroController) ValidateConnection(c *gin.Context) {
	err := ctr.xeroService.ValidateConnection(c)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Connection validation failed")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"message": "Connection is valid",
	})
}

// DisconnectXero 中斷Xero連接
func (ctr *XeroController) DisconnectXero(c *gin.Context) {
	err := ctr.xeroService.Disconnect(c)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to disconnect")
		return
	}

	utils.HandleSuccess(c, nil)
}

// GetInvoices 取得發票列表
func (ctr *XeroController) GetInvoices(c *gin.Context) {
	// 解析查詢參數
	params := &domain.XeroInvoiceParams{}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			params.Page = p
		}
	}

	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			params.PageSize = ps
		}
	}

	params.Status = c.Query("status")
	params.ContactID = c.Query("contactId")
	params.DateFrom = c.Query("dateFrom")
	params.DateTo = c.Query("dateTo")
	if c.Query("order") != "" {
		params.Order = c.Query("order")
	} else {
		params.Order = "Date"
	}

	if c.Query("asc") == "true" {
		params.Asc = true
	}

	invoices, err := ctr.xeroService.GetInvoices(c, params)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to get invoices")
		return
	}

	// 轉換為前端期望的格式
	convertedInvoices := make([]gin.H, len(invoices.Invoices))
	for i, invoice := range invoices.Invoices {
		convertedInvoices[i] = convertInvoiceToFrontend(invoice)
	}

	result := gin.H{
		"invoices":    convertedInvoices,
		"total_count": len(invoices.Invoices),
		"page":        params.Page,
		"page_size":   params.PageSize,
	}

	utils.HandleSuccess(c, result)
}

// GetInvoice 取得單一發票
func (ctr *XeroController) GetInvoice(c *gin.Context) {
	invoiceID := c.Param("invoiceId")
	if invoiceID == "" {
		utils.HandleError(c, http.StatusBadRequest, nil, "Invoice ID is required")
		return
	}

	invoice, err := ctr.xeroService.GetInvoice(c, invoiceID)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to get invoice")
		return
	}

	// 轉換為前端期望的格式
	var result gin.H
	if len(invoice.Invoices) > 0 {
		result = gin.H{"invoice": convertInvoiceToFrontend(invoice.Invoices[0])}
	} else {
		utils.HandleError(c, http.StatusNotFound, nil, "Invoice not found")
		return
	}

	utils.HandleSuccess(c, result)
}

// convertInvoiceToFrontend 將 Xero Invoice 轉換為前端期望的格式
func convertInvoiceToFrontend(invoice domain.XeroInvoice) gin.H {
	// 轉換 line items
	lineItems := make([]gin.H, len(invoice.LineItems))
	for i, item := range invoice.LineItems {
		lineItems[i] = gin.H{
			"line_item_id": item.LineItemID,
			"description":  item.Description,
			"quantity":     item.Quantity,
			"unit_amount":  item.UnitAmount,
			"line_amount":  item.LineAmount,
			"tax_amount":   item.TaxAmount,
		}
	}

	return gin.H{
		"invoice_id":     invoice.InvoiceID,
		"invoice_number": invoice.InvoiceNumber,
		"type":           invoice.Type,
		"contact": gin.H{
			"contact_id": invoice.Contact.ContactID,
			"name":       invoice.Contact.Name,
		},
		"date":              formatXeroDate(invoice.Date),
		"due_date":          formatXeroDate(invoice.DueDate),
		"status":            invoice.Status,
		"line_amount_types": invoice.LineAmountTypes,
		"sub_total":         invoice.SubTotal,
		"total_tax":         invoice.TotalTax,
		"total":             invoice.Total,
		"currency_code":     invoice.CurrencyCode,
		"updated_date_utc":  formatXeroDate(invoice.UpdatedDateUTC),
		"line_items":        lineItems,
	}
}

// SyncOrderToXero 同步訂單到 Xero
func (ctrl XeroController) SyncOrderToXero(c *gin.Context) {
	orderUUID := c.Param("orderUUID")
	if orderUUID == "" {
		utils.HandleError(c, http.StatusBadRequest, nil, "Order UUID is required")
		return
	}

	response, err := ctrl.xeroService.SyncOrderToXero(c.Request.Context(), orderUUID)
	if err != nil {
		// 使用增強的錯誤處理，提供更多上下文
		details := map[string]interface{}{
			"orderUUID": orderUUID,
			"operation": "SyncOrderToXero",
			"service":   "XeroService",
		}
		utils.HandleErrorWithDetails(c, http.StatusInternalServerError, err, "Failed to sync order to Xero", details)
		return
	}

	result := gin.H{
		"success": true,
		"message": "Order synced to Xero successfully",
		"invoice": response,
	}

	utils.HandleSuccess(c, result)
}

// GetInvoicePDF 獲取發票 PDF
func (ctrl XeroController) GetInvoicePDF(c *gin.Context) {
	invoiceID := c.Param("invoiceId")
	if invoiceID == "" {
		utils.HandleError(c, http.StatusBadRequest, nil, "Invoice ID is required")
		return
	}

	fmt.Printf("Controller: Requesting PDF for invoice ID: %s\n", invoiceID)

	pdfData, err := ctrl.xeroService.GetInvoicePDF(c.Request.Context(), invoiceID)
	if err != nil {
		fmt.Printf("Controller: Error getting PDF: %v\n", err)
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to get invoice PDF")
		return
	}

	fmt.Printf("Controller: PDF data received, size: %d bytes\n", len(pdfData))

	// 驗證 PDF 數據
	if len(pdfData) < 100 {
		fmt.Printf("Controller: PDF data too small\n")
		utils.HandleError(c, http.StatusInternalServerError, nil, "Invalid PDF data received")
		return
	}

	// 檢查 PDF 魔術數字
	if len(pdfData) >= 4 && string(pdfData[:4]) != "%PDF" {
		fmt.Printf("Controller: Data is not a valid PDF, first 50 bytes: %s\n", string(pdfData[:min(50, len(pdfData))]))
		utils.HandleError(c, http.StatusInternalServerError, nil, "Received data is not a valid PDF")
		return
	}

	fmt.Printf("Controller: PDF validation passed, magic number: %s\n", string(pdfData[:4]))

	// 設置 PDF 回應 headers
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", fmt.Sprintf("inline; filename=\"xero-invoice-%s.pdf\"", invoiceID))
	c.Header("Content-Length", fmt.Sprintf("%d", len(pdfData)))
	c.Header("Cache-Control", "no-cache")

	fmt.Printf("Controller: Sending PDF response\n")

	// 返回 PDF 數據
	c.Data(http.StatusOK, "application/pdf", pdfData)
}

// SendInvoiceEmail 發送發票 Email
func (ctrl XeroController) SendInvoiceEmail(c *gin.Context) {
	invoiceID := c.Param("invoiceId")
	if invoiceID == "" {
		utils.HandleError(c, http.StatusBadRequest, nil, "Invoice ID is required")
		return
	}

	// 解析請求 body
	var request struct {
		Email string `json:"email" binding:"required,email"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	fmt.Printf("Controller: Sending email for invoice %s to %s\n", invoiceID, request.Email)

	err := ctrl.xeroService.SendInvoiceEmail(c.Request.Context(), invoiceID, request.Email)
	if err != nil {
		fmt.Printf("Controller: Error sending email: %v\n", err)

		// 使用增強的錯誤處理，提供更多上下文
		details := map[string]interface{}{
			"invoiceID": invoiceID,
			"email":     request.Email,
			"operation": "SendInvoiceEmail",
		}
		utils.HandleErrorWithDetails(c, http.StatusInternalServerError, err, "Failed to send invoice email", details)
		return
	}

	fmt.Printf("Controller: Email sent successfully\n")

	result := map[string]interface{}{
		"success": true,
		"message": "Invoice email sent successfully",
	}

	utils.HandleSuccess(c, result)
}

// CheckEmailLimitStatus 檢查 Email 發送限制狀態
func (ctrl XeroController) CheckEmailLimitStatus(c *gin.Context) {
	canSendEmail, err := ctrl.xeroService.CheckEmailLimitStatus(c.Request.Context())
	if err != nil {
		fmt.Printf("Controller: Error checking email status: %v\n", err)
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to check email status")
		return
	}

	result := map[string]interface{}{
		"canSendEmail": canSendEmail,
		"message":      "Email status checked successfully",
	}

	utils.HandleSuccess(c, result)
}

// GetAccounts 取得科目表
func (ctrl XeroController) GetAccounts(c *gin.Context) {
	accounts, err := ctrl.xeroService.GetAccounts(c.Request.Context())
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to get accounts")
		return
	}

	utils.HandleSuccess(c, accounts)
}

// GetCashAccount 取得現金帳戶
func (ctrl XeroController) GetCashAccount(c *gin.Context) {
	account, err := ctrl.xeroService.GetCashAccount(c.Request.Context())
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to get cash account")
		return
	}

	utils.HandleSuccess(c, account)
}

// formatXeroDate 格式化 XeroDate 為 ISO 8601 字符串，處理空值情況
func formatXeroDate(xeroDate domain.XeroDate) string {
	if xeroDate.Time.IsZero() {
		return ""
	}
	return xeroDate.Time.Format(time.RFC3339)
}
