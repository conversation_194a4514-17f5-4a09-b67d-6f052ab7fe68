import{u as r,aN as f}from"./index.9477d5a3.js";const m=()=>{const{t:s}=r();return{showMessage:({message:a,title:i=s("hint"),timeout:l=1500,persistent:n=!1,color:o="primary",ok:t,onRedirect:e})=>{const c=f.create({title:i,message:a,color:o,persistent:n,html:!0,style:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",textAlign:"center"},ok:{label:s("confirm"),color:"positive"},cancel:l>0||!t?void 0:{label:s("cancel"),color:"negative"}}).onOk(()=>{t?t():e&&e()});l>0&&setTimeout(()=>{c.hide(),e&&e()},l)}}};export{m as u};
