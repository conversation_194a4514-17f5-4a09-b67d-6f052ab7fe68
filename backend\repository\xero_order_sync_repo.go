package repository

import (
	"context"
	"cx/domain"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type XeroOrderSyncRepository interface {
	Create(ctx context.Context, sync *domain.XeroOrderSync) error
	Update(ctx context.Context, sync *domain.XeroOrderSync) error
	GetByOrderUUID(ctx context.Context, orderUUID string) (*domain.XeroOrderSync, error)
	UpdateSyncStatus(ctx context.Context, orderUUID string, status domain.XeroSyncStatus, errorMessage string) error
	UpdateSyncSuccess(ctx context.Context, orderUUID string, xeroInvoiceID, xeroInvoiceNo string) error
}

type xeroOrderSyncRepository struct {
	db *gorm.DB
}

func NewXeroOrderSyncRepository(db *gorm.DB) XeroOrderSyncRepository {
	return &xeroOrderSyncRepository{db: db}
}

func (r *xeroOrderSyncRepository) Create(ctx context.Context, sync *domain.XeroOrderSync) error {
	if sync.UUID == "" {
		sync.UUID = uuid.New().String()
	}
	return r.db.WithContext(ctx).Create(sync).Error
}

func (r *xeroOrderSyncRepository) Update(ctx context.Context, sync *domain.XeroOrderSync) error {
	return r.db.WithContext(ctx).Save(sync).Error
}

func (r *xeroOrderSyncRepository) GetByOrderUUID(ctx context.Context, orderUUID string) (*domain.XeroOrderSync, error) {
	var sync domain.XeroOrderSync
	err := r.db.WithContext(ctx).Where("order_uuid = ?", orderUUID).First(&sync).Error
	if err != nil {
		return nil, err
	}
	return &sync, nil
}

func (r *xeroOrderSyncRepository) UpdateSyncStatus(ctx context.Context, orderUUID string, status domain.XeroSyncStatus, errorMessage string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"sync_status":   status,
		"error_message": errorMessage,
		"last_sync_at":  &now,
		"updated_at":    now,
	}

	return r.db.WithContext(ctx).
		Model(&domain.XeroOrderSync{}).
		Where("order_uuid = ?", orderUUID).
		Updates(updates).Error
}

func (r *xeroOrderSyncRepository) UpdateSyncSuccess(ctx context.Context, orderUUID string, xeroInvoiceID, xeroInvoiceNo string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"sync_status":     domain.XeroSyncStatusSuccess,
		"xero_invoice_id": xeroInvoiceID,
		"xero_invoice_no": xeroInvoiceNo,
		"error_message":   "",
		"last_sync_at":    &now,
		"updated_at":      now,
	}

	return r.db.WithContext(ctx).
		Model(&domain.XeroOrderSync{}).
		Where("order_uuid = ?", orderUUID).
		Updates(updates).Error
}
