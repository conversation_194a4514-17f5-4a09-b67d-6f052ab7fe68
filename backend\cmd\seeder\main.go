package main

import (
	"cx/config"
	"cx/domain"
	"cx/utils"
	"log"
	"net/http"

	"cx/seeder"
)

func main() {
	c, err := config.LoadConfig()
	if err != nil {
		panic(err)
	}

	// 初始化資料庫連線
	db, err := config.ConnectDB(c)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := config.CloseDB(db); err != nil {
			utils.ErrorLog(domain.HttpResponse{
				Status:  http.StatusInternalServerError,
				Message: "failed to close database",
				Error:   err.Error(),
			})
		}
	}()

	// 創建 seeder manager
	manager := seeder.NewManager(db)

	// 註冊所有 seeders
	manager.Register(
		seeder.NewUserSeeder(2),
		seeder.NewProductSeeder(10),
		seeder.NewCustomerSeeder(10),
	)

	// 執行所有 seeders
	if err := manager.Execute(); err != nil {
		log.Fatal(err)
	}

	log.Println("Database seeding completed successfully")
}
