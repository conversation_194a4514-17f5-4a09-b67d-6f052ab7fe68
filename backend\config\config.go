package config

import (
	"cx/domain"
	"cx/utils"
	"net/http"
	"time"
	_ "time/tzdata"
)

type Config struct {
	Server *ServerConfig
	DB     *DBConfig
	WpDB   *DBConfig
	Auth   *AuthConfig
}

var AppConfig *Config

func LoadConfig() (*Config, error) {
	v, err := setupViper()
	if err != nil {
		return nil, err
	}

	serverConfig := setupServerConfig(v)
	dbConfig := setupDBConfig(v)
	wpDBConfig := setupWpDBConfig(v)
	authConfig := setupAuthConfig(v)

	c := Config{
		Server: serverConfig,
		DB:     dbConfig,
		WpDB:   wpDBConfig,
		Auth:   authConfig,
	}
	AppConfig = &c

	setupTimezone()

	return &c, nil
}

func setupTimezone() {
	serverTimezone := AppConfig.Server.ServerTimezone
	if serverTimezone == "" {
		serverTimezone = "Asia/Taipei"
	}

	// set time zone
	timeZone, err := time.LoadLocation(serverTimezone)
	if err != nil {
		utils.ErrorLog(domain.HttpResponse{
			Status:  http.StatusInternalServerError,
			Message: "failed to set timezone",
			Error:   err.Error(),
		})
	} else {
		time.Local = timeZone
	}
}
