package domain

import (
	"time"

	"gorm.io/gorm"
)

type InventoryCountStatus string

const (
	InventoryCountStatusDraft      InventoryCountStatus = "draft"       // 草稿
	InventoryCountStatusInProgress InventoryCountStatus = "in_progress" // 進行中
	InventoryCountStatusCompleted  InventoryCountStatus = "completed"   // 完成
	InventoryCountStatusCancelled  InventoryCountStatus = "cancelled"   // 取消
)

// 盤點單
type InventoryCount struct {
	ID          int64                `gorm:"primaryKey" json:"-"`
	UUID        string               `gorm:"<-:create" json:"uuid"`
	CountNumber string               `json:"count_number"`
	Status      InventoryCountStatus `json:"status"`
	CountDate   time.Time            `json:"count_date"`
	Notes       string               `json:"notes"`
	CreatedByID int64                `json:"-"` // 操作人員ID
	UpdatedByID int64                `json:"-"` // 操作人員ID
	Items       []InventoryCountItem `gorm:"foreignKey:InventoryCountID" json:"items"`
	CreatedAt   time.Time            `gorm:"<-:create" json:"created_at"`
	UpdatedAt   time.Time            `json:"updated_at"`
	DeletedAt   gorm.DeletedAt       `gorm:"index" json:"-"`
}

// 盤點明細
type InventoryCountItem struct {
	ID               int64       `gorm:"primaryKey" json:"-"`
	UUID             string      `gorm:"<-:create" json:"uuid"`
	InventoryCountID int64       `json:"-"`
	ProductID        int64       `json:"-"`
	Product          ProductInfo `gorm:"foreignKey:ProductID" json:"product"`
	ExpectedQuantity int         `json:"expected_quantity"` // 系統中的庫存數量
	ActualQuantity   int         `json:"actual_quantity"`   // 實際盤點數量
	Difference       int         `json:"difference"`        // 差異數量 (actual_quantity - expected_quantity)
	Notes            string      `json:"notes"`
	CreatedByID      int64       `json:"-"` // 操作人員ID
	UpdatedByID      int64       `json:"-"` // 操作人員ID
	CreatedAt        time.Time   `gorm:"<-:create" json:"created_at"`
	UpdatedAt        time.Time   `json:"updated_at"`
}

type CreateStocktakingRequest struct {
	ID          int64                       `gorm:"primaryKey" json:"-"`
	UUID        string                      `json:"uuid"`
	CountNumber string                      `json:"count_number"`
	Status      InventoryCountStatus        `json:"status"`
	CountDate   string                      `json:"count_date"`
	Notes       string                      `json:"notes"`
	CreatedByID int64                       `json:"-"` // 操作人員ID
	UpdatedByID int64                       `json:"-"` // 操作人員ID
	Items       []InventoryCountItemRequest `gorm:"-" json:"items"`
}

func (CreateStocktakingRequest) TableName() string {
	return "inventory_counts"
}

type InventoryCountItemRequest struct {
	ID               int64       `gorm:"primaryKey" json:"-"`
	UUID             string      `json:"uuid"`
	InventoryCountID int64       `json:"-"`
	ProductID        int64       `json:"-"`
	Product          ProductInfo `gorm:"-" json:"product"`
	ExpectedQuantity int         `json:"expected_quantity"` // 系統中的庫存數量
	ActualQuantity   int         `json:"actual_quantity"`   // 實際盤點數量
	Difference       int         `json:"difference"`        // 差異數量 (actual_quantity - expected_quantity)
	Notes            string      `json:"notes"`
	CreatedByID      int64       `json:"-"` // 操作人員ID
	UpdatedByID      int64       `json:"-"` // 操作人員ID
}

func (InventoryCountItemRequest) TableName() string {
	return "inventory_count_items"
}
