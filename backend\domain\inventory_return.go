package domain

import (
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type ReturnType string

const (
	ReturnTypeCustomerReturn ReturnType = "customer_return" // 客戶退貨
	ReturnTypeSupplierReturn ReturnType = "supplier_return" // 供應商退貨
)

type ReturnStatus string

const (
	ReturnStatusDraft      ReturnStatus = "draft"       // 草稿
	ReturnStatusInProgress ReturnStatus = "in_progress" // 進行中
	ReturnStatusCompleted  ReturnStatus = "completed"   // 完成
	ReturnStatusCancelled  ReturnStatus = "cancelled"   // 取消
)

// 退貨單
type Return struct {
	ID             int64          `gorm:"primaryKey" json:"-"`
	UUID           string         `gorm:"<-:create" json:"uuid"`
	ReturnNumber   string         `json:"return_number"`
	ReturnType     ReturnType     `json:"return_type"`
	OrderID        null.Int       `json:"-"` // 相關訂單ID
	CustomerID     null.Int       `json:"-"` // 客戶/供應商 ID
	ReturnDate     time.Time      `json:"return_date"`
	Status         ReturnStatus   `json:"status"`
	TotalAmount    float64        `json:"total_amount"`
	RefundedAmount float64        `json:"refunded_amount"` // 退款金額
	Notes          string         `json:"notes"`
	CreatedByID    int64          `json:"-"` // 操作人員ID
	UpdatedByID    int64          `json:"-"` // 操作人員ID
	Items          []ReturnItem   `gorm:"foreignKey:ReturnID" json:"items"`
	CreatedAt      time.Time      `gorm:"<-:create" json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"-"`
}

// 退貨明細
type ReturnItem struct {
	ID          int64       `gorm:"primaryKey" json:"-"`
	UUID        string      `gorm:"<-:create" json:"uuid"`
	ReturnID    int64       `json:"-"`
	ProductID   int64       `json:"-"`
	Product     ProductInfo `gorm:"foreignKey:ProductID" json:"product"`
	Quantity    int         `json:"quantity"`
	UnitPrice   float64     `json:"unit_price"`
	TotalPrice  float64     `json:"total_price"`
	Notes       string      `json:"notes"`
	CreatedByID int64       `json:"-"` // 操作人員ID
	UpdatedByID int64       `json:"-"` // 操作人員ID
	CreatedAt   time.Time   `gorm:"<-:create" json:"created_at"`
	UpdatedAt   time.Time   `json:"updated_at"`
}

type ReturnFilter struct {
	ReturnNumber string `form:"return_number"`
	ReturnType   string `form:"return_type"`
	Status       string `form:"status"`
	CustomerID   int64  `form:"customer_id"`
	OrderID      int64  `form:"order_id"`
	StartDate    string `form:"start_date"`
	EndDate      string `form:"end_date"`
}

// 創建退貨單請求
type CreateReturnRequest struct {
	ID           int64               `gorm:"primaryKey" json:"-"`
	UUID         string              `json:"-"`
	ReturnNumber string              `json:"return_number"`
	ReturnType   ReturnType          `json:"return_type" binding:"required,oneof=customer_return supplier_return"`
	OrderNo      string              `gorm:"-" json:"order_no"`
	OrderID      null.Int            `json:"-"`
	Customer     CustomerInfo        `gorm:"-" json:"customer"`
	CustomerID   null.Int            `json:"-"`
	ReturnDate   string              `json:"return_date"`
	Notes        *string             `json:"notes"`
	Items        []ReturnItemRequest `gorm:"-" json:"items" binding:"required,dive"`
	CreatedByID  int64               `json:"-"`
	UpdatedByID  int64               `json:"-"`
}

func (CreateReturnRequest) TableName() string {
	return "returns"
}

// 退貨單項目請求
type ReturnItemRequest struct {
	ID          int64       `gorm:"primaryKey" json:"-"`
	UUID        string      `json:"-"`
	ReturnID    int64       `json:"-"`
	ProductID   int64       `json:"product_id"`
	Product     ProductInfo `gorm:"-" json:"product"`
	Quantity    int         `json:"quantity" binding:"required,min=1"`
	Notes       *string     `json:"notes"`
	CreatedByID int64       `json:"-"` // 操作人員ID
	UpdatedByID int64       `json:"-"` // 操作人員ID
}
