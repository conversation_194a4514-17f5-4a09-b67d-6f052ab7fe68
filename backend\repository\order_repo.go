package repository

import (
	"context"
	"cx/domain"
	"cx/utils"
	"fmt"
	"sync"
	"time"

	"gorm.io/gorm"
)

type OrderRepository interface {
	WithTx(tx *gorm.DB) OrderRepository

	Create(ctx context.Context, order *domain.Order) error
	Update(ctx context.Context, uuid string, order *domain.Order) error
	UpdateNotes(ctx context.Context, uuid string, notes string, updatedByID int64) error
	Fetch(ctx context.Context, filter *domain.OrderFilter, pagination *domain.Pagination) ([]domain.Order, error)
	GetByID(ctx context.Context, id int64) (*domain.Order, error)
	GetByUUID(ctx context.Context, uuid string) (*domain.Order, error)
	GetByOrderNo(ctx context.Context, orderNo string) (*domain.Order, error)

	Checkout(ctx context.Context, uuid string, payload *domain.CheckoutPayload) error

	GenerateOrderNumber(ctx context.Context, orderDate time.Time) (string, error)
	VoidOrder(ctx context.Context, uuid string, reason string) error
	Delete(ctx context.Context, uuid string) error
}

type orderRepository struct {
	db    *gorm.DB
	mutex sync.Mutex
}

func NewOrderRepository(db *gorm.DB) OrderRepository {
	return &orderRepository{db: db}
}

func (r *orderRepository) WithTx(tx *gorm.DB) OrderRepository {
	return &orderRepository{db: tx}
}

func (r *orderRepository) Create(ctx context.Context, order *domain.Order) error {
	order.UUID = utils.GenerateUUID()
	return r.db.WithContext(ctx).Create(order).Error
}

func (r *orderRepository) Update(ctx context.Context, uuid string, order *domain.Order) error {
	return r.db.WithContext(ctx).Model(&domain.Order{}).Where("uuid = ?", uuid).Updates(order).Error
}

func (r *orderRepository) UpdateNotes(ctx context.Context, uuid string, notes string, updatedByID int64) error {
	tx := r.db.WithContext(ctx)

	return tx.Model(&domain.Order{}).Where("uuid = ?", uuid).Updates(map[string]interface{}{
		"notes":         notes,
		"updated_by_id": updatedByID,
	}).Error
}

func (r *orderRepository) Fetch(ctx context.Context, filter *domain.OrderFilter, pagination *domain.Pagination) ([]domain.Order, error) {
	var orders []domain.Order

	tx := r.db.WithContext(ctx)

	if filter.Search != "" {
		tx = tx.Where("order_no LIKE ?", "%"+filter.Search+"%")
	}

	if filter.CustomerUUID != "" {
		tx = tx.Joins("JOIN customers ON orders.customer_id = customers.id").
			Where("customers.uuid = ?", filter.CustomerUUID)
	}

	if len(filter.Status) > 0 {
		tx = tx.Where("orders.status IN (?)", filter.Status)
	}

	if len(filter.ExcludeStatus) > 0 {
		tx = tx.Where("orders.status NOT IN (?)", filter.ExcludeStatus)
	}

	if filter.StartDate != "" {
		tx = tx.Where("DATE(order_at) >= DATE(?)", filter.StartDate)
	}

	if filter.EndDate != "" {
		tx = tx.Where("DATE(order_at) <= DATE(?)", filter.EndDate)
	}

	if err := tx.Model(&domain.Order{}).Count(&pagination.RowsNumber).Error; err != nil {
		return nil, err
	}

	tx = pagination.SetPaginationToDB(tx, "order_at", true)

	err := tx.Model(&domain.Order{}).Preload("Customer").Preload("XeroSync").Find(&orders).Error
	if err != nil {
		return nil, err
	}

	return orders, nil
}

func (r *orderRepository) GetByID(ctx context.Context, id int64) (*domain.Order, error) {
	var order domain.Order

	err := r.db.WithContext(ctx).Where("id = ?", id).First(&order).Error
	if err != nil {
		return nil, err
	}

	return &order, nil
}

func (r *orderRepository) GetByUUID(ctx context.Context, uuid string) (*domain.Order, error) {
	var order domain.Order

	tx := r.db.WithContext(ctx)

	err := tx.Preload("Customer").Preload("OrderItems").Preload("OrderItems.Product").Preload("XeroSync").
		Where("uuid = ?", uuid).First(&order).Error
	if err != nil {
		return nil, err
	}

	return &order, nil
}

func (r *orderRepository) GetByOrderNo(ctx context.Context, orderNo string) (*domain.Order, error) {
	var order domain.Order

	err := r.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error

	if err != nil {
		return nil, err
	}

	return &order, nil
}

func (r *orderRepository) Checkout(ctx context.Context, uuid string, payload *domain.CheckoutPayload) error {
	tx := r.db.WithContext(ctx)

	payload.Status = domain.OrderStatusCompleted

	err := tx.Where("uuid = ?", uuid).Updates(payload).Error
	if err != nil {
		return err
	}

	return err
}

func (r *orderRepository) GenerateOrderNumber(ctx context.Context, orderDate time.Time) (string, error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	var prefix string
	// 如果訂單日期大於今天，則為預訂
	if utils.DateAfter(orderDate, time.Now()) {
		prefix = fmt.Sprintf("R%s", orderDate.Format("20060102"))
	} else {
		prefix = fmt.Sprintf("D%s", orderDate.Format("20060102"))
	}

	var lastOrder domain.Order
	result := r.db.WithContext(ctx).
		Where("order_no LIKE ?", prefix+"%").
		Order("order_no DESC").
		First(&lastOrder)

	var sequence int
	if result.Error == gorm.ErrRecordNotFound {
		sequence = 1
	} else {
		fmt.Sscanf(lastOrder.OrderNo[len(prefix):], "%d", &sequence)
		sequence++
	}

	return fmt.Sprintf("%s%04d", prefix, sequence), nil
}

func (r *orderRepository) VoidOrder(ctx context.Context, uuid string, reason string) error {
	tx := r.db.WithContext(ctx).Model(&domain.Order{})

	return tx.Where("uuid = ?", uuid).Updates(map[string]interface{}{
		"status":      domain.OrderStatusVoid,
		"void_reason": reason,
		"voided_at":   time.Now(),
	}).Error
}

func (r *orderRepository) Delete(ctx context.Context, uuid string) error {
	tx := r.db.WithContext(ctx)

	return tx.Where("uuid = ?", uuid).Delete(&domain.Order{}).Error
}
