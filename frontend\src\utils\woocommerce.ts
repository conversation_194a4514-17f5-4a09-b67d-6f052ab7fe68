import { t } from './i18n';

/**
 * 運送方式顯示函數
 * 根據運送方式和標題返回統一的顯示名稱
 * @param method 運送方式代碼
 * @param title 運送方式標題
 * @returns 格式化的運送方式顯示名稱
 */
export const getShippingMethodDisplay = (method?: string, title?: string): string => {
  if (!method && !title) return t('unknown');

  if (title?.includes('Parcel')) {
    return 'Parcel Post';
  } else if (title?.includes('Express')) {
    return 'Express Post';
  } else {
    return title || method || t('unknown');
  }
};
