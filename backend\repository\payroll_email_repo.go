package repository

import (
	"context"
	"cx/domain"

	"gorm.io/gorm"
)

type PayrollEmailRepository interface {
	Create(ctx context.Context, email *domain.PayrollEmailCreatePayload) error
	GetByPayrollID(ctx context.Context, payrollID int64) ([]*domain.PayrollEmail, error)
}

type payrollEmailRepository struct {
	db *gorm.DB
}

func NewPayrollEmailRepository(db *gorm.DB) PayrollEmailRepository {
	return &payrollEmailRepository{db: db}
}

func (r *payrollEmailRepository) Create(ctx context.Context, email *domain.PayrollEmailCreatePayload) error {
	return r.db.WithContext(ctx).Create(email).Error
}

func (r *payrollEmailRepository) GetByPayrollID(ctx context.Context, payrollID int64) ([]*domain.PayrollEmail, error) {
	var emails []*domain.PayrollEmail
	err := r.db.WithContext(ctx).Where("payroll_id = ?", payrollID).Find(&emails).Error
	if err != nil {
		return nil, err
	}
	return emails, nil
}
