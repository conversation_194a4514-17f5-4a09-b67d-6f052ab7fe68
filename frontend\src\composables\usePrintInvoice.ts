import { OrderApi, Order } from '@/api/order';
import { XeroApi } from '@/api/xero';

export function usePrintInvoice() {
  const printInvoice = async (order: Order) => {
    if (!order.xero_sync || order.xero_sync.sync_status == 'syncing') {
      // 取得訂單紀錄
      const orderRecord = await OrderApi.get(order.uuid);
      order.xero_sync = orderRecord.result.xero_sync;

      if (!order.xero_sync) {
        throw new Error('This order has not been synced to Xero, cannot print invoice');
      }
    }

    // 檢查是否有 Xero Invoice ID
    if (order.xero_sync?.xero_invoice_id && order.xero_sync.sync_status === 'success') {
      try {
        console.log(`Printing Xero invoice: ${order.xero_sync.xero_invoice_id}`);
        await printXeroInvoice(order.xero_sync.xero_invoice_id);
        return;
      } catch (error) {
        console.error('Failed to print Xero invoice:', error);

        // 提供更詳細的錯誤信息
        let errorMessage = 'Failed to print Xero invoice';
        if (error instanceof Error) {
          if (error.message.includes('AUTHORISED') || error.message.includes('APPROVED')) {
            errorMessage = 'This invoice needs to be authorised or approved in Xero before printing PDF';
          } else if (error.message.includes('popup blocked')) {
            errorMessage = 'Browser popup blocked, please allow popups and try again';
          } else if (error.message.includes('too small')) {
            errorMessage = 'Invalid PDF data received from Xero, please check invoice status';
          } else {
            errorMessage = `Failed to print Xero invoice: ${error.message}`;
          }
        }

        throw new Error(errorMessage);
      }
    } else if (order.xero_sync?.sync_status === 'pending') {
      throw new Error('This order is still being synced to Xero, please try again later');
    } else if (order.xero_sync?.sync_status === 'failed') {
      throw new Error('Failed to sync this order to Xero, cannot print invoice');
    } else {
      throw new Error('This order has not been synced to Xero, cannot print invoice');
    }
  };

  const printXeroInvoice = async (invoiceId: string) => {
    try {
      console.log(`Requesting PDF for invoice: ${invoiceId}`);

      // 從 Xero API 獲取 PDF (現在直接返回 Blob)
      const blob = await XeroApi.getInvoicePDF(invoiceId);

      console.log('PDF Blob received:', blob);
      console.log(`PDF Blob size: ${blob.size} bytes`);
      console.log(`PDF Blob type: ${blob.type}`);

      // 檢查 Blob 大小
      if (blob.size < 100) {
        throw new Error('PDF data too small, likely invalid');
      }

      // 檢查 Blob 類型
      if (!blob.type.includes('application/pdf')) {
        console.warn(`Unexpected blob type: ${blob.type}`);
      }

      // 創建 Blob URL
      const pdfUrl = URL.createObjectURL(blob);
      console.log(`PDF URL created: ${pdfUrl}`);

      // 先測試 PDF 是否可以正常顯示
      console.log('Testing PDF URL before opening print window...');

      // 在新視窗中開啟 PDF
      const printWindow = window.open(pdfUrl, '_blank');
      if (!printWindow) {
        URL.revokeObjectURL(pdfUrl);
        throw new Error('Failed to open print window - popup blocked?');
      }

      console.log('Print window opened successfully');

      // 等待 PDF 載入
      printWindow.onload = () => {
        console.log('PDF loaded in print window');
        setTimeout(() => {
          printWindow.print();
        }, 1000); // 增加等待時間確保 PDF 完全載入
      };

      // 錯誤處理
      printWindow.onerror = (error) => {
        console.error('Error loading PDF in print window:', error);
        URL.revokeObjectURL(pdfUrl);
        printWindow.close();
      };

      // 清理資源
      setTimeout(() => {
        URL.revokeObjectURL(pdfUrl);
      }, 15000); // 15秒後清理，給更多時間

    } catch (error) {
      console.error('Error printing Xero invoice:', error);
      throw error;
    }
  };

  return {
    printInvoice,
  };
}
