import { apiWrapper } from '@/boot/axios';
import { CreateResponse } from './modules/response';
import { Product } from './product';
import { Pagination } from '@/types';

export interface Customer {
  uuid: string;
  name: string;
  email: string;
  phone: string;
  license_plate: string;
  tax_id: string;
  country: string;
  state: string;
  city: string;
  zipcode: string;
  address: string;
  is_supplier: boolean;
  is_vip: boolean;
  vip_end_date: string | null;
  is_active: boolean;
  note: string;

  products: Product[]; // for supplier
}

export interface CustomerInfo {
  uuid: string;
  name: string;
  email: string;
  phone: string;
  license_plate: string;
  tax_id: string;
}

export interface CustomerFilter {
  search?: string;
  name?: string;
  phone?: string;
  email?: string;
  tax_id?: string;
  is_supplier?: boolean;
  is_active?: boolean;
}

export const CustomerApi = {
  fetch: ({
    filter,
    pagination,
  }: {
    filter?: CustomerFilter;
    pagination?: Pagination;
  } = {}) =>
    apiWrapper.get<{
      data: Customer[];
      pagination: Pagination;
    }>('/v1/customers', {
      params: {
        ...filter,
        ...pagination,
      },
    }),
  get: (uuid: string) => apiWrapper.get<Customer>(`/v1/customers/${uuid}`),
  create: (customer: Customer) =>
    apiWrapper.post<CreateResponse>('/v1/customers', customer),
  update: (customer: Customer) =>
    apiWrapper.put<Customer>(`/v1/customers/${customer.uuid}`, customer),
  delete: (uuid: string) => apiWrapper.delete(`/v1/customers/${uuid}`),
};
