package repository

import (
	"context"
	"cx/domain"

	"gorm.io/gorm"
)

type XeroRepository interface {
	CreateConfig(ctx context.Context, config *domain.XeroConfig) error
	UpdateConfig(ctx context.Context, config *domain.XeroConfig) error
	GetConfig(ctx context.Context) (*domain.XeroConfig, error)
	DeleteConfig(ctx context.Context, id int64) error

	CreateToken(ctx context.Context, token *domain.XeroToken) error
	UpdateToken(ctx context.Context, token *domain.XeroToken) error
	GetToken(ctx context.Context) (*domain.XeroToken, error)
	DeleteToken(ctx context.Context) error

	CreateConnection(ctx context.Context, connection *domain.XeroConnection) error
	UpdateConnection(ctx context.Context, connection *domain.XeroConnection) error
	GetConnection(ctx context.Context, configID int64, tenantID string) (*domain.XeroConnection, error)
	DeleteConnection(ctx context.Context) error
}

type xeroRepository struct {
	db *gorm.DB
}

func NewXeroRepository(db *gorm.DB) XeroRepository {
	return &xeroRepository{db}
}

func (r *xeroRepository) CreateConfig(ctx context.Context, config *domain.XeroConfig) error {
	return r.db.WithContext(ctx).Create(config).Error
}

func (r *xeroRepository) UpdateConfig(ctx context.Context, config *domain.XeroConfig) error {
	return r.db.WithContext(ctx).Model(&domain.XeroConfig{}).Where("id = ?", config.ID).Updates(config).Error
}

func (r *xeroRepository) GetConfig(ctx context.Context) (*domain.XeroConfig, error) {
	var config domain.XeroConfig
	err := r.db.WithContext(ctx).Where("is_active = TRUE").First(&config).Error
	return &config, err
}

func (r *xeroRepository) DeleteConfig(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Delete(&domain.XeroConfig{}, id).Error
}

func (r *xeroRepository) CreateToken(ctx context.Context, token *domain.XeroToken) error {
	return r.db.WithContext(ctx).Create(token).Error
}

func (r *xeroRepository) UpdateToken(ctx context.Context, token *domain.XeroToken) error {
	return r.db.WithContext(ctx).Model(&domain.XeroToken{}).Where("id = ?", token.ID).Updates(token).Error
}

func (r *xeroRepository) GetToken(ctx context.Context) (*domain.XeroToken, error) {
	var token domain.XeroToken
	err := r.db.WithContext(ctx).Preload("Config").First(&token).Error
	return &token, err
}

func (r *xeroRepository) DeleteToken(ctx context.Context) error {
	return r.db.WithContext(ctx).Exec("DELETE FROM xero_tokens").Error
}

func (r *xeroRepository) CreateConnection(ctx context.Context, connection *domain.XeroConnection) error {
	return r.db.WithContext(ctx).Create(connection).Error
}

func (r *xeroRepository) UpdateConnection(ctx context.Context, connection *domain.XeroConnection) error {
	return r.db.WithContext(ctx).Model(&domain.XeroConnection{}).Where("id = ?", connection.ID).Updates(connection).Error
}

func (r *xeroRepository) GetConnection(ctx context.Context, configID int64, tenantID string) (*domain.XeroConnection, error) {
	var connection domain.XeroConnection
	err := r.db.WithContext(ctx).Where("config_id = ? AND tenant_id = ?", configID, tenantID).First(&connection).Error
	return &connection, err
}

func (r *xeroRepository) DeleteConnection(ctx context.Context) error {
	return r.db.WithContext(ctx).Exec("DELETE FROM xero_connections").Error
}
