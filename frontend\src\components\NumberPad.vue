<!-- 數字鍵，包含數字、加減乘除、小數點及Enter鍵 -->
<template>
  <!-- input -->
  <div class="input">
    <span v-for="char in input" :key="char" class="number-input">
      {{ char }}
    </span>
  </div>
  <!-- error hint -->
  <q-form-hint
    v-if="input.length < minLength && isEnter"
    color="negative"
    class="q-mt-sm"
  >
    {{ t('error.min', { min: minLength }) }}
  </q-form-hint>
  <!-- pad -->
  <div class="number-pad">
    <div class="number-row">
      <q-btn class="number" @click="inputNumber(7)">7</q-btn>
      <q-btn class="number" @click="inputNumber(8)">8</q-btn>
      <q-btn class="number" @click="inputNumber(9)">9</q-btn>
    </div>
    <div class="number-row">
      <q-btn class="number" @click="inputNumber(4)">4</q-btn>
      <q-btn class="number" @click="inputNumber(5)">5</q-btn>
      <q-btn class="number" @click="inputNumber(6)">6</q-btn>
    </div>
    <div class="number-row">
      <q-btn class="number" @click="inputNumber(1)">1</q-btn>
      <q-btn class="number" @click="inputNumber(2)">2</q-btn>
      <q-btn class="number" @click="inputNumber(3)">3</q-btn>
    </div>
    <div class="number-row">
      <q-btn class="number" @click="clear">C</q-btn>
      <q-btn class="number" @click="inputNumber(0)">0</q-btn>
      <q-btn class="number" @click="enter">Enter</q-btn>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps<{
  minLength: number;
  maxLength: number;
  isEnter: boolean;
}>();

const emit = defineEmits([
  'update:minLength',
  'update:maxLength',
  'update:isEnter',
  'enterInput',
]);

const input = ref('');

const minLength = computed({
  get: () => props.minLength,
  set: (value) => emit('update:minLength', value),
});

const maxLength = computed({
  get: () => props.maxLength,
  set: (value) => emit('update:maxLength', value),
});

const isError = ref(false);
const inputNumber = (num: number | string) => {
  isError.value = false;
  if (input.value.length >= maxLength.value) {
    return;
  }

  input.value += num;
};

const clear = () => {
  input.value = '';
};

const enter = () => {
  if (input.value.length < minLength.value) {
    isError.value = true;
    return;
  }

  emit('enterInput', input.value);
};
</script>

<style lang="scss" scoped>
.input {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 3rem;
  font-size: 1.5rem;
  border: 1px solid #ccc;
  border-radius: 0.5rem;
  padding: 0 1rem;
}

.number-input {
  margin: 0 0.25rem;
}

.number-pad {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 2rem;
}

.number-row {
  display: flex;
  justify-content: center;
}

.number {
  width: 5rem;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ccc;
  border-radius: 50%;
  margin: 0.5rem;
  cursor: pointer;
}
</style>
