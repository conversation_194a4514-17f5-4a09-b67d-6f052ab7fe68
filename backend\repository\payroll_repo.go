package repository

import (
	"context"
	"cx/domain"
	"cx/utils"

	"gorm.io/gorm"
)

type PayrollRepository interface {
	ListPeriods(ctx context.Context, filter *domain.PayrollPeriodFilter, pagination *domain.Pagination) ([]domain.PayrollPeriodResponse, error)
	GetPeriodByID(ctx context.Context, id int64) (*domain.PayrollPeriodResponse, error)
	GetPeriodByUUID(ctx context.Context, uuid string) (*domain.PayrollPeriodResponse, error)
	CreatePeriod(ctx context.Context, payload *domain.PayrollPeriodCreatePayload) error
	UpdatePeriod(ctx context.Context, payload *domain.PayrollPeriodUpdatePayload) error
	DeletePeriod(ctx context.Context, uuid string) error

	List(ctx context.Context, filter *domain.PayrollFilter, pagination *domain.Pagination) ([]domain.PayrollResponse, error)
	GetByID(ctx context.Context, id int64) (*domain.PayrollResponse, error)
	GetByUUID(ctx context.Context, uuid string) (*domain.PayrollResponse, error)
	GetByUserID(ctx context.Context, userID int64, periodUUID string) (*domain.PayrollResponse, error)
	Create(ctx context.Context, payload *domain.PayrollCreatePayload) error
	Update(ctx context.Context, payload *domain.PayrollUpdatePayload) error

	SaveDetails(ctx context.Context, details []domain.PayrollDetail) error
}

type payrollRepository struct {
	db *gorm.DB
}

func NewPayrollRepository(db *gorm.DB) PayrollRepository {
	return &payrollRepository{db}
}

func (r *payrollRepository) ListPeriods(ctx context.Context, filter *domain.PayrollPeriodFilter, pagination *domain.Pagination) ([]domain.PayrollPeriodResponse, error) {
	var periods []domain.PayrollPeriodResponse

	tx := r.db.WithContext(ctx).Model(&domain.PayrollPeriod{})

	if filter.StartDate != "" {
		tx = tx.Where("start_date >= ?", filter.StartDate)
	}

	if filter.EndDate != "" {
		tx = tx.Where("end_date <= ?", filter.EndDate)
	}

	err := tx.Count(&pagination.RowsNumber).Error
	if err != nil {
		return nil, err
	}

	tx = pagination.SetPaginationToDB(tx, "payroll_periods.created_at", true)

	err = tx.Find(&periods).Error
	if err != nil {
		return nil, err
	}

	return periods, nil
}

func (r *payrollRepository) GetPeriodByID(ctx context.Context, id int64) (*domain.PayrollPeriodResponse, error) {
	var period domain.PayrollPeriodResponse

	tx := r.db.WithContext(ctx)

	err := tx.Where("id = ?", id).First(&period).Error
	if err != nil {
		return nil, err
	}
	return &period, nil
}

func (r *payrollRepository) GetPeriodByUUID(ctx context.Context, uuid string) (*domain.PayrollPeriodResponse, error) {
	var period domain.PayrollPeriodResponse

	tx := r.db.WithContext(ctx)

	err := tx.Where("uuid = ?", uuid).First(&period).Error
	if err != nil {
		return nil, err
	}
	return &period, nil
}

func (r *payrollRepository) CreatePeriod(ctx context.Context, payload *domain.PayrollPeriodCreatePayload) error {
	payload.UUID = utils.GenerateUUID()
	tx := r.db.WithContext(ctx)
	return tx.Create(payload).Error
}

func (r *payrollRepository) UpdatePeriod(ctx context.Context, payload *domain.PayrollPeriodUpdatePayload) error {
	tx := r.db.WithContext(ctx)
	return tx.Where("uuid = ?", payload.UUID).
		Updates(payload).Error
}

func (r *payrollRepository) DeletePeriod(ctx context.Context, uuid string) error {
	tx := r.db.WithContext(ctx)
	return tx.Where("uuid = ?", uuid).Delete(&domain.PayrollPeriod{}).Error
}

func (r *payrollRepository) List(ctx context.Context, filter *domain.PayrollFilter, pagination *domain.Pagination) ([]domain.PayrollResponse, error) {
	var payrollList []domain.PayrollResponse

	tx := r.db.WithContext(ctx).Model(&domain.Payroll{})

	if filter.UserUUID != "" {
		tx = tx.Joins("JOIN users ON users.id = payrolls.user_id").
			Where("users.uuid = ?", filter.UserUUID)
	}

	if filter.PeriodUUID != "" {
		tx = tx.Joins("JOIN payroll_periods ON payroll_periods.id = payrolls.period_id").
			Where("payroll_periods.uuid = ?", filter.PeriodUUID)
	}

	err := tx.Count(&pagination.RowsNumber).Error
	if err != nil {
		return nil, err
	}

	tx = pagination.SetPaginationToDB(tx, "payrolls.id", true)

	err = tx.Preload("Period").Preload("User").Preload("PayrollDetails.SalaryItem").Preload("Emails").
		Find(&payrollList).Error
	if err != nil {
		return nil, err
	}

	return payrollList, nil
}

func (r *payrollRepository) GetByID(ctx context.Context, id int64) (*domain.PayrollResponse, error) {
	var payroll domain.PayrollResponse

	tx := r.db.WithContext(ctx)

	err := tx.Preload("Period").Preload("User").Preload("PayrollDetails.SalaryItem").Preload("Emails").
		Where("id = ?", id).
		First(&payroll).Error
	if err != nil {
		return nil, err
	}
	return &payroll, nil
}

func (r *payrollRepository) GetByUUID(ctx context.Context, uuid string) (*domain.PayrollResponse, error) {
	var payroll domain.PayrollResponse

	tx := r.db.WithContext(ctx)

	err := tx.Preload("Period").Preload("User").Preload("PayrollDetails.SalaryItem").Preload("Emails").
		Where("uuid = ?", uuid).
		First(&payroll).Error
	if err != nil {
		return nil, err
	}
	return &payroll, nil
}
func (r *payrollRepository) GetByUserID(ctx context.Context, userID int64, periodUUID string) (*domain.PayrollResponse, error) {
	var payroll domain.PayrollResponse

	tx := r.db.WithContext(ctx)

	err := tx.Preload("Period").Preload("User").Preload("PayrollDetails.SalaryItem").Preload("Emails").
		Where("user_id = ? AND period_id = (SELECT id FROM payroll_periods WHERE uuid = ?)",
			userID, periodUUID).
		First(&payroll).Error
	if err != nil {
		return nil, err
	}

	return &payroll, nil
}

func (r *payrollRepository) Create(ctx context.Context, payload *domain.PayrollCreatePayload) error {
	payload.UUID = utils.GenerateUUID()
	tx := r.db.WithContext(ctx)
	return tx.Create(payload).Error
}

func (r *payrollRepository) Update(ctx context.Context, payload *domain.PayrollUpdatePayload) error {
	tx := r.db.WithContext(ctx)
	return tx.Where("uuid = ?", payload.UUID).
		Updates(payload).Error
}

func (r *payrollRepository) SaveDetails(ctx context.Context, details []domain.PayrollDetail) error {
	tx := r.db.WithContext(ctx)
	return tx.Save(details).Error
}
