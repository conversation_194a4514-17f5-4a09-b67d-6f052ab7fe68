import { i18n } from '@/boot/i18n';

/**
 * 獲取當前語言
 */
export const getCurrentLocale = (): string => {
  return i18n.global.locale.value;
};

/**
 * 設置語言
 */
export const setLocale = (locale: string): void => {
  i18n.global.locale.value = locale;
  localStorage.setItem('user-locale', locale);
};

/**
 * 獲取支持的語言列表
 */
export const getSupportedLocales = () => {
  return [
    { code: 'en-US', label: 'English', flag: '🇺🇸' },
    { code: 'zh-TW', label: '中文', flag: '🇹🇼' },
  ];
};

/**
 * 從 localStorage 恢復語言設置
 */
export const restoreLocale = (): void => {
  const savedLocale = localStorage.getItem('user-locale');
  if (savedLocale && getSupportedLocales().some(lang => lang.code === savedLocale)) {
    setLocale(savedLocale);
  }
};

/**
 * 翻譯函數（可在非 Vue 組件中使用）
 */
export const t = (key: string, params?: Record<string, any>): string => {
  return i18n.global.t(key, params);
};

/**
 * 檢查是否為中文語言
 */
export const isChineseLocale = (): boolean => {
  return getCurrentLocale().startsWith('zh');
};

/**
 * 檢查是否為英文語言
 */
export const isEnglishLocale = (): boolean => {
  return getCurrentLocale().startsWith('en');
};

/**
 * 格式化日期（根據當前語言）
 */
export const formatDateByLocale = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isChineseLocale()) {
    return dateObj.toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  } else {
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }
};

/**
 * 格式化數字（根據當前語言）
 */
export const formatNumberByLocale = (number: number): string => {
  if (isChineseLocale()) {
    return number.toLocaleString('zh-TW');
  } else {
    return number.toLocaleString('en-US');
  }
};

/**
 * 格式化貨幣（根據當前語言）
 */
export const formatCurrencyByLocale = (amount: number, currency = 'AUD'): string => {
  if (isChineseLocale()) {
    return new Intl.NumberFormat('zh-TW', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  } else {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  }
};
