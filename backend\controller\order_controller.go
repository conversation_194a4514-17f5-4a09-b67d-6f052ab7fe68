package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"gopkg.in/guregu/null.v4"
)

type OrderController struct {
	orderService service.OrderService
	userService  service.UserService
}

func NewOrderController(r *gin.RouterGroup, orderService service.OrderService, userService service.UserService) {
	orderController := OrderController{orderService, userService}

	v1 := r.Group("/v1/orders")
	{
		v1.GET("", orderController.FetchHandler)
		v1.GET("/:uuid", orderController.GetHandler) // uuid
		v1.POST("", orderController.CreateHandler)
		v1.POST("/:uuid/checkout", orderController.CheckoutHandler)
		v1.PUT("/:uuid", orderController.UpdateHandler)
		v1.PATCH("/:uuid/notes", orderController.UpdateNotesHandler)
		v1.POST("/:uuid", orderController.VoidOrderHandler)
		v1.DELETE("/:uuid", orderController.DeleteOrderHandler)
	}
}

func (ctr *OrderController) FetchHandler(c *gin.Context) {
	req := struct {
		Filter     domain.OrderFilter `json:"filter"`
		Pagination domain.Pagination  `json:"pagination"`
	}{}

	if err := c.ShouldBindQuery(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	orders, err := ctr.orderService.Fetch(c, &req.Filter, &req.Pagination)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to fetch orders")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"data":       orders,
		"pagination": req.Pagination,
	})
}

func (ctr *OrderController) GetHandler(c *gin.Context) {
	uuid := c.Param("uuid")

	order, err := ctr.orderService.GetByUUID(c, uuid)
	if err != nil {
		utils.HandleError(c, http.StatusNotFound, err, "Order not found")
		return
	}

	utils.HandleSuccess(c, order)
}

func (ctr *OrderController) CreateHandler(c *gin.Context) {
	var order domain.Order

	userID := c.GetInt64("user_id")
	order.CreatedByID = null.IntFrom(userID)
	order.UpdatedByID = null.IntFrom(userID)
	fmt.Println("userUUID:", c.GetString("user_uuid"))
	fmt.Println("userID:", userID)

	err := ctr.orderService.Create(c, &order)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to create order")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"uuid": order.UUID,
	})
}

func (ctr *OrderController) CheckoutHandler(c *gin.Context) {
	var req domain.CheckoutPayload

	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	userID := c.GetInt64("user_id")
	req.UpdatedByID = null.IntFrom(userID)

	orderUUID := c.Param("uuid")
	req.UserAgent = c.Request.UserAgent()
	req.IPAddress = c.ClientIP()

	err := ctr.orderService.Checkout(c, orderUUID, &req)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to checkout order")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *OrderController) UpdateHandler(c *gin.Context) {
	uuid := c.Param("uuid")

	var order domain.Order
	if err := c.ShouldBind(&order); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	err := ctr.orderService.Update(c, uuid, &order)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update order")
		return
	}

	utils.HandleSuccess(c, order)
}

func (ctr *OrderController) UpdateNotesHandler(c *gin.Context) {
	uuid := c.Param("uuid")

	var payload struct {
		Notes       string `json:"notes"`
		UpdatedByID int64  `json:"-"`
	}
	if err := c.ShouldBind(&payload); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	userID := c.GetInt64("user_id")
	payload.UpdatedByID = userID

	err := ctr.orderService.UpdateNotes(c, uuid, payload.Notes, payload.UpdatedByID)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update order notes")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *OrderController) VoidOrderHandler(c *gin.Context) {
	var payload domain.VoidOrderPayload

	uuid := c.Param("uuid")

	if err := c.ShouldBind(&payload); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	userID := c.GetInt64("user_id")
	payload.UpdatedByID = userID

	err := ctr.orderService.VoidOrder(c, uuid, &payload)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to void order")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *OrderController) DeleteOrderHandler(c *gin.Context) {
	uuid := c.Param("uuid")

	err := ctr.orderService.DeleteOrder(c, uuid)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to delete order")
		return
	}

	utils.HandleSuccess(c, nil)
}
