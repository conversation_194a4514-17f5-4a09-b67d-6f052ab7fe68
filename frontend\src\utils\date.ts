export function formatDate(
  dateStr: Date | string | number | undefined,
  format = 'YYYY-MM-DD'
) {
  if (!dateStr) {
    return '';
  }

  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();
  const pad = (n: number) => n.toString().padStart(2, '0');

  return format
    .replace('YYYY', year.toString())
    .replace('MM', pad(month))
    .replace('DD', pad(day))
    .replace('HH', pad(hour))
    .replace('mm', pad(minute))
    .replace('ss', pad(second));
}

// 自定義日期驗證規則
export const dateRules = [
  (val: string) => {
    // 如果值為空，則直接通過驗證
    if (!val || val === '') return true;

    // 否則，使用 Quasar 內建的日期驗證
    const datePattern = /^(\d{4})\-(\d{1,2})\-(\d{1,2})$/;
    return datePattern.test(val) || 'YYYY-MM-DD';
  },
];
