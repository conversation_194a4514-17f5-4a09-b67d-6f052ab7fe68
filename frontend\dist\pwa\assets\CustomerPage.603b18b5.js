import{Q as ye,a as Re}from"./QToolbar.0e8beb30.js";import{d as ve,u as be,c as Y,r as C,s as Fe,w as Ve,o as y,a as K,b as a,f as i,z as ie,q as n,g as Me,y as j,i as l,F as H,p,t as d,m as N,aS as oe,Q as g,aI as Ae,h as ue,C as J,aH as Ce,k as de,x as pe,bI as Ue,a$ as Oe,n as Le,l as Pe,bw as Ke}from"./index.9477d5a3.js";import{Q as T}from"./QTd.6cf74813.js";import{Q as Be,f as we}from"./QTr.5f91a8aa.js";import{Q as ce}from"./QTable.e4650bec.js";import{Q as xe}from"./QPage.8bf63692.js";import{C as Z}from"./customer.c1e7442d.js";import{u as He}from"./useCustomer.1cae112a.js";import{f as me,d as ke}from"./date.6d29930c.js";import{Q as Ye}from"./QSpace.bd91c020.js";import{Q as Ze,d as We}from"./QDate.10e993fc.js";import{Q as Qe}from"./QPopupProxy.1fd4617d.js";import{Q as fe}from"./QSelect.c4b20219.js";import{Q as qe}from"./QScrollArea.44613085.js";import{C as X}from"./ClosePopup.f68b6158.js";import{Q as ze}from"./QForm.534886fc.js";import{P as ne}from"./product.84ec250a.js";import{O as Je}from"./usePrintInvoice.e1a96b8f.js";import{u as Xe}from"./dialog.c8d4f0ed.js";import{c as $e}from"./order.31b2d3ca.js";import{_ as je}from"./TablePagination.215cbc35.js";import{_ as ea}from"./OrderDetailDialog.c8ef92b5.js";import{_ as aa}from"./DateRangePicker.7842c5b2.js";import{_ as ua}from"./plugin-vue_export-helper.21dcd24c.js";import"./QList.f0282e16.js";import"./use-fullscreen.e0d6e2b2.js";import"./format.054b8074.js";import"./QMenu.abf49c1b.js";import"./selection.787abe58.js";import"./QItemSection.7dc1f54f.js";import"./QItemLabel.3b58be08.js";import"./QScrollObserver.b02e8e20.js";import"./TouchPan.ddd1c5b8.js";import"./xero.72a68f5d.js";import"./use-quasar.66282a44.js";import"./useOrder.c3e6878c.js";var ee={},ta=[["AF","AFG","004","ISO 3166-2:AF"],["AL","ALB","008","ISO 3166-2:AL"],["DZ","DZA","012","ISO 3166-2:DZ"],["AS","ASM","016","ISO 3166-2:AS"],["AD","AND","020","ISO 3166-2:AD"],["AO","AGO","024","ISO 3166-2:AO"],["AI","AIA","660","ISO 3166-2:AI"],["AQ","ATA","010","ISO 3166-2:AQ"],["AG","ATG","028","ISO 3166-2:AG"],["AR","ARG","032","ISO 3166-2:AR"],["AM","ARM","051","ISO 3166-2:AM"],["AW","ABW","533","ISO 3166-2:AW"],["AU","AUS","036","ISO 3166-2:AU"],["AT","AUT","040","ISO 3166-2:AT"],["AZ","AZE","031","ISO 3166-2:AZ"],["BS","BHS","044","ISO 3166-2:BS"],["BH","BHR","048","ISO 3166-2:BH"],["BD","BGD","050","ISO 3166-2:BD"],["BB","BRB","052","ISO 3166-2:BB"],["BY","BLR","112","ISO 3166-2:BY"],["BE","BEL","056","ISO 3166-2:BE"],["BZ","BLZ","084","ISO 3166-2:BZ"],["BJ","BEN","204","ISO 3166-2:BJ"],["BM","BMU","060","ISO 3166-2:BM"],["BT","BTN","064","ISO 3166-2:BT"],["BO","BOL","068","ISO 3166-2:BO"],["BA","BIH","070","ISO 3166-2:BA"],["BW","BWA","072","ISO 3166-2:BW"],["BV","BVT","074","ISO 3166-2:BV"],["BR","BRA","076","ISO 3166-2:BR"],["IO","IOT","086","ISO 3166-2:IO"],["BN","BRN","096","ISO 3166-2:BN"],["BG","BGR","100","ISO 3166-2:BG"],["BF","BFA","854","ISO 3166-2:BF"],["BI","BDI","108","ISO 3166-2:BI"],["KH","KHM","116","ISO 3166-2:KH"],["CM","CMR","120","ISO 3166-2:CM"],["CA","CAN","124","ISO 3166-2:CA"],["CV","CPV","132","ISO 3166-2:CV"],["KY","CYM","136","ISO 3166-2:KY"],["CF","CAF","140","ISO 3166-2:CF"],["TD","TCD","148","ISO 3166-2:TD"],["CL","CHL","152","ISO 3166-2:CL"],["CN","CHN","156","ISO 3166-2:CN"],["CX","CXR","162","ISO 3166-2:CX"],["CC","CCK","166","ISO 3166-2:CC"],["CO","COL","170","ISO 3166-2:CO"],["KM","COM","174","ISO 3166-2:KM"],["CG","COG","178","ISO 3166-2:CG"],["CD","COD","180","ISO 3166-2:CD"],["CK","COK","184","ISO 3166-2:CK"],["CR","CRI","188","ISO 3166-2:CR"],["CI","CIV","384","ISO 3166-2:CI"],["HR","HRV","191","ISO 3166-2:HR"],["CU","CUB","192","ISO 3166-2:CU"],["CY","CYP","196","ISO 3166-2:CY"],["CZ","CZE","203","ISO 3166-2:CZ"],["DK","DNK","208","ISO 3166-2:DK"],["DJ","DJI","262","ISO 3166-2:DJ"],["DM","DMA","212","ISO 3166-2:DM"],["DO","DOM","214","ISO 3166-2:DO"],["EC","ECU","218","ISO 3166-2:EC"],["EG","EGY","818","ISO 3166-2:EG"],["SV","SLV","222","ISO 3166-2:SV"],["GQ","GNQ","226","ISO 3166-2:GQ"],["ER","ERI","232","ISO 3166-2:ER"],["EE","EST","233","ISO 3166-2:EE"],["ET","ETH","231","ISO 3166-2:ET"],["FK","FLK","238","ISO 3166-2:FK"],["FO","FRO","234","ISO 3166-2:FO"],["FJ","FJI","242","ISO 3166-2:FJ"],["FI","FIN","246","ISO 3166-2:FI"],["FR","FRA","250","ISO 3166-2:FR"],["GF","GUF","254","ISO 3166-2:GF"],["PF","PYF","258","ISO 3166-2:PF"],["TF","ATF","260","ISO 3166-2:TF"],["GA","GAB","266","ISO 3166-2:GA"],["GM","GMB","270","ISO 3166-2:GM"],["GE","GEO","268","ISO 3166-2:GE"],["DE","DEU","276","ISO 3166-2:DE"],["GH","GHA","288","ISO 3166-2:GH"],["GI","GIB","292","ISO 3166-2:GI"],["GR","GRC","300","ISO 3166-2:GR"],["GL","GRL","304","ISO 3166-2:GL"],["GD","GRD","308","ISO 3166-2:GD"],["GP","GLP","312","ISO 3166-2:GP"],["GU","GUM","316","ISO 3166-2:GU"],["GT","GTM","320","ISO 3166-2:GT"],["GN","GIN","324","ISO 3166-2:GN"],["GW","GNB","624","ISO 3166-2:GW"],["GY","GUY","328","ISO 3166-2:GY"],["HT","HTI","332","ISO 3166-2:HT"],["HM","HMD","334","ISO 3166-2:HM"],["VA","VAT","336","ISO 3166-2:VA"],["HN","HND","340","ISO 3166-2:HN"],["HK","HKG","344","ISO 3166-2:HK"],["HU","HUN","348","ISO 3166-2:HU"],["IS","ISL","352","ISO 3166-2:IS"],["IN","IND","356","ISO 3166-2:IN"],["ID","IDN","360","ISO 3166-2:ID"],["IR","IRN","364","ISO 3166-2:IR"],["IQ","IRQ","368","ISO 3166-2:IQ"],["IE","IRL","372","ISO 3166-2:IE"],["IL","ISR","376","ISO 3166-2:IL"],["IT","ITA","380","ISO 3166-2:IT"],["JM","JAM","388","ISO 3166-2:JM"],["JP","JPN","392","ISO 3166-2:JP"],["JO","JOR","400","ISO 3166-2:JO"],["KZ","KAZ","398","ISO 3166-2:KZ"],["KE","KEN","404","ISO 3166-2:KE"],["KI","KIR","296","ISO 3166-2:KI"],["KP","PRK","408","ISO 3166-2:KP"],["KR","KOR","410","ISO 3166-2:KR"],["KW","KWT","414","ISO 3166-2:KW"],["KG","KGZ","417","ISO 3166-2:KG"],["LA","LAO","418","ISO 3166-2:LA"],["LV","LVA","428","ISO 3166-2:LV"],["LB","LBN","422","ISO 3166-2:LB"],["LS","LSO","426","ISO 3166-2:LS"],["LR","LBR","430","ISO 3166-2:LR"],["LY","LBY","434","ISO 3166-2:LY"],["LI","LIE","438","ISO 3166-2:LI"],["LT","LTU","440","ISO 3166-2:LT"],["LU","LUX","442","ISO 3166-2:LU"],["MO","MAC","446","ISO 3166-2:MO"],["MG","MDG","450","ISO 3166-2:MG"],["MW","MWI","454","ISO 3166-2:MW"],["MY","MYS","458","ISO 3166-2:MY"],["MV","MDV","462","ISO 3166-2:MV"],["ML","MLI","466","ISO 3166-2:ML"],["MT","MLT","470","ISO 3166-2:MT"],["MH","MHL","584","ISO 3166-2:MH"],["MQ","MTQ","474","ISO 3166-2:MQ"],["MR","MRT","478","ISO 3166-2:MR"],["MU","MUS","480","ISO 3166-2:MU"],["YT","MYT","175","ISO 3166-2:YT"],["MX","MEX","484","ISO 3166-2:MX"],["FM","FSM","583","ISO 3166-2:FM"],["MD","MDA","498","ISO 3166-2:MD"],["MC","MCO","492","ISO 3166-2:MC"],["MN","MNG","496","ISO 3166-2:MN"],["MS","MSR","500","ISO 3166-2:MS"],["MA","MAR","504","ISO 3166-2:MA"],["MZ","MOZ","508","ISO 3166-2:MZ"],["MM","MMR","104","ISO 3166-2:MM"],["NA","NAM","516","ISO 3166-2:NA"],["NR","NRU","520","ISO 3166-2:NR"],["NP","NPL","524","ISO 3166-2:NP"],["NL","NLD","528","ISO 3166-2:NL"],["NC","NCL","540","ISO 3166-2:NC"],["NZ","NZL","554","ISO 3166-2:NZ"],["NI","NIC","558","ISO 3166-2:NI"],["NE","NER","562","ISO 3166-2:NE"],["NG","NGA","566","ISO 3166-2:NG"],["NU","NIU","570","ISO 3166-2:NU"],["NF","NFK","574","ISO 3166-2:NF"],["MP","MNP","580","ISO 3166-2:MP"],["MK","MKD","807","ISO 3166-2:MK"],["NO","NOR","578","ISO 3166-2:NO"],["OM","OMN","512","ISO 3166-2:OM"],["PK","PAK","586","ISO 3166-2:PK"],["PW","PLW","585","ISO 3166-2:PW"],["PS","PSE","275","ISO 3166-2:PS"],["PA","PAN","591","ISO 3166-2:PA"],["PG","PNG","598","ISO 3166-2:PG"],["PY","PRY","600","ISO 3166-2:PY"],["PE","PER","604","ISO 3166-2:PE"],["PH","PHL","608","ISO 3166-2:PH"],["PN","PCN","612","ISO 3166-2:PN"],["PL","POL","616","ISO 3166-2:PL"],["PT","PRT","620","ISO 3166-2:PT"],["PR","PRI","630","ISO 3166-2:PR"],["QA","QAT","634","ISO 3166-2:QA"],["RE","REU","638","ISO 3166-2:RE"],["RO","ROU","642","ISO 3166-2:RO"],["RU","RUS","643","ISO 3166-2:RU"],["RW","RWA","646","ISO 3166-2:RW"],["SH","SHN","654","ISO 3166-2:SH"],["KN","KNA","659","ISO 3166-2:KN"],["LC","LCA","662","ISO 3166-2:LC"],["PM","SPM","666","ISO 3166-2:PM"],["VC","VCT","670","ISO 3166-2:VC"],["WS","WSM","882","ISO 3166-2:WS"],["SM","SMR","674","ISO 3166-2:SM"],["ST","STP","678","ISO 3166-2:ST"],["SA","SAU","682","ISO 3166-2:SA"],["SN","SEN","686","ISO 3166-2:SN"],["SC","SYC","690","ISO 3166-2:SC"],["SL","SLE","694","ISO 3166-2:SL"],["SG","SGP","702","ISO 3166-2:SG"],["SK","SVK","703","ISO 3166-2:SK"],["SI","SVN","705","ISO 3166-2:SI"],["SB","SLB","090","ISO 3166-2:SB"],["SO","SOM","706","ISO 3166-2:SO"],["ZA","ZAF","710","ISO 3166-2:ZA"],["GS","SGS","239","ISO 3166-2:GS"],["ES","ESP","724","ISO 3166-2:ES"],["LK","LKA","144","ISO 3166-2:LK"],["SD","SDN","729","ISO 3166-2:SD"],["SR","SUR","740","ISO 3166-2:SR"],["SJ","SJM","744","ISO 3166-2:SJ"],["SZ","SWZ","748","ISO 3166-2:SZ"],["SE","SWE","752","ISO 3166-2:SE"],["CH","CHE","756","ISO 3166-2:CH"],["SY","SYR","760","ISO 3166-2:SY"],["TW","TWN","158","ISO 3166-2:TW"],["TJ","TJK","762","ISO 3166-2:TJ"],["TZ","TZA","834","ISO 3166-2:TZ"],["TH","THA","764","ISO 3166-2:TH"],["TL","TLS","626","ISO 3166-2:TL"],["TG","TGO","768","ISO 3166-2:TG"],["TK","TKL","772","ISO 3166-2:TK"],["TO","TON","776","ISO 3166-2:TO"],["TT","TTO","780","ISO 3166-2:TT"],["TN","TUN","788","ISO 3166-2:TN"],["TR","TUR","792","ISO 3166-2:TR"],["TM","TKM","795","ISO 3166-2:TM"],["TC","TCA","796","ISO 3166-2:TC"],["TV","TUV","798","ISO 3166-2:TV"],["UG","UGA","800","ISO 3166-2:UG"],["UA","UKR","804","ISO 3166-2:UA"],["AE","ARE","784","ISO 3166-2:AE"],["GB","GBR","826","ISO 3166-2:GB"],["US","USA","840","ISO 3166-2:US"],["UM","UMI","581","ISO 3166-2:UM"],["UY","URY","858","ISO 3166-2:UY"],["UZ","UZB","860","ISO 3166-2:UZ"],["VU","VUT","548","ISO 3166-2:VU"],["VE","VEN","862","ISO 3166-2:VE"],["VN","VNM","704","ISO 3166-2:VN"],["VG","VGB","092","ISO 3166-2:VG"],["VI","VIR","850","ISO 3166-2:VI"],["WF","WLF","876","ISO 3166-2:WF"],["EH","ESH","732","ISO 3166-2:EH"],["YE","YEM","887","ISO 3166-2:YE"],["ZM","ZMB","894","ISO 3166-2:ZM"],["ZW","ZWE","716","ISO 3166-2:ZW"],["AX","ALA","248","ISO 3166-2:AX"],["BQ","BES","535","ISO 3166-2:BQ"],["CW","CUW","531","ISO 3166-2:CW"],["GG","GGY","831","ISO 3166-2:GG"],["IM","IMN","833","ISO 3166-2:IM"],["JE","JEY","832","ISO 3166-2:JE"],["ME","MNE","499","ISO 3166-2:ME"],["BL","BLM","652","ISO 3166-2:BL"],["MF","MAF","663","ISO 3166-2:MF"],["RS","SRB","688","ISO 3166-2:RS"],["SX","SXM","534","ISO 3166-2:SX"],["SS","SSD","728","ISO 3166-2:SS"],["XK","XKK","983","ISO 3166-2:XK"]],la=["br","cy","dv","sw","eu","af","am","ha","ku","ml","mt","no","ps","sd","so","sq","ta","tg","tt","ug","ur","vi","ar","az","be","bg","bn","bs","ca","cs","da","de","el","en","es","et","fa","fi","fr","ga","gl","he","hi","hr","hu","hy","id","is","it","ja","ka","kk","km","ko","ky","lt","lv","mk","mn","mr","ms","nb","nl","nn","pl","pt","ro","ru","sk","sl","sr","sv","th","tk","tr","uk","uz","zh"],te={};te.remove=sa;var ae=[{base:" ",chars:"\xA0"},{base:"0",chars:"\u07C0"},{base:"A",chars:"\u24B6\uFF21\xC0\xC1\xC2\u1EA6\u1EA4\u1EAA\u1EA8\xC3\u0100\u0102\u1EB0\u1EAE\u1EB4\u1EB2\u0226\u01E0\xC4\u01DE\u1EA2\xC5\u01FA\u01CD\u0200\u0202\u1EA0\u1EAC\u1EB6\u1E00\u0104\u023A\u2C6F"},{base:"AA",chars:"\uA732"},{base:"AE",chars:"\xC6\u01FC\u01E2"},{base:"AO",chars:"\uA734"},{base:"AU",chars:"\uA736"},{base:"AV",chars:"\uA738\uA73A"},{base:"AY",chars:"\uA73C"},{base:"B",chars:"\u24B7\uFF22\u1E02\u1E04\u1E06\u0243\u0181"},{base:"C",chars:"\u24B8\uFF23\uA73E\u1E08\u0106C\u0108\u010A\u010C\xC7\u0187\u023B"},{base:"D",chars:"\u24B9\uFF24\u1E0A\u010E\u1E0C\u1E10\u1E12\u1E0E\u0110\u018A\u0189\u1D05\uA779"},{base:"Dh",chars:"\xD0"},{base:"DZ",chars:"\u01F1\u01C4"},{base:"Dz",chars:"\u01F2\u01C5"},{base:"E",chars:"\u025B\u24BA\uFF25\xC8\xC9\xCA\u1EC0\u1EBE\u1EC4\u1EC2\u1EBC\u0112\u1E14\u1E16\u0114\u0116\xCB\u1EBA\u011A\u0204\u0206\u1EB8\u1EC6\u0228\u1E1C\u0118\u1E18\u1E1A\u0190\u018E\u1D07"},{base:"F",chars:"\uA77C\u24BB\uFF26\u1E1E\u0191\uA77B"},{base:"G",chars:"\u24BC\uFF27\u01F4\u011C\u1E20\u011E\u0120\u01E6\u0122\u01E4\u0193\uA7A0\uA77D\uA77E\u0262"},{base:"H",chars:"\u24BD\uFF28\u0124\u1E22\u1E26\u021E\u1E24\u1E28\u1E2A\u0126\u2C67\u2C75\uA78D"},{base:"I",chars:"\u24BE\uFF29\xCC\xCD\xCE\u0128\u012A\u012C\u0130\xCF\u1E2E\u1EC8\u01CF\u0208\u020A\u1ECA\u012E\u1E2C\u0197"},{base:"J",chars:"\u24BF\uFF2A\u0134\u0248\u0237"},{base:"K",chars:"\u24C0\uFF2B\u1E30\u01E8\u1E32\u0136\u1E34\u0198\u2C69\uA740\uA742\uA744\uA7A2"},{base:"L",chars:"\u24C1\uFF2C\u013F\u0139\u013D\u1E36\u1E38\u013B\u1E3C\u1E3A\u0141\u023D\u2C62\u2C60\uA748\uA746\uA780"},{base:"LJ",chars:"\u01C7"},{base:"Lj",chars:"\u01C8"},{base:"M",chars:"\u24C2\uFF2D\u1E3E\u1E40\u1E42\u2C6E\u019C\u03FB"},{base:"N",chars:"\uA7A4\u0220\u24C3\uFF2E\u01F8\u0143\xD1\u1E44\u0147\u1E46\u0145\u1E4A\u1E48\u019D\uA790\u1D0E"},{base:"NJ",chars:"\u01CA"},{base:"Nj",chars:"\u01CB"},{base:"O",chars:"\u24C4\uFF2F\xD2\xD3\xD4\u1ED2\u1ED0\u1ED6\u1ED4\xD5\u1E4C\u022C\u1E4E\u014C\u1E50\u1E52\u014E\u022E\u0230\xD6\u022A\u1ECE\u0150\u01D1\u020C\u020E\u01A0\u1EDC\u1EDA\u1EE0\u1EDE\u1EE2\u1ECC\u1ED8\u01EA\u01EC\xD8\u01FE\u0186\u019F\uA74A\uA74C"},{base:"OE",chars:"\u0152"},{base:"OI",chars:"\u01A2"},{base:"OO",chars:"\uA74E"},{base:"OU",chars:"\u0222"},{base:"P",chars:"\u24C5\uFF30\u1E54\u1E56\u01A4\u2C63\uA750\uA752\uA754"},{base:"Q",chars:"\u24C6\uFF31\uA756\uA758\u024A"},{base:"R",chars:"\u24C7\uFF32\u0154\u1E58\u0158\u0210\u0212\u1E5A\u1E5C\u0156\u1E5E\u024C\u2C64\uA75A\uA7A6\uA782"},{base:"S",chars:"\u24C8\uFF33\u1E9E\u015A\u1E64\u015C\u1E60\u0160\u1E66\u1E62\u1E68\u0218\u015E\u2C7E\uA7A8\uA784"},{base:"T",chars:"\u24C9\uFF34\u1E6A\u0164\u1E6C\u021A\u0162\u1E70\u1E6E\u0166\u01AC\u01AE\u023E\uA786"},{base:"Th",chars:"\xDE"},{base:"TZ",chars:"\uA728"},{base:"U",chars:"\u24CA\uFF35\xD9\xDA\xDB\u0168\u1E78\u016A\u1E7A\u016C\xDC\u01DB\u01D7\u01D5\u01D9\u1EE6\u016E\u0170\u01D3\u0214\u0216\u01AF\u1EEA\u1EE8\u1EEE\u1EEC\u1EF0\u1EE4\u1E72\u0172\u1E76\u1E74\u0244"},{base:"V",chars:"\u24CB\uFF36\u1E7C\u1E7E\u01B2\uA75E\u0245"},{base:"VY",chars:"\uA760"},{base:"W",chars:"\u24CC\uFF37\u1E80\u1E82\u0174\u1E86\u1E84\u1E88\u2C72"},{base:"X",chars:"\u24CD\uFF38\u1E8A\u1E8C"},{base:"Y",chars:"\u24CE\uFF39\u1EF2\xDD\u0176\u1EF8\u0232\u1E8E\u0178\u1EF6\u1EF4\u01B3\u024E\u1EFE"},{base:"Z",chars:"\u24CF\uFF3A\u0179\u1E90\u017B\u017D\u1E92\u1E94\u01B5\u0224\u2C7F\u2C6B\uA762"},{base:"a",chars:"\u24D0\uFF41\u1E9A\xE0\xE1\xE2\u1EA7\u1EA5\u1EAB\u1EA9\xE3\u0101\u0103\u1EB1\u1EAF\u1EB5\u1EB3\u0227\u01E1\xE4\u01DF\u1EA3\xE5\u01FB\u01CE\u0201\u0203\u1EA1\u1EAD\u1EB7\u1E01\u0105\u2C65\u0250\u0251"},{base:"aa",chars:"\uA733"},{base:"ae",chars:"\xE6\u01FD\u01E3"},{base:"ao",chars:"\uA735"},{base:"au",chars:"\uA737"},{base:"av",chars:"\uA739\uA73B"},{base:"ay",chars:"\uA73D"},{base:"b",chars:"\u24D1\uFF42\u1E03\u1E05\u1E07\u0180\u0183\u0253\u0182"},{base:"c",chars:"\uFF43\u24D2\u0107\u0109\u010B\u010D\xE7\u1E09\u0188\u023C\uA73F\u2184"},{base:"d",chars:"\u24D3\uFF44\u1E0B\u010F\u1E0D\u1E11\u1E13\u1E0F\u0111\u018C\u0256\u0257\u018B\u13E7\u0501\uA7AA"},{base:"dh",chars:"\xF0"},{base:"dz",chars:"\u01F3\u01C6"},{base:"e",chars:"\u24D4\uFF45\xE8\xE9\xEA\u1EC1\u1EBF\u1EC5\u1EC3\u1EBD\u0113\u1E15\u1E17\u0115\u0117\xEB\u1EBB\u011B\u0205\u0207\u1EB9\u1EC7\u0229\u1E1D\u0119\u1E19\u1E1B\u0247\u01DD"},{base:"f",chars:"\u24D5\uFF46\u1E1F\u0192"},{base:"ff",chars:"\uFB00"},{base:"fi",chars:"\uFB01"},{base:"fl",chars:"\uFB02"},{base:"ffi",chars:"\uFB03"},{base:"ffl",chars:"\uFB04"},{base:"g",chars:"\u24D6\uFF47\u01F5\u011D\u1E21\u011F\u0121\u01E7\u0123\u01E5\u0260\uA7A1\uA77F\u1D79"},{base:"h",chars:"\u24D7\uFF48\u0125\u1E23\u1E27\u021F\u1E25\u1E29\u1E2B\u1E96\u0127\u2C68\u2C76\u0265"},{base:"hv",chars:"\u0195"},{base:"i",chars:"\u24D8\uFF49\xEC\xED\xEE\u0129\u012B\u012D\xEF\u1E2F\u1EC9\u01D0\u0209\u020B\u1ECB\u012F\u1E2D\u0268\u0131"},{base:"j",chars:"\u24D9\uFF4A\u0135\u01F0\u0249"},{base:"k",chars:"\u24DA\uFF4B\u1E31\u01E9\u1E33\u0137\u1E35\u0199\u2C6A\uA741\uA743\uA745\uA7A3"},{base:"l",chars:"\u24DB\uFF4C\u0140\u013A\u013E\u1E37\u1E39\u013C\u1E3D\u1E3B\u017F\u0142\u019A\u026B\u2C61\uA749\uA781\uA747\u026D"},{base:"lj",chars:"\u01C9"},{base:"m",chars:"\u24DC\uFF4D\u1E3F\u1E41\u1E43\u0271\u026F"},{base:"n",chars:"\u24DD\uFF4E\u01F9\u0144\xF1\u1E45\u0148\u1E47\u0146\u1E4B\u1E49\u019E\u0272\u0149\uA791\uA7A5\u043B\u0509"},{base:"nj",chars:"\u01CC"},{base:"o",chars:"\u24DE\uFF4F\xF2\xF3\xF4\u1ED3\u1ED1\u1ED7\u1ED5\xF5\u1E4D\u022D\u1E4F\u014D\u1E51\u1E53\u014F\u022F\u0231\xF6\u022B\u1ECF\u0151\u01D2\u020D\u020F\u01A1\u1EDD\u1EDB\u1EE1\u1EDF\u1EE3\u1ECD\u1ED9\u01EB\u01ED\xF8\u01FF\uA74B\uA74D\u0275\u0254\u1D11"},{base:"oe",chars:"\u0153"},{base:"oi",chars:"\u01A3"},{base:"oo",chars:"\uA74F"},{base:"ou",chars:"\u0223"},{base:"p",chars:"\u24DF\uFF50\u1E55\u1E57\u01A5\u1D7D\uA751\uA753\uA755\u03C1"},{base:"q",chars:"\u24E0\uFF51\u024B\uA757\uA759"},{base:"r",chars:"\u24E1\uFF52\u0155\u1E59\u0159\u0211\u0213\u1E5B\u1E5D\u0157\u1E5F\u024D\u027D\uA75B\uA7A7\uA783"},{base:"s",chars:"\u24E2\uFF53\u015B\u1E65\u015D\u1E61\u0161\u1E67\u1E63\u1E69\u0219\u015F\u023F\uA7A9\uA785\u1E9B\u0282"},{base:"ss",chars:"\xDF"},{base:"t",chars:"\u24E3\uFF54\u1E6B\u1E97\u0165\u1E6D\u021B\u0163\u1E71\u1E6F\u0167\u01AD\u0288\u2C66\uA787"},{base:"th",chars:"\xFE"},{base:"tz",chars:"\uA729"},{base:"u",chars:"\u24E4\uFF55\xF9\xFA\xFB\u0169\u1E79\u016B\u1E7B\u016D\xFC\u01DC\u01D8\u01D6\u01DA\u1EE7\u016F\u0171\u01D4\u0215\u0217\u01B0\u1EEB\u1EE9\u1EEF\u1EED\u1EF1\u1EE5\u1E73\u0173\u1E77\u1E75\u0289"},{base:"v",chars:"\u24E5\uFF56\u1E7D\u1E7F\u028B\uA75F\u028C"},{base:"vy",chars:"\uA761"},{base:"w",chars:"\u24E6\uFF57\u1E81\u1E83\u0175\u1E87\u1E85\u1E98\u1E89\u2C73"},{base:"x",chars:"\u24E7\uFF58\u1E8B\u1E8D"},{base:"y",chars:"\u24E8\uFF59\u1EF3\xFD\u0177\u1EF9\u0233\u1E8F\xFF\u1EF7\u1E99\u1EF5\u01B4\u024F\u1EFF"},{base:"z",chars:"\u24E9\uFF5A\u017A\u1E91\u017C\u017E\u1E93\u1E95\u01B6\u0225\u0240\u2C6C\uA763"}],Se={};for(var $=0;$<ae.length;$+=1)for(var he=ae[$].chars,re=0;re<he.length;re+=1)Se[he[re]]=ae[$].base;function sa(m){return m.replace(/[^\u0000-\u007e]/g,function(h){return Se[h]||h})}te.replacementList=ae;te.diacriticsMap=Se;(function(m){const h=ta,o=la,k=te.remove,D={},F={},f={},R={},V={};h.forEach(function(u){const r=u;F[r[0]]=r[1],f[r[1]]=r[0],R[r[2]]=r[0],V[r[0]]=r[2]});function B(u){return String("000"+(u||"")).slice(-3)}function O(u,r){return Object.prototype.hasOwnProperty.call(u,r)}function t(u,r){return Object.keys(u).reduce(function(I,b){const S=u[b];return I[b]=r(S,b),I},{})}function _(u,r){switch(u){case"official":return Array.isArray(r)?r[0]:r;case"all":return typeof r=="string"?[r]:r;case"alias":return Array.isArray(r)?r[1]||r[0]:r;default:throw new TypeError("LocaleNameType must be one of these: all, official, alias!")}}m.registerLocale=function(u){if(!u.locale)throw new TypeError("Missing localeData.locale");if(!u.countries)throw new TypeError("Missing localeData.countries");D[u.locale]=u.countries};function U(u){return f[u]}m.alpha3ToAlpha2=U;function v(u){return F[u]}m.alpha2ToAlpha3=v;function M(u){return V[U(u)]}m.alpha3ToNumeric=M;function G(u){return V[u]}m.alpha2ToNumeric=G;function c(u){const r=B(u);return v(R[r])}m.numericToAlpha3=c;function L(u){const r=B(u);return R[r]}m.numericToAlpha2=L;function le(u){if(typeof u=="string"){if(/^[0-9]*$/.test(u))return c(u);if(u.length===2)return v(u.toUpperCase());if(u.length===3)return u.toUpperCase()}if(typeof u=="number")return c(u)}m.toAlpha3=le;function W(u){if(typeof u=="string"){if(/^[0-9]*$/.test(u))return L(u);if(u.length===2)return u.toUpperCase();if(u.length===3)return U(u.toUpperCase())}if(typeof u=="number")return L(u)}m.toAlpha2=W,m.getName=function(u,r,I={}){"select"in I||(I.select="official");try{const S=D[r.toLowerCase()][W(u)];return _(I.select,S)}catch{return}},m.getNames=function(u,r={}){"select"in r||(r.select="official");const I=D[u.toLowerCase()];return I===void 0?{}:t(I,function(b){return _(r.select,b)})},m.getAlpha2Code=function(u,r){const I=S=>S.toLowerCase(),b=(S,A)=>I(S)===I(A);try{const S=D[r.toLowerCase()];for(const A in S)if(!!O(S,A)){if(typeof S[A]=="string"&&b(S[A],u))return A;if(Array.isArray(S[A])){for(const w of S[A])if(b(w,u))return A}}return}catch{return}},m.getSimpleAlpha2Code=function(u,r){const I=S=>k(S.toLowerCase()),b=(S,A)=>I(S)===I(A);try{const S=D[r.toLowerCase()];for(const A in S)if(!!O(S,A)){if(typeof S[A]=="string"&&b(S[A],u))return A;if(Array.isArray(S[A])){for(const w of S[A])if(b(w,u))return A}}return}catch{return}},m.getAlpha2Codes=function(){return F},m.getAlpha3Code=function(u,r){const I=m.getAlpha2Code(u,r);if(I)return m.toAlpha3(I)},m.getSimpleAlpha3Code=function(u,r){const I=m.getSimpleAlpha2Code(u,r);if(I)return m.toAlpha3(I)},m.getAlpha3Codes=function(){return f},m.getNumericCodes=function(){return R},m.langs=function(){return Object.keys(D)},m.getSupportedLanguages=function(){return o},m.isValid=function(u){if(!u)return!1;const r=u.toString().toUpperCase();return O(f,r)||O(F,r)||O(R,r)}})(ee);const oa="en",na={AF:"Afghanistan",AL:"Albania",DZ:"Algeria",AS:"American Samoa",AD:"Andorra",AO:"Angola",AI:"Anguilla",AQ:"Antarctica",AG:"Antigua and Barbuda",AR:"Argentina",AM:"Armenia",AW:"Aruba",AU:"Australia",AT:"Austria",AZ:"Azerbaijan",BS:"Bahamas",BH:"Bahrain",BD:"Bangladesh",BB:"Barbados",BY:"Belarus",BE:"Belgium",BZ:"Belize",BJ:"Benin",BM:"Bermuda",BT:"Bhutan",BO:"Bolivia",BA:"Bosnia and Herzegovina",BW:"Botswana",BV:"Bouvet Island",BR:"Brazil",IO:"British Indian Ocean Territory",BN:"Brunei Darussalam",BG:"Bulgaria",BF:"Burkina Faso",BI:"Burundi",KH:"Cambodia",CM:"Cameroon",CA:"Canada",CV:"Cape Verde",KY:"Cayman Islands",CF:"Central African Republic",TD:"Chad",CL:"Chile",CN:["People's Republic of China","China"],CX:"Christmas Island",CC:"Cocos (Keeling) Islands",CO:"Colombia",KM:"Comoros",CG:["Republic of the Congo","Congo"],CD:["Democratic Republic of the Congo","Congo"],CK:"Cook Islands",CR:"Costa Rica",CI:["Cote d'Ivoire","C\xF4te d'Ivoire","Ivory Coast"],HR:"Croatia",CU:"Cuba",CY:"Cyprus",CZ:["Czech Republic","Czechia"],DK:"Denmark",DJ:"Djibouti",DM:"Dominica",DO:"Dominican Republic",EC:"Ecuador",EG:"Egypt",SV:"El Salvador",GQ:"Equatorial Guinea",ER:"Eritrea",EE:"Estonia",ET:"Ethiopia",FK:"Falkland Islands (Malvinas)",FO:"Faroe Islands",FJ:"Fiji",FI:"Finland",FR:"France",GF:"French Guiana",PF:"French Polynesia",TF:"French Southern Territories",GA:"Gabon",GM:["Republic of The Gambia","The Gambia","Gambia"],GE:"Georgia",DE:"Germany",GH:"Ghana",GI:"Gibraltar",GR:"Greece",GL:"Greenland",GD:"Grenada",GP:"Guadeloupe",GU:"Guam",GT:"Guatemala",GN:"Guinea",GW:"Guinea-Bissau",GY:"Guyana",HT:"Haiti",HM:"Heard Island and McDonald Islands",VA:"Holy See (Vatican City State)",HN:"Honduras",HK:"Hong Kong",HU:"Hungary",IS:"Iceland",IN:"India",ID:"Indonesia",IR:["Islamic Republic of Iran","Iran"],IQ:"Iraq",IE:"Ireland",IL:"Israel",IT:"Italy",JM:"Jamaica",JP:"Japan",JO:"Jordan",KZ:"Kazakhstan",KE:"Kenya",KI:"Kiribati",KP:"North Korea",KR:["South Korea","Korea, Republic of","Republic of Korea"],KW:"Kuwait",KG:"Kyrgyzstan",LA:"Lao People's Democratic Republic",LV:"Latvia",LB:"Lebanon",LS:"Lesotho",LR:"Liberia",LY:"Libya",LI:"Liechtenstein",LT:"Lithuania",LU:"Luxembourg",MO:"Macao",MG:"Madagascar",MW:"Malawi",MY:"Malaysia",MV:"Maldives",ML:"Mali",MT:"Malta",MH:"Marshall Islands",MQ:"Martinique",MR:"Mauritania",MU:"Mauritius",YT:"Mayotte",MX:"Mexico",FM:"Micronesia, Federated States of",MD:"Moldova, Republic of",MC:"Monaco",MN:"Mongolia",MS:"Montserrat",MA:"Morocco",MZ:"Mozambique",MM:"Myanmar",NA:"Namibia",NR:"Nauru",NP:"Nepal",NL:["Netherlands","The Netherlands","Netherlands (Kingdom of the)"],NC:"New Caledonia",NZ:"New Zealand",NI:"Nicaragua",NE:"Niger",NG:"Nigeria",NU:"Niue",NF:"Norfolk Island",MK:["The Republic of North Macedonia","North Macedonia"],MP:"Northern Mariana Islands",NO:"Norway",OM:"Oman",PK:"Pakistan",PW:"Palau",PS:["State of Palestine","Palestine"],PA:"Panama",PG:"Papua New Guinea",PY:"Paraguay",PE:"Peru",PH:"Philippines",PN:["Pitcairn","Pitcairn Islands"],PL:"Poland",PT:"Portugal",PR:"Puerto Rico",QA:"Qatar",RE:"Reunion",RO:"Romania",RU:["Russian Federation","Russia"],RW:"Rwanda",SH:"Saint Helena",KN:"Saint Kitts and Nevis",LC:"Saint Lucia",PM:"Saint Pierre and Miquelon",VC:"Saint Vincent and the Grenadines",WS:"Samoa",SM:"San Marino",ST:"Sao Tome and Principe",SA:"Saudi Arabia",SN:"Senegal",SC:"Seychelles",SL:"Sierra Leone",SG:"Singapore",SK:"Slovakia",SI:"Slovenia",SB:"Solomon Islands",SO:"Somalia",ZA:"South Africa",GS:"South Georgia and the South Sandwich Islands",ES:"Spain",LK:"Sri Lanka",SD:"Sudan",SR:"Suriname",SJ:"Svalbard and Jan Mayen",SZ:"Eswatini",SE:"Sweden",CH:"Switzerland",SY:"Syrian Arab Republic",TW:["Taiwan, Province of China","Taiwan"],TJ:"Tajikistan",TZ:["United Republic of Tanzania","Tanzania"],TH:"Thailand",TL:"Timor-Leste",TG:"Togo",TK:"Tokelau",TO:"Tonga",TT:"Trinidad and Tobago",TN:"Tunisia",TR:["T\xFCrkiye","Turkey"],TM:"Turkmenistan",TC:"Turks and Caicos Islands",TV:"Tuvalu",UG:"Uganda",UA:"Ukraine",AE:["United Arab Emirates","UAE"],GB:["United Kingdom","UK","Great Britain"],US:["United States of America","United States","USA","U.S.A.","US","U.S."],UM:"United States Minor Outlying Islands",UY:"Uruguay",UZ:"Uzbekistan",VU:"Vanuatu",VE:"Venezuela",VN:"Vietnam",VG:"Virgin Islands, British",VI:"Virgin Islands, U.S.",WF:"Wallis and Futuna",EH:"Western Sahara",YE:"Yemen",ZM:"Zambia",ZW:"Zimbabwe",AX:["\xC5land Islands","Aland Islands"],BQ:"Bonaire, Sint Eustatius and Saba",CW:"Cura\xE7ao",GG:"Guernsey",IM:"Isle of Man",JE:"Jersey",ME:"Montenegro",BL:"Saint Barth\xE9lemy",MF:"Saint Martin (French part)",RS:"Serbia",SX:"Sint Maarten (Dutch part)",SS:"South Sudan",XK:"Kosovo"};var ra={locale:oa,countries:na};const ia="zh-tw",da={AD:"\u5B89\u9053\u723E",AE:"\u963F\u806F\u914B",AF:"\u963F\u5BCC\u6C57",AG:"\u5B89\u5730\u74DC\u8207\u5DF4\u5E03\u9054",AI:"\u5B89\u572D\u62C9",AL:"\u963F\u723E\u5DF4\u5C3C\u4E9E",AM:"\u4E9E\u7F8E\u5C3C\u4E9E",AO:"\u5B89\u54E5\u62C9",AQ:"\u5357\u6975",AR:"\u963F\u6839\u5EF7",AS:"\u7F8E\u5C6C\u85A9\u6469\u4E9E",AT:"\u5967\u5730\u5229",AU:"\u6FB3\u6D32",AW:"\u963F\u9B6F\u5DF4",AX:"\u5967\u862D",AZ:"\u963F\u585E\u62DC\u7586",BA:"\u6CE2\u9ED1",BB:"\u5DF4\u8C9D\u591A",BD:"\u5B5F\u52A0\u62C9",BE:"\u6BD4\u5229\u6642",BF:"\u5E03\u5409\u7D0D\u6CD5\u7D22",BG:"\u4FDD\u52A0\u5229\u4E9E",BH:"\u5DF4\u6797",BI:"\u5E03\u9686\u8FEA",BJ:"\u8C9D\u5357",BL:"\u8056\u5DF4\u6CF0\u52D2\u7C73",BM:"\u767E\u6155\u9054",BN:"\u6C76\u840A",BO:"\u73BB\u5229\u7DAD\u4E9E",BQ:"\u8377\u862D\u52A0\u52D2\u6BD4\u6D77\u5340",BR:"\u5DF4\u897F",BS:"\u5DF4\u54C8\u99AC",BT:"\u4E0D\u4E39",BV:"\u5E03\u97CB\u5CF6",BW:"\u6CE2\u672D\u90A3",BY:"\u767D\u4FC4\u7F85\u65AF",BZ:"\u4F2F\u5229\u8332",CA:"\u52A0\u62FF\u5927",CC:"\u79D1\u79D1\u65AF\uFF08\u57FA\u6797\uFF09\u7FA4\u5CF6",CD:"\u525B\u679C\uFF08\u91D1\uFF09",CF:"\u4E2D\u975E",CG:"\u525B\u679C\uFF08\u5E03\uFF09",CH:"\u745E\u58EB",CI:"\u79D1\u7279\u8FEA\u74E6",CK:"\u5EAB\u514B\u7FA4\u5CF6",CL:"\u667A\u5229",CM:"\u5580\u9EA5\u9686",CN:"\u4E2D\u570B",CO:"\u54E5\u502B\u6BD4\u4E9E",CR:"\u54E5\u65AF\u5927\u9ECE\u52A0",CU:"\u53E4\u5DF4",CV:"\u4F5B\u5F97\u89D2",CW:"\u5EAB\u62C9\u7D22",CX:"\u8056\u8A95\u5CF6",CY:"\u585E\u6D66\u8DEF\u65AF",CZ:"\u6377\u514B",DE:"\u5FB7\u570B",DJ:"\u5409\u5E03\u5730",DK:"\u4E39\u9EA5",DM:"\u591A\u660E\u5C3C\u514B",DO:"\u591A\u660E\u5C3C\u52A0",DZ:"\u963F\u723E\u53CA\u5229\u4E9E",EC:"\u5384\u74DC\u591A",EE:"\u611B\u6C99\u5C3C\u4E9E",EG:"\u57C3\u53CA",EH:"\u963F\u62C9\u4F2F\u6492\u54C8\u62C9\u6C11\u4E3B\u5171\u548C\u570B",ER:"\u5384\u7ACB\u7279\u91CC\u4E9E",ES:"\u897F\u73ED\u7259",ET:"\u57C3\u585E\u4FC4\u6BD4\u4E9E",FI:"\u82AC\u862D",FJ:"\u6590\u6FDF",FK:"\u798F\u514B\u862D\u7FA4\u5CF6",FM:"\u5BC6\u514B\u7F85\u5C3C\u897F\u4E9E\u806F\u90A6",FO:"\u6CD5\u7F85\u7FA4\u5CF6",FR:"\u6CD5\u570B",GA:"\u52A0\u5F6D",GB:"\u82F1\u570B",GD:"\u683C\u6797\u7D0D\u9054",GE:"\u55AC\u6CBB\u4E9E",GF:"\u6CD5\u5C6C\u572D\u4E9E\u90A3",GG:"\u6839\u897F",GH:"\u52A0\u7D0D",GI:"\u76F4\u5E03\u7F85\u9640",GL:"\u683C\u9675\u862D",GM:"\u7518\u6BD4\u4E9E",GN:"\u5E7E\u5167\u4E9E",GP:"\u74DC\u5FB7\u7F85\u666E",GQ:"\u8D64\u9053\u5E7E\u5167\u4E9E",GR:"\u5E0C\u81D8",GS:"\u5357\u55AC\u6CBB\u4E9E\u548C\u5357\u6851\u5A01\u5947\u7FA4\u5CF6",GT:"\u74DC\u5730\u99AC\u62C9",GU:"\u95DC\u5CF6",GW:"\u5E7E\u5167\u4E9E\u6BD4\u7D39",GY:"\u572D\u4E9E\u90A3",HK:"\u9999\u6E2F",HM:"\u8D6B\u5FB7\u5CF6\u548C\u9EA5\u514B\u5510\u7D0D\u7FA4\u5CF6",HN:"\u5B8F\u90FD\u62C9\u65AF",HR:"\u514B\u7F85\u57C3\u897F\u4E9E",HT:"\u6D77\u5730",HU:"\u5308\u7259\u5229",ID:"\u5370\u5C3C",IE:"\u611B\u723E\u862D",IL:"\u4EE5\u8272\u5217",IM:"\u99AC\u6069\u5CF6",IN:"\u5370\u5EA6",IO:"\u82F1\u5C6C\u5370\u5EA6\u6D0B\u9818\u5730",IQ:"\u4F0A\u62C9\u514B",IR:"\u4F0A\u6717",IS:"\u51B0\u5CF6",IT:"\u7FA9\u5927\u5229",JE:"\u6FA4\u897F",JM:"\u7259\u8CB7\u52A0",JO:"\u7D04\u65E6",JP:"\u65E5\u672C",KE:"\u80AF\u4E9E",KG:"\u5409\u723E\u5409\u65AF",KH:"\u67EC\u57D4\u5BE8",KI:"\u5409\u91CC\u5DF4\u65AF",KM:"\u79D1\u6469\u7F85",KN:"\u8056\u514B\u91CC\u65AF\u591A\u798F\u53CA\u5C3C\u7DAD\u65AF",KP:"\u671D\u9BAE",KR:"\u97D3\u570B",KW:"\u79D1\u5A01\u7279",KY:"\u958B\u66FC\u7FA4\u5CF6",KZ:"\u54C8\u85A9\u514B",LA:"\u5BEE\u570B",LB:"\u9ECE\u5DF4\u5AE9",LC:"\u8056\u9732\u897F\u4E9E",LI:"\u5217\u652F\u6566\u58EB\u767B",LK:"\u65AF\u91CC\u862D\u5361",LR:"\u8CF4\u6BD4\u745E\u4E9E",LS:"\u8CF4\u7D22\u6258",LT:"\u7ACB\u9676\u5B9B",LU:"\u76E7\u68EE\u5821",LV:"\u62C9\u812B\u7DAD\u4E9E",LY:"\u5229\u6BD4\u4E9E",MA:"\u6469\u6D1B\u54E5",MC:"\u6469\u7D0D\u54E5",MD:"\u6469\u723E\u591A\u74E6",ME:"\u8499\u7279\u5167\u54E5\u7F85",MF:"\u6CD5\u5C6C\u8056\u99AC\u4E01",MG:"\u99AC\u9054\u52A0\u65AF\u52A0",MH:"\u99AC\u7D39\u723E\u7FA4\u5CF6",MK:"\u5317\u99AC\u5176\u9813",ML:"\u99AC\u88E1",MM:"\u7DEC\u7538",MN:"\u8499\u53E4",MO:"\u6FB3\u9580",MP:"\u5317\u99AC\u88E1\u4E9E\u7D0D\u7FA4\u5CF6",MQ:"\u99AC\u63D0\u5C3C\u514B",MR:"\u8305\u5229\u5854\u5C3C\u4E9E",MS:"\u8499\u7279\u585E\u62C9\u7279",MT:"\u99AC\u723E\u4ED6",MU:"\u6BDB\u91CC\u6C42\u65AF",MV:"\u99AC\u723E\u5730\u592B",MW:"\u99AC\u62C9\u5A01",MX:"\u58A8\u897F\u54E5",MY:"\u99AC\u4F86\u897F\u4E9E",MZ:"\u83AB\u4E09\u6BD4\u514B",NA:"\u7D0D\u7C73\u6BD4\u4E9E",NC:"\u65B0\u5580\u88E1\u591A\u5C3C\u4E9E",NE:"\u5C3C\u65E5",NF:"\u8AFE\u798F\u514B\u5CF6",NG:"\u5948\u53CA\u5229\u4E9E",NI:"\u5C3C\u52A0\u62C9\u74DC",NL:"\u8377\u862D",NO:"\u632A\u5A01",NP:"\u5C3C\u6CCA\u723E",NR:"\u8AFE\u9B6F",NU:"\u7D10\u57C3",NZ:"\u7D10\u897F\u862D",OM:"\u963F\u66FC",PA:"\u5DF4\u62FF\u99AC",PE:"\u79D8\u9B6F",PF:"\u6CD5\u5C6C\u73BB\u91CC\u5C3C\u897F\u4E9E",PG:"\u5DF4\u5E03\u4E9E\u7D10\u5E7E\u5167\u4E9E",PH:"\u83F2\u5F8B\u8CD3",PK:"\u5DF4\u57FA\u65AF\u5766",PL:"\u6CE2\u862D",PM:"\u8056\u76AE\u8036\u8207\u5BC6\u514B\u9686",PN:"\u76AE\u7279\u51F1\u6069\u7FA4\u5CF6",PR:"\u6CE2\u591A\u9ECE\u5404",PS:"\u5DF4\u52D2\u65AF\u5766",PT:"\u8461\u8404\u7259",PW:"\u5E1B\u7409",PY:"\u5DF4\u62C9\u572D",QA:"\u5361\u9054",RE:"\u7559\u5C3C\u6C6A",RO:"\u7F85\u99AC\u5C3C\u4E9E",RS:"\u585E\u723E\u7DAD\u4E9E",RU:"\u4FC4\u7F85\u65AF",RW:"\u76E7\u5B89\u9054",SA:"\u6C99\u70CF\u5730\u963F\u62C9\u4F2F",SB:"\u7D22\u7F85\u9580\u7FA4\u5CF6",SC:"\u585E\u5E2D\u723E",SD:"\u8607\u4E39",SE:"\u745E\u5178",SG:"\u65B0\u52A0\u5761",SH:"\u8056\u8D6B\u52D2\u62FF",SI:"\u65AF\u6D1B\u7DAD\u5C3C\u4E9E",SJ:"\u65AF\u74E6\u723E\u5DF4\u548C\u63DA\u99AC\u5EF6",SK:"\u65AF\u6D1B\u4F10\u514B",SL:"\u7345\u5B50\u5C71",SM:"\u8056\u99AC\u5229\u8AFE",SN:"\u585E\u5167\u52A0\u723E",SO:"\u7D22\u99AC\u5229\u4E9E",SR:"\u8607\u5229\u5357",SS:"\u5357\u8607\u4E39",ST:"\u8056\u591A\u7F8E\u8207\u666E\u6797\u897F\u6BD4",SV:"\u85A9\u723E\u74E6\u591A",SX:"\u8377\u5C6C\u8056\u99AC\u4E01",SY:"\u6558\u5229\u4E9E",SZ:"\u53F2\u74E6\u5E1D\u5C3C",TC:"\u7279\u514B\u65AF\u8207\u51F1\u79D1\u65AF\u7FA4\u5CF6",TD:"\u4E4D\u5F97",TF:"\u6CD5\u5C6C\u5357\u90E8\u9818\u5730",TG:"\u591A\u54E5",TH:"\u6CF0\u570B",TJ:"\u5854\u5409\u514B\u65AF\u5766",TK:"\u6258\u514B\u52DE",TL:"\u6771\u5E1D\u6C76",TM:"\u571F\u5EAB\u66FC\u65AF\u5766",TN:"\u7A81\u5C3C\u897F\u4E9E",TO:"\u6771\u52A0",TR:"\u571F\u8033\u5176",TT:"\u5343\u91CC\u9054\u53CA\u6258\u5DF4\u54E5",TV:"\u5410\u74E6\u9B6F",TW:"\u53F0\u7063",TZ:"\u5766\u5C1A\u5C3C\u4E9E",UA:"\u70CF\u514B\u862D",UG:"\u70CF\u5E72\u9054",UM:"\u7F8E\u570B\u672C\u571F\u5916\u5C0F\u5CF6\u5DBC",US:"\u7F8E\u570B",UY:"\u70CF\u62C9\u572D",UZ:"\u70CF\u8332\u5225\u514B",VA:"\u68B5\u8482\u5CA1",VC:"\u8056\u6587\u68EE\u8207\u683C\u6797\u7D0D\u4E01\u65AF",VE:"\u59D4\u5167\u745E\u62C9",VG:"\u82F1\u5C6C\u7DAD\u4EAC\u7FA4\u5CF6",VI:"\u7F8E\u5C6C\u7DAD\u4EAC\u7FA4\u5CF6",VN:"\u8D8A\u5357",VU:"\u842C\u90A3\u675C",WF:"\u74E6\u5229\u65AF\u8207\u5BCC\u5716\u7D0D",WS:"\u85A9\u6469\u4E9E",XK:"\u79D1\u7D22\u6C83",YE:"\u8449\u9580",YT:"\u99AC\u7D04\u7279",ZA:"\u5357\u975E",ZM:"\u5C1A\u6BD4\u4E9E",ZW:"\u8F9B\u5DF4\u5A01"};var ca={locale:ia,countries:da};const ma={class:"row items-center"},Sa={class:"text-h6"},Ea={class:"row q-mb-sm"},Ia={class:"col-12 text-h6 text-bold text-black"},Aa={class:"row items-center q-mb-md"},Ca={class:"col-12 col-md-2 text-subtitle1 text-md-center"},pa={class:"col-12 col-md-10"},Oa={class:"row items-center q-mb-md"},fa={class:"col-12 col-md-2 text-subtitle1 text-md-center"},ha={class:"col-12 col-md-10"},va={class:"row items-center q-mb-md"},ba={class:"col-12 col-md-2 text-subtitle1 text-md-center"},Fa={class:"col-12 col-md-10"},Ma={class:"row items-center q-mb-md"},Ba={class:"col-12 col-md-2 text-subtitle1 text md-center"},Da={class:"col-12 col-md-10"},_a={class:"row items-center justify-end"},ga={class:"row items-center q-mb-md"},Ta={class:"col-12 col-md-2 text-subtitle1 text-md-center"},Ga={class:"col-12 col-md-10"},Na={class:"row items-center q-mb-md"},ya={class:"col-12 col-md-2 text-subtitle1 text-md-center"},Ra={class:"col-12 col-md-10"},Va={class:"row items-center q-mb-md"},Ua={class:"col-12 col-md-2 text-subtitle1 text-md-center"},La={class:"col-12 col-md-10"},Pa={class:"row items-center q-mb-md"},Ka={class:"col-12 col-md-2 text-subtitle1 text-md-center"},wa={class:"col-12 col-md-10"},xa={class:"row items-center q-mb-md"},Ha={class:"col-12 col-md-2 text-subtitle1 text-md-center"},ka={class:"col-12 col-md-10"},Ya={class:"row items-center q-mb-md"},Za={class:"col-12 col-md-2 text-subtitle1 text-md-center"},Wa={class:"col-12 col-md-10"},Qa={class:"row items-center q-mb-md"},qa={class:"col-12 col-md-2 text-subtitle1 text-md-center"},za={class:"col-12 col-md-10"},Ja={class:"row items-center q-mb-md"},Xa={class:"col-12 col-md-2 text-subtitle1 text-md-center"},$a={class:"col-12 col-md-10"},ja={class:"row items-center q-mb-md"},eu={class:"col-12 col-md-2 text-subtitle1 text-md-center"},au={class:"col-12 col-md-10"},uu={class:"row items-center q-mb-md"},tu={class:"col-12 col-md-2 text-subtitle1 text-md-center"},lu={class:"col-12 col-md-10"},su={class:"row q-mt-lg q-mb-sm"},ou={class:"col-12 text-h6 text-bold text-black"},nu={class:"row q-mb-sm"},ru={class:"col-12 flex"},iu={class:"row q-mb-sm"},du={class:"col-12"},cu={key:1,class:"row q-mb-sm"},mu={class:"text-subtitle1 text-bold"},Su={class:"row q-mt-lg q-mb-sm"},Eu={class:"col-12 text-h6 text-bold text-black"},Iu={class:"row"},Au={class:"q-ml-sm"},Cu=ve({__name:"CustomerProfile",props:{modelValue:{type:Boolean},customerUUID:{}},emits:["update:modelValue","refreshData"],setup(m,{emit:h}){const{t:o,locale:k}=be(),D=Xe(),F=m,f=h,R=Y({get:()=>F.modelValue,set:E=>f("update:modelValue",E)}),V=C(null),B=C(!1),O=C(!1),t=C({uuid:"",name:"",email:"",phone:"",license_plate:"",tax_id:"",country:"",state:"",city:"",zipcode:"",address:"",is_supplier:!1,is_vip:!1,vip_end_date:"",is_active:!0,note:"",products:[]}),_=C([]),U=C(""),v=Y({get:()=>!t.value.vip_end_date||t.value.vip_end_date.startsWith("0001-01-01")?null:me(t.value.vip_end_date,"YYYY-MM-DD"),set:E=>{t.value.vip_end_date=E===null?"":E}}),M=Y(()=>{let E="en";return k.value=="en-US"?E="en":k.value=="zh-TW"&&(E="zh-tw"),Object.entries(ee.getNames(E)).map(([s,e])=>({label:e,value:s}))}),G=C(""),c=C([]),L=C([]),le=[{name:"name",label:o("name"),field:"name",align:"left"},{name:"actions",label:o("actions"),field:"actions",align:"center"}],W=async()=>{const E=await ne.listProducts({});c.value=E.result.data},u=(E,s)=>{s(E===""?()=>{L.value=c.value.filter(e=>!t.value.products.some(x=>x.uuid===e.uuid))}:()=>{const e=E.toLowerCase();L.value=c.value.filter(x=>x.name.toLowerCase().indexOf(e)>-1)})},r=async()=>{if(!!G.value)try{O.value=!0,await ne.addSupplier(G.value,t.value.uuid)}finally{O.value=!1,G.value="",w()}},I=C(!1),b=C(""),S=async E=>{b.value=E,I.value=!0},A=async()=>{try{O.value=!0,await ne.removeSupplier(b.value,t.value.uuid)}finally{O.value=!1,I.value=!1,w()}},w=async()=>{try{B.value=!0;const E=await Z.get(t.value.uuid);t.value=E.result,U.value=t.value.name}finally{B.value=!1}},De=async()=>{try{O.value=!0,t.value.vip_end_date?t.value.vip_end_date=We.formatDate(t.value.vip_end_date,"YYYY-MM-DDT00:00:00Z"):t.value.vip_end_date=null,t.value.uuid?await Z.update(t.value):await Z.create(t.value),Oe.create({message:o("success"),position:"top",color:"positive"}),f("refreshData"),Q()}finally{O.value=!1}},_e=async()=>{D.showMessage({title:o("confirmDelete"),message:"",timeout:0,ok:async()=>{try{O.value=!0,await Z.delete(t.value.uuid),Oe.create({message:o("success"),position:"top",color:"positive"}),f("refreshData"),Q()}finally{O.value=!1}}})},Q=()=>{f("update:modelValue",!1)},ge=Y(()=>[{name:"order_no",label:o("orderNo"),field:"order_no",align:"left"},{name:"order_at",label:o("orderDate"),field:"order_at",align:"left"},{name:"total",label:o("total"),field:"total",align:"left"},{name:"status",label:o("status"),field:"status",align:"left"}]),P=C({sortBy:"order_at",descending:!0,page:1,rowsPerPage:10,rowsNumber:0}),q=C({from:"",to:""}),se=C(!1),Ee=C(""),Te=E=>{se.value=!0,Ee.value=E},Ge=async E=>{if(!E)return;const s=E,{sortBy:e,descending:x}=s.pagination;P.value.sortBy=e,P.value.descending=x,z()},z=async()=>{try{B.value=!0;const E=await Je.fetch({filter:{customer_uuid:t.value.uuid,start_date:q.value.from,end_date:q.value.to},pagination:P.value});_.value=E.result.data,P.value=E.result.pagination}finally{B.value=!1}};Fe(()=>{F.customerUUID&&(t.value.uuid=F.customerUUID,Ie()),W(),ee.registerLocale(ra),ee.registerLocale(ca)}),Ve(()=>F.modelValue,E=>{var s;!E||((s=V.value)==null||s.resetValidation(),F.customerUUID?(t.value.uuid=F.customerUUID,Ie()):(Ne(),U.value=""),t.value.country||(t.value.country="AU"))});const Ie=()=>{w(),z()},Ne=()=>{t.value={uuid:"",name:"",email:"",phone:"",license_plate:"",tax_id:"",country:"",state:"",city:"",zipcode:"",address:"",is_supplier:!1,is_vip:!1,vip_end_date:"",is_active:!0,note:"",products:[]}};return(E,s)=>(y(),K(H,null,[a(pe,{modelValue:R.value,"onUpdate:modelValue":s[20]||(s[20]=e=>R.value=e),persistent:"","no-refocus":"",class:"card-dialog q-px-md q-pb-lg"},{default:i(()=>[a(ie,{class:"q-mx-sm q-my-md q-py-sm"},{default:i(()=>[a(n(ze),{ref_key:"formRef",ref:V,onSubmit:Me(De,["prevent"]),greedy:"",class:"column full-height"},{default:i(()=>[a(j,{class:"col-1 q-py-none"},{default:i(()=>[l("div",ma,[l("div",Sa,[t.value.uuid?(y(),K(H,{key:0},[p(d(n(o)("editCustomer"))+" - "+d(U.value),1)],64)):(y(),K(H,{key:1},[p(d(n(o)("createCustomer")),1)],64))]),a(Ye),a(N,{type:"button",icon:"close",flat:"",round:"",dense:"",onClick:Q})])]),_:1}),a(j,{class:"col-10 q-pr-md q-pt-none"},{default:i(()=>[a(qe,{visible:"",class:"full-height q-pr-md q-pm-md"},{default:i(()=>[l("div",Ea,[l("div",Ia,[p(d(n(o)("customerData"))+" ",1),a(oe,{color:"black",size:"2px"})])]),l("div",Aa,[l("div",Ca,d(n(o)("name")),1),l("div",pa,[a(g,{modelValue:t.value.name,"onUpdate:modelValue":s[0]||(s[0]=e=>t.value.name=e),maxlength:"50",outlined:"",dense:"","hide-bottom-space":"",rules:[e=>!!e||n(o)("error.required"),e=>e.length<=50||n(o)("error.max",{max:50})],"lazy-rules":""},null,8,["modelValue","rules"])])]),l("div",Oa,[l("div",fa,d(n(o)("supplier")),1),l("div",ha,[a(Ae,{modelValue:t.value.is_supplier,"onUpdate:modelValue":s[1]||(s[1]=e=>t.value.is_supplier=e),color:"positive",dense:""},null,8,["modelValue"])])]),l("div",va,[l("div",ba,d(n(o)("vip.label")),1),l("div",Fa,[a(Ae,{modelValue:t.value.is_vip,"onUpdate:modelValue":s[2]||(s[2]=e=>t.value.is_vip=e),color:"positive",dense:""},null,8,["modelValue"])])]),l("div",Ma,[l("div",Ba,d(n(o)("vip.expiryDate")),1),l("div",Da,[a(g,{dense:"",modelValue:v.value,"onUpdate:modelValue":s[5]||(s[5]=e=>v.value=e),mask:"date",rules:n(ke),"lazy-rules":""},{prepend:i(()=>[a(ue,{name:"event",class:"cursor-pointer"},{default:i(()=>[a(Qe,{cover:"","transition-show":"scale","transition-hide":"scale"},{default:i(()=>[a(Ze,{modelValue:t.value.vip_end_date,"onUpdate:modelValue":s[4]||(s[4]=e=>t.value.vip_end_date=e)},{default:i(()=>[l("div",_a,[J(a(N,{label:n(o)("clear"),color:"negative",flat:"",onClick:s[3]||(s[3]=e=>t.value.vip_end_date="")},null,8,["label"]),[[X]]),J(a(N,{label:n(o)("Close"),color:"primary",flat:""},null,8,["label"]),[[X]])])]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue","rules"])])]),l("div",ga,[l("div",Ta,d(n(o)("phone")),1),l("div",Ga,[a(g,{type:"tel",modelValue:t.value.phone,"onUpdate:modelValue":s[6]||(s[6]=e=>t.value.phone=e),maxlength:"20",outlined:"",dense:"","hide-bottom-space":"",rules:[e=>e.length<=20||n(o)("error.max",{max:20})],"lazy-rules":""},null,8,["modelValue","rules"])])]),l("div",Na,[l("div",ya,d(n(o)("email.label")),1),l("div",Ra,[a(g,{modelValue:t.value.email,"onUpdate:modelValue":s[7]||(s[7]=e=>t.value.email=e),type:"email",maxlength:"50",outlined:"",dense:"","hide-bottom-space":"",rules:[e=>e.length<=50||n(o)("error.max",{max:50}),e=>!e||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)||n(o)("error.email")],"lazy-rules":""},null,8,["modelValue","rules"])])]),l("div",Va,[l("div",Ua,d(n(o)("licensePlate")),1),l("div",La,[a(g,{modelValue:t.value.license_plate,"onUpdate:modelValue":s[8]||(s[8]=e=>t.value.license_plate=e),modelModifiers:{trim:!0},maxlength:"50",outlined:"",dense:"","hide-bottom-space":"",rules:[e=>e.length<=50||n(o)("error.max",{max:50})],"lazy-rules":""},null,8,["modelValue","rules"])])]),l("div",Pa,[l("div",Ka,d(n(o)("taxID")),1),l("div",wa,[a(g,{modelValue:t.value.tax_id,"onUpdate:modelValue":s[9]||(s[9]=e=>t.value.tax_id=e),maxlength:"20",outlined:"",dense:"","hide-bottom-space":"",rules:[e=>e.length<=20||n(o)("error.max",{max:20})],"lazy-rules":""},null,8,["modelValue","rules"])])]),l("div",xa,[l("div",Ha,d(n(o)("country")),1),l("div",ka,[a(fe,{modelValue:t.value.country,"onUpdate:modelValue":s[10]||(s[10]=e=>t.value.country=e),outlined:"",dense:"","hide-bottom-space":"",options:M.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),l("div",Ya,[l("div",Za,d(n(o)("state")),1),l("div",Wa,[a(g,{modelValue:t.value.state,"onUpdate:modelValue":s[11]||(s[11]=e=>t.value.state=e),maxlength:"50",outlined:"",dense:"","hide-bottom-space":"",rules:[e=>e.length<=50||n(o)("error.max",{max:50})],"lazy-rules":""},null,8,["modelValue","rules"])])]),l("div",Qa,[l("div",qa,d(n(o)("city")),1),l("div",za,[a(g,{modelValue:t.value.city,"onUpdate:modelValue":s[12]||(s[12]=e=>t.value.city=e),maxlength:"50",outlined:"",dense:"","hide-bottom-space":"",rules:[e=>e.length<=50||n(o)("error.max",{max:50})],"lazy-rules":""},null,8,["modelValue","rules"])])]),l("div",Ja,[l("div",Xa,d(n(o)("zipcode")),1),l("div",$a,[a(g,{modelValue:t.value.zipcode,"onUpdate:modelValue":s[13]||(s[13]=e=>t.value.zipcode=e),maxlength:"20",outlined:"",dense:"","hide-bottom-space":"",rules:[e=>e.length<=20||n(o)("error.max",{max:20})],"lazy-rules":""},null,8,["modelValue","rules"])])]),l("div",ja,[l("div",eu,d(n(o)("address")),1),l("div",au,[a(g,{modelValue:t.value.address,"onUpdate:modelValue":s[14]||(s[14]=e=>t.value.address=e),maxlength:"100",outlined:"",dense:"","hide-bottom-space":"",rules:[e=>e.length<=100||n(o)("error.max",{max:100})],"lazy-rules":""},null,8,["modelValue","rules"])])]),l("div",uu,[l("div",tu,d(n(o)("note.label")),1),l("div",lu,[a(g,{modelValue:t.value.note,"onUpdate:modelValue":s[15]||(s[15]=e=>t.value.note=e),type:"textarea",outlined:"",dense:"","hide-bottom-space":"","lazy-rules":""},null,8,["modelValue"])])]),l("div",su,[l("div",ou,[p(d(n(o)("products"))+" ",1),a(oe,{color:"black",size:"2px"})])]),t.value.is_supplier?(y(),K(H,{key:0},[l("div",nu,[l("div",ru,[a(fe,{modelValue:G.value,"onUpdate:modelValue":s[16]||(s[16]=e=>G.value=e),options:L.value,"option-value":"uuid","option-label":"name","map-options":"","emit-value":"",label:n(o)("product.label"),"stack-label":"","use-input":"","hide-selected":"","fill-input":"","input-debounce":"0",onFilter:u,clearable:"",outlined:"",dense:"","hide-bottom-space":""},null,8,["modelValue","options","label"]),a(N,{icon:"add",color:"positive",size:"sm",class:"q-pa-sm q-ml-sm",onClick:r,loading:O.value},null,8,["loading"])])]),l("div",iu,[l("div",du,[a(ce,{rows:t.value.products,columns:le,"hide-pagination":""},{"body-cell-actions":i(e=>[a(T,{props:e},{default:i(()=>[a(N,{type:"button",icon:"delete",color:"negative",size:"sm",class:"q-pa-sm",onClick:x=>S(e.row.uuid),loading:O.value},null,8,["onClick","loading"])]),_:2},1032,["props"])]),_:1},8,["rows"])])])],64)):(y(),K("div",cu,[l("div",mu,d(n(o)("customer.notSupplier")),1)])),l("div",Su,[l("div",Eu,[p(d(n(o)("order.history"))+" ",1),a(oe,{color:"black",size:"2px"})])]),a(ce,{rows:_.value,columns:ge.value,"row-key":"uuid",pagination:P.value,"onUpdate:pagination":s[18]||(s[18]=e=>P.value=e),"hide-pagination":"","binary-state-sort":"","table-header-class":"bg-grey-3",class:"q-mt-md",onRequest:Ge,loading:B.value},{top:i(()=>[l("div",Iu,[a(aa,{modelValue:q.value,"onUpdate:modelValue":[s[17]||(s[17]=e=>q.value=e),z]},null,8,["modelValue"])])]),body:i(e=>[a(Be,{clickable:"",onClick:x=>Te(e.row.uuid)},{default:i(()=>[a(T,{props:e,key:"order_no"},{default:i(()=>[p(d(e.row.order_no),1)]),_:2},1032,["props"]),a(T,{props:e,key:"order_at"},{default:i(()=>[p(d(n(me)(e.row.order_at)),1)]),_:2},1032,["props"]),a(T,{props:e,key:"total"},{default:i(()=>[p(" AU$ "+d(n(we)(e.row.total,2)),1)]),_:2},1032,["props"]),a(T,{props:e,key:"status"},{default:i(()=>[p(d(n($e)(e.row.status)),1)]),_:2},1032,["props"])]),_:2},1032,["onClick"])]),_:1},8,["rows","columns","pagination","loading"]),a(je,{modelValue:P.value,"onUpdate:modelValue":s[19]||(s[19]=e=>P.value=e),onGetData:z},null,8,["modelValue"])]),_:1})]),_:1}),a(Ce,{class:"col-1",align:"between"},{default:i(()=>[t.value.uuid?(y(),de(N,{key:0,type:"button",onClick:_e,color:"negative",size:"md",loading:O.value},{default:i(()=>[a(ue,{name:"delete"})]),_:1},8,["loading"])):(y(),de(N,{key:1,type:"button",onClick:Q,color:"cancel",size:"md"},{default:i(()=>[p(d(n(o)("close")),1)]),_:1})),a(N,{type:"submit",color:"create",size:"md",loading:O.value},{default:i(()=>[t.value.uuid?(y(),K(H,{key:0},[p(d(n(o)("submit")),1)],64)):(y(),K(H,{key:1},[p(d(n(o)("create")),1)],64))]),_:1},8,["loading"])]),_:1})]),_:1},512)]),_:1})]),_:1},8,["modelValue"]),a(pe,{modelValue:I.value,"onUpdate:modelValue":s[21]||(s[21]=e=>I.value=e),"no-refocus":""},{default:i(()=>[a(ie,null,{default:i(()=>[a(j,{class:"row items-center"},{default:i(()=>[a(Ue,{icon:"warning",color:"negative","text-color":"white"}),l("span",Au,d(n(o)("confirmDelete")),1)]),_:1}),a(Ce,{align:"right"},{default:i(()=>[J(a(N,{flat:"",label:n(o)("cancel"),color:"primary"},null,8,["label"]),[[X]]),J(a(N,{flat:"",label:n(o)("delete"),color:"negative",onClick:A},null,8,["label"]),[[X]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),a(ea,{modelValue:se.value,"onUpdate:modelValue":s[22]||(s[22]=e=>se.value=e),orderID:Ee.value},null,8,["modelValue","orderID"])],64))}});const pu=ve({__name:"CustomerPage",setup(m){const{t:h}=be(),o=C(!1),k=Y(()=>[{name:"name",field:"name",label:h("name"),align:"left",sortable:!0},{name:"phone",field:"phone",label:h("phone"),align:"left",sortable:!0},{name:"email",field:"email",label:h("email.label"),align:"left",sortable:!0},{name:"license_plate",field:"license_plate",label:h("licensePlate"),align:"left",sortable:!0},{name:"tax_id",field:"tax_id",label:h("taxID"),align:"left",sortable:!0},{name:"is_supplier",field:"is_supplier",label:h("supplier"),align:"left",sortable:!0},{name:"created_at",field:"created_at",label:h("createdAt"),align:"left",sortable:!0}]),D=C(""),F=C([]),f=C({sortBy:"created_at",descending:!0,page:1,rowsPerPage:20,rowsNumber:0});Fe(()=>{_()});const R=He(),V=C({}),B=C(!1),O=()=>{V.value=R.newCustomer(),B.value=!0},t=v=>{V.value=v,B.value=!0},_=async()=>{try{o.value=!0;const v=await Z.fetch({filter:{search:D.value},pagination:f.value});F.value=v.result.data,f.value=v.result.pagination}finally{o.value=!1}},U=async v=>{if(!v)return;const M=v,{sortBy:G,descending:c}=M.pagination;f.value.sortBy=G,f.value.descending=c,_()};return(v,M)=>{const G=Le("TablePagination");return y(),de(xe,null,{default:i(()=>[a(ie,{flat:"",square:"",bordered:"",class:"bg-cream"},{default:i(()=>[a(j,null,{default:i(()=>[a(ce,{"virtual-scroll":"",rows:F.value,columns:k.value,"row-key":"uuid",pagination:f.value,"onUpdate:pagination":M[1]||(M[1]=c=>f.value=c),"hide-pagination":"","binary-state-sort":"","table-header-class":"bg-grey-3",onRequest:U,loading:o.value},{top:i(()=>[a(ye,null,{default:i(()=>[a(Re,null,{default:i(()=>[p(d(n(h)("customers")),1)]),_:1})]),_:1}),a(N,{type:"button",onClick:O,color:"create",class:"q-pa-sm",size:v.$q.screen.lt.md?"sm":"md"},{default:i(()=>[a(ue,{name:"add"}),v.$q.screen.gt.sm?(y(),K(H,{key:0},[p(d(n(h)("customer.label")),1)],64)):Pe("",!0)]),_:1},8,["size"]),a(g,{modelValue:D.value,"onUpdate:modelValue":[M[0]||(M[0]=c=>D.value=c),_],outlined:"",dense:"",placeholder:n(h)("search.customer"),class:"q-ml-md",clearable:"","clear-icon":"close",onKeyup:Ke(Me(_,["prevent"]),["enter"]),"input-debounce":"500",style:{width:"300px"}},{prepend:i(()=>[a(ue,{name:"search"})]),_:1},8,["modelValue","placeholder","onKeyup"])]),body:i(c=>[a(Be,{clickable:"",onClick:L=>t(c.row)},{default:i(()=>[a(T,{props:c,key:"name"},{default:i(()=>[p(d(c.row.name),1)]),_:2},1032,["props"]),a(T,{props:c,key:"phone"},{default:i(()=>[p(d(c.row.phone),1)]),_:2},1032,["props"]),a(T,{props:c,key:"email"},{default:i(()=>[p(d(c.row.email),1)]),_:2},1032,["props"]),a(T,{props:c,key:"license_plate"},{default:i(()=>[p(d(c.row.license_plate),1)]),_:2},1032,["props"]),a(T,{props:c,key:"tax_id"},{default:i(()=>[p(d(c.row.tax_id),1)]),_:2},1032,["props"]),a(T,{props:c,key:"is_supplier"},{default:i(()=>[p(d(c.row.is_supplier?"Y":"N"),1)]),_:2},1032,["props"]),a(T,{props:c,key:"created_at"},{default:i(()=>[p(d(n(me)(c.row.created_at,"YYYY-MM-DD HH:mm")),1)]),_:2},1032,["props"])]),_:2},1032,["onClick"])]),_:1},8,["rows","columns","pagination","loading"]),a(G,{modelValue:f.value,"onUpdate:modelValue":M[2]||(M[2]=c=>f.value=c),onGetData:_},null,8,["modelValue"])]),_:1})]),_:1}),a(Cu,{modelValue:B.value,"onUpdate:modelValue":M[3]||(M[3]=c=>B.value=c),customerUUID:V.value.uuid,onRefreshData:_},null,8,["modelValue","customerUUID"])]),_:1})}}});var at=ua(pu,[["__scopeId","data-v-5e0e16e5"]]);export{at as default};
