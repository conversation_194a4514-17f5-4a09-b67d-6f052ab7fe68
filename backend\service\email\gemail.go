package email

import (
	"crypto/tls"
	"fmt"
	"net/smtp"
	"strings"
)

const (
	GMAIL_SMTP_HOST = "smtp.gmail.com"
	GMAIL_SMTP_PORT = "587" // 587 or 465
)

type GmailConfig struct {
	FromEmail string
	Password  string
}

func SendGmail(config *GmailConfig, to []string, subject, body string) error {
	// Gmail SMTP 設定
	auth := smtp.PlainAuth("", config.FromEmail, config.Password, GMAIL_SMTP_HOST)

	// 建立完整的郵件標頭和內容
	msg := buildEmailMessage(config.FromEmail, to, subject, body)

	// 使用 STARTTLS 方式連接 (推薦)
	return sendWithSTARTTLS(auth, config.FromEmail, to, msg)
}

// 建立完整的郵件訊息，包含必要的標頭
func buildEmailMessage(from string, to []string, subject, body string) string {
	// 建立完整的郵件標頭
	headers := make(map[string]string)
	headers["From"] = from
	headers["To"] = strings.Join(to, ",")
	headers["Subject"] = subject
	headers["MIME-Version"] = "1.0"
	headers["Content-Type"] = "text/html; charset=UTF-8"
	headers["Content-Transfer-Encoding"] = "quoted-printable"

	// 組合標頭
	message := ""
	for k, v := range headers {
		message += fmt.Sprintf("%s: %s\r\n", k, v)
	}

	// 空行分隔標頭和內容
	message += "\r\n" + body

	return message
}

// 使用 STARTTLS 方式發送郵件（推薦方式）
func sendWithSTARTTLS(auth smtp.Auth, from string, to []string, msg string) error {
	// 先建立普通連接
	client, err := smtp.Dial(GMAIL_SMTP_HOST + ":" + GMAIL_SMTP_PORT)
	if err != nil {
		return fmt.Errorf("failed to connect to SMTP server: %w", err)
	}
	defer client.Quit()

	// 使用 STARTTLS 升級連接
	tlsConfig := &tls.Config{
		ServerName: GMAIL_SMTP_HOST,
	}

	if err = client.StartTLS(tlsConfig); err != nil {
		return fmt.Errorf("failed to start TLS: %w", err)
	}

	// 認證
	if err = client.Auth(auth); err != nil {
		return fmt.Errorf("authentication failed: %w", err)
	}

	// 設定發送者
	if err = client.Mail(from); err != nil {
		return fmt.Errorf("failed to set sender: %w", err)
	}

	// 設定接收者
	for _, addr := range to {
		if err = client.Rcpt(addr); err != nil {
			return fmt.Errorf("failed to set recipient %s: %w", addr, err)
		}
	}

	// 發送郵件內容
	w, err := client.Data()
	if err != nil {
		return fmt.Errorf("failed to get data writer: %w", err)
	}
	defer w.Close()

	_, err = w.Write([]byte(msg))
	if err != nil {
		return fmt.Errorf("failed to write message: %w", err)
	}

	return nil
}

// 備用方案：使用直接 TLS 連接（port 465）
func SendGmailWithDirectTLS(config *GmailConfig, to []string, subject, body string) error {
	auth := smtp.PlainAuth("", config.FromEmail, config.Password, GMAIL_SMTP_HOST)
	msg := buildEmailMessage(config.FromEmail, to, subject, body)

	// 直接使用 TLS 連接 port 465
	tlsconfig := &tls.Config{
		ServerName: GMAIL_SMTP_HOST,
	}

	conn, err := tls.Dial("tcp", GMAIL_SMTP_HOST+":465", tlsconfig)
	if err != nil {
		return fmt.Errorf("failed to connect with TLS: %w", err)
	}
	defer conn.Close()

	client, err := smtp.NewClient(conn, GMAIL_SMTP_HOST)
	if err != nil {
		return fmt.Errorf("failed to create SMTP client: %w", err)
	}
	defer client.Quit()

	// 認證
	if err = client.Auth(auth); err != nil {
		return fmt.Errorf("authentication failed: %w", err)
	}

	// 發送郵件
	if err = client.Mail(config.FromEmail); err != nil {
		return fmt.Errorf("failed to set sender: %w", err)
	}

	for _, addr := range to {
		if err = client.Rcpt(addr); err != nil {
			return fmt.Errorf("failed to set recipient %s: %w", addr, err)
		}
	}

	w, err := client.Data()
	if err != nil {
		return fmt.Errorf("failed to get data writer: %w", err)
	}
	defer w.Close()

	_, err = w.Write([]byte(msg))
	if err != nil {
		return fmt.Errorf("failed to write message: %w", err)
	}

	return nil
}

// 最簡單的方式：使用 Go 內建的 smtp.SendMail
func SendGmailSimple(config *GmailConfig, to []string, subject, body string) error {
	auth := smtp.PlainAuth("", config.FromEmail, config.Password, GMAIL_SMTP_HOST)
	msg := buildEmailMessage(config.FromEmail, to, subject, body)

	err := smtp.SendMail(GMAIL_SMTP_HOST+":"+GMAIL_SMTP_PORT, auth, config.FromEmail, to, []byte(msg))
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	return nil
}
