<template>
  <q-dialog
    v-model="open"
    persistent
    no-refocus
    class="card-dialog q-px-md q-pb-lg"
  >
    <q-card class="q-mx-sm q-my-md q-py-sm">
      <!-- data form -->
      <q-form
        ref="formRef"
        @submit.prevent="onSubmit"
        greedy
        class="column full-height"
      >
        <!-- title and close btn -->
        <q-card-section class="col-1 q-py-none">
          <div class="row items-center">
            <div class="text-h6">
              <template v-if="localCustomer.uuid">
                {{ t('editCustomer') }} - {{ title }}
              </template>
              <template v-else>
                {{ t('createCustomer') }}
              </template>
            </div>
            <q-space />
            <q-btn type="button" icon="close" flat round dense @click="close" />
          </div>
        </q-card-section>

        <!-- Body -->
        <q-card-section class="col-10 q-pr-md q-pt-none">
          <!-- Data -->
          <q-scroll-area visible class="full-height q-pr-md q-pm-md">
            <!-- Customer Data / 客戶資料 -->
            <div class="row q-mb-sm">
              <div class="col-12 text-h6 text-bold text-black">
                {{ t('customerData') }}
                <q-separator color="black" size="2px" />
              </div>
            </div>

            <!-- name -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('name') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model="localCustomer.name"
                  maxlength="50"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[
                      (val: string) => !!val || t('error.required'),
                          (val: string) => val.length <= 50 || t('error.max', { max: 50 })
                          ]"
                  lazy-rules
                />
              </div>
            </div>
            <!-- is supplier -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('supplier') }}
              </div>
              <div class="col-12 col-md-10">
                <q-toggle
                  v-model="localCustomer.is_supplier"
                  color="positive"
                  dense
                />
              </div>
            </div>
            <!-- is vip -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('vip.label') }}
              </div>
              <div class="col-12 col-md-10">
                <q-toggle
                  v-model="localCustomer.is_vip"
                  color="positive"
                  dense
                />
              </div>
            </div>
            <!-- vip end date -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text md-center">
                {{ t('vip.expiryDate') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  dense
                  v-model="formattedVipEndDate"
                  mask="date"
                  :rules="dateRules"
                  lazy-rules
                >
                  <template v-slot:prepend>
                    <q-icon name="event" class="cursor-pointer">
                      <q-popup-proxy
                        cover
                        transition-show="scale"
                        transition-hide="scale"
                      >
                        <q-date v-model="localCustomer.vip_end_date">
                          <div class="row items-center justify-end">
                            <q-btn
                              v-close-popup
                              :label="t('clear')"
                              color="negative"
                              flat
                              @click="localCustomer.vip_end_date = ''"
                            />
                            <q-btn
                              v-close-popup
                              :label="t('Close')"
                              color="primary"
                              flat
                            />
                          </div>
                        </q-date>
                      </q-popup-proxy>
                    </q-icon>
                  </template>
                </q-input>
              </div>
            </div>
            <!-- phone -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('phone') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  type="tel"
                  v-model="localCustomer.phone"
                  maxlength="20"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[
                          (val: string) => val.length <= 20 || t('error.max', { max: 20 })
                          ]"
                  lazy-rules
                />
              </div>
            </div>
            <!-- email -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('email.label') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model="localCustomer.email"
                  type="email"
                  maxlength="50"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[
                          (val: string) => val.length <= 50 || t('error.max', { max: 50 }),
                          (val: string) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || t('error.email')
                          ]"
                  lazy-rules
                />
              </div>
            </div>
            <!-- license plate -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('licensePlate') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model.trim="localCustomer.license_plate"
                  maxlength="50"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[(val: string) => val.length <= 50 || t('error.max', { max: 50 })]"
                  lazy-rules
                />
              </div>
            </div>
            <!-- tax ID -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('taxID') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model="localCustomer.tax_id"
                  maxlength="20"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[(val: string) => val.length <= 20 || t('error.max', { max: 20 })]"
                  lazy-rules
                />
              </div>
            </div>
            <!-- country -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('country') }}
              </div>
              <div class="col-12 col-md-10">
                <q-select
                  v-model="localCustomer.country"
                  outlined
                  dense
                  hide-bottom-space
                  :options="countryOptions"
                  map-options
                  emit-value
                />
              </div>
            </div>
            <!-- state -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('state') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model="localCustomer.state"
                  maxlength="50"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[(val: string) => val.length <= 50 || t('error.max', { max: 50 })]"
                  lazy-rules
                />
              </div>
            </div>
            <!-- city -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('city') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model="localCustomer.city"
                  maxlength="50"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[(val: string) => val.length <= 50 || t('error.max', { max: 50 })]"
                  lazy-rules
                />
              </div>
            </div>
            <!-- zipcode -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('zipcode') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model="localCustomer.zipcode"
                  maxlength="20"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[(val: string) => val.length <= 20 || t('error.max', { max: 20 })]"
                  lazy-rules
                />
              </div>
            </div>
            <!-- address -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('address') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model="localCustomer.address"
                  maxlength="100"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[(val: string) => val.length <= 100 || t('error.max', { max: 100 })]"
                  lazy-rules
                />
              </div>
            </div>

            <!-- note -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('note.label') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model="localCustomer.note"
                  type="textarea"
                  outlined
                  dense
                  hide-bottom-space
                  lazy-rules
                />
              </div>
            </div>

            <!-- 產品 -->
            <div class="row q-mt-lg q-mb-sm">
              <div class="col-12 text-h6 text-bold text-black">
                {{ t('products') }}
                <q-separator color="black" size="2px" />
              </div>
            </div>
            <template v-if="localCustomer.is_supplier">
              <div class="row q-mb-sm">
                <div class="col-12 flex">
                  <q-select
                    v-model="addProduct"
                    :options="productOptions"
                    option-value="uuid"
                    option-label="name"
                    map-options
                    emit-value
                    :label="t('product.label')"
                    stack-label
                    use-input
                    hide-selected
                    fill-input
                    input-debounce="0"
                    @filter="productFilterFn"
                    clearable
                    outlined
                    dense
                    hide-bottom-space
                  />
                  <q-btn
                    icon="add"
                    color="positive"
                    size="sm"
                    class="q-pa-sm q-ml-sm"
                    @click="addSupplierToProduct"
                    :loading="isSubmit"
                  />
                </div>
              </div>
              <div class="row q-mb-sm">
                <div class="col-12">
                  <q-table
                    :rows="localCustomer.products"
                    :columns="productColumns"
                    hide-pagination
                  >
                    <template v-slot:body-cell-actions="props">
                      <q-td :props="props">
                        <q-btn
                          type="button"
                          icon="delete"
                          color="negative"
                          size="sm"
                          class="q-pa-sm"
                          @click="confirmRemoveProduct(props.row.uuid)"
                          :loading="isSubmit"
                        />
                      </q-td>
                    </template>
                  </q-table>
                </div>
              </div>
            </template>
            <template v-else>
              <div class="row q-mb-sm">
                <div class="text-subtitle1 text-bold">
                  {{ t('customer.notSupplier') }}
                </div>
              </div>
            </template>

            <!-- 訂單紀錄 -->
            <div class="row q-mt-lg q-mb-sm">
              <div class="col-12 text-h6 text-bold text-black">
                {{ t('order.history') }}
                <q-separator color="black" size="2px" />
              </div>
            </div>
            <!-- order records -->
            <q-table
              :rows="localOrderRecords"
              :columns="orderColumns"
              row-key="uuid"
              v-model:pagination="orderPagination"
              hide-pagination
              binary-state-sort
              table-header-class="bg-grey-3"
              class="q-mt-md"
              @request="onRequestOrder"
              :loading="isLoading"
            >
              <!-- Top -->
              <template v-slot:top>
                <div class="row">
                  <DateRangePicker
                    v-model="orderDateRange"
                    @update:model-value="getOrderRecords"
                  />
                </div>
              </template>

              <!-- Body -->
              <template v-slot:body="props">
                <q-tr clickable @click="openOrderDetail(props.row.uuid)">
                  <q-td :props="props" key="order_no">
                    {{ props.row.order_no }}
                  </q-td>
                  <q-td :props="props" key="order_at">
                    {{ formatDate(props.row.order_at) }}
                  </q-td>
                  <q-td :props="props" key="total">
                    AU$ {{ formatNumber(props.row.total, 2) }}
                  </q-td>
                  <q-td :props="props" key="status">
                    {{ getOrderStatusLabel(props.row.status) }}
                  </q-td>
                </q-tr>
              </template>
            </q-table>

            <TablePagination
              v-model="orderPagination"
              @getData="getOrderRecords"
            />
          </q-scroll-area>
        </q-card-section>

        <!-- actions -->
        <q-card-actions class="col-1" align="between">
          <q-btn
            v-if="localCustomer.uuid"
            type="button"
            @click="onDelete"
            color="negative"
            size="md"
            :loading="isSubmit"
          >
            <q-icon name="delete" />
          </q-btn>
          <q-btn type="button" @click="close" color="cancel" size="md" v-else>
            {{ t('close') }}
          </q-btn>

          <q-btn type="submit" color="create" size="md" :loading="isSubmit">
            <template v-if="localCustomer.uuid">
              {{ t('submit') }}
            </template>
            <template v-else>
              {{ t('create') }}
            </template>
          </q-btn>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>

  <!-- 刪除商品確認刪除對話 -->
  <q-dialog v-model="removeProductDialog" no-refocus>
    <q-card>
      <q-card-section class="row items-center">
        <q-avatar icon="warning" color="negative" text-color="white" />
        <span class="q-ml-sm">
          {{ t('confirmDelete') }}
        </span>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat :label="t('cancel')" color="primary" v-close-popup />
        <q-btn
          flat
          :label="t('delete')"
          color="negative"
          @click="removeProductSupply"
          v-close-popup
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <OrderDetailDialog v-model="orderDetailDialog" :orderID="selectedOrder" />
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from 'vue';
import { date } from 'quasar';
import { useI18n } from 'vue-i18n';
import countries from 'i18n-iso-countries';
import en from 'i18n-iso-countries/langs/en.json';
import zh_tw from 'i18n-iso-countries/langs/zh-tw.json';
import { Notify, QForm } from 'quasar';
import { CustomerApi, Customer } from '@/api/customer';
import { ProductApi, Product } from '@/api/product';
import { OrderApi, Order } from '@/api/order';
import { useDialog, dateRules, formatDate, formatNumber } from '@/utils';
import { TableRequestProps, getOrderStatusLabel } from '@/types';
import TablePagination from './TablePagination.vue';
import OrderDetailDialog from '@/components/OrderDetailDialog.vue';
import DateRangePicker from './DateRangePicker.vue';

const { t, locale } = useI18n();
const dialog = useDialog();

const props = defineProps<{
  modelValue: boolean;
  customerUUID: string;
}>();

const emit = defineEmits(['update:modelValue', 'refreshData']);

const open = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const formRef = ref<InstanceType<typeof QForm> | null>(null);
const isLoading = ref(false);
const isSubmit = ref(false);
const localCustomer = ref<Customer>({
  uuid: '',
  name: '',
  email: '',
  phone: '',
  license_plate: '',
  tax_id: '',
  country: '',
  state: '',
  city: '',
  zipcode: '',
  address: '',
  is_supplier: false,
  is_vip: false,
  vip_end_date: '',
  is_active: true,
  note: '',

  products: [],
});
const localOrderRecords = ref<Order[]>([]);
const title = ref('');

const formattedVipEndDate = computed({
  get: () => {
    // 檢查是否為零值日期
    if (
      !localCustomer.value.vip_end_date ||
      localCustomer.value.vip_end_date.startsWith('0001-01-01')
    ) {
      return null;
    }
    return formatDate(localCustomer.value.vip_end_date, 'YYYY-MM-DD');
  },
  set: (value) => {
    localCustomer.value.vip_end_date = value === null ? '' : value;
  },
});

const countryOptions = computed(() => {
  let lang = 'en';
  if (locale.value == 'en-US') {
    lang = 'en';
  } else if (locale.value == 'zh-TW') {
    lang = 'zh-tw';
  }

  return Object.entries(countries.getNames(lang)).map(([code, name]) => ({
    label: name,
    value: code,
  }));
});

// 供應商/商品
const addProduct = ref('');
const products = ref<Product[]>([]);
const productOptions = ref<Product[]>([]);
const productColumns = [
  {
    name: 'name',
    label: t('name'),
    field: 'name',
    align: 'left' as const,
  },
  {
    name: 'actions',
    label: t('actions'),
    field: 'actions',
    align: 'center' as const,
  },
];

const getProducts = async () => {
  const response = await ProductApi.listProducts({});

  products.value = response.result.data;
};

const productFilterFn = (
  val: string,
  update: (callback: () => void) => void
) => {
  if (val === '') {
    update(() => {
      productOptions.value = products.value.filter(
        (v: Product) =>
          !localCustomer.value.products.some((s) => s.uuid === v.uuid)
      );
    });
  } else {
    update(() => {
      const needle = val.toLowerCase();
      productOptions.value = products.value.filter(
        (v: Product) => v.name.toLowerCase().indexOf(needle) > -1
      );
    });
  }
};

const addSupplierToProduct = async () => {
  if (!addProduct.value) return;

  try {
    isSubmit.value = true;
    await ProductApi.addSupplier(addProduct.value, localCustomer.value.uuid);
  } finally {
    isSubmit.value = false;
    addProduct.value = '';
    getCustomer();
  }
};

const removeProductDialog = ref(false);
const removeProductUUID = ref('');
const confirmRemoveProduct = async (productUUID: string) => {
  removeProductUUID.value = productUUID;
  removeProductDialog.value = true;
};

const removeProductSupply = async () => {
  try {
    isSubmit.value = true;
    await ProductApi.removeSupplier(
      removeProductUUID.value,
      localCustomer.value.uuid
    );
  } finally {
    isSubmit.value = false;
    removeProductDialog.value = false;
    getCustomer();
  }
};

const getCustomer = async () => {
  try {
    isLoading.value = true;
    const response = await CustomerApi.get(localCustomer.value.uuid);

    localCustomer.value = response.result;
    title.value = localCustomer.value.name;
  } finally {
    isLoading.value = false;
  }
};

const onSubmit = async () => {
  try {
    isSubmit.value = true;

    if (localCustomer.value.vip_end_date) {
      localCustomer.value.vip_end_date = date.formatDate(
        localCustomer.value.vip_end_date,
        'YYYY-MM-DDT00:00:00Z'
      );
    } else {
      localCustomer.value.vip_end_date = null;
    }

    if (localCustomer.value.uuid) {
      // update
      await CustomerApi.update(localCustomer.value);
    } else {
      // create
      await CustomerApi.create(localCustomer.value);
    }

    Notify.create({
      message: t('success'),
      position: 'top',
      color: 'positive',
    });

    emit('refreshData');
    close();
  } finally {
    isSubmit.value = false;
  }
};

const onDelete = async () => {
  dialog.showMessage({
    title: t('confirmDelete'),
    message: '',
    timeout: 0,
    ok: async () => {
      try {
        isSubmit.value = true;
        await CustomerApi.delete(localCustomer.value.uuid);

        Notify.create({
          message: t('success'),
          position: 'top',
          color: 'positive',
        });

        emit('refreshData');
        close();
      } finally {
        isSubmit.value = false;
      }
    },
  });
};

const close = () => {
  emit('update:modelValue', false);
};

// 客戶訂單紀錄
const orderColumns = computed(() => [
  {
    name: 'order_no',
    label: t('orderNo'),
    field: 'order_no',
    align: 'left' as const,
  },
  {
    name: 'order_at',
    label: t('orderDate'),
    field: 'order_at',
    align: 'left' as const,
  },
  {
    name: 'total',
    label: t('total'),
    field: 'total',
    align: 'left' as const,
  },
  {
    name: 'status',
    label: t('status'),
    field: 'status',
    align: 'left' as const,
  },
]);
const orderPagination = ref({
  sortBy: 'order_at',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});
const orderDateRange = ref({
  from: '',
  to: '',
});

const orderDetailDialog = ref(false);
const selectedOrder = ref('');
const openOrderDetail = (orderUUID: string) => {
  orderDetailDialog.value = true;
  selectedOrder.value = orderUUID;
};

const onRequestOrder = async (props: unknown) => {
  if (!props) return;

  const reqProps = props as TableRequestProps;
  const { sortBy, descending } = reqProps.pagination;

  orderPagination.value.sortBy = sortBy;
  orderPagination.value.descending = descending;

  getOrderRecords();
};

const getOrderRecords = async () => {
  try {
    isLoading.value = true;
    const response = await OrderApi.fetch({
      filter: {
        customer_uuid: localCustomer.value.uuid,
        start_date: orderDateRange.value.from,
        end_date: orderDateRange.value.to,
      },
      pagination: orderPagination.value,
    });

    localOrderRecords.value = response.result.data;
    orderPagination.value = response.result.pagination;
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  if (props.customerUUID) {
    localCustomer.value.uuid = props.customerUUID;
    getData();
  }
  getProducts();

  countries.registerLocale(en);
  countries.registerLocale(zh_tw);
});

watch(
  () => props.modelValue,
  (newVal) => {
    if (!newVal) return;

    formRef.value?.resetValidation();

    if (props.customerUUID) {
      localCustomer.value.uuid = props.customerUUID;
      getData();
    } else {
      initLocalCustomer();
      title.value = '';
    }

    // 預設澳洲
    if (!localCustomer.value.country) {
      localCustomer.value.country = 'AU';
    }
  }
);

const getData = () => {
  getCustomer();
  getOrderRecords();
};

const initLocalCustomer = () => {
  localCustomer.value = {
    uuid: '',
    name: '',
    email: '',
    phone: '',
    license_plate: '',
    tax_id: '',
    country: '',
    state: '',
    city: '',
    zipcode: '',
    address: '',
    is_supplier: false,
    is_vip: false,
    vip_end_date: '',
    is_active: true,
    note: '',

    products: [],
  };
};
</script>
