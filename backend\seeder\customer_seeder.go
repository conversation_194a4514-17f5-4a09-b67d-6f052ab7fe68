package seeder

import (
	"context"
	"cx/domain"
	"cx/repository"
	"cx/service"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"gorm.io/gorm"
)

type CustomerSeeder struct {
	seed int
}

func NewCustomerSeeder(seed int) *CustomerSeeder {
	return &CustomerSeeder{seed}
}

func (s *CustomerSeeder) Seed(db *gorm.DB) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	customerRepo := repository.NewCustomerRepository(db)
	customerService := service.NewCustomerService(db, customerRepo)

	for i := 0; i < s.seed; i++ {
		customer := &domain.Customer{
			Name:       gofakeit.Name(),
			Email:      gofakeit.Email(),
			Phone:      gofakeit.Phone(),
			Address:    gofakeit.Address().Address,
			TaxID:      gofakeit.Numerify("##########"),
			IsSupplier: gofakeit.Bool(),
			IsActive:   true,
			Note:       gofakeit.Sentence(10),
		}
		if err := customerService.Create(ctx, customer); err != nil {
			continue
		}
	}

	return nil
}
