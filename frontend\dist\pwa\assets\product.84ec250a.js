import{bh as p}from"./index.9477d5a3.js";const r={listProducts:({filter:e,pagination:t}={})=>p.get("v1/products",{params:{...e,...t}}),get:e=>p.get(`v1/products/${e}`),create:e=>p.post("v1/products",e),update:e=>p.put(`v1/products/${e.uuid}`,e),updateStatus:(e,t)=>p.patch(`v1/products/${e}/status`,{is_active:t}),delete:e=>p.delete(`v1/products/${e}`),uploadImage:(e,t)=>p.uploadFile(`v1/products/${e}/images`,t),updateImageSortOrder:e=>p.patch("v1/products/images/sort-order",{images:e.map(t=>({uuid:t.uuid,sort_order:t.sort_order}))}),deleteImage:(e,t)=>p.delete(`v1/products/${e}/images/${t}`),syncWcImages:e=>p.post(`v1/products/${e}/wc/sync-images`),addSupplier:(e,t)=>p.post(`v1/products/${e}/suppliers`,{supplier_uuid:t}),removeSupplier:(e,t)=>p.delete(`v1/products/${e}/suppliers/${t}`)};export{r as P};
