import { LocationQueryValue } from 'vue-router';

/**
 * 安全地從查詢參數中獲取字符串值
 * 處理 Vue Router 的 LocationQueryValue 類型，可能是 string | string[] | null
 */
export const getQueryParam = (
  param: LocationQueryValue | LocationQueryValue[]
): string | undefined => {
  if (param === null || param === undefined) {
    return undefined;
  }
  if (Array.isArray(param)) {
    return param[0] || undefined;
  }
  return param;
};

/**
 * 獲取必需的查詢參數，如果不存在則拋出錯誤
 */
export const getRequiredQueryParam = (
  param: LocationQueryValue | LocationQueryValue[],
  paramName: string
): string => {
  const value = getQueryParam(param);
  if (!value || typeof value !== 'string') {
    throw new Error(`Missing required query parameter: ${paramName}`);
  }
  return value;
};

/**
 * 批量獲取查詢參數
 */
export const getQueryParams = (
  params: Record<string, LocationQueryValue | LocationQueryValue[]>
): Record<string, string | undefined> => {
  const result: Record<string, string | undefined> = {};
  
  for (const [key, value] of Object.entries(params)) {
    result[key] = getQueryParam(value);
  }
  
  return result;
};

/**
 * 驗證查詢參數是否為有效字符串
 */
export const isValidQueryParam = (
  param: LocationQueryValue | LocationQueryValue[]
): param is string => {
  const value = getQueryParam(param);
  return typeof value === 'string' && value.length > 0;
};
