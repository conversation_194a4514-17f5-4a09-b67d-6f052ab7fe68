<template>
  <!-- title -->
  <div class="col-1">
    <q-item dense>
      <q-item-section>
        <q-item-label header class="text-h6 text-weight-bold q-pa-sm">
          {{ panelTitle }}
        </q-item-label>
      </q-item-section>
    </q-item>
    <q-separator />
  </div>

  <!-- form -->
  <q-card-section class="col-10">
    <q-form
      ref="formRef"
      @submit.prevent="onSubmit"
      greedy
      autocomplete="off"
      class="fit q-px-md-xl"
    >
      <div class="column fit">
        <!-- fields -->
        <q-scroll-area visible class="col-11">
          <!-- group name -->
          <div class="row q-mb-md">
            <div class="col-12 col-sm-2 text-subtitle1 text-md-center">
              {{ t('group') }}
            </div>
            <div class="col-12 col-sm-10">
              <q-input
                type="text"
                v-model="localGroup.name"
                maxlength="50"
                outlined
                dense
                hide-bottom-space
                :rules="[(val: string) => !!val || t('error.required')]"
                lazy-rules
              />
            </div>
          </div>
          <!-- permissions -->
          <div class="row q-mt-lg q-mb-md">
            <div class="col-12 text-h6 text-weight-bold">
              {{ t('permissions') }}
            </div>
          </div>
          <!-- Order permissions -->
          <div class="row q-mb-md q-mt-lg">
            <div class="col-12 text-h6">
              {{ t('order.label') }}
              <q-separator class="q-my-sm" />
            </div>
          </div>
          <div
            class="row q-mb-md"
            v-for="permission in localPermissions.filter((item) =>
              item.code.includes('order')
            )"
            :key="permission.code"
          >
            <div class="col-12 col-sm-2 text-subtitle1 text-md-center">
              <!-- {{ t('permission.create_order') }} -->
              {{ t(`permission.${permission.code}`) }}
            </div>
            <div class="col-12 col-sm-10">
              <q-toggle
                dense
                v-model="selectedPermissions[permission.code]"
                color="positive"
                size="lg"
              />
            </div>
          </div>
        </q-scroll-area>

        <!-- actions -->
        <div class="row col-1">
          <div class="offset-7 offset-md-10">
            <q-btn
              type="submit"
              :label="t('submit')"
              color="submit"
              class="q-mt-md"
              :loading="isSubmit"
            />
          </div>
        </div>
      </div>
    </q-form>
  </q-card-section>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { QForm, Notify } from 'quasar';
import { useI18n } from 'vue-i18n';
import { UserGroupApi, UserGroup } from '@/api/userGroup';
import { PermissionApi, Permission } from '@/api/permission';

const { t } = useI18n();

const props = defineProps<{
  id: number;
}>();

const emit = defineEmits(['dataUpdated', 'close']);

const panelTitle = ref('');
const isSubmit = ref(false);
const formRef = ref<InstanceType<typeof QForm> | null>(null);
const localGroup = ref<UserGroup>({
  id: 0,
  name: '',
});
const localPermissions = ref<Permission[]>([]); // all permissions
const localGroupPermissions = ref<Permission[]>([]); // group permissions

const selectedPermissions = ref<Record<string, boolean>>({});

onMounted(() => {
  initUserGroupData(props.id);
});

const initUserGroupData = async (id: number) => {
  formRef.value?.resetValidation();

  panelTitle.value = t('createGroup');
  localGroup.value = {
    id: 0,
    name: '',
  };

  if (id) {
    localGroup.value.id = id;
    await getData();
  }
};

watch(
  () => props.id,
  (newVal) => {
    initUserGroupData(newVal);
  }
);

const getData = async () => {
  const res = await Promise.all([
    UserGroupApi.get(localGroup.value.id),
    PermissionApi.fetch({
      group_id: localGroup.value.id,
    }),
  ]);

  const groupData = res[0].result;
  const permissionData = res[1].result;

  localGroup.value = groupData;
  localPermissions.value = permissionData.permissions;
  localGroupPermissions.value = permissionData.group_permissions;

  for (const permission of localPermissions.value) {
    selectedPermissions.value[permission.code] = !!localGroupPermissions.value
      ? localGroupPermissions.value.some(
          (item) => item.code === permission.code
        )
      : false;
  }

  panelTitle.value = `${t('group')} - ${localGroup.value.name}`;
};

const onSubmit = async () => {
  try {
    isSubmit.value = true;

    const permissionCodes = localPermissions.value
      .filter((item) => selectedPermissions.value[item.code])
      .map((item) => item.code);

    if (props.id) {
      // update
      await UserGroupApi.update(localGroup.value, permissionCodes);
    } else {
      // create
      const response = await UserGroupApi.create(
        localGroup.value,
        permissionCodes
      );
      localGroup.value.id = response.result.id;
    }

    getData();

    Notify.create({
      message: t('success'),
      position: 'top',
      color: 'positive',
    });
  } finally {
    isSubmit.value = false;
    emit('dataUpdated');
  }
};
</script>
