package domain

import (
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type User struct {
	ID          int64          `gorm:"<-:create;primary_key" json:"-"`
	UUID        string         `gorm:"<-:create" json:"uuid"`
	Username    string         `gorm:"<-:create" json:"username"`
	Password    string         `json:"-"`
	GroupID     null.Int       `gorm:"default:null" json:"group_id"`
	Name        string         `json:"name"`
	Sex         string         `json:"sex"`
	Phone       string         `json:"phone"`
	Birthday    null.String    `gorm:"default:null" json:"birthday"`
	Email       string         `json:"email"`
	Note        string         `json:"note"`
	IsAdmin     bool           `gorm:"default:false" json:"is_admin"`
	IsDeveloper bool           `gorm:"-" json:"-"`
	IsActive    bool           `gorm:"default:true" json:"is_active"`
	CreatedAt   time.Time      `gorm:"<-:create" json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

type UserCreatePayload struct {
	ID        int64       `json:"-"` // This field is only for seeder
	UUID      string      `json:"-"`
	Username  string      `gorm:"<-:create" form:"username" json:"username"`
	Password  string      `form:"password" json:"password"`
	GroupID   null.Int    `gorm:"default:null" form:"group_id" json:"group_id"`
	Name      string      `form:"name" json:"name"`
	Sex       string      `gorm:"default:'M'" form:"sex" json:"sex"`
	Phone     string      `form:"phone" json:"phone"`
	Birthday  null.String `gorm:"default:null" form:"birthday" json:"birthday"`
	Email     string      `form:"email" json:"email"`
	Note      string      `form:"note" json:"note"`
	IsAdmin   bool        `gorm:"default:false" json:"-"`
	CreatedAt time.Time   `json:"-"`
}

func (UserCreatePayload) TableName() string {
	return "users"
}

type UserUpdatePayload struct {
	Password string  `form:"password" json:"password"`
	GroupID  int64   `form:"group_id" json:"group_id"`
	Name     string  `form:"name" json:"name"`
	Email    *string `form:"email" json:"email"`
	IsActive *bool   `form:"is_active" json:"is_active"`
	Note     *string `form:"note" json:"note"`
}

func (UserUpdatePayload) TableName() string {
	return "users"
}
