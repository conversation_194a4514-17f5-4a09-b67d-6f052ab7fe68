package domain

import (
	"time"

	"gorm.io/gorm"
)

const (
	SalaryItemTypeIncome    SalaryItemType = 1 // 收入、應付項目
	SalaryItemTypeDeduction SalaryItemType = 2 // 扣除、應扣項目
	SalaryItemTypeBonus     SalaryItemType = 3 // 獎金、獎勵項目
	SalaryItemTypeExcluded  SalaryItemType = 4 // 不計入薪資的項目
)

type SalaryItemType int

const (
	SalaryTypeBoth    SalaryType = 0 // 全部
	SalaryTypeMonthly SalaryType = 1 // 月薪, 固定薪資
	SalaryTypeHourly  SalaryType = 2 // 時薪
)

type SalaryType int

type PayrollPeriod struct {
	ID          int64          `gorm:"primaryKey" json:"-"`
	UUID        string         `gorm:"<-:create" json:"uuid"`
	StartDate   string         `gorm:"type:date" json:"start_date"` // 薪資期間開始日:YYYY-MM-DD
	EndDate     string         `gorm:"type:date" json:"end_date"`   // 薪資期間結束日:YYYY-MM-DD
	CreatedByID int64          `json:"-"`
	UpdatedByID int64          `json:"-"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"-"`
	DeletedAt   gorm.DeletedAt `json:"-"`
}

type PayrollPeriodInfo struct {
	ID        int64     `json:"-"`
	UUID      string    `json:"uuid"`
	StartDate string    `json:"start_date"` // 薪資期間開始日:YYYY-MM-DD
	EndDate   string    `json:"end_date"`   // 薪資期間結束日:YYYY-MM-DD
	CreatedAt time.Time `json:"created_at"`
}

func (PayrollPeriodInfo) TableName() string {
	return "payroll_periods"
}

type PayrollPeriodFilter struct {
	StartDate string `form:"start_date"` // 薪資期間開始日:YYYY-MM-DD
	EndDate   string `form:"end_date"`   // 薪資期間結束日:YYYY-MM-DD
}

type PayrollPeriodResponse struct {
	ID        int64     `json:"-"`
	UUID      string    `json:"uuid"`
	StartDate string    `json:"start_date"` // 薪資期間開始日:YYYY-MM-DD
	EndDate   string    `json:"end_date"`   // 薪資期間結束日:YYYY-MM-DD
	CreatedAt time.Time `json:"created_at"`
}

func (PayrollPeriodResponse) TableName() string {
	return "payroll_periods"
}

type PayrollPeriodCreatePayload struct {
	UUID        string `gorm:"<-:create" json:"uuid"`
	StartDate   string `gorm:"type:date" json:"start_date"` // 薪資期間開始日:YYYY-MM-DD
	EndDate     string `gorm:"type:date" json:"end_date"`   // 薪資期間結束日:YYYY-MM-DD
	CreatedByID int64  `json:"-"`
	UpdatedByID int64  `json:"-"`
}

func (PayrollPeriodCreatePayload) TableName() string {
	return "payroll_periods"
}

type PayrollPeriodUpdatePayload struct {
	UUID        string `json:"uuid"`
	StartDate   string `gorm:"type:date" json:"start_date"` // 薪資期間開始日:YYYY-MM-DD
	EndDate     string `gorm:"type:date" json:"end_date"`   // 薪資期間結束日:YYYY-MM-DD
	UpdatedByID int64  `json:"-"`
}

func (PayrollPeriodUpdatePayload) TableName() string {
	return "payroll_periods"
}

type Payroll struct {
	ID              int64      `gorm:"primaryKey" json:"-"`
	UUID            string     `gorm:"<-:create" json:"uuid"`
	PeriodID        int64      `gorm:"<-:create" json:"-"`
	UserID          int64      `json:"-"`
	PayDate         string     `json:"pay_date"`         // 薪水發放日:YYYY-MM-DD
	SalaryType      SalaryType `json:"salary_type"`      // 薪資類型: 1: 月薪, 2: 時薪
	BasicSalary     float64    `json:"basic_salary"`     // 基本薪資
	HourlySalary    float64    `json:"hourly_salary"`    // 時薪
	WorkDays        int        `json:"work_days"`        // 工作天數
	WorkHours       int        `json:"work_hours"`       // 工作時數
	OvertimeHours   int        `json:"overtime_hours"`   // 加班時數
	GrossSalary     float64    `json:"gross_salary"`     // 總薪資
	TotalDeductions float64    `json:"total_deductions"` // 總扣除金額
	NetSalary       float64    `json:"net_salary"`       // 應發薪資
	Status          int        `json:"status"`           // 1: 未發放, 2: 已發放
	Notes           string     `json:"notes"`            // 備註
	CreatedByID     int64      `json:"-"`
	UpdatedByID     int64      `json:"-"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"-"`

	PayrollDetails []PayrollDetail    `gorm:"<-:false;foreignKey:PayrollID" json:"payroll_details,omitempty"` // 薪資明細
	Emails         []PayrollEmailInfo `gorm:"<-:false;foreignKey:PayrollID" json:"emails,omitempty"`          // 薪資發送紀錄
}

type PayrollFilter struct {
	PeriodUUID string `form:"period_uuid"` // 薪資期間UUID
	UserUUID   string `form:"user_uuid"`   // 員工UUID
}

type PayrollResponse struct {
	ID              int64             `gorm:"primaryKey" json:"-"`
	UUID            string            `json:"uuid"`
	PeriodID        int64             `json:"-"`
	Period          PayrollPeriodInfo `gorm:"foreignKey:PeriodID" json:"period"` // 薪資期間
	UserID          int64             `json:"-"`
	User            UserInfo          `gorm:"foreignKey:UserID" json:"user"`
	PayDate         string            `json:"pay_date"`         // 薪水發放日:YYYY-MM-DD
	SalaryType      SalaryType        `json:"salary_type"`      // 薪資類型: 1: 月薪, 2: 時薪
	BasicSalary     float64           `json:"basic_salary"`     // 基本薪資
	HourlySalary    float64           `json:"hourly_salary"`    // 時薪
	WorkDays        int               `json:"work_days"`        // 工作天數
	WorkHours       int               `json:"work_hours"`       // 工作時數
	OvertimeHours   int               `json:"overtime_hours"`   // 加班時數
	GrossSalary     float64           `json:"gross_salary"`     // 總薪資
	TotalDeductions float64           `json:"total_deductions"` // 總扣除金額
	NetSalary       float64           `json:"net_salary"`       // 應發薪資
	Status          int               `json:"status"`           // 1: 未發放, 2: 已發放
	Notes           string            `json:"notes"`
	CreatedAt       time.Time         `json:"created_at"`

	PayrollDetails []PayrollDetail    `gorm:"foreignKey:PayrollID" json:"payroll_details"`  // 薪資明細
	Emails         []PayrollEmailInfo `gorm:"foreignKey:PayrollID" json:"emails,omitempty"` // 薪資發送紀錄
}

func (PayrollResponse) TableName() string {
	return "payrolls"
}

type PayrollCreatePayload struct {
	ID              int64      `gorm:"primaryKey" json:"-"`
	UUID            string     `json:"-"`
	PeriodUUID      string     `gorm:"-" form:"period_uuid" json:"period_uuid" binding:"required"` // 薪資期間UUID
	PeriodID        int64      `json:"-"`                                                          // 薪資期間ID
	UserUUID        string     `gorm:"-" form:"user_uuid" json:"user_uuid" binding:"required"`
	UserID          int64      `json:"-"`                                        // 員工ID
	PayDate         *string    `form:"pay_date" json:"pay_date"`                 // 薪水發放日:YYYY-MM-DD
	SalaryType      SalaryType `form:"salary_type" json:"salary_type"`           // 薪資類型: 1: 月薪, 2: 時薪
	BasicSalary     float64    `form:"basic_salary" json:"basic_salary"`         // 基本薪資
	HourlySalary    float64    `form:"hourly_salary" json:"hourly_salary"`       // 時薪
	WorkDays        int        `form:"work_days" json:"work_days"`               // 工作天數
	WorkHours       int        `form:"work_hours" json:"work_hours"`             // 工作時數
	OvertimeHours   int        `form:"overtime_hours" json:"overtime_hours"`     // 加班時數
	GrossSalary     float64    `form:"gross_salary" json:"gross_salary"`         // 總薪資
	TotalDeductions float64    `form:"total_deductions" json:"total_deductions"` // 總扣除金額
	NetSalary       float64    `form:"net_salary" json:"net_salary"`             // 應發薪資
	Status          int        `form:"status" json:"status"`                     // 1: 未發放, 2: 已發放
	Notes           string     `form:"notes" json:"notes"`                       // 備註
	CreatedByID     int64      `json:"-"`
	UpdatedByID     int64      `json:"-"`

	PayrollDetails []PayrollDetail `gorm:"<-:false;foreignKey:PayrollID" json:"payroll_details"` // 薪資明細
}

func (s *PayrollCreatePayload) TableName() string {
	return "payrolls"
}

type PayrollUpdatePayload struct {
	ID              int64      `gorm:"primaryKey" json:"-"`
	UUID            string     `json:"-"`
	PayDate         string     `form:"pay_date" json:"pay_date"`                 // 薪水發放日:YYYY-MM-DD
	SalaryType      SalaryType `form:"salary_type" json:"salary_type"`           // 薪資類型: 1: 月薪, 2: 時薪
	BasicSalary     *float64   `form:"basic_salary" json:"basic_salary"`         // 基本薪資
	HourlySalary    *float64   `form:"hourly_salary" json:"hourly_salary"`       // 時薪
	WorkDays        *int       `form:"work_days" json:"work_days"`               // 工作天數
	WorkHours       *int       `form:"work_hours" json:"work_hours"`             // 工作時數
	OvertimeHours   *int       `form:"overtime_hours" json:"overtime_hours"`     // 加班時數
	GrossSalary     *float64   `form:"gross_salary" json:"gross_salary"`         // 總薪資
	TotalDeductions *float64   `form:"total_deductions" json:"total_deductions"` // 總扣除金額
	NetSalary       *float64   `form:"net_salary" json:"net_salary"`             // 應發薪資
	Status          int        `form:"status" json:"status"`                     // 1: 未發放, 2: 已發放
	Notes           *string    `form:"notes" json:"notes"`                       // 備註
	UpdatedByID     int64      `json:"-"`

	PayrollDetails []PayrollDetail `gorm:"<-:false;foreignKey:PayrollID" json:"payroll_details"` // 薪資明細
}

func (s *PayrollUpdatePayload) TableName() string {
	return "payrolls"
}

type PayrollDetail struct {
	ID           int64      `gorm:"primaryKey" json:"-"`
	UUID         string     `gorm:"<-:create" json:"uuid"`
	PayrollID    int64      `json:"-"`
	SalaryItemID int64      `json:"-"`                                                                      // 薪資項目ID
	SalaryItem   SalaryItem `gorm:"<-:false;foreignKey:SalaryItemID" form:"salary_item" json:"salary_item"` // 薪資項目
	Amount       float64    `form:"amount" json:"amount"`                                                   // 金額
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"-"`
}

type SalaryItem struct {
	ID                int64          `json:"id"`
	Name              string         `json:"name"`
	Type              SalaryItemType `json:"type"`                 // 1: 應付項目, 2: 應扣項目, 3: 獎金, 4: 不計入項目
	IsDefault         bool           `json:"is_default"`           // 是否為系統預設，系統預設的項目不可刪除
	IsRequired        bool           `json:"is_required"`          // 是否為必填項目
	DefaultValue      float64        `json:"default_value"`        // 預設值
	ApplyToSalaryType SalaryType     `json:"apply_to_salary_type"` // 0: 全部, 1: 月薪, 2: 時薪
	IsActive          bool           `json:"is_active"`            // 是否啟用
	SortOrder         int            `json:"sort_order"`           // 排序
	Note              string         `json:"note"`                 // 備註
	CreatedAt         time.Time      `json:"-"`
	UpdatedAt         time.Time      `json:"-"`
	DeletedAt         gorm.DeletedAt `json:"-"`
}

type SalaryItemResponse struct {
	ID                int64          `json:"id"`
	Name              string         `json:"name"`
	Type              SalaryItemType `json:"type"`
	IsDefault         bool           `json:"is_default"`
	IsRequired        bool           `json:"is_required"`
	DefaultValue      float64        `json:"default_value"`
	ApplyToSalaryType SalaryType     `json:"apply_to_salary_type"`
	SortOrder         int            `json:"sort_order"`
	Note              string         `json:"note"`
}

type SalaryItemCreatePayload struct {
	Name              string         `json:"name" binding:"required"`
	Type              SalaryItemType `json:"type" binding:"required"`
	IsRequired        *bool          `gorm:"default:false" json:"is_required"`
	DefaultValue      *float64       `gorm:"default:0.00" json:"default_value"`
	ApplyToSalaryType *SalaryType    `gorm:"default:0" json:"apply_to_salary_type" binding:"required"`
	Note              *string        `json:"note"`
}
