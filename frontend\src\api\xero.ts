import { apiWrapper } from '@/boot/axios';
import axios from 'axios';
import { useAuthStore } from '@/stores/auth-store';

// 創建專用的 API 實例
const createApiInstance = () => {
  const api = axios.create({
    baseURL: process.env.VUE_API_URL,
    timeout: 30000, // PDF 下載可能需要更長時間
  });

  // 添加認證 header
  api.interceptors.request.use((config) => {
    const authStore = useAuthStore();
    if (authStore.accessToken) {
      config.headers.Authorization = `Bearer ${authStore.accessToken}`;
      config.headers['X-Refresh-Token'] = authStore.refreshToken;
    }
    return config;
  });

  return api;
};

export interface XeroAuthRequest {
  client_id: string;
  client_secret: string;
  redirect_uri: string;
  scopes: string;
  default_email: string;
}

export interface XeroConfig {
  client_id: string;
  client_secret: string;
  redirect_uri: string;
  scopes: string;
  default_email: string;
}

export interface XeroAuthResponse {
  auth_url: string;
  state: string;
}

export interface XeroCallbackResponse {
  tenant_name: string;
  tenant_id: string;
}

export interface XeroConnectionStatus {
  connected: boolean;
  tenant_name: string;
  tenant_id: string;
  expires_at: string;
}

export interface XeroRefreshTokenResponse {
  expires_at: string;
}

export interface XeroInvoice {
  invoice_id: string;
  invoice_number: string;
  type: 'ACCREC' | 'ACCPAY';
  contact: {
    contact_id: string;
    name: string;
  };
  date: string;
  due_date: string;
  status: 'DRAFT' | 'SUBMITTED' | 'AUTHORISED' | 'PAID' | 'VOIDED';
  line_amount_types: string;
  sub_total: number;
  total_tax: number;
  total: number;
  currency_code: string;
  updated_date_utc: string;
  line_items: Array<{
    line_item_id: string;
    description: string;
    quantity: number;
    unit_amount: number;
    line_amount: number;
    tax_amount: number;
  }>;
}

export interface XeroInvoicesResponse {
  invoices: XeroInvoice[];
  total_count: number;
  page: number;
  page_size: number;
}

export interface XeroInvoiceResponse {
  invoice: XeroInvoice;
}

export const XeroApi = {
  // 設定相關
  saveConfig: (form: XeroAuthRequest) =>
    apiWrapper.post('/v1/xero/config', form),
  getConfig: () => apiWrapper.get<XeroConfig>('/v1/xero/config'),

  // OAuth2認證相關
  getAuthURL: () => apiWrapper.get<XeroAuthResponse>('/v1/xero/auth-url'),
  callback: (code: string, state: string) =>
    apiWrapper.post<XeroCallbackResponse>('/v1/xero/callback', { code, state }),

  // 連接
  getConnectionStatus: () =>
    apiWrapper.get<XeroConnectionStatus>('/v1/xero/status'),
  validateConnection: () =>
    apiWrapper.post('/v1/xero/validate-connection'),
  refreshToken: () =>
    apiWrapper.post<XeroRefreshTokenResponse>('/v1/xero/refresh'),
  disconnect: () => apiWrapper.delete('/v1/xero/disconnect'),

  // 發票相關
  getInvoices: (params?: {
    page?: number;
    pageSize?: number;
    status?: string;
    contactId?: string;
    dateFrom?: string;
    dateTo?: string;
  }) => apiWrapper.get<XeroInvoicesResponse>('/v1/xero/invoices', { params }),
  getInvoice: (invoiceId: string) =>
    apiWrapper.get<XeroInvoiceResponse>(`/v1/xero/invoices/${invoiceId}`),

  // 同步訂單到 Xero
  syncOrderToXero: (orderUUID: string) =>
    apiWrapper.post<{ success: boolean; message: string; invoice: XeroInvoiceResponse }>(`/v1/xero/sync-order/${orderUUID}`),

  // 獲取 Xero Invoice PDF
  getInvoicePDF: async (invoiceId: string): Promise<Blob> => {
    try {
      const api = createApiInstance();

      console.log(`Requesting PDF for invoice: ${invoiceId}`);

      // 直接使用 axios 而不是 apiWrapper，因為我們需要 Blob 回應
      const response = await api.get(`/v1/xero/invoices/${invoiceId}/pdf`, {
        responseType: 'blob',
      });

      console.log('Raw PDF response status:', response.status);
      console.log('Raw PDF response headers:', response.headers);
      console.log('Response data type:', typeof response.data);
      console.log('Response data instanceof Blob:', response.data instanceof Blob);
      console.log('Response data size:', response.data?.size || 'unknown');

      if (!(response.data instanceof Blob)) {
        console.error('Response data:', response.data);
        throw new Error('Response is not a Blob');
      }

      if (response.data.size === 0) {
        throw new Error('Received empty PDF data');
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching PDF:', error);
      if (axios.isAxiosError(error)) {
        console.error('Axios error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
        });
      }
      throw error;
    }
  },

  // 發送 Invoice Email
  sendInvoiceEmail: (invoiceId: string, email: string) =>
    apiWrapper.post(`/v1/xero/invoices/${invoiceId}/email`, { email }),

  // 檢查 Email 發送限制狀態
  checkEmailLimitStatus: () =>
    apiWrapper.get<{ canSendEmail: boolean; message: string }>('/v1/xero/email-status'),
};
