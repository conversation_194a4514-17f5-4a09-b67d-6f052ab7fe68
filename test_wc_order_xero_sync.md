# WooCommerce 訂單 Xero 同步功能測試

## API 端點測試

### 1. 獲取 WooCommerce 訂單詳情
```bash
GET /api/v1/wc-orders/{id}
```

預期回應應包含 `xero_sync` 字段：
```json
{
  "result": {
    "id": 123,
    "status": "wc-completed",
    "total": 100.00,
    "billing_email": "<EMAIL>",
    "line_items": [...],
    "xero_sync": {
      "uuid": "...",
      "wc_order_id": 123,
      "xero_invoice_id": "...",
      "xero_invoice_no": "INV-001",
      "sync_status": "success",
      "last_sync_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

### 2. 同步訂單到 Xero
```bash
POST /api/v1/wc-orders/{id}/sync-xero
```

預期回應：
```json
{
  "result": {
    "success": true,
    "message": "WooCommerce order synced to Xero successfully",
    "invoice": {
      "Invoices": [
        {
          "InvoiceID": "...",
          "InvoiceNumber": "INV-001",
          "Status": "AUTHORISED"
        }
      ]
    }
  }
}
```

## 前端功能測試

### 1. 訂單詳情對話框
- 打開 WCOrderDetailDialog
- 檢查是否顯示 Xero 同步狀態（對於已完成的訂單）
- 檢查同步按鈕是否可用

### 2. Xero 同步
- 點擊同步按鈕
- 檢查載入狀態
- 檢查成功通知
- 檢查同步狀態更新

### 3. 發票列印
- 對於已同步的訂單，檢查列印按鈕是否可用
- 點擊列印按鈕
- 檢查是否開啟新視窗並顯示 PDF

### 4. 發票發送
- 對於已同步的訂單，檢查發送 Email 按鈕是否可用
- 點擊發送按鈕
- 檢查 Email 輸入對話框
- 檢查預設 Email 填入
- 發送 Email 並檢查成功通知

## 錯誤情況測試

### 1. 未完成的訂單
- 對於狀態不是 `wc-completed` 的訂單
- 同步按鈕應該不顯示或禁用

### 2. 已同步的訂單
- 對於已成功同步的訂單
- 同步按鈕應該禁用
- 顯示 "已同步" 狀態

### 3. 同步失敗
- 模擬 Xero API 錯誤
- 檢查錯誤訊息顯示
- 檢查同步狀態更新為 "failed"

### 4. Email 發送限制
- 模擬 Xero 每日 Email 限制
- 檢查特殊錯誤對話框顯示

## 數據庫檢查

### 1. wc_xero_order_syncs 表
```sql
SELECT * FROM wc_xero_order_syncs WHERE wc_order_id = {order_id};
```

應該包含：
- uuid
- wc_order_id
- xero_invoice_id（同步成功後）
- xero_invoice_no（同步成功後）
- sync_status
- created_at, updated_at

### 2. 關聯查詢
```sql
SELECT o.*, x.* 
FROM wp_wc_orders o 
LEFT JOIN wc_xero_order_syncs x ON o.id = x.wc_order_id 
WHERE o.id = {order_id};
```

## 預期行為

### 同步狀態圖標
- `pending`: 橙色 cloud_queue 圖標
- `syncing`: 藍色 cloud_sync 圖標
- `success`: 綠色 cloud_done 圖標
- `failed`: 紅色 cloud_off 圖標

### 按鈕可用性
- 同步按鈕：只有 `wc-completed` 且未成功同步的訂單
- 列印按鈕：只有已成功同步的訂單
- Email 按鈕：只有已成功同步且未取消的訂單

### 錯誤處理
- 網路錯誤：顯示一般錯誤訊息
- Xero API 錯誤：顯示具體錯誤訊息
- Email 限制：顯示特殊對話框
- 無效 Email：顯示格式錯誤訊息
