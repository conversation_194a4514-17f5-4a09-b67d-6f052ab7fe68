package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type PayrollController struct {
	payrollService service.PayrollService
}

func NewPayrollController(r *gin.RouterGroup, payrollService service.PayrollService) {
	payrollController := PayrollController{payrollService}

	// 薪資相關路由
	payrollGroup := r.Group("/v1/payrolls")
	{
		// period
		payrollGroup.GET("/periods", payrollController.ListPayrollPeriodsHandler)
		payrollGroup.GET("/periods/:uuid", payrollController.GetPayrollPeriodHandler)
		payrollGroup.POST("/periods", payrollController.CreatePayrollPeriodHandler)
		payrollGroup.PUT("/periods/:uuid", payrollController.UpdatePayrollPeriodHandler)
		payrollGroup.DELETE("/periods/:uuid", payrollController.DeletePayrollPeriodHandler)

		// payroll
		payrollGroup.GET("", payrollController.ListPayrollHandler)
		payrollGroup.GET("/:uuid", payrollController.GetPayrollHandler)
		payrollGroup.POST("", payrollController.CreatePayrollHandler)
		payrollGroup.PUT("/:uuid", payrollController.UpdatePayrollHandler)

		payrollGroup.POST("/mails", payrollController.SendMailHandler)
	}
}

func (ctr *PayrollController) ListPayrollPeriodsHandler(c *gin.Context) {
	req := struct {
		Filter     domain.PayrollPeriodFilter `form:"filter"`
		Pagination domain.Pagination          `form:"pagination"`
	}{}

	c.ShouldBindQuery(&req)

	periods, err := ctr.payrollService.ListPeriods(c, &req.Filter, &req.Pagination)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to list payroll periods")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"data":       periods,
		"pagination": req.Pagination,
	})
}

func (ctr *PayrollController) GetPayrollPeriodHandler(c *gin.Context) {
	uuid := c.Param("uuid")

	period, err := ctr.payrollService.GetPeriodByUUID(c, uuid)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to get payroll period")
		return
	}

	utils.HandleSuccess(c, period)
}

func (ctr *PayrollController) CreatePayrollPeriodHandler(c *gin.Context) {
	req := domain.PayrollPeriodCreatePayload{}
	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to bind JSON")
		return
	}

	userID := c.GetInt64("user_id")
	req.CreatedByID = userID
	req.UpdatedByID = userID

	err := ctr.payrollService.CreatePeriod(c, &req)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to create payroll period")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"uuid": req.UUID,
	})
}

func (ctr *PayrollController) UpdatePayrollPeriodHandler(c *gin.Context) {
	req := domain.PayrollPeriodUpdatePayload{}
	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to bind JSON")
		return
	}

	req.UUID = c.Param("uuid")

	userID := c.GetInt64("user_id")
	req.UpdatedByID = userID

	err := ctr.payrollService.UpdatePeriod(c, &req)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to update payroll period")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *PayrollController) DeletePayrollPeriodHandler(c *gin.Context) {
	uuid := c.Param("uuid")

	err := ctr.payrollService.DeletePeriod(c, uuid)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to delete payroll period")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *PayrollController) ListPayrollHandler(c *gin.Context) {
	req := struct {
		Filter     domain.PayrollFilter `form:"filter"`
		Pagination domain.Pagination    `form:"pagination"`
	}{}

	c.ShouldBindQuery(&req)

	payrolls, err := ctr.payrollService.ListPayroll(c, &req.Filter, &req.Pagination)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to list payrolls")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"data":       payrolls,
		"pagination": req.Pagination,
	})
}

func (ctr *PayrollController) GetPayrollHandler(c *gin.Context) {
	uuid := c.Param("uuid")

	payroll, err := ctr.payrollService.GetPayrollByUUID(c, uuid)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to get payroll")
		return
	}

	utils.HandleSuccess(c, payroll)
}

func (ctr *PayrollController) CreatePayrollHandler(c *gin.Context) {
	req := domain.PayrollCreatePayload{}
	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to bind JSON")
		return
	}

	userID := c.GetInt64("user_id")
	req.CreatedByID = userID
	req.UpdatedByID = userID

	err := ctr.payrollService.CreatePayroll(c, &req)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to create payroll")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"uuid": req.UUID,
	})
}

func (ctr *PayrollController) UpdatePayrollHandler(c *gin.Context) {
	req := domain.PayrollUpdatePayload{}
	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to bind JSON")
		return
	}

	req.UUID = c.Param("uuid")

	userID := c.GetInt64("user_id")
	req.UpdatedByID = userID

	err := ctr.payrollService.UpdatePayroll(c, &req)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to update payroll")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *PayrollController) SendMailHandler(c *gin.Context) {
	req := domain.PayrollEmailCreateRequest{}

	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to bind data")
		return
	}

	err := ctr.payrollService.SendMail(c, &req)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to send mail")
		return
	}

	utils.HandleSuccess(c, nil)
}
