package service

import (
	"context"
	"cx/domain"
	"cx/repository"

	"gorm.io/gorm"
)

type CustomerService interface {
	Create(ctx context.Context, customer *domain.Customer) error
	Update(ctx context.Context, uuid string, customer *domain.CustomerUpdatePayload) error
	Fetch(ctx context.Context, filter *domain.CustomerFilter, pagination *domain.Pagination) ([]domain.Customer, error)
	GetByID(ctx context.Context, id int64) (*domain.Customer, error)
	GetByUUID(ctx context.Context, uuid string) (*domain.Customer, error)
	Delete(ctx context.Context, uuid string) error
}

type customerService struct {
	db   *gorm.DB
	repo repository.CustomerRepository
}

func NewCustomerService(db *gorm.DB, repo repository.CustomerRepository) CustomerService {
	return &customerService{db, repo}
}

func (s *customerService) Create(ctx context.Context, customer *domain.Customer) error {
	return s.repo.Create(ctx, customer)
}

func (s *customerService) Update(ctx context.Context, uuid string, customer *domain.CustomerUpdatePayload) error {
	return s.repo.Update(ctx, uuid, customer)
}

func (s *customerService) Fetch(ctx context.Context, filter *domain.CustomerFilter, pagination *domain.Pagination) ([]domain.Customer, error) {
	return s.repo.Fetch(ctx, filter, pagination)
}

func (s *customerService) GetByID(ctx context.Context, id int64) (*domain.Customer, error) {
	return s.repo.GetByID(ctx, id)
}

func (s *customerService) GetByUUID(ctx context.Context, uuid string) (*domain.Customer, error) {
	return s.repo.GetByUUID(ctx, uuid)
}

func (s *customerService) Delete(ctx context.Context, uuid string) error {
	return s.repo.Delete(ctx, uuid)
}
