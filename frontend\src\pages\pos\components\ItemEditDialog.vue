<template>
  <q-dialog v-model="visible" class="card-dialog" no-focus>
    <q-card>
      <q-scroll-area class="full-height">
        <q-card-section class="q-pb-none">
          <div class="row">
            <div class="col-1">
              <q-btn
                type="button"
                @click="removeItem"
                icon="delete"
                color="negative"
                rounded
                dense
              />
            </div>
            <div class="col-grow text-h5 text-bold text-center">
              {{ item.product.name }}
            </div>
            <div class="co">
              <q-btn
                type="button"
                @click="visible = false"
                icon="close"
                flat
                dense
              />
            </div>
          </div>
          <div class="row">
            <div class="col-12">
              <q-separator color="black" size="2px" class="q-my-sm" />
            </div>
          </div>
        </q-card-section>
        <q-card-section class="q-pt-none">
          <div class="row">
            <div class="col-12">
              <DiscountCalculator
                v-model:rebate="localItem.rebate"
                v-model:discount="localItem.discount"
              />
            </div>
          </div>
        </q-card-section>
        <q-card-section>
          <div class="row q-mb-md">
            <div class="col col-12">
              <q-separator color="grey" size="2px" />
            </div>
          </div>
          <div class="row">
            <div class="col-4">
              <q-btn
                type="button"
                :label="t('orderItem.is_free')"
                size="lg"
                class="fit"
                :class="{ active: localItem.is_free }"
                :ripple="false"
                @click.stop="toggleFree"
              />
            </div>
          </div>
        </q-card-section>
      </q-scroll-area>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import DiscountCalculator from '@/components/DiscountCalculator.vue';
import { OrderItem } from '@/api/order';

const { t } = useI18n();

const props = defineProps<{
  modelValue: boolean;
  item: OrderItem;
}>();

const emit = defineEmits(['update:modelValue', 'remove-item']);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const localItem = computed(() => props.item);

const toggleFree = () => {
  localItem.value.is_free = !localItem.value.is_free;
};

const removeItem = () => {
  emit('remove-item', localItem.value);
  visible.value = false;
};
</script>

<style lang="scss" scoped>
.active {
  background-color: #eb8317;
  color: white;
}
</style>
