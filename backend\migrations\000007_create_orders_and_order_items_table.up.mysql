CREATE TABLE IF NOT EXISTS `orders` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `uuid` CHAR(36) NOT NULL,
    `order_no` VARCHAR(255) NOT NULL,
    `customer_id` BIGINT NULL DEFAULT NULL,
    `item_qty` INT NOT NULL,
    `rebate` DECIMAL(10, 2) NOT NULL,
    `discount` DECIMAL(10, 2) NOT NULL,
    `subtotal` DECIMAL(10, 2) NOT NULL,
    `service_fee` DECIMAL(10, 2) NOT NULL COMMENT '服務費',
    `tax` DECIMAL(10, 2) NOT NULL COMMENT '稅金',
    `tax_id` VARCHAR(20) NOT NULL COMMENT '統一編號',
    `shipping_fee` DECIMAL(10, 2) NOT NULL COMMENT '運費',
    `total_discount` DECIMAL(10, 2) NOT NULL COMMENT '總折扣金額',
    `total` DECIMAL(10, 2) NOT NULL,
    `pay_type` VARCHAR(20) NOT NULL DEFAULT 'cash',
    `status` ENUM('pending', 'processing', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
    `order_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `source` ENUM('unknown', 'pos', 'web') NOT NULL DEFAULT 'unknown',
    `user_agent` VARCHAR(255) NOT NULL,
    `ip_address` VARCHAR(50) NOT NULL,
    `created_by_id` BIGINT NULL DEFAULT NULL,
    `updated_by_id` BIGINT NULL DEFAULT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` DATETIME NULL DEFAULT NULL,
    INDEX `orders_uuid_index` (`uuid`),
    INDEX `orders_order_no_index` (`order_no`),
    INDEX `orders_customer_id_index` (`customer_id`),
    INDEX `orders_status_index` (`status`),
    INDEX `orders_order_at_index` (`order_at`),
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (`created_by_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (`updated_by_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `order_items` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `uuid` CHAR(36) NOT NULL,
    `order_uuid` CHAR(36) NOT NULL,
    `product_id` BIGINT NOT NULL,
    `quantity` INT NOT NULL,
    `price` DECIMAL(10, 2) NOT NULL,
    `total` DECIMAL(10, 2) NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` DATETIME NULL DEFAULT NULL,
    INDEX `order_items_uuid_index` (`uuid`),
    INDEX `order_items_order_uuid_index` (`order_uuid`),
    INDEX `order_items_product_id_index` (`product_id`),
    CONSTRAINT `order_items_order_id_foreign` FOREIGN KEY (`order_uuid`) REFERENCES `orders`(`uuid`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `order_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;