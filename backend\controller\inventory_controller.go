package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type InventoryController struct {
	inventoryService service.InventoryService
}

func NewInventoryController(r *gin.RouterGroup, inventoryService service.InventoryService) {
	controller := InventoryController{inventoryService}

	// 庫存相關路由
	inventoryGroup := r.Group("/v1/inventory")
	{
		inventoryGroup.GET("/products", controller.ListProductStocksHandler)
		inventoryGroup.GET("/transactions", controller.ListInventoryTransactionsHandler)
	}

	// 採購單相關路由
	purchaseOrderGroup := r.Group("/v1/purchase-orders")
	{
		purchaseOrderGroup.GET("", controller.ListPurchaseOrdersHandler)
		purchaseOrderGroup.GET("/:uuid", controller.GetPurchaseOrderHandler)
		purchaseOrderGroup.POST("", controller.CreatePurchaseOrderHandler)
	}

	// 退貨相關路由
	returnGroup := r.Group("/v1/return-orders")
	{
		returnGroup.GET("", controller.ListReturnsHandler)
		returnGroup.POST("", controller.CreateReturnHandler)
	}

	// 報廢相關路由
	scrapGroup := r.Group("/v1/scraps")
	{
		scrapGroup.GET("", controller.ListScrapsHandler)
		scrapGroup.POST("", controller.CreateScrapHandler)
	}

	// 盤點相關路由
	stockStocktakingGroup := r.Group("/v1/stocktaking")
	{
		stockStocktakingGroup.GET("", controller.ListStocktakingHandler)
		stockStocktakingGroup.POST("", controller.CreateStocktakingHandler)
	}

}

// 庫存列表
func (ctr *InventoryController) ListProductStocksHandler(c *gin.Context) {
	req := struct {
		Filter     domain.ProductStockFilter `json:"filter"`
		Pagination domain.Pagination         `json:"pagination"`
	}{}

	c.ShouldBindQuery(&req)

	productStocks, err := ctr.inventoryService.ListProductStocks(c, &req.Filter, &req.Pagination)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to list product stocks")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"data":       productStocks,
		"pagination": req.Pagination,
	})
}

// 庫存交易列表
func (ctr *InventoryController) ListInventoryTransactionsHandler(c *gin.Context) {
	req := struct {
		Filter     domain.InventoryTransactionFilter `json:"filter"`
		Pagination domain.Pagination                 `json:"pagination"`
	}{}

	c.ShouldBindQuery(&req)

	transactions, err := ctr.inventoryService.ListInventoryTransactions(c, &req.Filter, &req.Pagination)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to list inventory transactions")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"data":       transactions,
		"pagination": req.Pagination,
	})
}

// 採購單列表
func (ctr *InventoryController) ListPurchaseOrdersHandler(c *gin.Context) {
	pagination := domain.Pagination{}

	c.ShouldBindQuery(&pagination)

	purchaseOrders, err := ctr.inventoryService.ListPurchaseOrders(c, &pagination)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to list purchase orders")
		return
	}

	utils.HandleSuccess(c, purchaseOrders)
}

// 獲取採購單詳情
func (ctr *InventoryController) GetPurchaseOrderHandler(c *gin.Context) {
	uuid := c.Param("uuid")

	order, err := ctr.inventoryService.GetPurchaseOrderByUUID(c, uuid)
	if err != nil {
		utils.HandleError(c, http.StatusNotFound, err, "Purchase order not found")
		return
	}

	utils.HandleSuccess(c, order)
}

// 採購單處理
func (ctr *InventoryController) CreatePurchaseOrderHandler(c *gin.Context) {
	var req domain.CreatePurchaseOrderRequest
	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	userID := c.GetInt64("user_id")
	req.CreatedByID = userID
	req.UpdatedByID = userID

	if err := ctr.inventoryService.CreatePurchaseOrder(c, &req); err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to stock in")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"uuid": req.UUID,
	})
}

// 退貨
func (ctr *InventoryController) ListReturnsHandler(c *gin.Context) {

}

func (ctr *InventoryController) CreateReturnHandler(c *gin.Context) {
	var req domain.CreateReturnRequest

	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	userID := c.GetInt64("user_id")
	req.CreatedByID = userID
	req.UpdatedByID = userID

	if err := ctr.inventoryService.CreateReturn(c, &req); err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to create return")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"uuid": req.UUID,
	})
}

// 報廢
func (ctr *InventoryController) ListScrapsHandler(c *gin.Context) {

}

func (ctr *InventoryController) CreateScrapHandler(c *gin.Context) {
	var req domain.CreateScrapRequest

	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	userID := c.GetInt64("user_id")
	req.CreatedByID = userID
	req.UpdatedByID = userID

	if err := ctr.inventoryService.CreateScrap(c, &req); err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to create return")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"uuid": req.UUID,
	})
}

// 盤點
func (ctr *InventoryController) ListStocktakingHandler(c *gin.Context) {

}

func (ctr *InventoryController) CreateStocktakingHandler(c *gin.Context) {
	var req domain.CreateStocktakingRequest

	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	userID := c.GetInt64("user_id")
	req.CreatedByID = userID
	req.UpdatedByID = userID

	if err := ctr.inventoryService.CreateStocktaking(c, &req); err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to create return")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"uuid": req.UUID,
	})
}
