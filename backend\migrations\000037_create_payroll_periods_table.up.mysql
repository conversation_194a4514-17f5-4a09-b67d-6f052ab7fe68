CREATE TABLE `payroll_periods` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `uuid` CHAR(36) NOT NULL,
    `start_date` DATE NOT NULL,
    `end_date` DATE NOT NULL,
    `created_by_id` BIGINT NOT NULL,
    `updated_by_id` BIGINT NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` DATETIME NULL DEFAULT NULL,
    UNIQUE KEY `payroll_periods_uuid_unique` (`uuid`),
    INDEX `payroll_periods_start_date_index` (`start_date`),
    INDEX `payroll_periods_end_date_index` (`end_date`),
    CONSTRAINT `payroll_periods_created_by_id_foreign` FOREIGN KEY (`created_by_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT `payroll_periods_updated_by_id_foreign` FOREIGN KEY (`updated_by_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

ALTER TABLE `payrolls` ADD COLUMN `period_id` BIGINT NULL AFTER `id`,
    ADD CONSTRAINT `payrolls_period_id_foreign` FOREIGN KEY (`period_id`) REFERENCES `payroll_periods` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `payrolls` DROP INDEX `payrolls_user_id_payroll_period_unique`;
ALTER TABLE `payrolls` DROP INDEX `payrolls_payroll_period_index`;
ALTER TABLE `payrolls` DROP COLUMN `payroll_period`;