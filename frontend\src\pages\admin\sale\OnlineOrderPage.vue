<!-- 線上WP訂單紀錄 -->
<template>
  <q-page>
    <q-card flat square bordered class="bg-cream">
      <q-card-section>
        <q-table
          virtual-scroll
          :rows="orders"
          :columns="columns"
          v-model:pagination="pagination"
          hide-pagination
          binary-state-sort
          table-header-class="bg-grey-3"
          @request="onRequest"
          :loading="isLoading"
        >
          <template v-slot:top>
            <q-toolbar>
              <q-toolbar-title>
                {{ t('orders') }} - {{ t('online') }}
              </q-toolbar-title>
            </q-toolbar>
            <!-- search name -->
            <!-- <q-toolbar>
              <q-input
                v-model="search"
                outlined
                dense
                :placeholder="t('search.order')"
                clearable
                clear-icon="close"
                @keyup.enter.prevent="getOrders"
                @update:model-value="getOrders"
                input-debounce="500"
                style="width: 300px"
              >
                <template v-slot:prepend>
                  <q-icon name="search" />
                </template>
              </q-input>
            </q-toolbar> -->

            <!-- Filter -->
            <q-toolbar style="display: none">
              <!-- select customers -->
              <q-select
                v-model="customerUUID"
                :options="customerOptions"
                option-label="name"
                option-value="uuid"
                :label="t('customer.label')"
                emit-value
                map-options
                @update:model-value="getOrders"
              />

              <!-- select order status -->
              <q-select
                v-model="orderStatus"
                :options="statusOptions"
                option-label="label"
                option-value="value"
                :label="t('orderStatus.label')"
                emit-value
                map-options
                class="q-mx-md"
                style="width: 120px"
              />
            </q-toolbar>
          </template>

          <template v-slot:body="props">
            <q-tr :props="props" @click="showDetail(props.row.id)">
              <q-td :props="props" key="id">
                {{ props.row.id }}
              </q-td>
              <q-td :props="props" key="date_created">
                {{ formatDate(props.row.date_created, 'YYYY-MM-DD HH:mm') }}
              </q-td>
              <q-td :props="props" key="customer">
                <template v-if="props.row.customer_name">
                  {{ props.row.customer_name }}
                </template>
                <template v-else>
                  {{ t('unknown.customer') }}
                </template>
              </q-td>
              <q-td :props="props" key="total" class="text-bold">
                AU$ {{ formatNumber(props.row.total, 2) }}
              </q-td>
              <q-td :props="props" key="status" class="text-bold">
                {{ getOrderStatusLabel(props.row.status) }}
              </q-td>
              <q-td :props="props" key="shipping_method">
                {{ getShippingMethodDisplay(props.row.shipping_method, props.row.shipping_method_title) }}
              </q-td>
            </q-tr>
          </template>
        </q-table>

        <TablePagination v-model="pagination" @getData="getOrders" />
      </q-card-section>
    </q-card>

    <WCOrderDetailDialog
      v-model="showDetailDialog"
      :orderID="selectedOrderID"
      @refresh="getOrders"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import WCOrderDetailDialog from '@/pages/pos/components/WCOrderDetailDialog.vue';
import TablePagination from '@/components/TablePagination.vue';
import { CustomerApi } from '@/api/customer';
import { OrderApi, WCOrderInfo } from '@/api/order';
import {
  Pagination,
  TableRequestProps,
  getOrderStatusLabel,
  orderStatusOptions,
} from '@/types';
import { formatDate, formatNumber, getShippingMethodDisplay } from '@/utils';

const { t } = useI18n();

const orders = ref<WCOrderInfo[]>([]);
const columns = computed(() => [
  {
    name: 'id',
    label: t('orderNo'),
    field: 'id',
    align: 'center' as const,
  },
  {
    name: 'date_created',
    label: t('dateAt'),
    field: 'date_created',
    align: 'center' as const,
    // sortable: true,
  },
  {
    name: 'customer',
    label: t('customer.label'),
    field: 'customer_name',
    align: 'center' as const,
  },
  {
    name: 'total',
    label: t('total'),
    field: 'total',
    align: 'center' as const,
    // sortable: true,
  },
  {
    name: 'status',
    label: t('status'),
    field: 'status',
    align: 'center' as const,
    // sortable: true,
  },
  {
    name: 'shipping_method',
    label: t('shippingMethod'),
    field: 'shipping_method',
    align: 'center' as const,
  },
]);

const pagination = ref<Pagination>({
  sortBy: 'date_created',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

const isLoading = ref(false);
// const search = ref('');
const customerUUID = ref('');
const orderStatus = ref('');
const statusOptions = computed(() => [
  {
    label: t('allStatus'),
    value: '',
  },
  ...orderStatusOptions.value,
]);

const getOrders = async () => {
  try {
    isLoading.value = true;
    const response = await OrderApi.listWCHistory({ pagination: pagination.value });

    orders.value = response.result.data;
    pagination.value = response.result.pagination;
  } finally {
    isLoading.value = false;
  }
};

const customers = ref<
  {
    uuid: string;
    name: string;
  }[]
>([]);
const customerOptions = computed(() => [
  {
    uuid: '',
    name: t('customer.all'),
  },
  ...customers.value,
]);
const getCustomers = async () => {
  const response = await CustomerApi.fetch();

  for (let customer of response.result.data) {
    customers.value.push({
      uuid: customer.uuid,
      name: customer.name,
    });
  }
};

onMounted(() => {
  getOrders();
  getCustomers();
});

const showDetailDialog = ref(false);
const selectedOrderID = ref(0);
const showDetail = (orderID: number) => {
  selectedOrderID.value = orderID;
  showDetailDialog.value = true;
};

const onRequest = (props: unknown) => {
  if (!props) return;

  const reqProps = props as TableRequestProps;
  const { sortBy, descending } = reqProps.pagination;

  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;

  getOrders();
};
</script>
