# 使用官方 Go 映像作為構建環境
FROM golang:1.21-alpine AS builder

# 設置工作目錄
WORKDIR /app

# 複製 go mod 和 sum 文件
COPY go.mod go.sum ./

# 下載依賴
RUN go mod download

# 複製源代碼
COPY . .

# 構建調度器應用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o scheduler ./cmd/scheduler

# 使用輕量級的 alpine 映像作為運行環境
FROM alpine:latest

# 安裝 ca-certificates 以支持 HTTPS 請求
RUN apk --no-cache add ca-certificates

# 設置工作目錄
WORKDIR /root/

# 從構建階段複製二進制文件
COPY --from=builder /app/scheduler .

# 設置時區
RUN apk add --no-cache tzdata
ENV TZ=Asia/Taipei

# 暴露端口（如果需要）
# EXPOSE 8080

# 運行調度器
CMD ["./scheduler"]
