@echo off
setlocal enabledelayedexpansion

@REM 顯示開始時間
set start_time=%time%
echo 開始時間：%start_time%

set DEFAULT_OS=linux
set TARGET_OS=%DEFAULT_OS%

:parse_args
if "%1"=="" goto end_parse_args
if "%1"=="-m" (
    set "TARGET_OS=%2"
    shift
)
shift
goto parse_args
:end_parse_args

@REM 將命令提示符的代碼頁更改為 UTF-8，以支持顯示中文字符。
chcp 65001 > nul

echo 正在建置...
set GOOS=%TARGET_OS%

if "%TARGET_OS%"=="windows" (
    set OUTPUT_FILE=main.exe
    set GOARCH=amd64
) else if "%TARGET_OS%"=="darwin" (
    set OUTPUT_FILE=main
    set GOARCH=arm64
    @REM 舊版本macos使用amd64
) else (
    set OUTPUT_FILE=main
    set GOARCH=amd64
)

go build -ldflags="-w -s" -o %OUTPUT_FILE%
echo 建置完成。輸出檔案：%OUTPUT_FILE%
echo 目標作業系統：%TARGET_OS%