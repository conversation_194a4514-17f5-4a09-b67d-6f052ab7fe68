package service

import (
	"context"
	"cx/domain"
	"cx/repository"
	"cx/utils"
	"mime/multipart"
	"path/filepath"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ProductCategoryService interface {
	Create(ctx context.Context, productCategory *domain.ProductCategory) (int64, error)
	Update(ctx context.Context, id int64, productCategory *domain.ProductCategory) error
	Fetch(ctx context.Context) ([]domain.ProductCategory, error)
	GetByID(ctx context.Context, id int64) (*domain.ProductCategory, error)

	UploadImage(c *gin.Context, categoryID int64, image *multipart.FileHeader) error
	DeleteImage(ctx context.Context, imageUUID string) error
}

type productCategoryService struct {
	db                    *gorm.DB
	wpDB                  *gorm.DB
	productCategoryRepo   repository.ProductCategoryRepository
	wcProductCategoryRepo repository.WcCategoryRepository
}

func NewProductCategoryService(db *gorm.DB, wpDB *gorm.DB, productCategoryRepo repository.ProductCategoryRepository) ProductCategoryService {
	return &productCategoryService{db, wpDB, productCategoryRepo, repository.NewWcCategoryRepository(wpDB)}
}

func (s *productCategoryService) Create(ctx context.Context, productCategory *domain.ProductCategory) (int64, error) {
	wcProductCategory := domain.WcCategory{
		Name: productCategory.Name,
		Slug: utils.ConvertWPSlug(productCategory.Name),
	}

	err := s.wcProductCategoryRepo.Create(&wcProductCategory)
	if err == nil {
		productCategory.WcID = wcProductCategory.ID
	}

	err = s.productCategoryRepo.Create(ctx, productCategory)
	return productCategory.ID, err
}

func (s *productCategoryService) Update(ctx context.Context, id int64, productCategory *domain.ProductCategory) error {
	category, err := s.productCategoryRepo.GetByID(ctx, productCategory.ID)
	if err != nil {
		return err
	}

	wcProductCategory := domain.WcCategory{
		ID:   category.WcID,
		Name: productCategory.Name,
		Slug: utils.ConvertWPSlug(productCategory.Name),
	}

	if category.WcID != 0 {
		if err := s.wcProductCategoryRepo.Update(&wcProductCategory); err != nil {
			return err
		}
	} else {
		if err := s.wcProductCategoryRepo.Create(&wcProductCategory); err != nil {
			return err
		}
		productCategory.WcID = wcProductCategory.ID
	}

	if category.Image.ID != 0 && category.Image.WcID == 0 {
		wpDB := s.wpDB.Begin()
		wcCatRepo := repository.NewWcCategoryRepository(wpDB)
		imageWcID, err := repository.InsertMediaToWordpress(wpDB, category.Image.ImagePath)
		if err != nil {
			wpDB.Rollback()
			return err
		}

		category.Image.WcID = imageWcID
		err = wcCatRepo.UpdateImage(productCategory.WcID, imageWcID)
		if err != nil {
			wpDB.Rollback()
			return err
		}

		err = s.productCategoryRepo.UpdateImageWcID(ctx, category.Image.ID, imageWcID)
		if err != nil {
			wpDB.Rollback()
			return err
		}

		wpDB.Commit()
	}

	return s.productCategoryRepo.Update(ctx, id, productCategory)
}

func (s *productCategoryService) Fetch(ctx context.Context) ([]domain.ProductCategory, error) {
	return s.productCategoryRepo.Fetch(ctx)
}

func (s *productCategoryService) GetByID(ctx context.Context, id int64) (*domain.ProductCategory, error) {
	return s.productCategoryRepo.GetByID(ctx, id)
}

func (s *productCategoryService) UploadImage(c *gin.Context, categoryID int64, image *multipart.FileHeader) error {
	filePath := "uploads/categories/"
	imageUUID := utils.GenerateUUID()
	fileName := imageUUID + filepath.Ext(image.Filename)

	category, err := s.productCategoryRepo.GetByID(c, categoryID)
	if err != nil {
		return err
	}

	uploadImage := domain.ProductCategoryImage{
		UUID:              imageUUID,
		ProductCategoryID: category.ID,
		ImagePath:         filePath + fileName,
	}

	// 確認路徑是否已建立，如果沒有則建立
	if err = utils.CreatePathIfNotExists(filePath); err != nil {
		return err
	}

	// 上傳檔案
	if err := c.SaveUploadedFile(image, uploadImage.ImagePath); err != nil {
		return err
	}

	wpDB := s.wpDB.Begin()
	err = s.db.Transaction(func(tx *gorm.DB) error {
		productCategoryRepo := repository.NewProductCategoryRepository(tx)
		wcProductCategoryRepo := repository.NewWcCategoryRepository(wpDB)

		if category.Image.ID != 0 {
			if err := productCategoryRepo.DeleteImage(c, category.Image.UUID); err != nil {
				return err
			}
		}

		imageWcID, err := repository.InsertMediaToWordpress(wpDB, uploadImage.ImagePath)
		if err != nil {
			return err
		}

		if err = wcProductCategoryRepo.UpdateImage(category.WcID, imageWcID); err != nil {
			return err
		}

		uploadImage.WcID = imageWcID
		if err := productCategoryRepo.CreateImage(c, &uploadImage); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		wpDB.Rollback()
		return err
	}

	wpDB.Commit()

	return nil
}

func (s *productCategoryService) DeleteImage(ctx context.Context, imageUUID string) error {
	err := s.db.Transaction(func(tx *gorm.DB) error {
		productCategoryRepo := repository.NewProductCategoryRepository(tx)

		image, err := productCategoryRepo.GetImageByUUID(ctx, imageUUID)
		if err != nil {
			return err
		}

		// 刪除資料庫記錄
		if err := productCategoryRepo.DeleteImage(ctx, imageUUID); err != nil {
			return err
		}

		// 刪除檔案
		if err := utils.DeleteFile(image.ImagePath); err != nil {
			return err
		}

		return nil
	})

	return err
}
