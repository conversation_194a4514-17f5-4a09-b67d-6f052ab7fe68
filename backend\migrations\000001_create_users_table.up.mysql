CREATE TABLE IF NOT EXISTS `users` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `uuid` CHAR(36) NOT NULL,
    `username` VARCHA<PERSON>(255) NOT NULL COMMENT '帳號',
    `password` VA<PERSON>HA<PERSON>(255) NOT NULL COMMENT '密碼',
    `name` VARCHAR(255) NOT NULL COMMENT '姓名',
    `sex` ENUM('M', 'F') NOT NULL DEFAULT 'M' COMMENT '性別',
    `phone` VARCHAR(20) NOT NULL COMMENT '手機號碼',
    `birthday` DATE NULL DEFAULT NULL COMMENT '生日',
    `email` VARCHAR(255) NOT NULL COMMENT '電子郵件',
    `is_admin` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否為管理員',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否啟用',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` DATETIME NULL DEFAULT NULL,
    INDEX `users_uuid_index` (`uuid`),
    INDEX `users_username_index` (`username`),
    INDEX `users_name_index` (`name`),
    INDEX `users_deleted_at_index` (`deleted_at`),
    UNIQUE KEY `users_username_unique` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;