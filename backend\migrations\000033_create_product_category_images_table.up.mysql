CREATE TABLE `product_category_images` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `wc_id` BIGINT UNSIGNED NOT NULL,
    `uuid` VARCHAR(36) NOT NULL,
    `product_category_id` BIGINT NOT NULL,
    `image_path` VARCHAR(255) NOT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_uuid`(`uuid`),
    CONSTRAINT `fk_product_category_image_product_category_id` FOREIGN KEY (`product_category_id`) 
    REFERENCES `product_categories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;