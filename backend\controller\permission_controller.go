package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"

	"github.com/gin-gonic/gin"
)

type PermissionController struct {
	permissionService service.PermissionService
}

func NewPermissionController(r *gin.RouterGroup, permissionService service.PermissionService) {
	permissionController := PermissionController{permissionService}

	v1 := r.Group("/v1/permissions")
	{
		v1.GET("", permissionController.FetchHandler)
	}
}

func (ctr *PermissionController) FetchHandler(c *gin.Context) {
	var res domain.PermissionResponse

	res.Permissions = ctr.permissionService.FetchPermissions(c, nil)

	groupID := c.Query("group_id")
	if groupID != "" {
		res.GroupPermissions = ctr.permissionService.FetchPermissions(c, &domain.PermissionFilter{GroupID: utils.ParseInt64(groupID)})
	}

	utils.HandleSuccess(c, res)
}
