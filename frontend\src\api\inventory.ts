import { apiWrapper } from '@/boot/axios';
import { CreateResponse } from './modules/response';
import { Pagination } from '@/types';
import { CustomerInfo } from './customer';
import { Product, ProductInfo } from './product';
import { Order } from './order';

export interface ProductStock {
  uuid: string;
  name: string;
  category: string;
  barcode: string;
  stock_qty: number;
  min_stock_qty: number;
  diff_stock_qty: number;
  unit: string;
}

export interface ProductStockFilter {
  search?: string;
  name?: string;
}

export interface InventoryTransaction {
  uuid: string;
  transaction_type: 'sale' | 'stock_in';
  order: Order;
  product: ProductInfo;
  quantity: number;
  before_quantity: number; // 改變前庫存量
  after_quantity: number; // 改變後庫存量
  unit_price: number; //  單價
  total_amount: number; // 總價
  created_at: string;
  updated_at: string;
}

export interface InventoryTransactionFilter {
  search?: string;
  product_uuid?: string;
  transaction_types?: string[];
}

// 採購單/進貨單
export interface PurchaseOrder {
  uuid?: string;
  po_number: string;
  customer: CustomerInfo | null;
  order_date: Date | string;
  expected_arrival_date?: Date | string;
  arrival_date?: Date | string;
  status: string;
  total_amount?: number;
  paid_amount?: number;
  notes: string;
  items: PurchaseOrderItem[];
}

export interface PurchaseOrderItem {
  uuid?: string;
  product: ProductInfo | Product;
  quantity_ordered: number; // 訂購數量
  quantity_received: number; // 實際到貨數量
  unit_price: number; // 單價
  total_price: number; // 總價
  notes?: string; // 備註
}

export const InventoryApi = {
  listProductStocks: ({
    filter,
    pagination,
  }: {
    filter?: ProductStockFilter;
    pagination?: Pagination;
  }) =>
    apiWrapper.get<{
      data: ProductStock[];
      pagination: Pagination;
    }>('/v1/inventory/products', {
      params: {
        ...filter,
        ...pagination,
      },
    }),
  listTransactions: ({
    filter,
    pagination,
  }: {
    filter?: InventoryTransactionFilter;
    pagination?: Pagination;
  }) =>
    apiWrapper.get<{
      data: InventoryTransaction[];
      pagination: Pagination;
    }>('/v1/inventory/transactions', {
      params: {
        ...filter,
        ...pagination,
      },
    }),
};

export const PurchaseOrderApi = {
  listPurchaseOrders: () =>
    apiWrapper.get<PurchaseOrder[]>('/v1/inventory/purchase-orders'),
  getPurchaseOrder: (uuid: string) =>
    apiWrapper.get<PurchaseOrder>(`/v1/inventory/purchase-orders/${uuid}`),
  createPurchaseOrder: (data: PurchaseOrder) =>
    apiWrapper.post<CreateResponse>('/v1/purchase-orders', data),
};

export interface ReturnOrder {
  uuid: string;
  return_number: string;
  return_type: string;
  order_no: string;
  customer: CustomerInfo | null;
  return_date: Date | string;
  notes: string;
  items: ReturnOrderItem[];
}

export interface ReturnOrderItem {
  uuid: string;
  product: ProductInfo | Product;
  quantity: number;
  notes: string;
}

export const ReturnOrderApi = {
  listReturnOrders: () => apiWrapper.get<ReturnOrder[]>('/v1/return-orders'),
  getReturnOrder: (uuid: string) =>
    apiWrapper.get<ReturnOrder>(`/v1/return-orders/${uuid}`),
  createReturnOrder: (data: ReturnOrder) =>
    apiWrapper.post<CreateResponse>('/v1/return-orders', data),
};

export interface ScrapStock {
  uuid: string;
  scrap_date: Date | string;
  notes: string;
  items: ScrapStockItem[];
}

export interface ScrapStockItem {
  uuid: string;
  product: ProductInfo | Product;
  quantity: number;
  notes: string;
}

export const ScrapStockApi = {
  listScrapStocks: () => apiWrapper.get<ScrapStock[]>('/v1/scraps'),
  getScrapStock: (uuid: string) =>
    apiWrapper.get<ScrapStock>(`/v1/scraps/${uuid}`),
  createScrapStock: (data: ScrapStock) =>
    apiWrapper.post<CreateResponse>('/v1/scraps', data),
};

export interface Stocktaking {
  uuid: string;
  count_number: string;
  count_date: Date | string;
  notes: string;
  items: StocktakingItem[];
}

export interface StocktakingItem {
  uuid: string;
  product: ProductInfo | Product;
  actual_quantity: number; // 盤點後的數量
  notes: string;
}

export const StocktakingApi = {
  listStocktaking: () => apiWrapper.get<Stocktaking[]>('/v1/stocktaking'),
  getStocktaking: (uuid: string) =>
    apiWrapper.get<Stocktaking>(`/v1/stocktaking/${uuid}`),
  createStocktaking: (data: Stocktaking) =>
    apiWrapper.post<CreateResponse>('/v1/stocktaking', data),
};
