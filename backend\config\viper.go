package config

import (
	"fmt"

	"github.com/spf13/viper"
)

var configPath = "."

func setupViper() (*viper.Viper, error) {
	v := viper.New()

	// 設定配置文件
	v.AddConfigPath(configPath)
	v.SetConfigName(".env")
	v.SetConfigType("env")

	// 讀取環境變數
	v.AutomaticEnv()

	// 讀取配置文件
	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}

	return v, nil
}

func SetConfigPath(path string) {
	configPath = path
}
