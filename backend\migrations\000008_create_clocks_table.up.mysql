CREATE TABLE IF NOT EXISTS `clocks` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `uuid` CHAR(36) NOT NULL,
    `user_id` BIGINT NOT NULL,
    `type` ENUM('check_in', 'check_out') NOT NULL,
    `clock_time` DATETIME NOT NULL,
    `user_agent` VARCHAR(255) NOT NULL,
    `ip_address` VARCHAR(50) NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` DATETIME NULL DEFAULT NULL,
    INDEX `attendances_user_id_index` (`user_id`),
    INDEX `attendances_date_time_index` (`clock_time`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `clock_pairs` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `uuid` CHAR(36) NOT NULL,
    `user_id` BIGINT NOT NULL,
    `date_at` DATE NOT NULL,
    `clock_in_id` BIGINT NULL DEFAULT NULL,
    `clock_out_id` BIGINT NULL DEFAULT NULL,
    `status` ENUM('pending', 'paired', 'missing') NOT NULL DEFAULT 'pending',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` DATETIME NULL DEFAULT NULL,
    INDEX `clock_pairs_user_id_index` (`user_id`),
    INDEX `clock_pairs_date_at_index` (`date_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`clock_in_id`) REFERENCES `clocks`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`clock_out_id`) REFERENCES `clocks`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;