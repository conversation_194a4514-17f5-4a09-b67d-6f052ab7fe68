import{Q as he}from"./QSpace.bd91c020.js";import{by as we,u as te,r as S,c as z,o as y,k as O,f as l,b as e,z as oe,y as T,i as $,t as n,bz as Oe,Q as ke,l as q,aH as ne,m as R,x as re,a$ as Z,d as Ie,s as Ce,w as Ee,q as c,p as u,h as w,a as ee,aS as qe,j as Se,F as De,v as Ve}from"./index.9477d5a3.js";import{Q as f,a as o}from"./QItemSection.7dc1f54f.js";import{Q as Qe}from"./QSelect.c4b20219.js";import{Q as le}from"./QItemLabel.3b58be08.js";import{Q as $e}from"./QList.f0282e16.js";import{Q as Re}from"./QScrollArea.44613085.js";import{u as ze}from"./use-quasar.66282a44.js";import{O as P,E as Le,u as Ue}from"./usePrintInvoice.e1a96b8f.js";import{X as ae}from"./xero.72a68f5d.js";import{f as Me}from"./date.6d29930c.js";import{f as B}from"./QTr.5f91a8aa.js";import{u as Ne}from"./dialog.c8d4f0ed.js";const se=(V,b)=>we.global.t(V,b),Xe=(V,b)=>!V&&!b?se("unknown"):b!=null&&b.includes("Parcel")?"Parcel Post":b!=null&&b.includes("Express")?"Express Post":b||V||se("unknown"),Pe={class:"text-h6"},Ae={__name:"WCOrderCancelDialog",props:{orderId:{type:Number,required:!0},otherReason:{type:String,default:""}},emits:["order-cancelled"],setup(V,{expose:b,emit:t}){const{t:m}=te(),M=V,F=t,k=S(!1),g=S(""),E=S(""),p=S(!1),L=z(()=>[{label:m("wcOrder.cancel.reasons.outOfStock"),value:m("wcOrder.cancel.reasons.outOfStock")},{label:m("wcOrder.cancel.reasons.customerRequest"),value:m("wcOrder.cancel.reasons.customerRequest")},{label:m("wcOrder.cancel.reasons.paymentIssue"),value:m("wcOrder.cancel.reasons.paymentIssue")},{label:m("wcOrder.cancel.reasons.duplicateOrder"),value:m("wcOrder.cancel.reasons.duplicateOrder")},{label:m("wcOrder.cancel.reasons.incorrectInfo"),value:m("wcOrder.cancel.reasons.incorrectInfo")},{label:m("wcOrder.cancel.reasons.other"),value:"other"}]),a=()=>{k.value=!0,g.value="",E.value=M.otherReason||"",M.otherReason?g.value="other":g.value=L.value[0].value},U=()=>{k.value=!1,p.value=!1},D=()=>{if(!g.value){Z.create({type:"warning",position:"top",message:m("wcOrder.cancel.reasonRequired")});return}if(g.value==="other"&&E.value===""){Z.create({type:"warning",position:"top",message:m("wcOrder.cancel.reasonRequired")});return}const I={orderId:M.orderId,reason:g.value,otherReason:g.value==="other"?E.value:null};F("order-cancelled",I),U()};return b({openDialog:a}),(I,Q)=>(y(),O(re,{modelValue:k.value,"onUpdate:modelValue":Q[2]||(Q[2]=C=>k.value=C),persistent:"","no-refocus":""},{default:l(()=>[e(oe,{style:{"min-width":"350px"}},{default:l(()=>[e(T,null,{default:l(()=>[$("div",Pe,n(I.$t("wcOrder.cancel.title")),1)]),_:1}),e(T,null,{default:l(()=>[e(Oe,{modelValue:g.value,"onUpdate:modelValue":Q[0]||(Q[0]=C=>g.value=C),options:L.value,type:"radio"},null,8,["modelValue","options"]),g.value==="other"?(y(),O(ke,{key:0,modelValue:E.value,"onUpdate:modelValue":Q[1]||(Q[1]=C=>E.value=C),modelModifiers:{trim:!0},type:"textarea",label:I.$t("wcOrder.cancel.reasonLabel"),placeholder:I.$t("wcOrder.cancel.reasonPlaceholder"),class:"q-mt-md",outlined:"",rules:[C=>C.length>0||I.$t("wcOrder.cancel.reasonRequired")]},null,8,["modelValue","label","placeholder","rules"])):q("",!0)]),_:1}),e(ne,{align:"right"},{default:l(()=>[e(R,{flat:"",label:I.$t("cancel"),color:"primary",onClick:U},null,8,["label"]),e(R,{label:I.$t("confirm"),color:"negative",onClick:D,loading:p.value},null,8,["label","loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"]))}},We={class:"row q-mt-sm"},Be={class:"text-h5 text-bold"},Te={class:"row q-mb-sm"},Fe={class:"row"},He={key:0,class:"text-caption text-grey"},Ye={class:"text-red"},je={class:"row q-gutter-sm"},ul=Ie({__name:"WCOrderDetailDialog",props:{modelValue:{type:Boolean},orderID:{}},emits:["update:modelValue","refresh"],setup(V,{emit:b}){const{t}=te(),m=ze(),M=Ne(),{printInvoice:F}=Ue(),k=V,g=b,E=z({get:()=>k.modelValue,set:s=>g("update:modelValue",s)});Ce(()=>{ce()}),Ee(()=>k.modelValue,s=>{s&&A()});const p=S(!1),L=S(!1),a=S(),U=S(),D=S(!1),I=S(),Q=z(()=>[{label:t("orderStatus.processing"),value:"processing"},{label:t("orderStatus.on-hold"),value:"on-hold"},{label:t("orderStatus.packing"),value:"packing"},{label:t("orderStatus.shipping"),value:"shipping"},{label:t("orderStatus.completed"),value:"completed"},{label:t("orderStatus.cancelled"),value:"cancelled"},{label:t("orderStatus.refunded"),value:"refunded"}]),C=z({get:()=>{var r;const s=(r=a.value)==null?void 0:r.status;return s?s.startsWith("wc-")?s.substring(3):s:""},set:()=>{}}),A=async()=>{try{p.value=!0;const s=await P.getWCOrder(k.orderID);a.value=s.result}finally{p.value=!1}},ie=async s=>{var r;if(!s||typeof s!="string"||s.trim()===""){console.error("Invalid status:",s);return}if(s=s.trim(),s!==C.value){if(s==="cancelled"){(r=U.value)==null||r.openDialog();return}M.showMessage({title:t("wcOrder.statusUpdate.title"),message:t("wcOrder.statusUpdate.confirm"),timeout:0,persistent:!0,ok:async()=>{try{p.value=!0,await P.updateWCOrderStatus(k.orderID,s),await A()}finally{p.value=!1,g("refresh")}}})}},ue=async s=>{try{p.value=!0;const r=s.reason==="other"&&s.otherReason?`${t("wcOrder.cancel.title")}: ${s.otherReason}`:`${t("wcOrder.cancel.title")}: ${s.reason}`;await P.updateWCOrderCustomerNote(k.orderID,r),await P.updateWCOrderStatus(k.orderID,"cancelled"),await A()}finally{p.value=!1,g("refresh")}},ce=async()=>{try{const s=await ae.getConfig();I.value=s.result}catch(s){console.error("Failed to load Xero config:",s)}},de=()=>{var s;if(!((s=a.value)!=null&&s.xero_sync))return"cloud_off";switch(a.value.xero_sync.sync_status){case"success":return"cloud_done";case"failed":return"cloud_off";case"syncing":return"cloud_sync";case"pending":return"cloud_queue";default:return"cloud_off"}},me=()=>{var s;if(!((s=a.value)!=null&&s.xero_sync))return"grey";switch(a.value.xero_sync.sync_status){case"success":return"green";case"failed":return"red";case"syncing":return"blue";case"pending":return"orange";default:return"grey"}},fe=()=>{var s;if(!((s=a.value)!=null&&s.xero_sync))return t("xero.notSynced");switch(a.value.xero_sync.sync_status){case"success":return t("xero.syncSuccess");case"failed":return t("xero.syncFailed");case"syncing":return t("xero.syncing");case"pending":return t("xero.syncPending");default:return t("xero.notSynced")}},pe=async()=>{var s;if(!!((s=a.value)!=null&&s.id))try{L.value=!0;const r=await P.syncWCOrderToXero(a.value.id);m.notify({type:"positive",message:r.result.message||t("xero.syncSuccess"),position:"top"}),await A(),g("refresh")}catch(r){Ve(r)}finally{L.value=!1}},H=z(()=>{var r;const s=(r=a.value)==null?void 0:r.status;return!!(s&&["wc-processing","wc-packing","wc-shipping","wc-completed"].includes(s))}),ve=z(()=>{var s,r,v,h;return!!(((r=(s=a.value)==null?void 0:s.xero_sync)==null?void 0:r.xero_invoice_id)&&((h=(v=a.value)==null?void 0:v.xero_sync)==null?void 0:h.sync_status)==="success"&&H.value)}),_e=z(()=>{var s,r;return!!(((r=(s=a.value)==null?void 0:s.xero_sync)==null?void 0:r.xero_invoice_id)&&H.value)}),ge=async()=>{var s,r;if(!!((r=(s=a.value)==null?void 0:s.xero_sync)!=null&&r.xero_invoice_id))try{p.value=!0;const v={uuid:`wc-${a.value.id}`,xero_sync:{xero_invoice_id:a.value.xero_sync.xero_invoice_id,sync_status:a.value.xero_sync.sync_status}};await F(v),m.notify({type:"positive",message:t("printInvoiceSuccess"),position:"top"})}catch(v){console.error("Print invoice error:",v),m.notify({type:"negative",message:t("printInvoiceError"),position:"top"})}finally{p.value=!1}},ye=()=>{D.value=!0},be=()=>{var v;if(!a.value)return"";const s=a.value.billing_email,r=(v=I.value)==null?void 0:v.default_email;return t(s?"emailInput.usingCustomerEmail":r?"emailInput.usingDefaultEmail":"emailInput.noDefaultEmail")},xe=async s=>{var r,v;if(!!((v=(r=a.value)==null?void 0:r.xero_sync)!=null&&v.xero_invoice_id)){if(a.value.xero_sync.sync_status!=="success"){m.notify({type:"warning",message:t("sendEmailNotSynced"),position:"top"}),D.value=!1;return}try{p.value=!0,await ae.sendInvoiceEmail(a.value.xero_sync.xero_invoice_id,s),m.notify({type:"positive",message:t("sendEmailSuccess"),position:"top"}),D.value=!1}catch(h){console.error("Send email error:",h);let x=t("sendEmailError");if(h instanceof Error)if(h.message.includes("daily email limit")||h.message.includes("Daily Email Rate Limit")){x=t("sendEmailRateLimitError"),m.dialog({title:t("sendEmailRateLimitTitle"),message:t("sendEmailRateLimitMessage"),ok:{label:t("understood"),color:"primary"},persistent:!1}),D.value=!1;return}else h.message.includes("invalid email")||h.message.includes("Invalid email")?x=t("sendEmailInvalidEmailError"):h.message.includes("manually from Xero")&&(x=t("sendEmailManualError"));m.notify({type:"negative",message:x,position:"top",timeout:5e3})}finally{p.value=!1}}};return(s,r)=>(y(),O(re,{modelValue:E.value,"onUpdate:modelValue":r[6]||(r[6]=v=>E.value=v),class:"card-dialog","no-refocus":""},{default:l(()=>{var v,h;return[e(oe,{class:"column"},{default:l(()=>[e(T,{class:"col-1 q-py-none"},{default:l(()=>[$("div",We,[$("div",Be,n(c(t)("orderDetail")),1),e(he),e(R,{type:"button",icon:"close",flat:"",round:"",dense:"",onClick:r[0]||(r[0]=x=>E.value=!1)})])]),_:1}),e(T,{class:"col-10 text-h6 q-py-sm"},{default:l(()=>[e(Re,{class:"full-height"},{default:l(()=>{var x,W,N,Y,j,G,J;return[$("div",Te,[e(f,{class:"col-12"},{default:l(()=>[e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("orderNo")),1)]),_:1}),e(o,null,{default:l(()=>{var i;return[u(n((i=a.value)==null?void 0:i.id),1)]}),_:1})]),_:1}),e(f,{class:"col-12 col-md-6"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:"event",size:"sm"})]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("orderDate")),1)]),_:1}),e(o,null,{default:l(()=>{var i;return[u(n(c(Me)((i=a.value)==null?void 0:i.date_created,"YYYY-MM-DD HH:mm")),1)]}),_:1})]),_:1}),e(f,{class:"col-12 col-md-6"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:"person",size:"sm"})]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("customer.label")),1)]),_:1}),e(o,null,{default:l(()=>{var i,d;return[u(n((i=a.value)==null?void 0:i.billing_first_name)+" "+n((d=a.value)==null?void 0:d.billing_last_name),1)]}),_:1})]),_:1}),e(f,{class:"col-12"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:"email",size:"sm"})]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("email.label")),1)]),_:1}),e(o,null,{default:l(()=>{var i;return[u(n((i=a.value)==null?void 0:i.billing_email),1)]}),_:1})]),_:1}),e(f,{class:"col-12"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:"phone",size:"sm"})]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("phone")),1)]),_:1}),e(o,null,{default:l(()=>{var i;return[u(n((i=a.value)==null?void 0:i.billing_phone),1)]}),_:1})]),_:1}),e(f,{class:"col-12"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:"check_circle",size:"sm"})]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("status")),1)]),_:1}),e(o,null,{default:l(()=>[e(Qe,{modelValue:C.value,"onUpdate:modelValue":[r[1]||(r[1]=i=>C.value=i),ie],options:Q.value,"option-value":"value","option-label":"label","emit-value":"","map-options":"",outlined:"",dense:"",loading:p.value},null,8,["modelValue","options","loading"])]),_:1})]),_:1}),H.value?(y(),O(f,{key:0,class:"col-12"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:de(),size:"sm",color:me()},null,8,["name","color"])]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>{var i,d,_,X;return[$("div",Fe,[u(n(c(t)("xero.syncStatus"))+" ",1),((d=(i=a.value)==null?void 0:i.xero_sync)==null?void 0:d.sync_status)!=="syncing"?(y(),O(R,{key:0,flat:"",dense:"",onClick:pe,icon:"sync",size:"sm",class:"q-ml-xs",loading:L.value,disable:((X=(_=a.value)==null?void 0:_.xero_sync)==null?void 0:X.sync_status)==="success"},null,8,["loading","disable"])):q("",!0)])]}),_:1}),e(o,null,{default:l(()=>{var i,d;return[$("div",null,[u(n(fe())+" ",1),(d=(i=a.value)==null?void 0:i.xero_sync)!=null&&d.xero_invoice_no?(y(),ee("div",He," Invoice: "+n(a.value.xero_sync.xero_invoice_no),1)):q("",!0)])]}),_:1})]),_:1})):((x=a.value)==null?void 0:x.status)==="wc-cancelled"&&((N=(W=a.value)==null?void 0:W.xero_sync)==null?void 0:N.xero_invoice_no)?(y(),O(f,{key:1,class:"col-12"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:"warning",size:"sm"})]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("xero.voidedInvoice")),1)]),_:1}),e(o,null,{default:l(()=>{var i,d;return[u(n((d=(i=a.value)==null?void 0:i.xero_sync)==null?void 0:d.xero_invoice_no),1)]}),_:1})]),_:1})):q("",!0),e(f,{class:"col-12 col-md-6"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:"payment",size:"sm"})]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("payment.label")),1)]),_:1}),e(o,null,{default:l(()=>{var i;return[u(n((i=a.value)==null?void 0:i.payment_method_title),1)]}),_:1})]),_:1}),e(f,{class:"col-12 col-md-6"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:"attach_money",size:"sm"})]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("total")),1)]),_:1}),e(o,null,{default:l(()=>{var i;return[u(" AU$ "+n(c(B)((i=a.value)==null?void 0:i.total,2)),1)]}),_:1})]),_:1}),e(f,{class:"col-12"},{default:l(()=>[e(o,null,{default:l(()=>[e(qe,{color:"black",size:"2px"})]),_:1})]),_:1}),e(f,{class:"col-12"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:"person",size:"sm"})]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("receiver")),1)]),_:1}),e(o,null,{default:l(()=>{var i,d;return[u(n((i=a.value)==null?void 0:i.shipping_first_name)+" "+n((d=a.value)==null?void 0:d.shipping_last_name),1)]}),_:1})]),_:1}),((Y=a.value)==null?void 0:Y.shipping_method)||((j=a.value)==null?void 0:j.shipping_method_title)?(y(),O(f,{key:2,class:"col-12"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:"local_shipping",size:"sm"})]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("shippingMethod")),1)]),_:1}),e(o,null,{default:l(()=>{var i,d;return[u(n(c(Xe)((i=a.value)==null?void 0:i.shipping_method,(d=a.value)==null?void 0:d.shipping_method_title)),1)]}),_:1})]),_:1})):q("",!0),e(f,{class:"col-12"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:"location_on",size:"sm"})]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("address")),1)]),_:1}),e(o,null,{default:l(()=>{var i,d,_,X,K;return[u(n((i=a.value)==null?void 0:i.shipping_address_1)+", "+n((d=a.value)==null?void 0:d.shipping_city)+", "+n((_=a.value)==null?void 0:_.shipping_state)+", "+n((X=a.value)==null?void 0:X.shipping_postcode)+", "+n((K=a.value)==null?void 0:K.shipping_country),1)]}),_:1})]),_:1}),((G=a.value)==null?void 0:G.status)==="wc-cancelled"&&((J=a.value)==null?void 0:J.customer_note)?(y(),O(f,{key:3,class:"col-12"},{default:l(()=>[e(o,{side:""},{default:l(()=>[e(w,{name:"cancel",size:"sm",color:"red"})]),_:1}),e(o,{class:"text-bold q-mr-sm",side:""},{default:l(()=>[u(n(c(t)("wcOrder.cancel.reasonLabel")),1)]),_:1}),e(o,null,{default:l(()=>[$("span",Ye,n(a.value.customer_note),1)]),_:1})]),_:1})):q("",!0)]),e($e,{bordered:"",separator:""},{default:l(()=>{var i,d;return[e(f,{class:"bg-grey-3"},{default:l(()=>[e(o,{class:"text-bold"},{default:l(()=>[u(n(c(t)("product.label")),1)]),_:1}),e(o,{class:"text-bold",side:""},{default:l(()=>[u(n(c(t)("price")),1)]),_:1})]),_:1}),(y(!0),ee(De,null,Se((i=a.value)==null?void 0:i.line_items,_=>(y(),O(f,{key:_.name},{default:l(()=>[e(o,null,{default:l(()=>[u(n(_.name),1)]),_:2},1024),e(o,{class:"text-subtitle1",side:""},{default:l(()=>[u(" x "+n(_.quantity),1)]),_:2},1024),e(o,{class:"text-bold",side:""},{default:l(()=>[u(" AU$ "+n(c(B)(_.total,2)),1)]),_:2},1024)]),_:2},1024))),128)),(d=a.value)!=null&&d.shipping_total?(y(),O(f,{key:0},{default:l(()=>[e(o,null,{default:l(()=>[u(n(c(t)("shippingFee")),1)]),_:1}),e(o,{class:"text-bold",side:""},{default:l(()=>{var _;return[u(" AU$ "+n(c(B)((_=a.value)==null?void 0:_.shipping_total,2)),1)]}),_:1})]),_:1})):q("",!0),e(f,{class:"bg-grey-3"},{default:l(()=>[e(o,null,{default:l(()=>[e(le,{class:"text-bold"},{default:l(()=>[u(n(c(t)("total")),1)]),_:1})]),_:1}),e(o,{side:""},{default:l(()=>[e(le,{class:"text-bold"},{default:l(()=>{var _;return[u(" AU$ "+n(c(B)((_=a.value)==null?void 0:_.total,2)),1)]}),_:1})]),_:1})]),_:1})]}),_:1})]}),_:1})]),_:1}),e(ne,{align:"between",class:"col-1 bg-grey-2 q-pa-sm"},{default:l(()=>{var x;return[$("div",je,[((x=a.value)==null?void 0:x.status)!="wc-cancelled"?(y(),O(R,{key:0,color:"red",icon:"delete",label:c(t)("wpOrder.actions.cancel"),"no-caps":"",onClick:r[2]||(r[2]=W=>{var N;return(N=U.value)==null?void 0:N.openDialog()}),loading:p.value},null,8,["label","loading"])):q("",!0),ve.value?(y(),O(R,{key:1,color:"primary",icon:"print",label:c(t)("printInvoice"),onClick:ge,loading:p.value},null,8,["label","loading"])):q("",!0),_e.value?(y(),O(R,{key:2,color:"secondary",icon:"email",label:c(t)("sendEmail"),onClick:ye,loading:p.value},null,8,["label","loading"])):q("",!0)]),e(R,{label:c(t)("close"),color:"grey",onClick:r[3]||(r[3]=W=>E.value=!1)},null,8,["label"])]}),_:1})]),_:1}),e(Ae,{ref_key:"orderCancelDialog",ref:U,"order-id":k.orderID,onOrderCancelled:ue},null,8,["order-id"]),e(Le,{modelValue:D.value,"onUpdate:modelValue":r[4]||(r[4]=x=>D.value=x),"default-email":((v=I.value)==null?void 0:v.default_email)||"","customer-email":((h=a.value)==null?void 0:h.billing_email)||"",loading:p.value,"hint-message":be(),onConfirm:xe,onCancel:r[5]||(r[5]=x=>D.value=!1)},null,8,["modelValue","default-email","customer-email","loading","hint-message"])]}),_:1},8,["modelValue"]))}});export{ul as _,Xe as g};
