package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type UserGroupController struct {
	userGroupService service.UserGroupService
}

func NewUserGroupController(r *gin.RouterGroup, userGroupService service.UserGroupService) {
	userGroupController := UserGroupController{userGroupService}

	v1 := r.Group("/v1/user-groups")
	{
		v1.GET("", userGroupController.FetchHandler)
		v1.GET("/:id", userGroupController.GetByIDHandler)
		v1.POST("", userGroupController.CreateHandler)
		v1.PUT("/:id", userGroupController.UpdateHandler)
		v1.DELETE("/:id", userGroupController.DeleteHandler)
	}
}

func (ctr *UserGroupController) FetchHandler(c *gin.Context) {
	userGroups, err := ctr.userGroupService.Fetch(c)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to fetch user groups")
		return
	}

	utils.HandleSuccess(c, userGroups)
}

func (ctr *UserGroupController) GetByIDHandler(c *gin.Context) {
	id := c.Param("id")

	userGroup, err := ctr.userGroupService.GetByID(c, utils.ParseInt64(id))
	if err != nil {
		utils.HandleError(c, http.StatusNotFound, err, "User group not found")
		return
	}

	utils.HandleSuccess(c, userGroup)
}

func (ctr *UserGroupController) CreateHandler(c *gin.Context) {
	var userGroup domain.UserGroup
	if err := c.ShouldBind(&userGroup); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	err := ctr.userGroupService.Create(c, &userGroup)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to create user group")
		return
	}

	utils.HandleSuccess(c, userGroup)
}

func (ctr *UserGroupController) UpdateHandler(c *gin.Context) {
	id := c.Param("id")

	var payload domain.UserGroupUpdatePayload
	if err := c.ShouldBind(&payload); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	userUUID, _ := c.Get("user_uuid")
	payload.UpdatedBy = userUUID.(string)

	payload.UserGroup.ID = utils.ParseInt64(id)
	err := ctr.userGroupService.Update(c, &payload)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update user group")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *UserGroupController) DeleteHandler(c *gin.Context) {
	id := c.Param("id")

	err := ctr.userGroupService.Delete(c, utils.ParseInt64(id))
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to delete user group")
		return
	}

	utils.HandleSuccess(c, nil)
}
