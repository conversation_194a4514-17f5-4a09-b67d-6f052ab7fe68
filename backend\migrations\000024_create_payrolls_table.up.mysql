CREATE TABLE `payrolls` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `uuid` VARCHAR(36) NOT NULL,
    `user_id` BIGINT NOT NULL,
    `payroll_period` CHAR(7) NOT NULL,
    `pay_date` DATE  NULL DEFAULT NULL,
    `salary_type` TINYINT(1) NOT NULL DEFAULT 1,
    `basic_salary` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `hourly_salary` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `work_days` INT NOT NULL DEFAULT 0,
    `work_hours` INT NOT NULL DEFAULT 0,
    `overtime_hours` INT NOT NULL DEFAULT 0,
    `gross_salary` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `total_deductions` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `net_salary` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `status` TINYINT(1) NOT NULL DEFAULT 1,
    `notes` TEXT NOT NULL,
    `created_by_id` BIGINT NOT NULL,
    `updated_by_id` BIGINT NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `payrolls_uuid_unique` (`uuid`),
    UNIQUE KEY `payrolls_user_id_payroll_period_unique` (`user_id`, `payroll_period`),
    INDEX `payrolls_payroll_period_index` (`payroll_period`),
    CONSTRAINT `fk_payrolls_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_payrolls_created_by` FOREIGN KEY (`created_by_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_payrolls_updated_by` FOREIGN KEY (`updated_by_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `payroll_details` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `uuid` VARCHAR(36) NOT NULL,
    `payroll_id` BIGINT NOT NULL,
    `salary_item_id` BIGINT NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `payroll_details_uuid_unique` (`uuid`),
    UNIQUE KEY `payroll_details_payroll_id_salary_item_id_unique` (`payroll_id`, `salary_item_id`),
    INDEX `payroll_details_payroll_id_index` (`payroll_id`),
    CONSTRAINT `fk_payroll_details_payroll_id` FOREIGN KEY (`payroll_id`) REFERENCES `payrolls` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_payroll_details_salary_item_id` FOREIGN KEY (`salary_item_id`) REFERENCES `salary_items` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;