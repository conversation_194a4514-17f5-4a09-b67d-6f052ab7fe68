package service

import (
	"context"
	"cx/domain"
	"cx/repository"
	"errors"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AuthService interface {
	Login(c *gin.Context, request *domain.LoginRequest) (*domain.LoginResponse, error)
	Logout(ctx context.Context, claims *TokenClaims) error
	VerifyUser(ctx context.Context, request *domain.LoginRequest) (*domain.UserInfo, error)
	ParseToken(tokenString string) (*TokenClaims, bool, error)
	ValidateToken(c context.Context, claims *TokenClaims) (bool, error)
	RefreshToken(ctx context.Context, payload *domain.RefreshTokenPayload) (*domain.TokenPair, error)
}

type authService struct {
	db        *gorm.DB
	tokenRepo repository.TokenRepository
	userRepo  repository.UserRepository
}

func NewAuthService(db *gorm.DB, tokenRepo repository.TokenRepository, userRepo repository.UserRepository) AuthService {
	return &authService{db, tokenRepo, userRepo}
}

func (s *authService) Login(c *gin.Context, request *domain.LoginRequest) (*domain.LoginResponse, error) {
	userService := NewUserService(s.db, s.userRepo)
	tokenService := NewTokenService(s.db, s.tokenRepo)

	user, err := userService.Verify(c, request)
	if err != nil {
		return nil, err
	}

	tokenPair, err := tokenService.GenerateTokenPair(c, user, c.Request.UserAgent(), c.ClientIP())
	if err != nil {
		return nil, err
	}

	response := domain.LoginResponse{
		TokenPair: *tokenPair,
		User: domain.UserInfo{
			UUID: user.UUID,
			Name: user.Name,
		},
	}

	return &response, nil
}

func (s *authService) Logout(ctx context.Context, claims *TokenClaims) error {
	tokenService := NewTokenService(s.db, s.tokenRepo)
	if err := tokenService.RevokeTokenByJwtID(ctx, claims.ID); err != nil {
		return err
	}

	return nil
}

func (s *authService) VerifyUser(ctx context.Context, request *domain.LoginRequest) (*domain.UserInfo, error) {
	userService := NewUserService(s.db, s.userRepo)
	user, err := userService.Verify(ctx, request)
	if err != nil {
		return nil, err
	}

	return &domain.UserInfo{
		UUID: user.UUID,
		Name: user.Name,
	}, nil
}

func (s *authService) ParseToken(tokenString string) (*TokenClaims, bool, error) {
	tokenService := NewTokenService(s.db, s.tokenRepo)
	claims, isExpired, err := tokenService.ParseToken(tokenString)
	if err != nil {
		return nil, isExpired, err
	}

	return claims, isExpired, nil
}

// 驗證令牌是否有效，包括令牌是否過期、用戶是否存在、用戶是否被禁用、令牌與用戶是否匹配
func (s *authService) ValidateToken(ctx context.Context, claims *TokenClaims) (bool, error) {
	tokenService := NewTokenService(s.db, s.tokenRepo)
	token, err := tokenService.GetTokenByJwtID(ctx, claims.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, domain.ErrTokenRevoked
		}
		return false, err
	}

	// 檢查令牌是否過期
	if time.Now().After(claims.ExpiresAt.Time) {
		return false, domain.ErrTokenExpired
	}

	// 檢查用戶是否存在
	userService := NewUserService(s.db, s.userRepo)
	user, err := userService.GetByUUID(ctx, claims.UserUUID)
	if err != nil {
		return false, err
	}

	// 檢查用戶是否被禁用
	if !user.IsActive {
		return false, ErrUserDisabled
	}

	// 檢查用戶 ID 是否匹配
	if token.UserID != user.ID {
		return false, domain.ErrTokenInvalid
	}

	// 更新最後使用時間
	tokenService.UpdateLastUsedAt(ctx, claims.ID)

	return true, nil
}

func (s *authService) RefreshToken(ctx context.Context, payload *domain.RefreshTokenPayload) (*domain.TokenPair, error) {
	// 在資料庫中查找刷新令牌
	tokenService := NewTokenService(s.db, s.tokenRepo)
	refreshTokenRecord, err := tokenService.GetRefreshToken(ctx, payload.RefreshToken)
	if err != nil {
		return nil, domain.ErrRefreshInvalid
	}

	// 檢查更新令牌是否過期
	if time.Now().After(refreshTokenRecord.ExpiresAt) {
		return nil, domain.ErrTokenExpired
	}

	// 撤銷使用者舊的訪問令牌
	if err := tokenService.RevokeToken(ctx, refreshTokenRecord.AccessTokenID); err != nil {
		return nil, err
	}

	// 撤銷舊的刷新令牌
	if err := tokenService.RevokeRefreshToken(ctx, refreshTokenRecord.ID); err != nil {
		return nil, err
	}

	// 查找用戶信息
	userService := NewUserService(s.db, s.userRepo)
	user, err := userService.GetByID(ctx, refreshTokenRecord.UserID)
	if err != nil {
		return nil, err
	}

	// 生成新的令牌對
	newTokenPair, err := tokenService.GenerateTokenPair(ctx, user, payload.UserAgent, payload.ClientIP)
	if err != nil {
		return nil, err
	}

	return newTokenPair, nil
}
