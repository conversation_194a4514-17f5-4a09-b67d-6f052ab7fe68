// app global css in SCSS form
html {
  touch-action: pan-x pan-y; /* 禁用雙擊縮放 */
  user-select: none; /* 禁用文字選取 */
  font-size: 20px;
}

.q-tabs {
  .q-tab__label {
    font-size: 0.9rem;
  }
}

.q-header {
  background-color: #789dbc;
  color: #fef9f2;
}

.q-table {
  thead th {
    font-size: 0.8rem;
  }

  tbody td {
    font-size: 0.8rem;
  }
}

.full-dialog {
  .q-card {
    max-width: 100% !important;
    max-height: 100% !important;
    width: 100%;
    height: 100%;
  }
}

.card-dialog {
  .q-card {
    width: 800px;
    max-width: 100%;

    max-height: 100% !important;
    height: 100%;
  }
}

.q-card {
  background-color: $light;
  color: #5d5d5d;
}

.bg-main {
  background: $light;
}

.bg-cream {
  background: $cream;
}

.bg-origin {
  background-color: $origin;
}

.bg-submit {
  background-color: $submit;
}

.bg-create {
  background-color: $create;
}

.text-create {
  color: $create;
}

.bg-cancel {
  background-color: $cancel;
}

.text-cancel {
  color: $cancel;
}

.text-main {
  color: #5d5d5d;
}

.text-toggle {
  color: #5bd6c3;
}

.bg-cream {
  background-color: $cream;
}

.sticky-scroll-table {
  /* bg color is important for th; just specify one */
  .q-table__top,
  .q-table__bottom {
    background-color: #fff;
  }

  thead tr th {
    position: sticky;
    z-index: 1;
  }

  /* this will be the loading indicator */
  // thead tr:last-child th {
  /* height of all previous header rows */
  // top: 48px;
  // }

  thead tr:first-child th {
    top: 0;
  }
}

.original-price {
  text-decoration: line-through;
}

@media (min-width: 1920px) {
  .text-xl-right {
    text-align: right;
  }
  .text-xl-left {
    text-align: left;
  }
  .text-xl-center {
    text-align: center;
  }
  .text-xl-justify {
    text-align: justify;
  }
}

@media (min-width: 1440px) {
  .text-lg-right {
    text-align: right;
  }
  .text-lg-left {
    text-align: left;
  }
  .text-lg-center {
    text-align: center;
  }
  .text-lg-justify {
    text-align: justify;
  }
}

@media (min-width: 1024px) {
  .text-md-right {
    text-align: right;
  }
  .text-md-left {
    text-align: left;
  }
  .text-md-center {
    text-align: center;
  }
  .text-md-justify {
    text-align: justify;
  }
}

@media (min-width: 600px) {
  .text-sm-right {
    text-align: right;
  }
  .text-sm-left {
    text-align: left;
  }
  .text-sm-center {
    text-align: center;
  }
  .text-sm-justify {
    text-align: justify;
  }
}

@media (max-width: 768px) {
  .card-dialog {
    .q-card {
      width: 100%;
      height: 100%;
    }
  }
}
