package domain

import (
	"time"

	"gorm.io/gorm"
)

type ProductCategory struct {
	ID        int64          `gorm:"primaryKey" json:"id"`
	WcID      int64          `json:"-"`
	Name      string         `json:"name"`
	CreatedAt time.Time      `gorm:"<-:create" json:"-"`
	UpdatedAt time.Time      `json:"-"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	Image ProductCategoryImage `gorm:"foreignKey:ProductCategoryID" json:"image"`
}

type ProductCategoryImage struct {
	ID                int64     `gorm:"primaryKey" json:"-"`
	WcID              int64     `json:"-"`
	UUID              string    `gorm:"<-:create" json:"uuid"`
	ProductCategoryID int64     `json:"-"`
	ImagePath         string    `json:"image_path"`
	CreatedAt         time.Time `gorm:"<-:create" json:"-"`
	UpdatedAt         time.Time `json:"-"`
}
