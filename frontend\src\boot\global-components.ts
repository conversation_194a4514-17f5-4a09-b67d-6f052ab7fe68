import { boot } from 'quasar/wrappers';
import NumberInput from '@/components/NumberInput.vue';
import NumberPad from '@/components/NumberPad.vue';
import ItemQuantityInput from '@/components/ItemQuantityInput.vue';
import TablePagination from '@/components/TablePagination.vue';
import QrcodeScanner from '@/components/QrcodeScanner.vue';
import UserVerifyForm from '@/components/UserVerifyForm.vue';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';

export default boot(({ app }) => {
  // 全域註冊元件
  app.component('NumberInput', NumberInput);
  app.component('NumberPad', NumberPad);
  app.component('ItemQuantityInput', ItemQuantityInput);

  app.component('TablePagination', TablePagination);
  app.component('QrcodeScanner', QrcodeScanner);
  app.component('UserVerifyForm', UserVerifyForm);
  app.component('LanguageSwitcher', LanguageSwitcher);
});
