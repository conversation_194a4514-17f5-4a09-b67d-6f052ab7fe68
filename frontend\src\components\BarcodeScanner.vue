<template>
  <div class="barcode-scanner" style="width: 15rem; max-width: 80%">
    <q-input
      v-model="manualInput"
      :label="t('barcode')"
      :placeholder="statusText"
      @keydown.enter="handleInputSubmit"
      dense
      clearable
      :loading="!isReady"
    >
      <template v-slot:prepend>
        <q-icon name="sym_o_barcode_scanner" />
      </template>
      <template v-slot:append>
        <q-btn
          round
          flat
          icon="search"
          @click="handleInputSubmit"
          :disable="!manualInput || !isReady"
        />
      </template>
    </q-input>

    <q-inner-loading :showing="!isReady">
      <q-spinner-dots size="40px" color="primary" />
    </q-inner-loading>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useBarcodeScanner } from '@/composables/useBarcodeScanner';
import { Product } from '@/api/product';

const { t } = useI18n();

const props = defineProps({
  products: {
    type: Array as () => Product[],
    required: true,
  },
  barcodeField: {
    type: String,
    default: 'barcode',
  },
});

const emit = defineEmits([
  'update:modelValue',
  'barcode-detected',
  'product-found',
  'product-not-found',
]);

const { isReady, processBarcode, findProductByBarcode } = useBarcodeScanner({
  timeout: 200,
  barcodeField: props.barcodeField,
});

const manualInput = ref('');

const statusText = computed(() => {
  return isReady.value ? '' : t('barcodeScanner.scanning');
});

const handleInputSubmit = () => {
  if (manualInput.value && isReady.value) {
    processBarcode(manualInput.value);
    manualInput.value = '';
  }
};

const handleBarcodeScanned = (event: CustomEvent) => {
  const barcode = event.detail.barcode;

  // 發送條碼偵測事件
  emit('barcode-detected', barcode);

  // 查找對應的商品
  const product = findProductByBarcode(props.products, barcode);

  if (product) {
    emit('product-found', product);
  } else {
    emit('product-not-found', barcode);
  }
};

onMounted(() => {
  window.addEventListener(
    'barcode-scanned',
    handleBarcodeScanned as EventListener
  );
});

onUnmounted(() => {
  window.removeEventListener(
    'barcode-scanned',
    handleBarcodeScanned as EventListener
  );
});

watch(
  () => manualInput,
  (newVal) => {
    emit('update:modelValue', newVal);
  }
);
</script>

<style lang="scss" scoped>
.barcode-scanner {
  position: relative;

  .barcode-status {
    font-size: 1.2rem;
    opacity: 0.9;
    transition: background-color 0.3s ease;
  }
}
</style>
