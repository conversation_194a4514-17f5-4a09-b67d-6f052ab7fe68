package utils

import (
	"cx/domain"
	"fmt"
	"net/http"
	"runtime"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

func HandleSuccess(c *gin.Context, data interface{}) {
	res := domain.HttpResponse{
		Status:  http.StatusOK,
		Message: "success",
		Result:  data,
	}
	c.<PERSON>(http.StatusOK, res)
}

func HandleError(c *gin.Context, status int, err error, message string) {
	// 構建詳細的錯誤訊息
	var detailedError string

	if err != nil {
		// 創建更詳細的錯誤訊息
		detailedError = buildDetailedError(c, status, err, message)
	} else {
		detailedError = message
	}

	res := domain.HttpResponse{
		Status:  status,
		Message: message,
		Error:   detailedError, // 使用詳細的錯誤訊息
	}

	// 記錄詳細的錯誤日誌
	logDetailedError(c, status, err, message, detailedError)

	c.JSON(status, res)
}

// buildDetailedError 構建詳細的錯誤訊息
func buildDetailedError(c *gin.Context, status int, err error, message string) string {
	var details []string

	// 添加基本錯誤訊息
	details = append(details, message)

	// 添加原始錯誤
	if err != nil {
		details = append(details, "詳細錯誤: "+err.Error())
	}

	// 添加 HTTP 狀態碼說明
	statusText := getStatusText(status)
	if statusText != "" {
		details = append(details, statusText)
	}

	// 添加請求信息
	if c != nil {
		requestInfo := fmt.Sprintf("請求: %s %s", c.Request.Method, c.Request.URL.Path)
		details = append(details, requestInfo)

		// 添加查詢參數（如果有）
		if c.Request.URL.RawQuery != "" {
			details = append(details, "參數: "+c.Request.URL.RawQuery)
		}
	}

	return strings.Join(details, " | ")
}

// getStatusText 獲取 HTTP 狀態碼的中文說明
func getStatusText(status int) string {
	switch status {
	case 400:
		return "400 (請求錯誤)"
	case 401:
		return "401 (未授權)"
	case 403:
		return "403 (禁止訪問)"
	case 404:
		return "404 (資源不存在)"
	case 409:
		return "409 (資源衝突)"
	case 422:
		return "422 (請求格式錯誤)"
	case 500:
		return "500 (伺服器內部錯誤)"
	case 502:
		return "502 (網關錯誤)"
	case 503:
		return "503 (服務不可用)"
	default:
		return fmt.Sprintf("%d", status)
	}
}

// logDetailedError 記錄詳細的錯誤日誌
func logDetailedError(c *gin.Context, status int, err error, message string, detailedError string) {
	// 獲取調用堆棧信息
	_, file, line, ok := runtime.Caller(2)
	var caller string
	if ok {
		caller = fmt.Sprintf("%s:%d", file, line)
	}

	// 構建日誌訊息
	logMsg := fmt.Sprintf("[ERROR] %s | Time: %s | Status: %d | Message: %s | Detail: %s",
		caller,
		time.Now().Format("2006-01-02 15:04:05"),
		status,
		message,
		detailedError,
	)

	// 如果有原始錯誤，添加到日誌
	if err != nil {
		logMsg += " | Original Error: " + err.Error()
	}

	// 添加請求信息到日誌
	if c != nil {
		logMsg += fmt.Sprintf(" | Request: %s %s", c.Request.Method, c.Request.URL.String())

		// 添加用戶代理
		if userAgent := c.GetHeader("User-Agent"); userAgent != "" {
			logMsg += " | User-Agent: " + userAgent
		}

		// 添加客戶端 IP
		if clientIP := c.ClientIP(); clientIP != "" {
			logMsg += " | Client-IP: " + clientIP
		}
	}

	// 輸出到控制台（也可以寫入日誌文件）
	fmt.Println(logMsg)

	// 調用原有的 ErrorLog 函數
	res := domain.HttpResponse{
		Status:  status,
		Message: message,
		Error:   detailedError,
	}
	ErrorLog(res)
}

// HandleErrorWithDetails 處理錯誤並提供額外的上下文信息
func HandleErrorWithDetails(c *gin.Context, status int, err error, message string, details map[string]interface{}) {
	var detailedError string

	if err != nil {
		detailedError = buildDetailedErrorWithContext(c, status, err, message, details)
	} else {
		detailedError = message
	}

	res := domain.HttpResponse{
		Status:  status,
		Message: message,
		Error:   detailedError,
	}

	// 記錄詳細的錯誤日誌
	logDetailedError(c, status, err, message, detailedError)

	c.JSON(status, res)
}

// buildDetailedErrorWithContext 構建包含上下文的詳細錯誤訊息
func buildDetailedErrorWithContext(c *gin.Context, status int, err error, message string, details map[string]interface{}) string {
	var parts []string

	// 添加基本錯誤訊息
	parts = append(parts, message)

	// 添加原始錯誤
	if err != nil {
		parts = append(parts, "錯誤詳情: "+err.Error())
	}

	// 添加狀態碼
	parts = append(parts, getStatusText(status))

	// 添加請求信息
	if c != nil {
		parts = append(parts, fmt.Sprintf("請求: %s %s", c.Request.Method, c.Request.URL.Path))
	}

	// 添加額外的上下文信息
	if details != nil && len(details) > 0 {
		var contextParts []string
		for key, value := range details {
			contextParts = append(contextParts, fmt.Sprintf("%s: %v", key, value))
		}
		if len(contextParts) > 0 {
			parts = append(parts, "上下文: "+strings.Join(contextParts, ", "))
		}
	}

	return strings.Join(parts, " | ")
}
