import{d as z,u as B,aA as C,r as d,c as E,s as N,o as $,k as S,f as s,i as r,b as o,y as _,t as f,h,m as p,p as k,q as R,Q as m,z as j}from"./index.b4716878.js";import{Q as x}from"./QBanner.dd8f3c3c.js";import{Q as A}from"./QForm.b737edd8.js";import{Q as D}from"./QPage.cc993543.js";import{u as F}from"./use-quasar.59b22ad6.js";import{X as u}from"./xero.1823d800.js";import{f as O}from"./date.6d29930c.js";const P={class:"row justify-center"},H={class:"col-12 col-md-8 col-lg-6"},M={class:"text-h6 q-mb-md"},L={class:"row q-gutter-sm"},ae=z({__name:"XeroSetupPage",setup(G){const{t:n}=B(),i=F(),l=C({client_id:"",client_secret:"",redirect_uri:window.location.origin+"/xero/redirect",scopes:"accounting.transactions accounting.contacts",default_email:""}),c=C({connected:!1,tenant_name:"",tenant_id:"",expires_at:""}),g=d(!1),y=d(!1),v=d(!1),b=d(!1),Q=d(!1),q=E(()=>l.client_id&&l.client_secret&&l.redirect_uri),I=async()=>{const e=await u.getConfig();e.result&&Object.assign(l,e.result)},w=async()=>{const e=await u.getConnectionStatus();Object.assign(c,e.result)},V=async()=>{g.value=!0;try{await u.saveConfig(l),i.notify({position:"top",type:"positive",message:n("success")})}finally{g.value=!1}},U=async()=>{y.value=!0;try{await V();const e=await u.getAuthURL(),{auth_url:a,state:t}=e.result;localStorage.setItem("xero_oauth_state",t),localStorage.getItem("xero_oauth_state")!==t&&(console.error("Failed to save state to localStorage"),i.notify({position:"top",type:"warning",message:n("xero.setup.error.stateStorageFailed")})),window.location.href=a}catch(e){console.error("Connect to Xero error:",e),i.notify({position:"top",type:"negative",message:(e==null?void 0:e.message)||n("failed")})}finally{y.value=!1}},X=async()=>{v.value=!0;try{await u.refreshToken(),await w(),i.notify({position:"top",type:"positive",message:n("success")})}finally{v.value=!1}},T=async()=>{i.dialog({title:n("xero.setup.dialog.disconnectTitle"),message:n("xero.setup.dialog.disconnectMessage"),cancel:!0,persistent:!0}).onOk(async()=>{b.value=!0;try{await u.disconnect(),await w(),i.notify({position:"top",type:"positive",message:n("success")})}finally{b.value=!1}})};return N(()=>{I(),w()}),(e,a)=>($(),S(D,{class:"q-pa-md"},{default:s(()=>[r("div",P,[r("div",H,[o(j,{class:"q-pa-md"},{default:s(()=>[o(_,null,{default:s(()=>[r("div",M,f(e.$t("xero.setup.title")),1),c.connected?($(),S(x,{key:0,class:"bg-positive text-white q-mb-md",rounded:""},{avatar:s(()=>[o(h,{name:"check_circle",color:"white"})]),action:s(()=>[o(p,{flat:"",color:"white",label:e.$t("xero.setup.connectionStatus.disconnect"),onClick:T,loading:b.value},null,8,["label","loading"]),o(p,{flat:"",color:"white",label:e.$t("xero.setup.connectionStatus.refreshToken"),onClick:X,loading:v.value},null,8,["label","loading"])]),default:s(()=>[k(" "+f(e.$t("xero.setup.connectionStatus.connected",{tenantName:c.tenant_name}))+" ",1),a[6]||(a[6]=r("br",null,null,-1)),r("small",null,f(e.$t("xero.setup.connectionStatus.tokenExpiry",{expiryDate:R(O)(c.expires_at)})),1)]),_:1})):($(),S(x,{key:1,class:"bg-warning text-dark q-mb-md",rounded:""},{avatar:s(()=>[o(h,{name:"warning",color:"orange"})]),default:s(()=>[k(" "+f(e.$t("xero.setup.connectionStatus.notConnected")),1)]),_:1}))]),_:1}),o(_,null,{default:s(()=>[o(A,{onSubmit:V,class:"q-gutter-md"},{default:s(()=>[o(m,{modelValue:l.client_id,"onUpdate:modelValue":a[0]||(a[0]=t=>l.client_id=t),filled:"",label:e.$t("xero.setup.form.clientId")+" *","lazy-rules":"",rules:[t=>t&&t.length>0||e.$t("xero.setup.validation.clientIdRequired")]},null,8,["modelValue","label","rules"]),o(m,{modelValue:l.client_secret,"onUpdate:modelValue":a[1]||(a[1]=t=>l.client_secret=t),filled:"",type:"password",label:e.$t("xero.setup.form.clientSecret")+" *","lazy-rules":"",rules:[t=>t&&t.length>0||e.$t("xero.setup.validation.clientSecretRequired")]},null,8,["modelValue","label","rules"]),o(m,{modelValue:l.redirect_uri,"onUpdate:modelValue":a[2]||(a[2]=t=>l.redirect_uri=t),filled:"",label:e.$t("xero.setup.form.redirectUri")+" *","lazy-rules":"",rules:[t=>t&&t.length>0||e.$t("xero.setup.validation.redirectUriRequired")]},null,8,["modelValue","label","rules"]),o(m,{modelValue:l.scopes,"onUpdate:modelValue":a[4]||(a[4]=t=>l.scopes=t),filled:"",label:e.$t("xero.setup.form.scopes"),hint:e.$t("xero.setup.form.scopesHint"),placeholder:e.$t("xero.setup.form.scopesPlaceholder")},{append:s(()=>[o(p,{flat:"",round:"",dense:"",icon:"info",onClick:a[3]||(a[3]=t=>Q.value=!0)})]),_:1},8,["modelValue","label","hint","placeholder"]),o(m,{modelValue:l.default_email,"onUpdate:modelValue":a[5]||(a[5]=t=>l.default_email=t),filled:"",type:"email",label:e.$t("xero.setup.form.defaultEmail"),hint:e.$t("xero.setup.form.defaultEmailHint"),"lazy-rules":"",rules:[t=>!t||/.+@.+\..+/.test(t)||e.$t("xero.setup.validation.invalidEmail")]},{prepend:s(()=>[o(h,{name:"email"})]),_:1},8,["modelValue","label","hint","rules"]),r("div",L,[o(p,{label:e.$t("save"),type:"submit",color:"primary",loading:g.value},null,8,["label","loading"]),o(p,{label:e.$t("xero.setup.form.connectXero"),color:"secondary",disable:!q.value||c.connected,onClick:U,loading:y.value},null,8,["label","disable","loading"])])]),_:1})]),_:1})]),_:1})])])]),_:1}))}});export{ae as default};
