package repository

import (
	"context"
	"cx/domain"

	"gorm.io/gorm"
)

type UserGroupRepository interface {
	WithTx(tx *gorm.DB) UserGroupRepository

	Create(ctx context.Context, userGroup *domain.UserGroup) error
	Update(ctx context.Context, userGroup *domain.UserGroup) error
	Delete(ctx context.Context, id int64) error
	Fetch(ctx context.Context) ([]domain.UserGroup, error)
	GetByID(ctx context.Context, id int64) (*domain.UserGroup, error)
}

type userGroupRepository struct {
	db *gorm.DB
}

func NewUserGroupRepository(db *gorm.DB) UserGroupRepository {
	return &userGroupRepository{db}
}

func (r *userGroupRepository) WithTx(tx *gorm.DB) UserGroupRepository {
	return &userGroupRepository{tx}
}

func (r *userGroupRepository) Create(ctx context.Context, userGroup *domain.UserGroup) error {
	return r.db.WithContext(ctx).Create(userGroup).Error
}

func (r *userGroupRepository) Update(ctx context.Context, userGroup *domain.UserGroup) error {
	return r.db.WithContext(ctx).Model(userGroup).Updates(userGroup).Error
}

func (r *userGroupRepository) Delete(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Delete(&domain.UserGroup{}, id).Error
}

func (r *userGroupRepository) Fetch(ctx context.Context) ([]domain.UserGroup, error) {
	var userGroups []domain.UserGroup

	err := r.db.WithContext(ctx).Find(&userGroups).Error

	return userGroups, err
}

func (r *userGroupRepository) GetByID(ctx context.Context, id int64) (*domain.UserGroup, error) {
	var userGroup domain.UserGroup
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&userGroup).Error
	return &userGroup, err
}
