package utils

import (
	"cx/domain"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"
)

func ErrorLog(res domain.HttpResponse) {
	if res.Error == "" && res.Message == "" {
		return
	}

	logDir := "log"
	if err := os.<PERSON>(logDir, 0755); err != nil {
		log.Printf("failed to create log directory: %v\n", err)
	}

	logFileName := filepath.Join(logDir, fmt.Sprintf("error-%s.log", time.Now().Format("2006-01-02")))
	file, err := os.OpenFile(logFileName, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Printf("failed to open log file: %v\n", err)
		return
	}
	defer file.Close()

	log.SetOutput(file)
	log.Println(res.Error)
	fmt.Println(res.Error)
	if res.Message != "" {
		log.Println(res.Message)
		fmt.Println(res.Message)
	}
}
