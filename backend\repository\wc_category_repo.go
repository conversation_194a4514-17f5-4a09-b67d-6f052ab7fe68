package repository

import (
	"cx/domain"
	"fmt"

	"gorm.io/gorm"
)

type WcCategoryRepository interface {
	GetByID(id uint) (*domain.WcCategory, error)
	List() ([]domain.WcCategory, error)
	Create(category *domain.WcCategory) error
	Update(category *domain.WcCategory) error
	Delete(id uint) error

	UpdateImage(wcCategoryID int64, imageID int64) error
}

// 分類儲存庫實現
type wcCategoryRepository struct {
	db *gorm.DB
}

func NewWcCategoryRepository(db *gorm.DB) WcCategoryRepository {
	return &wcCategoryRepository{db: db}
}

func (r *wcCategoryRepository) GetByID(id uint) (*domain.WcCategory, error) {
	category := &domain.WcCategory{}

	query := `
        SELECT 
            t.term_id as id,
            t.name as name,
            t.slug as slug,
            tt.description as description,
            tt.parent as parent_id,
            tt.count as count
        FROM 
            wp_terms t
        JOIN 
            wp_term_taxonomy tt ON t.term_id = tt.term_id
        WHERE 
            t.term_id = ? AND tt.taxonomy = 'product_cat'
    `

	if err := r.db.Raw(query, id).Scan(category).Error; err != nil {
		return nil, err
	}

	if category.ID == 0 {
		return nil, fmt.Errorf("category not found")
	}

	return category, nil
}

func (r *wcCategoryRepository) List() ([]domain.WcCategory, error) {
	var categories []domain.WcCategory

	query := `
        SELECT 
            t.term_id as id,
            t.name as name,
            t.slug as slug,
            tt.description as description,
            tt.parent as parent_id,
            tt.count as count
        FROM 
            wp_terms t
        JOIN 
            wp_term_taxonomy tt ON t.term_id = tt.term_id
        WHERE 
            tt.taxonomy = 'product_cat'
        ORDER BY 
            name ASC
    `

	if err := r.db.Raw(query).Scan(&categories).Error; err != nil {
		return nil, err
	}

	return categories, nil
}

func (r *wcCategoryRepository) Create(category *domain.WcCategory) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 1. 插入到 wp_terms
		termInsertQuery := `
            INSERT INTO wp_terms (name, slug) VALUES (?, ?)
        `

		result := tx.Exec(termInsertQuery, category.Name, category.Slug)
		if result.Error != nil {
			return result.Error
		}

		// 獲取新插入分類的 ID
		var termID int64
		tx.Raw("SELECT LAST_INSERT_ID()").Scan(&termID)
		category.ID = termID

		// 2. 插入到 wp_term_taxonomy
		taxonomyInsertQuery := `
            INSERT INTO wp_term_taxonomy (term_id, taxonomy, description, parent, count) VALUES (?, 'product_cat', ?, ?, 0)
        `

		if err := tx.Exec(taxonomyInsertQuery, termID, category.Description, category.ParentID).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *wcCategoryRepository) Update(category *domain.WcCategory) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 1. 更新 wp_terms
		termUpdateQuery := `
            UPDATE wp_terms SET name = ?, slug = ? WHERE term_id = ?
        `

		result := tx.Exec(termUpdateQuery, category.Name, category.Slug, category.ID)
		if result.Error != nil {
			return result.Error
		}

		// 2. 更新 wp_term_taxonomy
		taxonomyUpdateQuery := `
            UPDATE wp_term_taxonomy 
            SET description = ?, parent = ? 
            WHERE term_id = ? AND taxonomy = 'product_cat'
        `

		if err := tx.Exec(taxonomyUpdateQuery, category.Description, category.ParentID, category.ID).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *wcCategoryRepository) Delete(id uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 1. 檢查是否有產品使用此分類
		var count int64
		tx.Raw(`
            SELECT COUNT(*) FROM wp_term_relationships tr
            JOIN wp_term_taxonomy tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
            WHERE tt.term_id = ? AND tt.taxonomy = 'product_cat'
        `, id).Scan(&count)

		if count > 0 {
			return fmt.Errorf("cannot delete category: %d products are using this category", count)
		}

		// 2. 獲取 term_taxonomy_id
		var termTaxonomyID uint
		if err := tx.Raw(
			"SELECT term_taxonomy_id FROM wp_term_taxonomy WHERE term_id = ? AND taxonomy = 'product_cat'",
			id,
		).Scan(&termTaxonomyID).Error; err != nil {
			return err
		}

		// 3. 刪除 wp_term_relationships 中的記錄
		if err := tx.Exec("DELETE FROM wp_term_relationships WHERE term_taxonomy_id = ?", termTaxonomyID).Error; err != nil {
			return err
		}

		// 4. 刪除 wp_term_taxonomy 中的記錄
		if err := tx.Exec("DELETE FROM wp_term_taxonomy WHERE term_taxonomy_id = ?", termTaxonomyID).Error; err != nil {
			return err
		}

		// 5. 刪除 wp_terms 中的記錄
		if err := tx.Exec("DELETE FROM wp_terms WHERE term_id = ?", id).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *wcCategoryRepository) UpdateImage(wcCategoryID int64, imageID int64) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 檢查是否已有縮圖元數據，若有則更新，若無則新增
		var termmeta domain.WpTermmeta
		result := tx.Where("term_id = ? AND meta_key = 'thumbnail_id'", wcCategoryID).First(&termmeta)

		if result.Error != nil && gorm.ErrRecordNotFound != result.Error {
			return result.Error
		}

		// 如果找到現有記錄，更新它
		if result.RowsAffected > 0 {
			termmeta.MetaValue = fmt.Sprintf("%d", imageID)
			if err := tx.Save(&termmeta).Error; err != nil {
				return err
			}
		} else {
			// 否則，創建新記錄
			newTermmeta := domain.WpTermmeta{
				TermID:    wcCategoryID,
				MetaKey:   "thumbnail_id",
				MetaValue: fmt.Sprintf("%d", imageID),
			}
			if err := tx.Create(&newTermmeta).Error; err != nil {
				return err
			}
		}

		return nil
	})
}
