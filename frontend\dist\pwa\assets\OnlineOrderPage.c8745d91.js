import{Q as h,a as q}from"./QToolbar.0e8beb30.js";import{Q as V}from"./QSelect.c4b20219.js";import{Q as d}from"./QTd.6cf74813.js";import{Q as I,f as M}from"./QTr.5f91a8aa.js";import{Q as N}from"./QTable.e4650bec.js";import{d as P,u as A,r as u,c as g,s as Y,o as v,k as $,f as o,b as t,y as H,p as m,t as r,q as n,a as D,F as C,z as F}from"./index.9477d5a3.js";import{Q as L}from"./QPage.8bf63692.js";import{_ as R,g as z}from"./WCOrderDetailDialog.c0a24920.js";import{_ as E}from"./TablePagination.215cbc35.js";import{C as G}from"./customer.c1e7442d.js";import{O as W}from"./usePrintInvoice.e1a96b8f.js";import{o as j,c as J}from"./order.31b2d3ca.js";import{f as K}from"./date.6d29930c.js";import"./QItemSection.7dc1f54f.js";import"./QItemLabel.3b58be08.js";import"./QMenu.abf49c1b.js";import"./selection.787abe58.js";import"./format.054b8074.js";import"./QList.f0282e16.js";import"./use-fullscreen.e0d6e2b2.js";import"./QSpace.bd91c020.js";import"./QScrollArea.44613085.js";import"./QScrollObserver.b02e8e20.js";import"./TouchPan.ddd1c5b8.js";import"./use-quasar.66282a44.js";import"./xero.72a68f5d.js";import"./dialog.c8d4f0ed.js";import"./plugin-vue_export-helper.21dcd24c.js";const Oe=P({__name:"OnlineOrderPage",setup(X){const{t:l}=A(),b=u([]),O=g(()=>[{name:"id",label:l("orderNo"),field:"id",align:"center"},{name:"date_created",label:l("dateAt"),field:"date_created",align:"center"},{name:"customer",label:l("customer.label"),field:"customer_name",align:"center"},{name:"total",label:l("total"),field:"total",align:"center"},{name:"status",label:l("status"),field:"status",align:"center"},{name:"shipping_method",label:l("shippingMethod"),field:"shipping_method",align:"center"}]),i=u({sortBy:"date_created",descending:!0,page:1,rowsPerPage:10,rowsNumber:0}),c=u(!1),_=u(""),y=u(""),S=g(()=>[{label:l("allStatus"),value:""},...j.value]),p=async()=>{try{c.value=!0;const s=await W.listWCHistory({pagination:i.value});b.value=s.result.data,i.value=s.result.pagination}finally{c.value=!1}},w=u([]),x=g(()=>[{uuid:"",name:l("customer.all")},...w.value]),U=async()=>{const s=await G.fetch();for(let a of s.result.data)w.value.push({uuid:a.uuid,name:a.name})};Y(()=>{p(),U()});const f=u(!1),k=u(0),T=s=>{k.value=s,f.value=!0},B=s=>{if(!s)return;const a=s,{sortBy:e,descending:Q}=a.pagination;i.value.sortBy=e,i.value.descending=Q,p()};return(s,a)=>(v(),$(L,null,{default:o(()=>[t(F,{flat:"",square:"",bordered:"",class:"bg-cream"},{default:o(()=>[t(H,null,{default:o(()=>[t(N,{"virtual-scroll":"",rows:b.value,columns:O.value,pagination:i.value,"onUpdate:pagination":a[2]||(a[2]=e=>i.value=e),"hide-pagination":"","binary-state-sort":"","table-header-class":"bg-grey-3",onRequest:B,loading:c.value},{top:o(()=>[t(h,null,{default:o(()=>[t(q,null,{default:o(()=>[m(r(n(l)("orders"))+" - "+r(n(l)("online")),1)]),_:1})]),_:1}),t(h,{style:{display:"none"}},{default:o(()=>[t(V,{modelValue:_.value,"onUpdate:modelValue":[a[0]||(a[0]=e=>_.value=e),p],options:x.value,"option-label":"name","option-value":"uuid",label:n(l)("customer.label"),"emit-value":"","map-options":""},null,8,["modelValue","options","label"]),t(V,{modelValue:y.value,"onUpdate:modelValue":a[1]||(a[1]=e=>y.value=e),options:S.value,"option-label":"label","option-value":"value",label:n(l)("orderStatus.label"),"emit-value":"","map-options":"",class:"q-mx-md",style:{width:"120px"}},null,8,["modelValue","options","label"])]),_:1})]),body:o(e=>[t(I,{props:e,onClick:Q=>T(e.row.id)},{default:o(()=>[t(d,{props:e,key:"id"},{default:o(()=>[m(r(e.row.id),1)]),_:2},1032,["props"]),t(d,{props:e,key:"date_created"},{default:o(()=>[m(r(n(K)(e.row.date_created,"YYYY-MM-DD HH:mm")),1)]),_:2},1032,["props"]),t(d,{props:e,key:"customer"},{default:o(()=>[e.row.customer_name?(v(),D(C,{key:0},[m(r(e.row.customer_name),1)],64)):(v(),D(C,{key:1},[m(r(n(l)("unknown.customer")),1)],64))]),_:2},1032,["props"]),t(d,{props:e,key:"total",class:"text-bold"},{default:o(()=>[m(" AU$ "+r(n(M)(e.row.total,2)),1)]),_:2},1032,["props"]),t(d,{props:e,key:"status",class:"text-bold"},{default:o(()=>[m(r(n(J)(e.row.status)),1)]),_:2},1032,["props"]),t(d,{props:e,key:"shipping_method"},{default:o(()=>[m(r(n(z)(e.row.shipping_method,e.row.shipping_method_title)),1)]),_:2},1032,["props"])]),_:2},1032,["props","onClick"])]),_:1},8,["rows","columns","pagination","loading"]),t(E,{modelValue:i.value,"onUpdate:modelValue":a[3]||(a[3]=e=>i.value=e),onGetData:p},null,8,["modelValue"])]),_:1})]),_:1}),t(R,{modelValue:f.value,"onUpdate:modelValue":a[4]||(a[4]=e=>f.value=e),orderID:k.value,onRefresh:p},null,8,["modelValue","orderID"])]),_:1}))}});export{Oe as default};
