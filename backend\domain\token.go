package domain

import (
	"time"
)

type AccessToken struct {
	ID          int64     `gorm:"<-:create;primary_key" json:"id"`
	UserID      int64     `gorm:"<-:create" json:"user_id"`
	JwtID       string    `gorm:"<-:create" json:"jwt_id"`
	AccessToken string    `json:"access_token"`
	UserAgent   string    `json:"user_agent"`
	IPAddress   string    `json:"ip_address"`
	LastUsedAt  time.Time `json:"last_used_at"`
	ExpiresAt   time.Time `json:"expires_at"`
	IsRevoked   bool      `json:"is_revoked"`
	CreatedAt   time.Time `gorm:"<-:create" json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type RefreshToken struct {
	ID            int64     `gorm:"<-:create;primary_key" json:"id"`
	UserID        int64     `gorm:"<-:create" json:"user_id"`
	AccessTokenID int64     `gorm:"<-:create" json:"access_token_id"`
	RefreshToken  string    `gorm:"<-:create" json:"refresh_token"`
	ExpiresAt     time.Time `json:"expires_at"`
	IsRevoked     bool      `json:"is_revoked"`
	CreatedAt     time.Time `gorm:"<-:create" json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

type TokenPair struct {
	AccessToken      string    `json:"access_token"`
	ExpiresAt        time.Time `json:"expires_at"`
	RefreshToken     string    `json:"refresh_token"`
	RefreshExpiresAt time.Time `json:"refresh_expires_at"`
}

type TokenRequest struct {
	UserUUID  string
	IsAdmin   bool
	UserAgent string
	IPAddress string
}
