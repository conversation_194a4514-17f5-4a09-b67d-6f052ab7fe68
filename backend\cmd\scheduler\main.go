package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"cx/config"
	"cx/domain"
	"cx/repository"
	"cx/service"
	"cx/utils"
)

func main() {
	log.Println("Starting WooCommerce to Xero Invoice Scheduler...")

	conf, err := config.LoadConfig()
	if err != nil {
		panic(err)
	}

	// 初始化資料庫連線
	DB, err := config.ConnectDB(conf)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := config.CloseDB(DB); err != nil {
			utils.ErrorLog(domain.HttpResponse{
				Status:  http.StatusInternalServerError,
				Message: "failed to close database",
				Error:   err.Error(),
			})
		}
	}()

	// 初始化WP資料庫連線
	wpDB, err := config.ConnectWpDB(conf)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := config.CloseDB(wpDB); err != nil {
			utils.ErrorLog(domain.HttpResponse{
				Status:  http.StatusInternalServerError,
				Message: "failed to close database",
				Error:   err.Error(),
			})
		}
	}()

	// 初始化服務
	wcOrderRepo := repository.NewWcOrderRepository(wpDB)
	wcOrderService := service.NewWcOrderService(wpDB, wcOrderRepo)
	xeroService := service.NewXeroService(DB)

	// 從環境變量獲取默認郵箱
	defaultEmail := os.Getenv("DEFAULT_EMAIL")
	if defaultEmail == "" {
		log.Println("Warning: DEFAULT_EMAIL environment variable not set")
	}

	// 創建調度器服務
	schedulerService := service.NewSchedulerService(
		DB,
		wpDB,
		wcOrderService,
		xeroService,
		defaultEmail,
	)

	// 創建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 啟動調度器
	if err := schedulerService.Start(ctx); err != nil {
		log.Fatalf("Failed to start scheduler: %v", err)
	}

	// 等待中斷信號
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	log.Println("Scheduler is running. Press Ctrl+C to stop.")
	<-sigChan

	log.Println("Received interrupt signal, stopping scheduler...")
	if err := schedulerService.Stop(); err != nil {
		log.Printf("Error stopping scheduler: %v", err)
	}

	cancel()
	log.Println("Scheduler stopped successfully")
}
