<template>
  <q-dialog v-model="visible" class="card-dialog" no-refocus>
    <q-card class="column">
      <!-- header -->
      <q-card-section class="col-1 q-py-none">
        <div class="row q-pt-sm">
          <!-- title -->
          <div class="text-h5 text-bold">
            {{ t('orderDetail') }}
          </div>
          <q-space />
          <!-- close -->
          <q-btn
            type="button"
            icon="close"
            flat
            round
            dense
            @click="emit('update:modelValue', false)"
          />
        </div>
      </q-card-section>

      <!-- 資訊 -->
      <q-card-section class="col-10">
        <q-scroll-area class="full-height">
          <!-- 訂單資訊 -->
          <q-card-section class="text-h6 q-mb-md q-pa-none">
            <div class="row">
              <!-- Order Date -->
              <q-item class="col-12 col-md-6">
                <q-item-section side>
                  <q-icon name="event" size="sm" />
                </q-item-section>
                <q-item-section side>
                  {{ t('orderDate') }}
                </q-item-section>
                <q-item-section class="text-subtitle1">
                  {{ formatDate(order?.order_at, 'YYYY-MM-DD HH:mm') }}
                </q-item-section>
              </q-item>
              <!-- Customer -->
              <q-item class="col-12 col-md-6">
                <q-item-section side>
                  <q-icon name="person" size="sm" />
                </q-item-section>
                <q-item-section>
                  <q-item-label v-if="order?.customer?.name">
                    {{ order.customer.name }}
                  </q-item-label>
                  <q-item-label caption v-else>
                    {{ t('unknown.customer') }}
                  </q-item-label>
                </q-item-section>
              </q-item>
              <!-- Item Number -->
              <q-item class="col-12 col-md-6">
                <q-item-section side>
                  <q-icon name="list" size="sm" />
                </q-item-section>
                <q-item-section side>
                  {{ t('itemNum') }}
                </q-item-section>
                <q-item-section class="text-subtitle1">
                  {{ order?.order_items?.length }}
                </q-item-section>
              </q-item>
              <!-- Total Amount -->
              <q-item class="col-12 col-md-6">
                <q-item-section side>
                  <q-icon name="attach_money" size="sm" />
                </q-item-section>
                <q-item-section side>
                  {{ t('total') }}
                </q-item-section>
                <q-item-section class="text-subtitle1">
                  AU$ {{ order?.total }}
                </q-item-section>
              </q-item>
              <!-- Status -->
              <q-item class="col-12 col-md-6">
                <q-item-section side>
                  <q-icon name="check_circle" size="sm" />
                </q-item-section>
                <q-item-section side>
                  {{ t('status') }}
                </q-item-section>
                <q-item-section class="text-subtitle1">
                  {{ getOrderStatusLabel(order?.status || '') }}
                </q-item-section>
              </q-item>
              <!-- Notes -->
              <q-item class="col-12">
                <q-item-section side>
                  <q-icon name="note" size="sm" />
                </q-item-section>
                <q-item-section side>
                  <div class="row">
                    {{ t('note.label') }}
                    <q-btn
                      flat
                      dense
                      @click="showNotesEdit"
                      icon="edit"
                      size="sm"
                      class="q-ml-xs"
                      :loading="isLoading"
                    />
                  </div>
                </q-item-section>
                <q-item-section class="text-subtitle1">
                  {{ order?.notes }}
                </q-item-section>
              </q-item>
              <!-- Void Reason -->
              <q-item class="col-12" v-if="order?.status == 'void'">
                <q-item-section side>
                  <q-icon name="warning" size="sm" />
                </q-item-section>
                <q-item-section side>
                  <div class="row">
                    {{ t('voidReason') }}
                    <q-btn
                      flat
                      dense
                      @click="orderVoidDialog.openDialog"
                      icon="edit"
                      size="sm"
                      class="q-ml-xs"
                      :loading="isLoading"
                    />
                  </div>
                </q-item-section>
                <q-item-section class="text-subtitle1">
                  {{ order?.void_reason }}
                </q-item-section>
              </q-item>
              <!-- Xero Sync Status -->
              <q-item class="col-12" v-if="order?.status == 'completed'">
                <q-item-section side>
                  <q-icon :name="getXeroSyncIcon()" size="sm" :color="getXeroSyncColor()" />
                </q-item-section>
                <q-item-section side>
                  <div class="row">
                    {{ t('xero.syncStatus') }}
                    <q-btn
                      flat
                      dense
                      @click="syncToXero"
                      icon="sync"
                      size="sm"
                      class="q-ml-xs"
                      :loading="isSyncing"
                      :disable="order?.xero_sync?.sync_status === 'success'"
                      v-if="order?.xero_sync?.sync_status !== 'syncing'"
                    />
                  </div>
                </q-item-section>
                <q-item-section class="text-subtitle1">
                  <div>
                    {{ getXeroSyncStatusLabel() }}
                    <div v-if="order?.xero_sync?.xero_invoice_no" class="text-caption text-grey">
                      Invoice: {{ order.xero_sync.xero_invoice_no }}
                    </div>
                  </div>
                </q-item-section>
              </q-item>
              <q-item class="col-12" v-else-if="order?.status == 'void' && order?.xero_sync?.xero_invoice_no">
                <q-item-section side>
                  <q-icon name="warning" size="sm" />
                </q-item-section>
                <q-item-section side>
                  {{ t('xero.voidedInvoice') }}
                </q-item-section>
                <q-item-section class="text-subtitle1">
                  {{ order?.xero_sync?.xero_invoice_no }}
                </q-item-section>
              </q-item>
            </div>
          </q-card-section>
          <!-- 商品列表 -->
          <q-list class="text-h6" bordered separator>
            <!-- 表頭 -->
            <q-item class="bg-grey-3">
              <q-item-section class="text-bold">
                {{ t('product.label') }}
              </q-item-section>
              <q-item-section class="text-bold" side>
                {{ t('price') }}
              </q-item-section>
            </q-item>
            <!-- 所有商品 -->
            <q-item v-for="item in order?.order_items" :key="item.uuid">
              <q-item-section>
                <q-item-label>{{ item.product.name }}</q-item-label>
                <q-item-label class="text-subtitle1">
                  {{ t('quantity') }}: {{ item.quantity }} |
                  <template v-if="item.is_free">
                    <span class="text-negative">{{
                      t('orderItem.is_free')
                    }}</span>
                  </template>
                  <template v-else>
                    {{ t('price') }}: AU$ {{ item.price }}
                  </template>
                </q-item-label>
                <q-item-label
                  class="text-subtitle1"
                  v-if="item.rebate > 0 || item.discount > 0"
                >
                  {{ t('discount') }}: - AU$
                  {{
                    item.rebate +
                    orderFac.getPercentageOff(
                      orderFac.getItemSubtotal(item),
                      item.rebate,
                      item.discount
                    )
                  }}
                </q-item-label>
              </q-item-section>
              <q-item-section class="text-bold" side>
                AU$ {{ orderFac.getItemTotal(item) }}
              </q-item-section>
            </q-item>
            <!-- 總計 -->
            <q-item class="bg-grey-3">
              <q-item-section>
                <q-item-label class="text-bold">{{ t('total') }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-item-label class="text-bold">
                  AU$ {{ order?.total }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-scroll-area>
      </q-card-section>

      <!-- 底部固定按鈕 -->
      <q-card-actions
        align="between"
        class="col-1 bg-grey-2 q-pa-sm"
      >
        <div class="row q-gutter-sm">
          <q-btn
            color="red"
            icon="delete"
            :label="t('void')"
            @click="orderVoidDialog.openDialog"
            :loading="isLoading"
            v-if="order?.status != 'void'"
          />
          <q-btn
            color="primary"
            icon="print"
            :label="t('printInvoice')"
            @click="handlePrintInvoice"
            :loading="isLoading"
            v-if="order?.status == 'completed'"
          />
          <q-btn
            color="secondary"
            icon="email"
            :label="t('sendEmail')"
            @click="showEmailDialog"
            :loading="isLoading"
            v-if="canSendEmailButton"
          />
        </div>
        <q-btn :label="t('close')" color="grey" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <VoidConfirmDialog
    ref="orderVoidDialog"
    :order-id="orderID"
    :other-reason="order?.void_reason || ''"
    @order-voided="handleOrderVoided"
  />

  <q-dialog v-model="notesEditDialog" persistent>
    <q-card style="width: 500px; max-width: 100%">
      <q-card-section class="text-h6">
        {{ t('note.edit') }}
      </q-card-section>
      <q-card-section>
        <p>{{ t('note.label') }}</p>
        <q-input
          v-model.trim="editNotes"
          type="textarea"
          :rows="5"
          outlined
          dense
        />
      </q-card-section>
      <q-card-actions align="right">
        <q-btn
          :label="t('cancel')"
          color="negative"
          @click="closeNotesEdit"
          :loading="isLoading"
        />
        <q-btn
          :label="t('save')"
          color="positive"
          @click="updateNotes"
          :loading="isLoading"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <EmailInputDialog
    v-model="emailDialogVisible"
    :default-email="xeroConfig?.default_email || ''"
    :customer-email="order?.customer?.email || ''"
    :loading="isLoading"
    :hint-message="getEmailHintMessage()"
    @confirm="handleSendEmail"
    @cancel="emailDialogVisible = false"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import VoidConfirmDialog from './VoidConfirmDialog.vue';
import EmailInputDialog from './EmailInputDialog.vue';
import { OrderApi, Order } from '@/api/order';
import { XeroApi, XeroConfig } from '@/api/xero';
import { useOrder } from '@/composables/useOrder';
import { formatDate, handleError } from '@/utils';
import { getOrderStatusLabel } from '@/types';
import { usePrintInvoice } from '@/composables/usePrintInvoice';

const { t } = useI18n();
const $q = useQuasar();
const orderFac = useOrder();
const { printInvoice } = usePrintInvoice();

// 計算屬性：檢查是否可以顯示發送 Email 按鈕
const canSendEmailButton = computed(() => {
  return !!(
    order.value?.xero_sync?.xero_invoice_id &&
    order.value?.xero_sync?.sync_status === 'success' &&
    order.value?.status !== 'void'
  );
});

// 獲取 Email 按鈕禁用的原因
const getEmailDisabledReason = () => {
  if (!order.value) return t('orderNotLoaded');

  if (order.value.status === 'void') {
    return t('cannotEmailVoidedOrder');
  }

  if (!order.value.customer?.email) {
    return t('sendEmailNoCustomerOrEmail');
  }

  if (!order.value.xero_sync?.xero_invoice_id) {
    return t('sendEmailNotSynced');
  }

  if (order.value.xero_sync.sync_status !== 'success') {
    return t('sendEmailSyncFailed');
  }

  return '';
};

const orderVoidDialog = ref();

const props = defineProps<{
  modelValue: boolean;
  orderID: string;
}>();

const emit = defineEmits(['update:modelValue', 'refresh']);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const order = ref<Order>();
const isLoading = ref(false);
const isSyncing = ref(false);
const emailDialogVisible = ref(false);
const xeroConfig = ref<XeroConfig>();

onMounted(() => {
  getData();
  loadXeroConfig();
});

watch(
  () => props.modelValue,
  (newVal) => {
    order.value = undefined;
    if (newVal) {
      getData();
    }
  }
);

const getData = async () => {
  const response = await OrderApi.get(props.orderID);
  order.value = response.result;
};

const loadXeroConfig = async () => {
  try {
    const response = await XeroApi.getConfig();
    xeroConfig.value = response.result;
  } catch (error) {
    console.error('Failed to load Xero config:', error);
  }
};

const closeDialog = () => {
  visible.value = false;
};

const showEmailDialog = () => {
  emailDialogVisible.value = true;
};

const getEmailHintMessage = () => {
  if (!order.value) return '';

  const hasCustomerEmail = order.value.customer?.email;
  const hasDefaultEmail = xeroConfig.value?.default_email;

  if (hasCustomerEmail) {
    return t('emailInput.usingCustomerEmail');
  } else if (hasDefaultEmail) {
    return t('emailInput.usingDefaultEmail');
  } else {
    return t('emailInput.noDefaultEmail');
  }
};

const handleOrderVoided = async (voidedData: {
  orderId: string;
  reason: string;
  otherReason: string;
}) => {
  try {
    isLoading.value = true;
    await OrderApi.voidOrder(
      voidedData.orderId,
      voidedData.reason,
      voidedData.otherReason
    );
  } finally {
    isLoading.value = false;
    getData();
    emit('refresh');
  }
};

const notesEditDialog = ref(false);
const editNotes = ref('');
const showNotesEdit = () => {
  notesEditDialog.value = true;
  editNotes.value = order.value?.notes || '';
};
const closeNotesEdit = () => {
  notesEditDialog.value = false;
};
const updateNotes = async () => {
  if (!order.value?.uuid) {
    closeNotesEdit();
    return;
  }

  try {
    isLoading.value = true;

    await OrderApi.updateNotes(order.value.uuid, editNotes.value);
  } finally {
    isLoading.value = false;
    getData();
    emit('refresh');
    closeNotesEdit();
  }
};

// Xero 同步相關方法
const getXeroSyncIcon = () => {
  if (!order.value?.xero_sync) return 'cloud_off';

  switch (order.value.xero_sync.sync_status) {
    case 'success':
      return 'cloud_done';
    case 'failed':
      return 'cloud_off';
    case 'syncing':
      return 'cloud_sync';
    case 'pending':
      return 'cloud_queue';
    default:
      return 'cloud_off';
  }
};

const getXeroSyncColor = () => {
  if (!order.value?.xero_sync) return 'grey';

  switch (order.value.xero_sync.sync_status) {
    case 'success':
      return 'green';
    case 'failed':
      return 'red';
    case 'syncing':
      return 'blue';
    case 'pending':
      return 'orange';
    default:
      return 'grey';
  }
};

const getXeroSyncStatusLabel = () => {
  if (!order.value?.xero_sync) return t('xero.notSynced');

  switch (order.value.xero_sync.sync_status) {
    case 'success':
      return t('xero.syncSuccess');
    case 'failed':
      return t('xero.syncFailed');
    case 'syncing':
      return t('xero.syncing');
    case 'pending':
      return t('xero.syncPending');
    default:
      return t('xero.notSynced');
  }
};

const syncToXero = async () => {
  if (!order.value?.uuid) return;

  try {
    isSyncing.value = true;

    const response = await XeroApi.syncOrderToXero(order.value.uuid);

    $q.notify({
      type: 'positive',
      message: response.result.message || t('xero.syncSuccess'),
      position: 'top',
    });

    // 重新獲取訂單數據以更新同步狀態
    await getData();
    emit('refresh');
  } catch (error) {
    handleError(error);
  } finally {
    isSyncing.value = false;
  }
};

// 列印 Invoice
const handlePrintInvoice = async () => {
  if (!order.value) return;

  try {
    isLoading.value = true;
    await printInvoice(order.value);

    $q.notify({
      type: 'positive',
      message: t('printInvoiceSuccess'),
      position: 'top',
    });
  } catch (error) {
    console.error('Print invoice error:', error);
    $q.notify({
      type: 'negative',
      message: t('printInvoiceError'),
      position: 'top',
    });
  } finally {
    isLoading.value = false;
  }
};

// 發送 Email
const handleSendEmail = async (email: string) => {
  if (!order.value) return;

  // 檢查是否已同步到 Xero
  if (!order.value.xero_sync?.xero_invoice_id || order.value.xero_sync.sync_status !== 'success') {
    $q.notify({
      type: 'warning',
      message: t('sendEmailNotSynced'),
      position: 'top',
    });
    emailDialogVisible.value = false;
    return;
  }

  try {
    isLoading.value = true;

    // 調用 Xero API 發送 email
    await XeroApi.sendInvoiceEmail(order.value.xero_sync.xero_invoice_id, email);

    $q.notify({
      type: 'positive',
      message: t('sendEmailSuccess'),
      position: 'top',
    });

    emailDialogVisible.value = false;
  } catch (error) {
    console.error('Send email error:', error);

    // 提供更詳細的錯誤處理
    let errorMessage = t('sendEmailError');

    if (error instanceof Error) {
      if (error.message.includes('daily email limit') || error.message.includes('Daily Email Rate Limit')) {
        errorMessage = t('sendEmailRateLimitError');

        // 顯示解決方案對話框
        $q.dialog({
          title: t('sendEmailRateLimitTitle'),
          message: t('sendEmailRateLimitMessage'),
          ok: {
            label: t('understood'),
            color: 'primary',
          },
          persistent: false,
        });

        emailDialogVisible.value = false;
        return; // 不顯示一般的錯誤通知
      } else if (error.message.includes('invalid email') || error.message.includes('Invalid email')) {
        errorMessage = t('sendEmailInvalidEmailError');
      } else if (error.message.includes('manually from Xero')) {
        errorMessage = t('sendEmailManualError');
      }
    }

    $q.notify({
      type: 'negative',
      message: errorMessage,
      position: 'top',
      timeout: 5000, // 顯示更長時間讓用戶看到完整訊息
    });
  } finally {
    isLoading.value = false;
  }
};
</script>
