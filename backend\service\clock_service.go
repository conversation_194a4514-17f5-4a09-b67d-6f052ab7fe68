package service

import (
	"cx/domain"
	"cx/repository"
	"cx/utils"
	"errors"
	"time"

	"github.com/gin-gonic/gin"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

var (
	ErrClockTypeInvalid = errors.New("invalid clock type")
)

type ClockService interface {
	CreateClock(ctx *gin.Context, req *domain.ClockCreatePayload) (*domain.Clock, error)
	FetchClocks(ctx *gin.Context, filter *domain.ClockFilter, pagination *domain.Pagination) ([]domain.Clock, error)
	GetLatest(ctx *gin.Context, userUUID string) (*domain.Clock, error)

	ListClockPairs(ctx *gin.Context, filter *domain.ClockPairFilter, pagination *domain.Pagination) ([]domain.ClockPairResponse, error)
}

type clockService struct {
	db        *gorm.DB
	clockRepo repository.ClockRepository
	userRepo  repository.UserRepository
}

func NewClockService(db *gorm.DB, clockRepo repository.ClockRepository, userRepo repository.UserRepository) *clockService {
	return &clockService{
		db, clockRepo, userRepo,
	}
}

func (s *clockService) CreateClock(ctx *gin.Context, req *domain.ClockCreatePayload) (*domain.Clock, error) {
	now := time.Now()

	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	userRepo := s.userRepo.WithTx(tx)
	clockRepo := s.clockRepo.WithTx(tx)

	user, err := userRepo.GetByUUID(ctx, req.UserUUID)
	if err != nil {
		return nil, err
	}

	// 查找當天最後的打卡記錄
	lastClock, err := s.clockRepo.FindLastClock(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	lastClockPair, err := s.clockRepo.FindClockPair(ctx, user.ID, lastClock.ID)
	if err != nil {
		return nil, err
	}

	// 創建打卡記錄
	clock := &domain.Clock{
		UserID:    user.ID,
		Type:      req.Type,
		ClockTime: now,
		UserAgent: req.UserAgent,
		IPAddress: req.IPAddress,
	}

	if err := clockRepo.CreateClock(ctx, clock); err != nil {
		tx.Rollback()
		return nil, err
	}

	// 嘗試配對打卡記錄
	if err := s.tryPairClocks(ctx, clockRepo, lastClockPair, clock); err != nil {
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	return clock, nil
}

func (s *clockService) tryPairClocks(ctx *gin.Context, clockRepo repository.ClockRepository, lastClockPair *domain.ClockPair, clock *domain.Clock) error {
	// 如果打卡是上班(check_in)，則不需要配對，建立新的clock pair，並檢查前一次打卡是否為缺失
	if clock.Type == domain.ClockTypeClockIn {
		if lastClockPair.ID != 0 && lastClockPair.Status == domain.ClockPairStatusPending &&
			(lastClockPair.ClockInID.IsZero() || lastClockPair.ClockOutID.IsZero()) {
			if err := clockRepo.MarkMissingPairs(ctx, lastClockPair); err != nil {
				return err
			}
		}

		return clockRepo.CreateClockPair(ctx, &domain.ClockPair{
			UserID:    clock.UserID,
			DateAt:    clock.ClockTime,
			ClockInID: null.IntFrom(clock.ID),
			Status:    domain.ClockPairStatusPending,
		})
	}

	// 如果打卡是下班(clock out)，則更新前一次打卡記錄的下班時間，並檢查是否需要更新打卡記錄的狀態
	if clock.Type == domain.ClockTypeClockOut {
		if lastClockPair.ID == 0 {
			return clockRepo.CreateClockPair(ctx, &domain.ClockPair{
				UserID:     clock.UserID,
				DateAt:     clock.ClockTime,
				ClockOutID: null.IntFrom(clock.ID),
				Status:     domain.ClockPairStatusMissing,
			})
		} else {
			if utils.DateEqual(lastClockPair.DateAt, clock.ClockTime) {
				// 同天
				if lastClockPair.ClockOutID.IsZero() {
					return clockRepo.UpdateClockPairWithCheckOut(ctx, lastClockPair, clock)
				} else {
					return clockRepo.CreateClockPair(ctx, &domain.ClockPair{
						UserID:     clock.UserID,
						DateAt:     clock.ClockTime,
						ClockOutID: null.IntFrom(clock.ID),
						Status:     domain.ClockPairStatusMissing,
					})
				}
			} else {
				// 不同天
				return clockRepo.CreateClockPair(ctx, &domain.ClockPair{
					UserID:     clock.UserID,
					DateAt:     clock.ClockTime,
					ClockOutID: null.IntFrom(clock.ID),
					Status:     domain.ClockPairStatusMissing,
				})
			}
		}
	}

	return ErrClockTypeInvalid
}

func (s *clockService) FetchClocks(ctx *gin.Context, filter *domain.ClockFilter, pagination *domain.Pagination) ([]domain.Clock, error) {
	return s.clockRepo.FetchClocks(ctx, filter, pagination)
}

func (s *clockService) GetLatest(ctx *gin.Context, userUUID string) (*domain.Clock, error) {
	user, err := s.userRepo.GetByUUID(ctx, userUUID)
	if err != nil {
		return nil, err
	}

	return s.clockRepo.FindLastClock(ctx, user.ID)
}

func (s *clockService) ListClockPairs(ctx *gin.Context, filter *domain.ClockPairFilter, pagination *domain.Pagination) ([]domain.ClockPairResponse, error) {
	return s.clockRepo.ListClockPairs(ctx, filter, pagination)
}
