package service

import (
	"context"
	"cx/domain"
	"cx/repository"
	"cx/utils"
	"mime/multipart"
	"path/filepath"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ProductService interface {
	Create(ctx context.Context, product *domain.Product) error
	Update(ctx context.Context, uuid string, product *domain.ProductUpdatePayload) error
	UpdateStatus(ctx context.Context, uuid string, isActive bool) error
	ListProducts(ctx context.Context, filter *domain.ProductFilter, pagination *domain.Pagination) ([]domain.Product, error)
	GetByID(ctx context.Context, id int64) (*domain.Product, error)
	GetByUUID(ctx context.Context, uuid string) (*domain.Product, error)
	Delete(ctx context.Context, uuid string) error

	UploadImage(c *gin.Context, productUUID string, image *multipart.FileHeader) error
	UpdateImageSortOrder(ctx context.Context, images []domain.ProductImage) error
	DeleteImage(ctx context.Context, imageUUID string) error

	AddSupplier(ctx context.Context, productUUID, supplierUUID string) error
	RemoveSupplier(ctx context.Context, productUUID, supplierUUID string) error

	SyncWcProductWithImages(ctx context.Context, product *domain.Product) error
}

type productService struct {
	db            *gorm.DB
	wpDB          *gorm.DB
	productRepo   repository.ProductRepository
	wcProductRepo repository.WcProductRepository
}

func NewProductService(db *gorm.DB, wpDB *gorm.DB, productRepo repository.ProductRepository) ProductService {
	return &productService{db, wpDB, productRepo, repository.NewWcProductRepository(wpDB)}
}

func (s *productService) Create(ctx context.Context, product *domain.Product) error {
	var category domain.ProductCategory
	if product.CategoryID != 0 {
		productCategoryRepo := repository.NewProductCategoryRepository(s.db)
		cat, err := productCategoryRepo.GetByID(ctx, product.CategoryID)
		if err != nil {
			return err
		}

		category = *cat
	}

	// 彈性金額產品不同步到WooCommerce，因為它們只在POS系統中使用
	if !product.IsFlexiblePrice {
		wcCategories := []domain.WcCategory{}
		if category.WcID != 0 {
			wcCategories = append(wcCategories, domain.WcCategory{
				ID:   category.WcID,
				Name: category.Name,
				Slug: utils.ConvertWPSlug(category.Name),
			})
		}

		wcProduct := domain.WcProduct{
			Name:             product.Name,
			Slug:             utils.ConvertWPSlug(product.Name),
			Description:      product.Description,
			ShortDescription: product.ShortDescription,
			RegularPrice:     product.Price,
			SalePrice:        product.SalePrice,
			Sku:              product.Barcode,
			Categories:       wcCategories,
			Status:           "publish",
		}

		err := s.wcProductRepo.Create(&wcProduct)
		if err == nil {
			product.WcID = wcProduct.ID
		}
	}

	err := s.productRepo.Create(ctx, product)
	if err != nil {
		return err
	}

	return err
}

func (s *productService) Update(ctx context.Context, uuid string, payload *domain.ProductUpdatePayload) error {
	product, err := s.productRepo.GetByUUID(ctx, uuid)
	if err != nil {
		return err
	}

	// 檢查是否為彈性金額產品（當前產品或更新後的產品）
	isFlexiblePrice := product.IsFlexiblePrice
	if payload.IsFlexiblePrice != nil {
		isFlexiblePrice = *payload.IsFlexiblePrice
	}

	// 處理 WooCommerce 同步邏輯
	if isFlexiblePrice {
		// 如果產品變為彈性金額且已同步到 WooCommerce，將其狀態設為 draft（關閉）
		if product.WcID != 0 {
			err = s.wcProductRepo.UpdateStatus(product.WcID, "draft")
			if err != nil {
				return err
			}
		}
	} else {
		// 非彈性金額產品，正常同步到 WooCommerce
		wcCategories := []domain.WcCategory{}
		if product.Category.WcID != 0 {
			wcCategories = append(wcCategories, domain.WcCategory{
				ID:   product.Category.WcID,
				Name: product.Category.Name,
				Slug: utils.ConvertWPSlug(product.Category.Name),
			})
		}

		wcProduct := domain.WcProduct{
			ID:               product.WcID,
			Name:             payload.Name,
			Slug:             utils.ConvertWPSlug(payload.Name),
			Description:      *payload.Description,
			ShortDescription: *payload.ShortDescription,
			RegularPrice:     *payload.Price,
			SalePrice:        *payload.SalePrice,
			Sku:              *payload.Barcode,
			StockQuantity:    product.StockQuantity,
			Categories:       wcCategories,
			Status:           "publish",
		}

		if product.WcID != 0 {
			err = s.wcProductRepo.Update(&wcProduct)
			if err != nil {
				return err
			}
		} else {
			err = s.wcProductRepo.Create(&wcProduct)
			if err != nil {
				return err
			}

			payload.WcID = wcProduct.ID
		}
	}

	return s.productRepo.Update(ctx, uuid, payload)
}

func (s *productService) UpdateStatus(ctx context.Context, uuid string, isActive bool) error {
	// 先獲取產品資訊
	product, err := s.productRepo.GetByUUID(ctx, uuid)
	if err != nil {
		return err
	}

	// 更新本地產品狀態
	err = s.productRepo.UpdateStatus(ctx, uuid, isActive)
	if err != nil {
		return err
	}

	// 如果產品已同步到 WooCommerce 且不是彈性金額產品，同步狀態
	if product.WcID != 0 && !product.IsFlexiblePrice {
		wcStatus := "draft" // 預設為關閉
		if isActive {
			wcStatus = "publish" // 啟用時設為發布
		}

		// 只更新狀態
		err = s.wcProductRepo.UpdateStatus(product.WcID, wcStatus)
		if err != nil {
			// WooCommerce 同步失敗不影響本地更新，只記錄錯誤
			// 可以考慮添加日誌記錄
			return err
		}
	}

	return nil
}

func (s *productService) ListProducts(ctx context.Context, filter *domain.ProductFilter, pagination *domain.Pagination) ([]domain.Product, error) {
	return s.productRepo.ListProducts(ctx, filter, pagination)
}

func (s *productService) GetByID(ctx context.Context, id int64) (*domain.Product, error) {
	return s.productRepo.GetByID(ctx, id)
}

func (s *productService) GetByUUID(ctx context.Context, uuid string) (*domain.Product, error) {
	return s.productRepo.GetByUUID(ctx, uuid)
}

func (s *productService) Delete(ctx context.Context, uuid string) error {
	return s.productRepo.Delete(ctx, uuid)
}

func (s *productService) UploadImage(c *gin.Context, productUUID string, image *multipart.FileHeader) error {
	filePath := "uploads/products/"
	imageUUID := utils.GenerateUUID()
	fileName := imageUUID + filepath.Ext(image.Filename)

	product, err := s.productRepo.GetByUUID(c, productUUID)
	if err != nil {
		return err
	}

	lastSortOrder, err := s.productRepo.GetImageLastSortOrder(c, product.ID)
	if err != nil {
		return err
	}

	uploadImage := domain.ProductImage{
		UUID:      imageUUID,
		ProductID: product.ID,
		ImagePath: filePath + fileName,
		SortOrder: lastSortOrder + 1,
	}

	// 確認路徑是否已建立，如果沒有則建立
	if err = utils.CreatePathIfNotExists(filePath); err != nil {
		return err
	}

	// 上傳檔案
	if err := c.SaveUploadedFile(image, uploadImage.ImagePath); err != nil {
		return err
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		productRepo := repository.NewProductRepository(tx)

		if err := productRepo.CreateImage(c, &uploadImage); err != nil {
			return err
		}

		return nil
	})

	return err
}

func (s *productService) UpdateImageSortOrder(ctx context.Context, images []domain.ProductImage) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		productRepo := repository.NewProductRepository(tx)

		for _, image := range images {
			if err := productRepo.UpdateImageSortOrder(ctx, image.UUID, image.SortOrder); err != nil {
				return err
			}
		}

		return nil
	})
}

func (s *productService) DeleteImage(ctx context.Context, imageUUID string) error {
	err := s.db.Transaction(func(tx *gorm.DB) error {
		productRepo := repository.NewProductRepository(tx)

		image, err := productRepo.GetImageByUUID(ctx, imageUUID)
		if err != nil {
			return err
		}

		// 刪除資料庫記錄

		if err := productRepo.DeleteImage(ctx, imageUUID); err != nil {
			return err
		}

		// 刪除檔案
		if err := utils.DeleteFile(image.ImagePath); err != nil {
			return err
		}

		return nil
	})

	return err
}

func (s *productService) AddSupplier(ctx context.Context, productUUID, supplierUUID string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		productRepo := repository.NewProductRepository(tx)
		customerRepo := repository.NewCustomerRepository(tx)

		product, err := productRepo.GetByUUID(ctx, productUUID)
		if err != nil {
			return err
		}

		supplier, err := customerRepo.GetByUUID(ctx, supplierUUID)
		if err != nil {
			return err
		}

		return productRepo.AddSupplier(ctx, product.ID, supplier.ID)
	})
}

func (s *productService) RemoveSupplier(ctx context.Context, productUUID, supplierUUID string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		productRepo := repository.NewProductRepository(tx)
		customerRepo := repository.NewCustomerRepository(tx)

		product, err := productRepo.GetByUUID(ctx, productUUID)
		if err != nil {
			return err
		}

		supplier, err := customerRepo.GetByUUID(ctx, supplierUUID)
		if err != nil {
			return err
		}

		return productRepo.RemoveSupplier(ctx, product.ID, supplier.ID)
	})
}

func (s *productService) SyncWcProductWithImages(ctx context.Context, product *domain.Product) error {
	// 1. 確認商品有同步到 Woocommerce
	if product.WcID == 0 || len(product.Images) == 0 {
		return nil
	}

	wpDB := s.wpDB.Begin()
	err := s.db.Transaction(func(tx *gorm.DB) error {
		productRepo := repository.NewProductRepository(tx)
		wcProductRepo := repository.NewWcProductRepository(wpDB)

		// 2. 處理主要圖片
		var mainImageID int64
		mainImage := product.Images[0]
		if mainImage.WcID != 0 {
			mainImageID = mainImage.WcID
		} else {
			wcID, err := repository.InsertMediaToWordpress(wpDB, mainImage.ImagePath)
			if err != nil {
				return err
			}
			err = productRepo.UpdateImageWcID(ctx, product.Images[0].ID, wcID)
			if err != nil {
				return err
			}

			mainImageID = wcID
		}

		// 3. 處理圖庫圖片
		var galleryImageIDs []int64
		if len(product.Images) > 1 {
			for i := 1; i < len(product.Images); i++ {
				var galleryImageID int64

				if product.Images[i].WcID != 0 {
					galleryImageID = product.Images[i].WcID
				} else {
					wcID, err := repository.InsertMediaToWordpress(wpDB, product.Images[i].ImagePath)
					if err != nil {
						continue
					}
					err = productRepo.UpdateImageWcID(ctx, product.Images[i].ID, wcID)
					if err != nil {
						return err
					}
					galleryImageID = wcID
				}

				galleryImageIDs = append(galleryImageIDs, galleryImageID)
			}
		}

		// 4. 更新WooCommerce商品圖片關聯
		return wcProductRepo.UpdateProductImages(product.WcID, mainImageID, galleryImageIDs)
	})
	if err != nil {
		wpDB.Rollback()
		return err
	}

	wpDB.Commit()

	return nil
}
