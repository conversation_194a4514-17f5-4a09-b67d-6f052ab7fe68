package service

import (
	"context"
	"cx/domain"
	"cx/repository"
	"cx/utils"
	"errors"
	"fmt"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type InventoryService interface {
	// 庫存相關
	ListProductStocks(ctx context.Context, filter *domain.ProductStockFilter, pagination *domain.Pagination) ([]domain.ProductStockResponse, error)
	ListInventoryTransactions(ctx context.Context, filter *domain.InventoryTransactionFilter, pagination *domain.Pagination) ([]domain.InventoryTransaction, error)

	// 採購單相關
	GetPurchaseOrderByUUID(ctx context.Context, uuid string) (*domain.PurchaseOrder, error)
	ListPurchaseOrders(ctx context.Context, pagination *domain.Pagination) ([]domain.PurchaseOrder, error)
	CreatePurchaseOrder(ctx context.Context, purchaseOrder *domain.CreatePurchaseOrderRequest) error

	// 退貨
	ListReturns(ctx context.Context, filter *domain.ReturnFilter, pagination *domain.Pagination) ([]domain.Return, error)
	CreateReturn(ctx context.Context, returnData *domain.CreateReturnRequest) error

	// 報廢
	// ListScraps(ctx context.Context, filter *domain.ScrapFilter, pagination *domain.Pagination) ([]domain.Scrap, error)
	CreateScrap(ctx context.Context, scrapData *domain.CreateScrapRequest) error

	// 盤點
	// ListStockStocktaking(ctx context.Context, filter *domain.StocktakingFilter, pagination *domain.Pagination)
	CreateStocktaking(ctx context.Context, stocktakingData *domain.CreateStocktakingRequest) error
}

type inventoryService struct {
	db                *gorm.DB
	wpDB              *gorm.DB
	customerRepo      repository.CustomerRepository
	productRepo       repository.ProductRepository
	inventoryRepo     repository.InventoryRepository
	purchaseOrderRepo repository.PurchaseOrderRepository
	returnRepo        repository.ReturnRepository
}

func NewInventoryService(
	db *gorm.DB,
	wpDB *gorm.DB,
	customerRepo repository.CustomerRepository,
	productRepo repository.ProductRepository,
	inventoryRepo repository.InventoryRepository,
	purchaseOrderRepo repository.PurchaseOrderRepository,
	returnRepo repository.ReturnRepository,
) InventoryService {
	return &inventoryService{
		db:                db,
		wpDB:              wpDB,
		customerRepo:      customerRepo,
		productRepo:       productRepo,
		inventoryRepo:     inventoryRepo,
		purchaseOrderRepo: purchaseOrderRepo,
		returnRepo:        returnRepo,
	}
}

// 獲取產品庫存列表
func (s *inventoryService) ListProductStocks(ctx context.Context, filter *domain.ProductStockFilter, pagination *domain.Pagination) ([]domain.ProductStockResponse, error) {
	return s.inventoryRepo.ListProductStocks(ctx, filter, pagination)
}

// 獲取庫存交易列表
func (s *inventoryService) ListInventoryTransactions(ctx context.Context, filter *domain.InventoryTransactionFilter, pagination *domain.Pagination) ([]domain.InventoryTransaction, error) {
	return s.inventoryRepo.ListInventoryTransactions(ctx, filter, pagination)
}

func (s *inventoryService) GetPurchaseOrderByUUID(ctx context.Context, uuid string) (*domain.PurchaseOrder, error) {
	return s.purchaseOrderRepo.GetPurchaseOrderByUUID(ctx, uuid)
}

func (s *inventoryService) ListPurchaseOrders(ctx context.Context, pagination *domain.Pagination) ([]domain.PurchaseOrder, error) {
	return s.purchaseOrderRepo.ListPurchaseOrders(ctx, pagination)
}

// 創建採購單
func (s *inventoryService) CreatePurchaseOrder(ctx context.Context, purchaseOrder *domain.CreatePurchaseOrderRequest) error {
	if purchaseOrder.UUID == "" {
		purchaseOrder.UUID = utils.GenerateUUID()
	}

	// 設定訂單編號
	if purchaseOrder.PONumber == "" {
		purchaseOrder.PONumber = fmt.Sprintf("PO-%s", time.Now().Format("20060102-150405"))
	}

	// 計算每個項目的總價
	var totalAmount float64
	for i := range purchaseOrder.Items {
		item := &purchaseOrder.Items[i]
		item.TotalPrice = item.UnitPrice * float64(item.QuantityOrdered)
		totalAmount += item.TotalPrice
	}

	if purchaseOrder.OrderDate == "" {
		purchaseOrder.OrderDate = time.Now().Format("2006-01-02")
	}

	purchaseOrder.TotalAmount = totalAmount

	if purchaseOrder.Customer.UUID != "" {
		if customer, err := s.customerRepo.GetByUUID(ctx, purchaseOrder.Customer.UUID); err != nil {
			return errors.New("customer/supplier doesn't exist")
		} else {
			purchaseOrder.CustomerID = null.IntFrom(customer.ID)
		}
	}

	// 事務
	wpDB := s.wpDB.Begin()
	err := s.db.Transaction(func(tx *gorm.DB) error {
		productRepo := repository.NewProductRepository(tx)
		purchaseOrderRepo := repository.NewPurchaseOrderRepository(tx)
		inventoryRepo := repository.NewInventoryRepository(tx, wpDB)

		// 創建採購單
		err := purchaseOrderRepo.CreatePurchaseOrder(ctx, purchaseOrder)
		if err != nil {
			return err
		}

		// 處理進貨內容
		for i := range purchaseOrder.Items {
			item := &purchaseOrder.Items[i]
			item.UUID = utils.GenerateUUID()
			item.PurchaseOrderID = purchaseOrder.ID

			if product, _ := s.productRepo.GetByUUID(ctx, item.Product.UUID); product.ID == 0 {
				return errors.New("product doesn't exist")
			} else {
				item.ProductID = product.ID
			}

			item.CreatedByID = purchaseOrder.CreatedByID
			item.UpdatedByID = purchaseOrder.UpdatedByID

			if err := purchaseOrderRepo.CreateStockInItem(ctx, item); err != nil {
				return err
			}
		}

		// 確認收貨
		// if purchaseOrder.Status == domain.PurchaseOrderStatusReceived {
		// 更新採購單狀態並更新庫存
		err = purchaseOrderRepo.ReceivePurchaseOrder(ctx, purchaseOrder.ID, purchaseOrder.Items)
		if err != nil {
			return err
		}

		// 對於每個項目，創建庫存交易記錄並更新庫存
		err = s.processPurchaseOrderItems(ctx, purchaseOrder, purchaseOrder.Items, productRepo, inventoryRepo)
		if err != nil {
			return err
		}
		// }

		return nil
	})
	if err != nil {
		wpDB.Rollback()
		return err
	}

	wpDB.Commit()

	return nil
}

// 對於每個項目，創建庫存交易記錄並更新庫存
func (s *inventoryService) processPurchaseOrderItems(
	ctx context.Context,
	purchaseOrder *domain.CreatePurchaseOrderRequest,
	items []domain.PurchaseOrderItem,
	productRepo repository.ProductRepository,
	inventoryRepo repository.InventoryRepository,
) error {
	for _, updateItem := range items {
		for _, originalItem := range purchaseOrder.Items {
			if updateItem.ID == originalItem.ID {
				// 計算本次新增的收貨數量
				// newReceived := updateItem.QuantityReceived - originalItem.QuantityReceived
				// if newReceived <= 0 {
				// 	continue
				// }
				newReceived := updateItem.QuantityReceived

				// 獲取產品信息
				product, err := productRepo.GetByUUID(ctx, originalItem.Product.UUID)
				if err != nil {
					return err
				}

				// 更新庫存
				err = inventoryRepo.UpdateProductStock(ctx, originalItem.ProductID, newReceived)
				if err != nil {
					return err
				}

				// 創建庫存交易記錄
				transaction := &domain.InventoryTransaction{
					UUID:            utils.GenerateUUID(),
					ProductID:       originalItem.ProductID,
					TransactionType: domain.InventoryTransactionTypeStockIn,
					Quantity:        newReceived,
					BeforeQuantity:  product.StockQuantity,
					AfterQuantity:   product.StockQuantity + newReceived,
					UnitPrice:       originalItem.UnitPrice,
					TotalAmount:     originalItem.UnitPrice * float64(newReceived),
					Notes:           fmt.Sprintf("Purchase Order: %s", purchaseOrder.PONumber),
					CreatedByID:     purchaseOrder.CreatedByID,
					UpdatedByID:     purchaseOrder.UpdatedByID,
				}

				err = inventoryRepo.CreateInventoryTransaction(ctx, transaction)
				if err != nil {
					return err
				}
				break
			}
		}
	}

	return nil
}

func (s *inventoryService) ListReturns(ctx context.Context, filter *domain.ReturnFilter, pagination *domain.Pagination) ([]domain.Return, error) {
	return s.returnRepo.List(ctx, filter, pagination)
}

func (s *inventoryService) CreateReturn(ctx context.Context, returnData *domain.CreateReturnRequest) error {
	if returnData.UUID == "" {
		returnData.UUID = utils.GenerateUUID()
	}

	if returnData.ReturnNumber == "" {
		returnData.ReturnNumber = fmt.Sprintf("RETURN-%s", time.Now().Format("20060102-150405"))
	}

	if returnData.OrderNo != "" {
		orderRepo := repository.NewOrderRepository(s.db)
		order, err := orderRepo.GetByOrderNo(ctx, returnData.OrderNo)
		if err != nil {
			return err
		}

		returnData.OrderID = null.IntFrom(order.ID)
	}

	if returnData.Customer.UUID != "" {
		customerRepo := repository.NewCustomerRepository(s.db)
		customer, err := customerRepo.GetByUUID(ctx, returnData.Customer.UUID)
		if err != nil {
			return err
		}

		returnData.CustomerID = null.IntFrom(customer.ID)
	}

	wpDB := s.wpDB.Begin()
	err := s.db.Transaction(func(tx *gorm.DB) error {
		returnRepo := repository.NewReturnRepository(tx)
		productRepo := repository.NewProductRepository(tx)
		inventoryRepo := repository.NewInventoryRepository(tx, wpDB)

		err := returnRepo.Create(ctx, returnData)
		if err != nil {
			return err
		}

		for i := range returnData.Items {
			item := &returnData.Items[i]
			item.UUID = utils.GenerateUUID()
			item.ReturnID = returnData.ID

			if product, _ := s.productRepo.GetByUUID(ctx, item.Product.UUID); product.ID == 0 {
				return errors.New("product doesn't exist")
			} else {
				item.ProductID = product.ID
			}

			item.CreatedByID = returnData.CreatedByID
			item.UpdatedByID = returnData.UpdatedByID
		}

		err = s.processReturnItems(ctx, returnData, returnData.Items, productRepo, inventoryRepo)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		wpDB.Rollback()
		return err
	}

	wpDB.Commit()

	return nil
}

func (s *inventoryService) processReturnItems(
	ctx context.Context,
	ret *domain.CreateReturnRequest,
	items []domain.ReturnItemRequest,
	productRepo repository.ProductRepository,
	inventoryRepo repository.InventoryRepository,
) error {
	for _, updateItem := range items {
		var transactionType domain.InventoryTransactionType
		var returnQty int

		if ret.ReturnType == domain.ReturnTypeCustomerReturn {
			transactionType = domain.InventoryTransactionTypeReturnIn
			returnQty = updateItem.Quantity
		} else {
			transactionType = domain.InventoryTransactionTypeReturnOut
			returnQty = -updateItem.Quantity
		}

		// 獲取產品信息
		product, err := productRepo.GetByUUID(ctx, updateItem.Product.UUID)
		if err != nil {
			return err
		}

		updateItem.ProductID = product.ID
		updateItem.CreatedByID = ret.CreatedByID
		updateItem.UpdatedByID = ret.UpdatedByID

		// 更新庫存
		err = inventoryRepo.UpdateProductStock(ctx, updateItem.ProductID, returnQty)
		if err != nil {
			return err
		}

		// 創建庫存交易記錄
		transaction := &domain.InventoryTransaction{
			UUID:            utils.GenerateUUID(),
			ProductID:       updateItem.ProductID,
			TransactionType: transactionType,
			Quantity:        returnQty,
			BeforeQuantity:  product.StockQuantity,
			AfterQuantity:   product.StockQuantity + returnQty,
			Notes:           fmt.Sprintf("Return Order: %s", ret.ReturnNumber),
			CreatedByID:     ret.CreatedByID,
			UpdatedByID:     ret.UpdatedByID,
		}

		err = inventoryRepo.CreateInventoryTransaction(ctx, transaction)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *inventoryService) CreateScrap(ctx context.Context, scrapData *domain.CreateScrapRequest) error {
	if scrapData.UUID == "" {
		scrapData.UUID = utils.GenerateUUID()
	}

	if scrapData.ScrapDate == "" {
		scrapData.ScrapDate = time.Now().Format("2006-01-02")
	}

	wpDB := s.wpDB.Begin()
	err := s.db.Transaction(func(tx *gorm.DB) error {
		productRepo := repository.NewProductRepository(tx)
		inventoryRepo := repository.NewInventoryRepository(tx, wpDB)

		err := s.processScrapItems(ctx, scrapData, productRepo, inventoryRepo)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		wpDB.Rollback()
		return err
	}

	wpDB.Commit()

	return nil
}

func (s *inventoryService) processScrapItems(
	ctx context.Context,
	scrapData *domain.CreateScrapRequest,
	productRepo repository.ProductRepository,
	inventoryRepo repository.InventoryRepository,
) error {
	for _, updateItem := range scrapData.Items {
		// 獲取產品信息
		product, err := productRepo.GetByUUID(ctx, updateItem.Product.UUID)
		if err != nil {
			return err
		}

		updateItem.ProductID = product.ID
		updateItem.CreatedByID = scrapData.CreatedByID
		updateItem.UpdatedByID = scrapData.UpdatedByID

		// 更新庫存
		err = inventoryRepo.UpdateProductStock(ctx, updateItem.ProductID, -updateItem.Quantity)
		if err != nil {
			return err
		}

		// 創建庫存交易記錄
		transaction := &domain.InventoryTransaction{
			UUID:            utils.GenerateUUID(),
			ProductID:       updateItem.ProductID,
			TransactionType: domain.InventoryTransactionTypeScrap,
			Quantity:        updateItem.Quantity,
			BeforeQuantity:  product.StockQuantity,
			AfterQuantity:   product.StockQuantity - updateItem.Quantity,
			CreatedByID:     scrapData.CreatedByID,
			UpdatedByID:     scrapData.UpdatedByID,
		}

		err = inventoryRepo.CreateInventoryTransaction(ctx, transaction)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *inventoryService) CreateStocktaking(ctx context.Context, stocktakingData *domain.CreateStocktakingRequest) error {
	if stocktakingData.UUID == "" {
		stocktakingData.UUID = utils.GenerateUUID()
	}

	if stocktakingData.CountNumber == "" {
		stocktakingData.CountNumber = fmt.Sprintf("COUNT-%s", time.Now().Format("20060102-150405"))
	}

	if stocktakingData.CountDate == "" {
		stocktakingData.CountDate = time.Now().Format("2006-01-02")
	}

	wpDB := s.wpDB.Begin()
	err := s.db.Transaction(func(tx *gorm.DB) error {
		stocktakingRepo := repository.NewStocktakingRepository(tx)
		productRepo := repository.NewProductRepository(tx)
		inventoryRepo := repository.NewInventoryRepository(tx, wpDB)

		err := stocktakingRepo.Create(ctx, stocktakingData)
		if err != nil {
			return err
		}

		for i := range stocktakingData.Items {
			item := &stocktakingData.Items[i]
			item.UUID = utils.GenerateUUID()
			item.InventoryCountID = stocktakingData.ID

			if product, _ := s.productRepo.GetByUUID(ctx, item.Product.UUID); product.ID == 0 {
				return errors.New("product doesn't exist")
			} else {
				item.ProductID = product.ID
			}

			item.CreatedByID = stocktakingData.CreatedByID
			item.UpdatedByID = stocktakingData.UpdatedByID
		}

		err = s.processStocktakingItems(ctx, stocktakingData, productRepo, inventoryRepo, stocktakingRepo)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		wpDB.Rollback()
		return err
	}

	wpDB.Commit()

	return nil
}

func (s *inventoryService) processStocktakingItems(
	ctx context.Context,
	stocktaking *domain.CreateStocktakingRequest,
	productRepo repository.ProductRepository,
	inventoryRepo repository.InventoryRepository,
	stocktakingRepo repository.StocktakingRepository,
) error {
	for _, updateItem := range stocktaking.Items {
		// 獲取產品信息
		product, err := productRepo.GetByUUID(ctx, updateItem.Product.UUID)
		if err != nil {
			return err
		}

		updateItem.ProductID = product.ID
		updateItem.ExpectedQuantity = product.StockQuantity
		updateItem.Difference = updateItem.ActualQuantity - updateItem.ExpectedQuantity

		// 新增
		err = stocktakingRepo.CreateItem(ctx, &updateItem)
		if err != nil {
			return err
		}

		// 更新庫存
		err = inventoryRepo.UpdateProductStock(ctx, updateItem.ProductID, updateItem.Difference)
		if err != nil {
			return err
		}

		// 創建庫存交易記錄
		transaction := &domain.InventoryTransaction{
			UUID:            utils.GenerateUUID(),
			ProductID:       updateItem.ProductID,
			TransactionType: domain.InventoryTransactionTypeStocktaking,
			Quantity:        updateItem.ActualQuantity,
			BeforeQuantity:  updateItem.ExpectedQuantity,
			AfterQuantity:   updateItem.ActualQuantity,
			Notes:           fmt.Sprintf("Stocktaking: %s", stocktaking.CountNumber),
			CreatedByID:     stocktaking.CreatedByID,
			UpdatedByID:     stocktaking.UpdatedByID,
		}

		err = inventoryRepo.CreateInventoryTransaction(ctx, transaction)
		if err != nil {
			return err
		}
	}

	return nil
}
