package repository

import (
	"context"
	"cx/domain"
	"cx/utils"

	"gorm.io/gorm"
)

type ClockRepository interface {
	WithTx(tx *gorm.DB) ClockRepository

	// Clock
	CreateClock(ctx context.Context, clock *domain.Clock) error
	FetchClocks(ctx context.Context, filter *domain.ClockFilter, pagination *domain.Pagination) ([]domain.Clock, error)
	FindLastClock(ctx context.Context, userID int64) (*domain.Clock, error)

	// ClockPair
	ListClockPairs(ctx context.Context, filter *domain.ClockPairFilter, pagination *domain.Pagination) ([]domain.ClockPairResponse, error)
	FindClockPair(ctx context.Context, userID int64, clockID int64) (*domain.ClockPair, error)
	CreateClockPair(ctx context.Context, pair *domain.ClockPair) error
	UpdateClockPairWithCheckIn(ctx context.Context, pair *domain.ClockPair, clock *domain.Clock) error
	UpdateClockPairWithCheckOut(ctx context.Context, pair *domain.ClockPair, clock *domain.Clock) error
	MarkMissingPairs(ctx context.Context, pair *domain.ClockPair) error
}

type clockRepository struct {
	db *gorm.DB
}

func NewClockRepository(db *gorm.DB) ClockRepository {
	return &clockRepository{db: db}
}

func (r *clockRepository) WithTx(tx *gorm.DB) ClockRepository {
	return &clockRepository{db: tx}
}

func (r *clockRepository) CreateClock(ctx context.Context, clock *domain.Clock) error {
	clock.UUID = utils.GenerateUUID()
	return r.db.WithContext(ctx).Create(clock).Error
}

func (r *clockRepository) FetchClocks(ctx context.Context, filter *domain.ClockFilter, pagination *domain.Pagination) ([]domain.Clock, error) {
	var clocks []domain.Clock

	tx := r.db.WithContext(ctx).Model(&domain.Clock{})

	if filter.UserUUID != "" {
		tx = tx.Joins("JOIN users ON clocks.user_id = users.id").
			Where("users.uuid = ?", filter.UserUUID)
	}

	if filter.Type != "" {
		tx = tx.Where("type = ?", filter.Type)
	}

	if filter.StartDate != "" {
		tx = tx.Where("DATE(clock_time) >= DATE(?)", filter.StartDate)
	}

	if filter.EndDate != "" {
		tx = tx.Where("DATE(clock_time) <= DATE(?)", filter.EndDate)
	}

	err := tx.Count(&pagination.RowsNumber).Error
	if err != nil {
		return nil, err
	}

	tx = pagination.SetPaginationToDB(tx, "clock_time", true)

	err = tx.Preload("User").Find(&clocks).Error
	if err != nil {
		return nil, err
	}
	return clocks, nil
}

func (r *clockRepository) ListClockPairs(ctx context.Context, filter *domain.ClockPairFilter, pagination *domain.Pagination) ([]domain.ClockPairResponse, error) {
	var pairs []domain.ClockPairResponse

	tx := r.db.WithContext(ctx).Model(&domain.ClockPairResponse{})

	if filter.UserUUID != "" {
		tx = tx.Joins("JOIN users ON clock_pairs.user_id = users.id").
			Where("users.uuid = ?", filter.UserUUID)
	}

	if filter.StartDate != "" {
		tx = tx.Where("DATE(clock_pairs.date_at) >= DATE(?)", filter.StartDate)
	}

	if filter.EndDate != "" {
		tx = tx.Where("DATE(clock_pairs.date_at) <= DATE(?)", filter.EndDate)
	}

	if len(filter.Status) > 0 {
		tx = tx.Where("status IN (?)", filter.Status)
	}

	if err := tx.Count(&pagination.RowsNumber).Error; err != nil {
		return nil, err
	}

	tx = pagination.SetPaginationToDB(tx, "clock_pairs.date_at", true)

	err := tx.Preload("User").Preload("ClockIn").Preload("ClockOut").Find(&pairs).Error
	if err != nil {
		return nil, err
	}
	return pairs, nil
}

func (r *clockRepository) FindClockPair(ctx context.Context, userID int64, clockID int64) (*domain.ClockPair, error) {
	var pair domain.ClockPair

	tx := r.db.WithContext(ctx)

	err := tx.Model(&domain.ClockPair{}).
		Where("user_id = ? ", userID).
		Where("clock_in_id = ? OR clock_out_id = ?", clockID, clockID).
		Limit(1).Find(&pair).Error
	if err != nil {
		return nil, err
	}
	return &pair, nil
}

func (r *clockRepository) FindLastClock(ctx context.Context, userID int64) (*domain.Clock, error) {
	var clock domain.Clock

	tx := r.db.WithContext(ctx)

	err := tx.Model(&domain.Clock{}).
		Where("user_id = ?", userID).
		Order("clock_time DESC").
		Limit(1).Find(&clock).Error
	if err != nil {
		return nil, err
	}
	return &clock, nil
}

func (r *clockRepository) CreateClockPair(ctx context.Context, pair *domain.ClockPair) error {
	pair.UUID = utils.GenerateUUID()
	return r.db.WithContext(ctx).Create(pair).Error
}

func (r *clockRepository) UpdateClockPairWithCheckIn(ctx context.Context, pair *domain.ClockPair, clock *domain.Clock) error {
	tx := r.db.WithContext(ctx)

	if err := tx.Model(pair).Update("clock_in_id", clock.ID).Error; err != nil {
		return err
	}

	if pair.ClockOutID.IsZero() {
		return tx.Model(pair).Update("status", domain.ClockPairStatusPending).Error
	}

	return tx.Model(pair).Update("status", domain.ClockPairStatusPaired).Error
}

func (r *clockRepository) UpdateClockPairWithCheckOut(ctx context.Context, pair *domain.ClockPair, clock *domain.Clock) error {
	tx := r.db.WithContext(ctx)

	if err := tx.Model(pair).Update("clock_out_id", clock.ID).Error; err != nil {
		return err
	}

	if pair.ClockInID.IsZero() {
		return tx.Model(pair).Update("status", domain.ClockPairStatusMissing).Error
	}

	return tx.Model(pair).Update("status", domain.ClockPairStatusPaired).Error
}

func (r *clockRepository) MarkMissingPairs(ctx context.Context, pair *domain.ClockPair) error {
	tx := r.db.WithContext(ctx)
	return tx.Model(pair).Updates(map[string]interface{}{
		"status": domain.ClockPairStatusMissing,
	}).Error
}
