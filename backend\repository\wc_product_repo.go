package repository

import (
	"fmt"
	"strconv"
	"strings"

	"gorm.io/gorm"

	"cx/domain"
)

type WcProductRepository interface {
	GetByID(id int64) (*domain.WcProduct, error)
	List(filter domain.WcProductFilter) ([]domain.WcProduct, int, error)
	Create(product *domain.WcProduct) error
	Update(product *domain.WcProduct) error
	UpdateStatus(id int64, status string) error
	UpdateStockQuantity(id int64, stockQuantity int) error
	Delete(id int64) error
	GetProductCategories(productID int64) ([]domain.WcCategory, error)
	SetProductCategories(productID int64, categoryIDs []int64) error
	UpdateProductImages(wcProductID int64, mainImageID int64, galleryImageIDs []int64) error
}

// 產品儲存庫實現
type wcProductRepository struct {
	db *gorm.DB
}

func NewWcProductRepository(db *gorm.DB) WcProductRepository {
	return &wcProductRepository{db: db}
}

func (r *wcProductRepository) GetByID(id int64) (*domain.WcProduct, error) {
	product := &domain.WcProduct{}

	// 從 wp_posts 表獲取基本信息
	query := `
        SELECT 
            p.ID as id,
            p.post_title as name,
            p.post_name as slug,
            p.post_content as description,
            p.post_excerpt as short_description,
            p.post_status as status,
            p.post_date as created_at,
            p.post_modified as updated_at
        FROM 
            wp_posts p
        WHERE 
            p.ID = ? 
            AND p.post_type = 'product'
    `

	if err := r.db.Raw(query, id).Scan(product).Error; err != nil {
		return nil, err
	}

	if product.ID == 0 {
		return nil, fmt.Errorf("product not found")
	}

	// 從 wp_postmeta 獲取產品元數據
	metaQuery := `
        SELECT 
            meta_key, 
            meta_value 
        FROM 
            wp_postmeta 
        WHERE 
            post_id = ?
    `

	type Meta struct {
		MetaKey   string `gorm:"column:meta_key"`
		MetaValue string `gorm:"column:meta_value"`
	}

	var metas []Meta
	if err := r.db.Raw(metaQuery, id).Scan(&metas).Error; err != nil {
		return nil, err
	}

	// 映射元數據到產品結構體
	for _, meta := range metas {
		switch meta.MetaKey {
		case "_sku":
			product.Sku = meta.MetaValue
		case "_price":
			product.Price, _ = strconv.ParseFloat(meta.MetaValue, 64)
		case "_regular_price":
			product.RegularPrice, _ = strconv.ParseFloat(meta.MetaValue, 64)
		case "_sale_price":
			product.SalePrice, _ = strconv.ParseFloat(meta.MetaValue, 64)
		case "_stock":
			stock, _ := strconv.Atoi(meta.MetaValue)
			product.StockQuantity = stock
		case "_stock_status":
			product.StockStatus = meta.MetaValue
		case "_weight":
			product.Weight, _ = strconv.ParseFloat(meta.MetaValue, 64)
		case "_length":
			product.Length, _ = strconv.ParseFloat(meta.MetaValue, 64)
		case "_width":
			product.Width, _ = strconv.ParseFloat(meta.MetaValue, 64)
		case "_height":
			product.Height, _ = strconv.ParseFloat(meta.MetaValue, 64)
		}
	}

	// 獲取產品分類
	categories, err := r.GetProductCategories(id)
	if err != nil {
		return nil, err
	}
	product.Categories = categories

	return product, nil
}

func (r *wcProductRepository) List(filter domain.WcProductFilter) ([]domain.WcProduct, int, error) {
	var products []domain.WcProduct
	var total int64

	// 基本查詢
	baseQuery := `
        SELECT 
            p.ID as id,
            p.post_title as name,
            p.post_name as slug,
            p.post_status as status,
            p.post_date as created_at,
            p.post_modified as updated_at
        FROM 
            wp_posts p
        WHERE 
            p.post_type = 'product'
    `

	countQuery := `
        SELECT COUNT(*) 
        FROM wp_posts p
        WHERE p.post_type = 'product'
    `

	// 構建 WHERE 子句
	whereClause := ""
	args := []interface{}{}

	if filter.Keyword != "" {
		whereClause += " AND (p.post_title LIKE ? OR p.post_content LIKE ?)"
		keyword := "%" + filter.Keyword + "%"
		args = append(args, keyword, keyword)
	}

	if filter.Status != "" {
		whereClause += " AND p.post_status = ?"
		args = append(args, filter.Status)
	}

	if filter.CategoryID != 0 {
		whereClause += ` AND p.ID IN (
            SELECT tr.object_id
            FROM wp_term_relationships tr
            JOIN wp_term_taxonomy tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
            WHERE tt.term_id = ? AND tt.taxonomy = 'product_cat'
        )`
		args = append(args, filter.CategoryID)
	}

	if filter.MinPrice > 0 || filter.MaxPrice > 0 {
		priceJoin := ` JOIN wp_postmeta pm_price ON pm_price.post_id = p.ID AND pm_price.meta_key = '_price' `
		baseQuery = strings.Replace(baseQuery, "FROM wp_posts p", "FROM wp_posts p"+priceJoin, 1)
		countQuery = strings.Replace(countQuery, "FROM wp_posts p", "FROM wp_posts p"+priceJoin, 1)

		if filter.MinPrice > 0 {
			whereClause += " AND CAST(pm_price.meta_value AS DECIMAL(10,2)) >= ? "
			args = append(args, filter.MinPrice)
		}

		if filter.MaxPrice > 0 {
			whereClause += " AND CAST(pm_price.meta_value AS DECIMAL(10,2)) <= ? "
			args = append(args, filter.MaxPrice)
		}
	}

	// 最終查詢
	finalQuery := baseQuery + whereClause + " ORDER BY p.post_date DESC"
	finalCountQuery := countQuery + whereClause

	// 添加分頁
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		finalQuery += " LIMIT ? OFFSET ?"
		args = append(args, filter.PageSize, offset)
	}

	// 執行計數查詢
	if err := r.db.Raw(finalCountQuery, args[:len(args)-2]...).Scan(&total).Error; err != nil {
		return nil, 0, err
	}

	// 執行主查詢
	if err := r.db.Raw(finalQuery, args...).Scan(&products).Error; err != nil {
		return nil, 0, err
	}

	// 填充更多產品資訊
	for i := range products {
		// 獲取產品元數據
		metaQuery := `
            SELECT 
                meta_key, 
                meta_value 
            FROM 
                wp_postmeta 
            WHERE 
                post_id = ? AND 
                meta_key IN ('_sku', '_price', '_regular_price', '_sale_price', '_stock', '_stock_status')
        `

		type Meta struct {
			MetaKey   string `gorm:"column:meta_key"`
			MetaValue string `gorm:"column:meta_value"`
		}

		var metas []Meta
		if err := r.db.Raw(metaQuery, products[i].ID).Scan(&metas).Error; err != nil {
			return nil, 0, err
		}

		for _, meta := range metas {
			switch meta.MetaKey {
			case "_sku":
				products[i].Sku = meta.MetaValue
			case "_price":
				products[i].Price, _ = strconv.ParseFloat(meta.MetaValue, 64)
			case "_regular_price":
				products[i].RegularPrice, _ = strconv.ParseFloat(meta.MetaValue, 64)
			case "_sale_price":
				products[i].SalePrice, _ = strconv.ParseFloat(meta.MetaValue, 64)
			case "_stock":
				stock, _ := strconv.Atoi(meta.MetaValue)
				products[i].StockQuantity = stock
			case "_stock_status":
				products[i].StockStatus = meta.MetaValue
			}
		}
	}

	return products, int(total), nil
}

func (r *wcProductRepository) Create(product *domain.WcProduct) error {
	// 使用事務進行插入
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 1. 插入到 wp_posts 表
		postQuery := `
            INSERT INTO wp_posts (
                post_author, post_date, post_date_gmt, post_content, 
                post_title, post_excerpt, post_status, comment_status, 
                ping_status, post_name, to_ping, pinged, post_modified, post_modified_gmt, 
                post_content_filtered, post_parent, menu_order, post_type
            ) VALUES (
                1, NOW(), NOW(), ?, 
				?, ?, ?, 'open', 
                'closed', ?, '', '', NOW(), NOW(), 
                '', 0, 0, 'product'
            )
        `

		result := tx.Exec(
			postQuery,
			product.Description,      // post_content
			product.Name,             // post_title
			product.ShortDescription, // post_excerpt
			product.Status,           // post_status
			product.Slug,             // post_name
		)

		if result.Error != nil {
			return result.Error
		}

		// 獲取新插入產品的 ID
		var postID int64
		tx.Raw("SELECT LAST_INSERT_ID()").Scan(&postID)
		product.ID = postID

		// 2. 插入產品元數據
		metaInsertQuery := `
            INSERT INTO wp_postmeta (post_id, meta_key, meta_value) VALUES (?, ?, ?)
        `

		price := product.RegularPrice
		isSale := false
		if product.SalePrice > 0 {
			isSale = true
			price = product.SalePrice
		}

		// 插入各種元數據
		metaData := []domain.WpMetaData{
			{Key: "_sku", Value: product.Sku},
			{Key: "_price", Value: fmt.Sprintf("%.2f", price)},
			{Key: "_regular_price", Value: fmt.Sprintf("%.2f", product.RegularPrice)},
			{Key: "_stock", Value: strconv.Itoa(product.StockQuantity)},
			{Key: "_stock_status", Value: product.StockStatus},
			{Key: "_weight", Value: fmt.Sprintf("%.2f", product.Weight)},
			{Key: "_length", Value: fmt.Sprintf("%.2f", product.Length)},
			{Key: "_width", Value: fmt.Sprintf("%.2f", product.Width)},
			{Key: "_height", Value: fmt.Sprintf("%.2f", product.Height)},
			{Key: "_visibility", Value: "visible"},
			{Key: "_wc_review_count", Value: "0"},
			{Key: "_wc_rating_count", Value: "0"},
			{Key: "_wc_average_rating", Value: "0"},
			{Key: "_tax_status", Value: "taxable"},
			{Key: "_tax_class", Value: ""},
			{Key: "_manage_stock", Value: "yes"},
			{Key: "_backorders", Value: "no"},
			{Key: "_sold_individually", Value: "no"},
			{Key: "_virtual", Value: "no"},
			{Key: "_downloadable", Value: "no"},
			{Key: "_download_limit", Value: "-1"},
			{Key: "_download_expiry", Value: "-1"},
			{Key: "total_sales", Value: "0"},
			{Key: "_product_version", Value: "7.0.0"},
		}

		if isSale {
			metaData = append(metaData, domain.WpMetaData{Key: "_sale_price", Value: fmt.Sprintf("%.2f", product.SalePrice)})
		}

		for _, meta := range metaData {
			if err := tx.Exec(metaInsertQuery, postID, meta.Key, meta.Value).Error; err != nil {
				return err
			}
		}

		// 3. 設置產品分類關係
		if len(product.Categories) > 0 {
			categoryIDs := make([]int64, len(product.Categories))
			for i, cat := range product.Categories {
				categoryIDs[i] = cat.ID
			}

			if err := r.SetProductCategories(postID, categoryIDs); err != nil {
				return err
			}
		}

		return nil
	})
}

func (r *wcProductRepository) Update(product *domain.WcProduct) error {
	// 使用事務進行更新
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 1. 更新 wp_posts 表
		postQuery := `
            UPDATE wp_posts SET
                post_title = ?,
                post_content = ?,
                post_excerpt = ?,
                post_name = ?,
                post_status = ?,
                post_modified = NOW(),
                post_modified_gmt = NOW()
            WHERE ID = ? AND post_type = 'product'
        `

		result := tx.Exec(
			postQuery,
			product.Name,             // post_title
			product.Description,      // post_content
			product.ShortDescription, // post_excerpt/
			product.Slug,             // post_name
			product.Status,           // post_status
			product.ID,               // ID
		)

		if result.Error != nil {
			return result.Error
		}

		stockStatus := "instock"
		if product.StockQuantity <= 0 {
			stockStatus = "outofstock"
		}

		// 2. 更新產品元數據
		price := product.RegularPrice
		isSale := false
		if product.SalePrice > 0 {
			isSale = true
			price = product.SalePrice
		}

		metaData := []domain.WpMetaData{
			{Key: "_sku", Value: product.Sku},
			{Key: "_price", Value: fmt.Sprintf("%.2f", price)},
			{Key: "_regular_price", Value: fmt.Sprintf("%.2f", product.RegularPrice)},
			{Key: "_stock", Value: strconv.Itoa(product.StockQuantity)},
			{Key: "_stock_status", Value: stockStatus},
			{Key: "_weight", Value: fmt.Sprintf("%.2f", product.Weight)},
			{Key: "_length", Value: fmt.Sprintf("%.2f", product.Length)},
			{Key: "_width", Value: fmt.Sprintf("%.2f", product.Width)},
			{Key: "_height", Value: fmt.Sprintf("%.2f", product.Height)},
		}

		if isSale {
			metaData = append(metaData, domain.WpMetaData{Key: "_sale_price", Value: fmt.Sprintf("%.2f", product.SalePrice)})
		} else {
			// 刪除特價
			if err := tx.Exec(
				"DELETE FROM wp_postmeta WHERE post_id = ? AND meta_key = ?",
				product.ID,
				"_sale_price",
			).Error; err != nil {
				return err
			}
		}

		for _, meta := range metaData {
			// 先檢查是否存在
			var count int64
			tx.Raw(
				"SELECT COUNT(*) FROM wp_postmeta WHERE post_id = ? AND meta_key = ?",
				product.ID,
				meta.Key,
			).Scan(&count)

			if count > 0 {
				// 存在則更新
				if err := tx.Exec(
					"UPDATE wp_postmeta SET meta_value = ? WHERE post_id = ? AND meta_key = ?",
					meta.Value,
					product.ID,
					meta.Key,
				).Error; err != nil {
					return err
				}
			} else {
				// 不存在則插入
				if err := tx.Exec(
					"INSERT INTO wp_postmeta (post_id, meta_key, meta_value) VALUES (?, ?, ?)",
					product.ID,
					meta.Key,
					meta.Value,
				).Error; err != nil {
					return err
				}
			}
		}

		// 3. 更新產品分類關係
		if len(product.Categories) > 0 {
			categoryIDs := make([]int64, len(product.Categories))
			for i, cat := range product.Categories {
				categoryIDs[i] = cat.ID
			}

			if err := r.SetProductCategories(product.ID, categoryIDs); err != nil {
				return err
			}
		}

		return nil
	})
}

func (r *wcProductRepository) UpdateStatus(id int64, status string) error {
	// 只更新產品狀態，不影響其他欄位
	query := `
		UPDATE wp_posts SET
			post_status = ?,
			post_modified = NOW(),
			post_modified_gmt = NOW()
		WHERE ID = ? AND post_type = 'product'
	`

	result := r.db.Exec(query, status, id)
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("product not found or no changes made")
	}

	return nil
}

func (r *wcProductRepository) UpdateStockQuantity(id int64, stockQuantity int) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 更新庫存量
		if err := tx.Exec(
			"UPDATE wp_postmeta SET meta_value = ? WHERE post_id = ? AND meta_key = '_stock'",
			stockQuantity,
			id,
		).Error; err != nil {
			return err
		}

		fmt.Println("_stock:", stockQuantity)

		// 更新庫存狀態
		stockStatus := "instock"
		if stockQuantity <= 0 {
			stockStatus = "outofstock"
		}

		if err := tx.Exec(
			"UPDATE wp_postmeta SET meta_value = ? WHERE post_id = ? AND meta_key = '_stock_status'",
			stockStatus,
			id,
		).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *wcProductRepository) Delete(id int64) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 1. 刪除產品元數據
		if err := tx.Exec("DELETE FROM wp_postmeta WHERE post_id = ?", id).Error; err != nil {
			return err
		}

		// 2. 刪除產品與分類的關聯
		if err := tx.Exec(
			`DELETE tr FROM wp_term_relationships tr
			 JOIN wp_term_taxonomy tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
			 WHERE tr.object_id = ? AND tt.taxonomy = 'product_cat'`,
			id,
		).Error; err != nil {
			return err
		}

		// 3. 刪除產品主記錄（或將其標記為已刪除）
		// 可以選擇真正刪除或標記為 trash
		if err := tx.Exec(
			"UPDATE wp_posts SET post_status = 'trash' WHERE ID = ? AND post_type = 'product'",
			id,
		).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *wcProductRepository) GetProductCategories(productID int64) ([]domain.WcCategory, error) {
	var categories []domain.WcCategory

	query := `
        SELECT 
            t.term_id as id,
            t.name as name,
            t.slug as slug,
            tt.description as description,
            tt.parent as parent_id,
            tt.count as count
        FROM 
            wp_terms t
        JOIN 
            wp_term_taxonomy tt ON t.term_id = tt.term_id
        JOIN 
            wp_term_relationships tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
        WHERE 
            tr.object_id = ? 
            AND tt.taxonomy = 'product_cat'
    `

	if err := r.db.Raw(query, productID).Scan(&categories).Error; err != nil {
		return nil, err
	}

	return categories, nil
}

func (r *wcProductRepository) SetProductCategories(productID int64, categoryIDs []int64) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 1. 刪除現有的分類關聯
		if err := tx.Exec(
			`DELETE tr FROM wp_term_relationships tr
			 JOIN wp_term_taxonomy tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
			 WHERE tr.object_id = ? AND tt.taxonomy = 'product_cat'`,
			productID,
		).Error; err != nil {
			return err
		}

		// 2. 插入新的分類關聯
		for _, catID := range categoryIDs {
			// 獲取 term_taxonomy_id
			var termTaxonomyID uint
			if err := tx.Raw(
				"SELECT term_taxonomy_id FROM wp_term_taxonomy WHERE term_id = ? AND taxonomy = 'product_cat'",
				catID,
			).Scan(&termTaxonomyID).Error; err != nil {
				return err
			}

			if termTaxonomyID == 0 {
				continue // 跳過無效的分類
			}

			// 插入關聯
			if err := tx.Exec(
				"INSERT INTO wp_term_relationships (object_id, term_taxonomy_id, term_order) VALUES (?, ?, 0)",
				productID,
				termTaxonomyID,
			).Error; err != nil {
				return err
			}

			// 更新分類計數
			if err := tx.Exec(
				"UPDATE wp_term_taxonomy SET count = count + 1 WHERE term_taxonomy_id = ?",
				termTaxonomyID,
			).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

func (r *wcProductRepository) UpdateProductImages(wcProductID int64, mainImageID int64, galleryImageIDs []int64) error {
	// 更新主要商品圖片
	mainImageMeta := map[string]interface{}{
		"post_id":    wcProductID,
		"meta_key":   "_thumbnail_id",
		"meta_value": fmt.Sprintf("%d", mainImageID),
	}

	// 先檢查是否已存在，決定新增或更新
	var count int64
	if err := r.db.Table("wp_postmeta").Where("post_id = ? AND meta_key = ?", wcProductID, "_thumbnail_id").Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		if err := r.db.Table("wp_postmeta").Where("post_id = ? AND meta_key = ?", wcProductID, "_thumbnail_id").
			Update("meta_value", fmt.Sprintf("%d", mainImageID)).Error; err != nil {
			return err
		}
	} else {
		if err := r.db.Table("wp_postmeta").Create(mainImageMeta).Error; err != nil {
			return err
		}
	}

	// 更新商品圖庫
	if len(galleryImageIDs) > 0 {
		// 將圖庫ID轉換為逗號分隔的字符串
		var galleryIDsStr []string
		for _, id := range galleryImageIDs {
			galleryIDsStr = append(galleryIDsStr, fmt.Sprintf("%d", id))
		}
		galleryValue := strings.Join(galleryIDsStr, ",")

		// 檢查圖庫是否已存在
		if err := r.db.Table("wp_postmeta").Where("post_id = ? AND meta_key = ?", wcProductID, "_product_image_gallery").Count(&count).Error; err != nil {
			return err
		}

		if count > 0 {
			if err := r.db.Table("wp_postmeta").Where("post_id = ? AND meta_key = ?", wcProductID, "_product_image_gallery").
				Update("meta_value", galleryValue).Error; err != nil {
				return err
			}
		} else {
			galleryMeta := map[string]interface{}{
				"post_id":    wcProductID,
				"meta_key":   "_product_image_gallery",
				"meta_value": galleryValue,
			}
			if err := r.db.Table("wp_postmeta").Create(galleryMeta).Error; err != nil {
				return err
			}
		}
	}

	return nil
}
