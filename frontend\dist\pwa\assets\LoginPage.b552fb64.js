import{d as Q,u as x,r as n,aF as V,aG as w,o as q,k as S,f as t,b as l,z as h,y as A,i as I,t as C,q as s,Q as f,h as k,aH as b,aI as z,m as L,A as P,v as B}from"./index.9477d5a3.js";import{Q as M}from"./QForm.534886fc.js";import{Q as U}from"./QPage.8bf63692.js";const F={class:"text-h6"},G=Q({name:"LoginPage",__name:"LoginPage",setup(N){const{t:o}=x(),r=n(""),m=n(""),i=n(!0),u=n(!1),c=n(!1),p=V(),g=w(),v=localStorage.getItem("remembered_account");v&&(r.value=v,u.value=!0);const _=async()=>{c.value=!0;try{const d=await P.login({username:r.value,password:m.value});u.value?localStorage.setItem("remembered_account",r.value):localStorage.removeItem("remembered_account"),g.login(d.result),g.isAdmin()?p.push("/admin/dashboard/user"):p.push("/order")}catch(d){B(d)}finally{c.value=!1}},y=()=>{console.log("rememberMe:",u.value)};return(d,a)=>(q(),S(U,null,{default:t(()=>[l(M,{onSubmit:_,class:"q-py-lg"},{default:t(()=>[l(h,{class:"q-mx-auto q-py-lg q-px-md",style:{"max-width":"min(100%, 28rem)"}},{default:t(()=>[l(A,{class:"q-gutter-md"},{default:t(()=>[I("div",F,C(s(o)("login")),1),l(f,{type:"text",modelValue:r.value,"onUpdate:modelValue":a[0]||(a[0]=e=>r.value=e),label:s(o)("account"),outlined:"",rules:[e=>!!e||s(o)("error.required")],"lazy-rules":""},null,8,["modelValue","label","rules"]),l(f,{type:i.value?"password":"text",modelValue:m.value,"onUpdate:modelValue":a[2]||(a[2]=e=>m.value=e),label:s(o)("password"),outlined:"",rules:[e=>!!e||s(o)("error.required")],"lazy-rules":""},{append:t(()=>[l(k,{name:i.value?"visibility_off":"visibility",class:"cursor-pointer",onClick:a[1]||(a[1]=e=>i.value=!i.value)},null,8,["name"])]),_:1},8,["type","modelValue","label","rules"])]),_:1}),l(b,{align:"between"},{default:t(()=>[l(z,{modelValue:u.value,"onUpdate:modelValue":a[3]||(a[3]=e=>u.value=e),onUpdate:y,label:s(o)("rememberMe"),size:"lg",color:"toggle",dense:"","keep-color":""},null,8,["modelValue","label"])]),_:1}),l(b,null,{default:t(()=>[l(L,{type:"submit",rounded:"",loading:c.value,label:s(o)("login"),class:"full-width q-mt-sm",color:"login","text-color":"login",size:"lg",ripple:{center:!0}},null,8,["loading","label"])]),_:1})]),_:1})]),_:1})]),_:1}))}});export{G as default};
