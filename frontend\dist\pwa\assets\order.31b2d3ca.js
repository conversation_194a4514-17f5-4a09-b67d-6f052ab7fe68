import{c as a,by as e}from"./index.9477d5a3.js";const c=a(()=>[{label:e.global.t("orderVoided.reasons.outOfStock"),value:"Out of Stock"},{label:e.global.t("orderVoided.reasons.duplicateOrder"),value:"Duplicate Order"},{label:e.global.t("orderVoided.reasons.incorrectInfo"),value:"Incorrect Information"},{label:e.global.t("orderVoided.reasons.customerReject"),value:"Customer Reject"},{label:e.global.t("orderVoided.reasons.other"),value:"other"}]),n=a(()=>[{label:e.global.t("orderStatus.processing"),value:"processing"},{label:e.global.t("orderStatus.completed"),value:"completed"},{label:e.global.t("orderStatus.cancelled"),value:"cancelled"},{label:e.global.t("orderStatus.void"),value:"void"}]),t=a(()=>({pending:{textColor:"black",label:e.global.t("orderStatus.pending")},"on-hold":{textColor:"black",label:e.global.t("orderStatus.on-hold")},processing:{textColor:"black",label:e.global.t("orderStatus.processing")},packing:{textColor:"black",label:e.global.t("orderStatus.packing")},shipping:{textColor:"black",label:e.global.t("orderStatus.shipping")},completed:{textColor:"black",label:e.global.t("orderStatus.completed")},cancelled:{color:"bg-red-1",textColor:"red-2",label:e.global.t("orderStatus.cancelled")},void:{color:"bg-red-1",textColor:"red-2",label:e.global.t("orderStatus.void")}})),r={cash:"green",credit:"blue",paypal:"purple"},d=a(()=>({cash:e.global.t("payment.cash"),credit:e.global.t("payment.creditCard"),paypal:e.global.t("payment.paypal")})),b=l=>{var o;return((o=t.value[l])==null?void 0:o.color)||""},g=l=>{var o;return((o=t.value[l])==null?void 0:o.label)||l},u=l=>r[l]||"grey",p=l=>d.value[l]||l;export{u as a,b,g as c,p as g,n as o,c as v};
