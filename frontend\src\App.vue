<template>
  <router-view />
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { Notify } from 'quasar';
import { versionChecker } from './services/versionChecker';

const router = useRouter();

// 更新管理器 - 簡化版本，只在路由切換時檢查版本
const updateManager = {
  init() {
    // 監聽版本檢查事件
    window.addEventListener('version-update-detected', () => {
      this.handleVersionUpdate();
    });

    // 監聽路由變化，在路由切換時檢查版本
    router.afterEach(() => {
      this.checkVersionOnRouteChange();
    });
  },

  // 路由切換時檢查版本
  async checkVersionOnRouteChange() {
    // 在開發環境中跳過
    if (process.env.DEV) {
      return;
    }

    // 檢查是否為第一次載入
    const isFirstLoad = !sessionStorage.getItem('app-session-started');
    if (isFirstLoad) {
      return;
    }

    try {
      const hasUpdate = await versionChecker.manualVersionCheck();
      if (hasUpdate) {
        this.performSilentUpdate();
      }
    } catch (error) {
      console.error('路由切換版本檢查失敗:', error);
    }
  },

  // 處理版本更新
  handleVersionUpdate() {
    // 檢查是否為第一次載入
    const isFirstLoad = !sessionStorage.getItem('app-session-started');
    if (isFirstLoad) {
      // 第一次載入，忽略版本更新檢查
      return;
    }

    // 直接執行靜默更新
    this.performSilentUpdate();
  },

  performSilentUpdate() {
    // 顯示更新中的通知
    this.showUpdatingNotification();

    // 延遲1秒後執行更新
    setTimeout(async () => {
      try {
        await versionChecker.triggerUpdate();
        // 更新完成後顯示成功通知
        this.showUpdateCompletedNotification();
      } catch (error) {
        console.error('自動更新失敗:', error);
        // 如果更新失敗，直接刷新頁面
        window.location.reload();
      }
    }, 1000);
  },

  showUpdatingNotification() {
    Notify.create({
      message: '正在更新到最新版本...',
      icon: 'cloud_download',
      color: 'primary',
      position: 'top-right',
      timeout: 2000,
      spinner: true
    });
  },

  showUpdateCompletedNotification() {
    Notify.create({
      message: '已更新到最新版本',
      icon: 'check_circle',
      color: 'positive',
      position: 'top-right',
      timeout: 3000
    });
  }
};

// 在應用啟動時初始化
onMounted(() => {
  // 初始化更新管理器
  updateManager.init();

  // 標記會話已開始
  sessionStorage.setItem('app-session-started', 'true');

  // 監聽來自 Service Worker 的消息
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && (event.data.type === 'RELOAD_PAGE' || event.data.type === 'FORCE_RELOAD')) {
        // 檢查是否為第一次載入
        const isFirstLoad = !sessionStorage.getItem('app-session-started');
        if (isFirstLoad) {
          // 第一次載入，忽略更新消息
          return;
        }

        // 直接執行靜默更新
        updateManager.performSilentUpdate();
      }
    });

    // 監聽 Service Worker 狀態變化
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      // 檢查是否為第一次載入
      const isFirstLoad = !sessionStorage.getItem('app-session-started');
      if (isFirstLoad) {
        // 第一次載入，忽略控制器變化
        return;
      }

      // 直接執行靜默更新
      updateManager.performSilentUpdate();
    });
  }
});

// 清理資源
onUnmounted(() => {
  versionChecker.destroy();
});

defineOptions({
  name: 'App'
});
</script>
