package service

import (
	"bytes"
	"context"
	"cx/domain"
	"cx/repository"
	"cx/service/email"
	"cx/utils"
	"fmt"
	"strings"
	"text/template"
	"time"

	"gorm.io/gorm"
)

type PayrollService interface {
	ListPeriods(ctx context.Context, filter *domain.PayrollPeriodFilter, pagination *domain.Pagination) ([]domain.PayrollPeriodResponse, error)
	GetPeriodByUUID(ctx context.Context, uuid string) (*domain.PayrollPeriodResponse, error)
	CreatePeriod(ctx context.Context, payload *domain.PayrollPeriodCreatePayload) error
	UpdatePeriod(ctx context.Context, payload *domain.PayrollPeriodUpdatePayload) error
	DeletePeriod(ctx context.Context, uuid string) error

	ListPayroll(ctx context.Context, filter *domain.PayrollFilter, pagination *domain.Pagination) ([]domain.PayrollResponse, error)
	GetPayrollByUUID(ctx context.Context, uuid string) (*domain.PayrollResponse, error)
	CreatePayroll(ctx context.Context, payload *domain.PayrollCreatePayload) error
	UpdatePayroll(ctx context.Context, payload *domain.PayrollUpdatePayload) error

	SendMail(ctx context.Context, req *domain.PayrollEmailCreateRequest) error
}

type payrollService struct {
	db               *gorm.DB
	userRepo         repository.UserRepository
	payrollRepo      repository.PayrollRepository
	payrollEmailRepo repository.PayrollEmailRepository
}

func NewPayrollService(
	db *gorm.DB,
	userRepo repository.UserRepository,
	payrollRepo repository.PayrollRepository,
	payrollEmailRepo repository.PayrollEmailRepository,
) PayrollService {
	return &payrollService{
		db:               db,
		userRepo:         userRepo,
		payrollRepo:      payrollRepo,
		payrollEmailRepo: payrollEmailRepo,
	}
}

func (s *payrollService) ListPeriods(ctx context.Context, filter *domain.PayrollPeriodFilter, pagination *domain.Pagination) ([]domain.PayrollPeriodResponse, error) {
	return s.payrollRepo.ListPeriods(ctx, filter, pagination)
}

func (s *payrollService) GetPeriodByUUID(ctx context.Context, uuid string) (*domain.PayrollPeriodResponse, error) {
	return s.payrollRepo.GetPeriodByUUID(ctx, uuid)
}

func (s *payrollService) CreatePeriod(ctx context.Context, payload *domain.PayrollPeriodCreatePayload) error {
	return s.payrollRepo.CreatePeriod(ctx, payload)
}

func (s *payrollService) UpdatePeriod(ctx context.Context, payload *domain.PayrollPeriodUpdatePayload) error {
	return s.payrollRepo.UpdatePeriod(ctx, payload)
}

func (s *payrollService) DeletePeriod(ctx context.Context, uuid string) error {
	return s.payrollRepo.DeletePeriod(ctx, uuid)
}

func (s *payrollService) ListPayroll(ctx context.Context, filter *domain.PayrollFilter, pagination *domain.Pagination) ([]domain.PayrollResponse, error) {
	return s.payrollRepo.List(ctx, filter, pagination)
}

func (s *payrollService) GetPayrollByUUID(ctx context.Context, uuid string) (*domain.PayrollResponse, error) {
	return s.payrollRepo.GetByUUID(ctx, uuid)
}

func (s *payrollService) CreatePayroll(ctx context.Context, payload *domain.PayrollCreatePayload) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		payrollRepo := repository.NewPayrollRepository(tx)
		userRepo := repository.NewUserRepository(tx)

		// 1. 檢查人員是否存在
		user, err := userRepo.GetByUUID(ctx, payload.UserUUID)
		if err != nil {
			return err
		}

		// 2. 檢查薪資期間是否存在
		period, err := payrollRepo.GetPeriodByUUID(ctx, payload.PeriodUUID)
		if err != nil {
			return err
		}

		payload.PeriodID = period.ID
		payload.UserID = user.ID
		err = payrollRepo.Create(ctx, payload)
		if err != nil {
			return err
		}

		payload.PayrollDetails = s.handlePayrollDetail(payload.ID, payload.PayrollDetails)

		return payrollRepo.SaveDetails(ctx, payload.PayrollDetails)
	})
}

func (s *payrollService) UpdatePayroll(ctx context.Context, payload *domain.PayrollUpdatePayload) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		payrollRepo := repository.NewPayrollRepository(tx)

		// 1. 檢查是否存在
		payroll, err := payrollRepo.GetByUUID(ctx, payload.UUID)
		if err != nil {
			return err
		}

		payload.ID = payroll.ID
		if err := payrollRepo.Update(ctx, payload); err != nil {
			return err
		}

		payload.PayrollDetails = s.handlePayrollDetail(payload.ID, payload.PayrollDetails)

		return payrollRepo.SaveDetails(ctx, payload.PayrollDetails)
	})
}

func (s *payrollService) handlePayrollDetail(payrollID int64, details []domain.PayrollDetail) []domain.PayrollDetail {
	payrollDetails := []domain.PayrollDetail{}

	for _, detail := range details {
		if detail.UUID == "" {
			detail.UUID = utils.GenerateUUID()
		}

		if detail.PayrollID == 0 {
			detail.PayrollID = payrollID
		}

		if detail.SalaryItemID == 0 {
			detail.SalaryItemID = detail.SalaryItem.ID
		}

		payrollDetails = append(payrollDetails, detail)
	}

	return payrollDetails
}

func (s *payrollService) SendMail(ctx context.Context, req *domain.PayrollEmailCreateRequest) error {
	systemRepo := repository.NewSystemRepository(s.db)
	sysOpt, err := systemRepo.Get(ctx)
	if err != nil {
		return err
	}

	if sysOpt.Gmail == "" || sysOpt.GmailAppPassword == "" {
		return fmt.Errorf("system email configuration is not set")
	}

	mails := []domain.PayrollEmailCreatePayload{}
	for _, userUUID := range req.UserUUIDs {
		user, err := s.userRepo.GetByUUID(ctx, userUUID)
		if err != nil {
			return err
		}

		if user.Email == "" {
			continue
		}

		mails = append(mails, domain.PayrollEmailCreatePayload{
			UUID:   utils.GenerateUUID(),
			UserID: user.ID,
			User:   user,
			Email:  user.Email,
		})
	}

	if len(mails) == 0 {
		return nil
	}

	for _, mail := range mails {
		// 取得薪資單
		payroll, err := s.payrollRepo.GetByUserID(ctx, mail.UserID, req.PeriodUUID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				continue
			} else {
				return fmt.Errorf("wrong to get payroll: %w", err)
			}
		}
		mail.PayrollID = payroll.ID

		// 生成郵件內容
		subject, body, err := s.generateEmailContent(payroll, mail.User)
		if err != nil {
			return fmt.Errorf("wrong to generate email content: %w", err)
		}

		mail.Subject = subject
		mail.Body = body

		// 寄信
		if err := email.SendGmail(&email.GmailConfig{
			FromEmail: sysOpt.Gmail,
			Password:  sysOpt.GmailAppPassword,
		}, []string{mail.Email}, mail.Subject, mail.Body); err != nil {
			mail.Status = domain.EmailStatusFailed
		} else {
			mail.Status = domain.EmailStatusSent
		}

		// 儲存寄信結果
		mail.SentAt = time.Now()
		if err := s.payrollEmailRepo.Create(ctx, &mail); err != nil {
			return fmt.Errorf("wrong to save payroll email: %w", err)
		}

	}

	return nil
}

// 生成郵件內容
func (s *payrollService) generateEmailContent(payroll *domain.PayrollResponse, user *domain.User) (string, string, error) {
	// 整理薪資項目
	var incomeItems, deductionItems, bonusItems []domain.PayrollDetailView

	for _, detail := range payroll.PayrollDetails {
		if detail.Amount == 0 {
			continue
		}

		item := domain.PayrollDetailView{
			Name:   s.formatSalaryItemName(detail.SalaryItem.Name),
			Amount: detail.Amount,
		}

		switch detail.SalaryItem.Type {
		case domain.SalaryItemTypeIncome:
			incomeItems = append(incomeItems, item)
		case domain.SalaryItemTypeDeduction:
			deductionItems = append(deductionItems, item)
		case domain.SalaryItemTypeBonus:
			bonusItems = append(bonusItems, item)
		}
	}

	// 準備模板資料
	startDate, _ := utils.FormatDateString(payroll.Period.StartDate, time.RFC3339, "2006-01-02")
	endDate, _ := utils.FormatDateString(payroll.Period.EndDate, time.RFC3339, "2006-01-02")
	templateData := domain.PayrollEmailTemplate{
		UserName:        user.Name,
		PayrollPeriod:   fmt.Sprintf("%s ~ %s", startDate, endDate),
		PayDate:         payroll.PayDate,
		SalaryType:      s.getSalaryTypeText(payroll.SalaryType),
		BasicSalary:     payroll.BasicSalary,
		HourlySalary:    payroll.HourlySalary,
		WorkDays:        payroll.WorkDays,
		WorkHours:       payroll.WorkHours,
		OvertimeHours:   payroll.OvertimeHours,
		IncomeItems:     incomeItems,
		DeductionItems:  deductionItems,
		BonusItems:      bonusItems,
		GrossSalary:     payroll.GrossSalary,
		TotalDeductions: payroll.TotalDeductions,
		NetSalary:       payroll.NetSalary,
		Notes:           payroll.Notes,
	}

	subject := fmt.Sprintf("Payroll Notification - %s (%s)", templateData.PayrollPeriod, user.Name)

	// 使用HTML模板生成郵件內容
	body, err := s.renderEmailTemplate(&templateData)
	if err != nil {
		return "", "", err
	}

	return subject, body, nil
}

// 將name改為單詞開頭大寫，下底線替換為空格
func (s *payrollService) formatSalaryItemName(name string) string {
	// 將下底線替換為空格
	name = strings.ReplaceAll(name, "_", " ")

	// 分割字串為單詞
	words := strings.Split(name, " ")

	// 將每個單詞首字母大寫
	for i, word := range words {
		if len(word) > 0 {
			words[i] = strings.ToUpper(word[:1]) + strings.ToLower(word[1:])
		}
	}

	// 重新組合字串
	return strings.Join(words, " ")
}

// 取得薪資類型文字
func (s *payrollService) getSalaryTypeText(salaryType domain.SalaryType) string {
	switch salaryType {
	case domain.SalaryTypeMonthly:
		return "Salary"
	case domain.SalaryTypeHourly:
		return "Hourly Wage"
	default:
		return "Unknown"
	}
}

// 格式化金額，加入千分位逗號並顯示小數點後兩位
func formatCurrency(amount float64) string {
	// 將數字轉為小數點後兩位的字串
	str := fmt.Sprintf("%.2f", amount)

	// 分離整數部分和小數部分
	parts := strings.Split(str, ".")
	integerPart := parts[0]
	decimalPart := parts[1]

	// 處理負數符號
	negative := false
	if integerPart[0] == '-' {
		negative = true
		integerPart = integerPart[1:]
	}

	// 為整數部分加入千分位逗號
	n := len(integerPart)
	if n <= 3 {
		// 三位以下直接返回
		if negative {
			return "-" + integerPart + "." + decimalPart
		}
		return integerPart + "." + decimalPart
	}

	// 從右到左每三位加逗號
	var result strings.Builder
	for i, digit := range integerPart {
		if i > 0 && (n-i)%3 == 0 {
			result.WriteByte(',')
		}
		result.WriteRune(digit)
	}

	// 組合最終結果
	finalResult := result.String() + "." + decimalPart
	if negative {
		return "-" + finalResult
	}
	return finalResult
}

// 渲染郵件模板
func (s *payrollService) renderEmailTemplate(data *domain.PayrollEmailTemplate) (string, error) {
	// 建立自定義函數映射
	funcMap := template.FuncMap{
		"formatCurrency": formatCurrency,
	}

	tmpl := `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Payroll Statement</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .section { margin-bottom: 20px; }
        .section h3 { color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; table-layout: fixed; }
        th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .item-col { width: 70%; }
        .amount-col { width: 30%; }
        .amount { text-align: right; font-family: 'Courier New', monospace; white-space: nowrap; }
        .total-row { background-color: #e9ecef; font-weight: bold; }
        .net-salary { background-color: #d4edda; color: #155724; font-size: 1.1em; }
        .currency-amount { display: inline-block; min-width: 120px; text-align: right; }
        .unit-amount { display: inline-block; min-width: 80px; text-align: right; }
    </style>
</head>
<body>
    <div class="header">
        <h2>Payroll Statement</h2>
        <p><strong>Employee Name:</strong> {{.UserName}}</p>
        <p><strong>Payroll Period:</strong> {{.PayrollPeriod}}</p>
        <p><strong>Salary Type:</strong> {{.SalaryType}}</p>
    </div>

    <div class="section">
        <h3>Basic Information</h3>
        <table>
            <colgroup>
                <col class="item-col">
                <col class="amount-col">
            </colgroup>
            {{if ne .BasicSalary 0.0}}
            <tr><td>Basic Salary</td><td class="amount"><span class="currency-amount">AU$ {{formatCurrency .BasicSalary}}</span></td></tr>
            {{end}}
            {{if ne .HourlySalary 0.0}}
            <tr><td>Hourly Rate</td><td class="amount"><span class="currency-amount">AU$ {{formatCurrency .HourlySalary}}</span></td></tr>
            {{end}}
            <tr><td>Work Days</td><td class="amount"><span class="unit-amount">{{.WorkDays}} days</span></td></tr>
            <tr><td>Work Hours</td><td class="amount"><span class="unit-amount">{{.WorkHours}} hours</span></td></tr>
            {{if ne .OvertimeHours 0}}
            <tr><td>Overtime Hours</td><td class="amount"><span class="unit-amount">{{.OvertimeHours}} hours</span></td></tr>
            {{end}}
        </table>
    </div>

    {{if .IncomeItems}}
    <div class="section">
        <h3>Income Items</h3>
        <table>
            <colgroup>
                <col class="item-col">
                <col class="amount-col">
            </colgroup>
            <thead>
                <tr><th>Item</th><th class="amount">Amount</th></tr>
            </thead>
            <tbody>
                {{range .IncomeItems}}
                <tr><td>{{.Name}}</td><td class="amount"><span class="currency-amount">AU$ {{formatCurrency .Amount}}</span></td></tr>
                {{end}}
            </tbody>
        </table>
    </div>
    {{end}}

    {{if .BonusItems}}
    <div class="section">
        <h3>Bonus Items</h3>
        <table>
            <colgroup>
                <col class="item-col">
                <col class="amount-col">
            </colgroup>
            <thead>
                <tr><th>Item</th><th class="amount">Amount</th></tr>
            </thead>
            <tbody>
                {{range .BonusItems}}
                <tr><td>{{.Name}}</td><td class="amount"><span class="currency-amount">AU$ {{formatCurrency .Amount}}</span></td></tr>
                {{end}}
            </tbody>
        </table>
    </div>
    {{end}}

    {{if .DeductionItems}}
    <div class="section">
        <h3>Deduction Items</h3>
        <table>
            <colgroup>
                <col class="item-col">
                <col class="amount-col">
            </colgroup>
            <thead>
                <tr><th>Item</th><th class="amount">Amount</th></tr>
            </thead>
            <tbody>
                {{range .DeductionItems}}
                <tr><td>{{.Name}}</td><td class="amount"><span class="currency-amount">-AU$ {{formatCurrency .Amount}}</span></td></tr>
                {{end}}
            </tbody>
        </table>
    </div>
    {{end}}

    <div class="section">
        <h3>Salary Summary</h3>
        <table>
            <colgroup>
                <col class="item-col">
                <col class="amount-col">
            </colgroup>
            <tr><td>Gross Salary</td><td class="amount"><span class="currency-amount">AU$ {{formatCurrency .GrossSalary}}</span></td></tr>
            <tr><td>Total Deductions</td><td class="amount"><span class="currency-amount">-AU$ {{formatCurrency .TotalDeductions}}</span></td></tr>
            <tr class="total-row net-salary"><td>Net Salary</td><td class="amount"><span class="currency-amount">AU$ {{formatCurrency .NetSalary}}</span></td></tr>
        </table>
    </div>

    {{if .Notes}}
    <div class="section">
        <h3>Notes</h3>
        <p>{{.Notes}}</p>
    </div>
    {{end}}
</body>
</html>`

	t, err := template.New("payroll_email").Funcs(funcMap).Parse(tmpl)
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	err = t.Execute(&buf, data)
	if err != nil {
		return "", err
	}

	return buf.String(), nil
}
