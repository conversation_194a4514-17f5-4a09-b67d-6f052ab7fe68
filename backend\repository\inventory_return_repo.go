package repository

import (
	"context"
	"cx/domain"
	"cx/utils"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ReturnRepository interface {
	Create(ctx context.Context, ret *domain.CreateReturnRequest) error
	GetByUUID(ctx context.Context, uuid string) (*domain.Return, error)
	GetByReturnNumber(ctx context.Context, returnNumber string) (*domain.Return, error)
	List(ctx context.Context, filter *domain.ReturnFilter, pagination *domain.Pagination) ([]domain.Return, error)
	Update(ctx context.Context, ret *domain.Return) error
	Delete(ctx context.Context, uuid string) error

	// 退貨單項目相關操作
	AddReturnItem(ctx context.Context, item *domain.ReturnItem) error
	UpdateReturnItem(ctx context.Context, item *domain.ReturnItem) error
	DeleteReturnItem(ctx context.Context, uuid string) error

	// 處理退貨單狀態變更
	UpdateStatus(ctx context.Context, uuid string, status domain.ReturnStatus, updatedByID int64) error

	// 批量查詢
	GetReturnsByOrderID(ctx context.Context, orderID int64) ([]*domain.Return, error)
	GetReturnsByCustomerID(ctx context.Context, customerID int64) ([]*domain.Return, error)
}

type returnRepository struct {
	db *gorm.DB
}

func NewReturnRepository(db *gorm.DB) ReturnRepository {
	return &returnRepository{
		db: db,
	}
}

func (r *returnRepository) Create(ctx context.Context, ret *domain.CreateReturnRequest) error {
	tx := r.db.WithContext(ctx)

	// 創建退貨單
	if err := tx.Create(ret).Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r *returnRepository) GetByUUID(ctx context.Context, uuid string) (*domain.Return, error) {
	var ret domain.Return
	err := r.db.WithContext(ctx).
		Preload("Items.Product").
		Where("uuid = ?", uuid).
		First(&ret).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("return not found")
		}
		return nil, err
	}

	return &ret, nil
}

func (r *returnRepository) GetByReturnNumber(ctx context.Context, returnNumber string) (*domain.Return, error) {
	var ret domain.Return
	err := r.db.WithContext(ctx).
		Preload("Items.Product").
		Where("return_number = ?", returnNumber).
		First(&ret).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("return not found")
		}
		return nil, err
	}

	return &ret, nil
}

func (r *returnRepository) List(ctx context.Context, filter *domain.ReturnFilter, pagination *domain.Pagination) ([]domain.Return, error) {
	var returns []domain.Return

	tx := r.db.WithContext(ctx).Model(&domain.Return{})

	// 應用過濾條件
	if filter.ReturnType != "" {
		tx = tx.Where("return_type = ?", filter.ReturnType)
	}

	if filter.Status != "" {
		tx = tx.Where("status = ?", filter.Status)
	}

	if filter.CustomerID > 0 {
		tx = tx.Where("customer_id = ?", filter.CustomerID)
	}

	if filter.OrderID > 0 {
		tx = tx.Where("order_id = ?", filter.OrderID)
	}

	if filter.StartDate != "" {
		tx = tx.Where("return_date >= ?", filter.StartDate)
	}

	if filter.EndDate != "" {
		tx = tx.Where("return_date <= ?", filter.EndDate)
	}

	if filter.ReturnNumber != "" {
		tx = tx.Where("return_number LIKE ?", "%"+filter.ReturnNumber+"%")
	}

	// 計算總數
	if err := tx.Count(&pagination.RowsNumber).Error; err != nil {
		return nil, err
	}

	tx = pagination.SetPaginationToDB(tx, "return_date", true)

	// 查詢數據並預加載關係
	err := tx.Preload("Items.Product").Find(&returns).Error
	if err != nil {
		return nil, err
	}

	return returns, nil
}

func (r *returnRepository) Update(ctx context.Context, ret *domain.Return) error {
	// 計算總金額
	var totalAmount float64
	for _, item := range ret.Items {
		item.TotalPrice = float64(item.Quantity) * item.UnitPrice
		totalAmount += item.TotalPrice
	}
	ret.TotalAmount = totalAmount

	// 開始事務
	tx := r.db.WithContext(ctx)

	// 更新退貨單基本信息（不包括 Items）
	if err := tx.Model(&domain.Return{}).
		Where("uuid = ?", ret.UUID).
		Updates(map[string]interface{}{
			"return_type":     ret.ReturnType,
			"order_id":        ret.OrderID,
			"customer_id":     ret.CustomerID,
			"return_date":     ret.ReturnDate,
			"status":          ret.Status,
			"total_amount":    ret.TotalAmount,
			"refunded_amount": ret.RefundedAmount,
			"notes":           ret.Notes,
			"updated_by_id":   ret.UpdatedByID,
		}).Error; err != nil {
		return err
	}

	// 獲取退貨單ID
	var existingReturn domain.Return
	if err := tx.Where("uuid = ?", ret.UUID).First(&existingReturn).Error; err != nil {
		return err
	}

	// 刪除舊的退貨單項目
	if err := tx.Where("return_id = ?", existingReturn.ID).Delete(&domain.ReturnItem{}).Error; err != nil {
		return err
	}

	// 創建新的退貨單項目
	for i := range ret.Items {
		ret.Items[i].ReturnID = existingReturn.ID
		ret.Items[i].UUID = uuid.New().String()
		ret.Items[i].CreatedByID = ret.UpdatedByID
		ret.Items[i].UpdatedByID = ret.UpdatedByID
		ret.Items[i].CreatedAt = time.Now()
		ret.Items[i].UpdatedAt = time.Now()

		if err := tx.Create(&ret.Items[i]).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r *returnRepository) Delete(ctx context.Context, uuid string) error {
	return r.db.WithContext(ctx).
		Where("uuid = ?", uuid).
		Delete(&domain.Return{}).Error
}

func (r *returnRepository) AddReturnItem(ctx context.Context, item *domain.ReturnItem) error {
	if item.UUID == "" {
		item.UUID = utils.GenerateUUID()
	}

	item.TotalPrice = float64(item.Quantity) * item.UnitPrice

	// 查找退貨單
	var ret domain.Return
	if err := r.db.WithContext(ctx).Where("id = ?", item.ReturnID).First(&ret).Error; err != nil {
		return err
	}

	tx := r.db.WithContext(ctx)

	// 添加項目
	if err := tx.Create(item).Error; err != nil {
		return err
	}

	// 更新退貨單總金額
	newTotalAmount := ret.TotalAmount + item.TotalPrice
	err := tx.Model(&domain.Return{}).
		Where("id = ?", ret.ID).
		Update("total_amount", newTotalAmount).Error

	return err
}

func (r *returnRepository) UpdateReturnItem(ctx context.Context, item *domain.ReturnItem) error {
	oldItem := &domain.ReturnItem{}
	if err := r.db.WithContext(ctx).Where("uuid = ?", item.UUID).First(oldItem).Error; err != nil {
		return err
	}

	item.TotalPrice = float64(item.Quantity) * item.UnitPrice
	priceDiff := item.TotalPrice - oldItem.TotalPrice

	tx := r.db.WithContext(ctx)

	// 更新項目
	if err := tx.Model(&domain.ReturnItem{}).
		Where("uuid = ?", item.UUID).
		Updates(map[string]interface{}{
			"quantity":      item.Quantity,
			"unit_price":    item.UnitPrice,
			"total_price":   item.TotalPrice,
			"notes":         item.Notes,
			"updated_by_id": item.UpdatedByID,
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 更新退貨單總金額
	if err := tx.Model(&domain.Return{}).
		Where("id = ?", oldItem.ReturnID).
		Update("total_amount", gorm.Expr("total_amount + ?", priceDiff)).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

func (r *returnRepository) DeleteReturnItem(ctx context.Context, uuid string) error {
	// 查找項目
	var item domain.ReturnItem
	if err := r.db.WithContext(ctx).Where("uuid = ?", uuid).First(&item).Error; err != nil {
		return err
	}

	// 開始事務
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 刪除項目
	if err := tx.Where("uuid = ?", uuid).Delete(&domain.ReturnItem{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 更新退貨單總金額
	if err := tx.Model(&domain.Return{}).
		Where("id = ?", item.ReturnID).
		Update("total_amount", gorm.Expr("total_amount - ?", item.TotalPrice)).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

func (r *returnRepository) UpdateStatus(ctx context.Context, uuid string, status domain.ReturnStatus, updatedByID int64) error {
	return r.db.WithContext(ctx).
		Model(&domain.Return{}).
		Where("uuid = ?", uuid).
		Updates(map[string]interface{}{
			"status":        status,
			"updated_by_id": updatedByID,
		}).Error
}

func (r *returnRepository) GetReturnsByOrderID(ctx context.Context, orderID int64) ([]*domain.Return, error) {
	var returns []*domain.Return
	err := r.db.WithContext(ctx).
		Preload("Items.Product").
		Where("order_id = ?", orderID).
		Find(&returns).Error

	return returns, err
}

func (r *returnRepository) GetReturnsByCustomerID(ctx context.Context, customerID int64) ([]*domain.Return, error) {
	var returns []*domain.Return
	err := r.db.WithContext(ctx).
		Preload("Items.Product").
		Where("customer_id = ?", customerID).
		Find(&returns).Error

	return returns, err
}

// 生成退貨單號
func generateReturnNumber(returnType domain.ReturnType) string {
	prefix := "RTN"
	if returnType == domain.ReturnTypeSupplierReturn {
		prefix = "RTS"
	}
	timestamp := time.Now().Format("20060102150405")
	randomDigits := fmt.Sprintf("%04d", time.Now().Nanosecond()%10000)
	return prefix + "-" + timestamp + "-" + randomDigits
}
