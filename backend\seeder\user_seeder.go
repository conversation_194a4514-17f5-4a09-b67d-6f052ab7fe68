package seeder

import (
	"context"
	"cx/domain"
	"cx/repository"
	"cx/service"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type UserSeeder struct {
	seed int
}

func NewUserSeeder(seed int) *UserSeeder {
	return &UserSeeder{seed}
}

func (s *UserSeeder) Seed(db *gorm.DB) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	userRepo := repository.NewUserRepository(db)
	userService := service.NewUserService(db, userRepo)

	userService.Create(ctx, &domain.UserCreatePayload{
		ID:       1,
		GroupID:  null.IntFromPtr(nil),
		Username: "admin",
		Password: "admin",
		Name:     "管理員",
		Birthday: null.StringFromPtr(nil),
		IsAdmin:  true,
	})

	userGroupRepo := repository.NewUserGroupRepository(db)
	userGroupService := service.NewUserGroupService(db, userGroupRepo, nil)

	for i := 0; i < 3; i++ {
		userGroup := &domain.UserGroup{
			Name: gofakeit.Name(),
		}
		if err := userGroupService.Create(ctx, userGroup); err != nil {
			continue
		}

		for i := 0; i < s.seed; i++ {
			user := domain.UserCreatePayload{
				GroupID:   null.IntFrom(userGroup.ID),
				Username:  gofakeit.Username(),
				Password:  gofakeit.Password(true, true, true, true, false, 12),
				Name:      gofakeit.Name(),
				Sex:       gofakeit.RandomString([]string{"M", "F"}),
				Phone:     gofakeit.Phone(),
				Birthday:  null.StringFrom(gofakeit.Date().Format("2006-01-02")),
				Email:     gofakeit.Email(),
				CreatedAt: gofakeit.DateRange(time.Now().AddDate(0, 0, -30), time.Now()),
			}

			userService.Create(ctx, &user)
		}
	}

	return nil
}
