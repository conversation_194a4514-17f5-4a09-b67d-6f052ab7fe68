package service

import (
	"context"
	"cx/domain"
	"cx/repository"

	"gorm.io/gorm"
)

type SystemService interface {
	Get(ctx context.Context) (*domain.SystemOption, error)
	Update(ctx context.Context, payload *domain.SystemOptionUpdatePayload) error
}

type systemService struct {
	db         *gorm.DB
	systemRepo repository.SystemRepository
}

func NewSystemService(db *gorm.DB, systemRepo repository.SystemRepository) SystemService {
	return &systemService{
		db,
		systemRepo,
	}
}

func (s *systemService) Get(ctx context.Context) (*domain.SystemOption, error) {
	return s.systemRepo.Get(ctx)
}

func (s *systemService) Update(ctx context.Context, payload *domain.SystemOptionUpdatePayload) error {
	sysOpt, err := s.systemRepo.Get(ctx)
	if err != nil {
		return err
	}

	payload.ID = sysOpt.ID

	return s.systemRepo.Update(ctx, payload)
}
