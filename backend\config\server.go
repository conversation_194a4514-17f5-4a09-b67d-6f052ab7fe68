package config

import "github.com/spf13/viper"

type ServerConfig struct {
	AppDomain      string
	ServerName     string
	ServerPort     string
	ServerEnv      string // development, production
	ServerTimezone string
}

func setupServerConfig(v *viper.Viper) *ServerConfig {
	return &ServerConfig{
		AppDomain:      v.GetString("APP_DOMAIN"),
		ServerName:     v.GetString("SERVER_NAME"),
		ServerPort:     v.GetString("SERVER_PORT"),
		ServerEnv:      v.GetString("SERVER_ENV"),
		ServerTimezone: v.GetString("SERVER_TIMEZONE"),
	}
}
