<!-- in checkout to select / create customer -->
<template>
  <q-dialog v-model="visible" persistent no-refocus class="card-dialog">
    <q-card class="column">
      <!-- close -->
      <q-card-section class="col-2 q-py-none">
        <div class="row q-mb-sm">
          <q-space />
          <q-btn
            type="button"
            icon="close"
            flat
            round
            dense
            @click="emit('update:modelValue', false)"
          />
        </div>
        <div>
          <q-input
            type="text"
            v-model.trim="search"
            debounce="500"
            :placeholder="t('search.customer')"
            dense
            outlined
            clearable
            @clear="search = ''"
            @update:model-value="filterCustomer"
            class="q-mb-sm"
          >
            <template v-slot:prepend>
              <q-icon name="search" />
            </template>
          </q-input>
        </div>
      </q-card-section>
      <!-- data -->
      <q-card-section class="col-grow q-pt-none">
        <q-scroll-area
          visible
          :thumb-style="scrollbarStyle"
          class="full-height"
        >
          <q-table
            :rows="list"
            :columns="columns"
            row-key="uuid"
            :rows-per-page-options="[0]"
            hide-pagination
          >
            <template v-slot:body="props">
              <q-tr clickable :props="props" @click="selectCustomer(props.row)">
                <q-td v-for="col in props.cols" :key="col.name">
                  {{ props.row[col.name] }}
                </q-td>
              </q-tr>
            </template>
          </q-table>
        </q-scroll-area>
      </q-card-section>
      <!-- actions -->
      <q-card-actions
        :align="selectedCustomerId ? 'between' : 'right'"
        class="col-1"
      >
        <q-btn
          type="button"
          color="negative"
          @click="resetCustomer"
          v-if="selectedCustomerId"
        >
          {{ t('reset') }}
        </q-btn>
        <q-btn
          type="button"
          color="positive"
          no-caps
          @click="createNewCustomer"
        >
          <q-icon name="add" />
          {{ t('customer.label') }}
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>

  <CustomerProfile
    v-model="showCustomerProfile"
    :customer="selectedCustomer"
    @dataUpdated="fetchCustomers"
  />
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { CustomerApi, Customer } from '@/api/customer';
import { useCustomer } from '@/composables/useCustomer';

const { t } = useI18n();

const props = defineProps<{
  modelValue: boolean;
  selectedCustomerId: string | undefined;
}>();

const emit = defineEmits(['update:modelValue', 'select']);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});
const search = ref();
const customers = ref<Customer[]>([]);
const list = ref<Customer[]>([]);

const scrollbarStyle = {
  width: '5px',
};

const columns = computed(() => [
  {
    label: t('name'),
    field: 'name',
    name: 'name',
    align: 'left' as const,
  },
  {
    label: t('phone'),
    field: 'phone',
    name: 'phone',
    align: 'left' as const,
  },
  {
    label: t('taxID'),
    field: 'tax_id',
    name: 'tax_id',
    align: 'left' as const,
  },
  {
    label: t('email.label'),
    field: 'email',
    name: 'email',
    align: 'left' as const,
  },
]);

const resetCustomer = () => {
  emit('select', {
    uuid: '',
    name: '',
  });
  visible.value = false;
};

const customerFac = useCustomer();
const showCustomerProfile = ref(false);
const selectedCustomer = ref<Customer>();

const createNewCustomer = async () => {
  selectedCustomer.value = customerFac.newCustomer();
  showCustomerProfile.value = true;
};

const selectCustomer = (customer: Customer) => {
  selectedCustomer.value = customer;
  emit('select', customer);
  visible.value = false;
};

const fetchCustomers = async () => {
  const response = await CustomerApi.fetch({});
  customers.value = response.result.data;
  list.value = response.result.data;
};

const filterCustomer = (value: string | number | null) => {
  if (value === '' || value == null) {
    list.value = customers.value;
    return;
  }

  if (typeof value === 'string') {
    list.value = customers.value.filter(
      (customer) =>
        customer.name.toLowerCase().includes(value.toLowerCase()) ||
        customer.phone.includes(value) ||
        customer.tax_id.includes(value)
    );
  }
};

onMounted(() => {
  fetchCustomers();
});
</script>
