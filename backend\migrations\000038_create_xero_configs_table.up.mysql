-- Xero設定表
CREATE TABLE `xero_configs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `uuid` CHAR(36) NOT NULL,
    `client_id` VARCHAR(255) NOT NULL,
    `client_secret` VARCHAR(255) NOT NULL,
    `redirect_uri` VARCHAR(255) NOT NULL,
    `scopes` TEXT,
    `is_active` BOOLEAN DEFAULT true,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `xero_configs_uuid_unique` (`uuid`)
);

-- Xero Token表
CREATE TABLE `xero_tokens` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `uuid` CHAR(36) NOT NULL,
    `config_id` BIGINT NOT NULL,
    `access_token` TEXT NOT NULL,
    `refresh_token` TEXT,
    `token_type` VARCHAR(50) DEFAULT 'Bearer',
    `expires_at` DATETIME NULL,
    `tenant_id` VARCHAR(255),
    `tenant_name` VARCHAR(255),
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `xero_tokens_uuid_unique` (`uuid`),
    FOREIGN KEY (`config_id`) REFERENCES `xero_configs`(`id`) ON DELETE CASCADE
);