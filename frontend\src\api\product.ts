import { apiWrapper } from '@/boot/axios';
import { CreateResponse } from './modules/response';
import { ProductCategory } from './productCategory';
import { Customer } from './customer';
import { Pagination } from '@/types';

export interface ProductFilter {
  search?: string;
  category_id?: number;
  name?: string;
  barcode?: string;
}

export interface Product {
  uuid: string;
  category_id?: number;
  category?: ProductCategory;
  barcode: string;
  name: string;
  unit: string;
  price: number;
  sale_price: number;
  cost: number;
  stock_quantity: number;
  min_stock_quantity: number;
  description: string;
  short_description: string;
  images: ProductImage[];
  suppliers: Customer[];
  is_active: boolean;
  is_flexible_price: boolean; // 是否為彈性金額產品
}

export interface ProductImage {
  uuid: string;
  image_path: string;
  sort_order: number;
}

export interface ProductInfo {
  uuid: string;
  name: string;
  category: ProductCategory;
  barcode: string;
  unit: string;
  is_flexible_price: boolean; // 是否為彈性金額產品
}

export const ProductApi = {
  listProducts: ({
    filter,
    pagination,
  }: {
    filter?: ProductFilter;
    pagination?: Pagination;
  } = {}) =>
    apiWrapper.get<{
      data: Product[];
      pagination: Pagination;
    }>('v1/products', {
      params: {
        ...filter,
        ...pagination,
      },
    }),
  get: (uuid: string) => apiWrapper.get<Product>(`v1/products/${uuid}`),
  create: (product: Product) =>
    apiWrapper.post<CreateResponse>('v1/products', product),
  update: (product: Product) =>
    apiWrapper.put(`v1/products/${product.uuid}`, product),
  updateStatus: (uuid: string, is_active: boolean) =>
    apiWrapper.patch(`v1/products/${uuid}/status`, {
      is_active,
    }),
  delete: (uuid: string) => apiWrapper.delete(`v1/products/${uuid}`),

  uploadImage: (productUUID: string, file: File) =>
    apiWrapper.uploadFile(`v1/products/${productUUID}/images`, file),
  updateImageSortOrder: (images: ProductImage[]) =>
    apiWrapper.patch('v1/products/images/sort-order', {
      images: images.map((image) => ({
        uuid: image.uuid,
        sort_order: image.sort_order,
      })),
    }),
  deleteImage: (productUUID: string, imageUUID: string) =>
    apiWrapper.delete(`v1/products/${productUUID}/images/${imageUUID}`),
  syncWcImages: (productUUID: string) =>
    apiWrapper.post(`v1/products/${productUUID}/wc/sync-images`),

  addSupplier: (productUUID: string, supplierUUID: string) =>
    apiWrapper.post(`v1/products/${productUUID}/suppliers`, {
      supplier_uuid: supplierUUID,
    }),
  removeSupplier: (productUUID: string, supplierUUID: string) =>
    apiWrapper.delete(`v1/products/${productUUID}/suppliers/${supplierUUID}`),
};
