package utils

import (
	"fmt"
	"time"
)

func TruncateToDay(t time.Time) time.Time {
	year, month, day := t.Date()
	return time.Date(year, month, day, 0, 0, 0, 0, t.Location())
}

// t1 is after t2 in date
func DateAfter(t1, t2 time.Time) bool {
	date1 := TruncateToDay(t1)
	date2 := TruncateToDay(t2)

	return date1.After(date2)
}

// t1 is before t2 in date
func DateBefore(t1, t2 time.Time) bool {
	date1 := TruncateToDay(t1)
	date2 := TruncateToDay(t2)

	return date1.Before(date2)
}

// t1 is equal t2 in time
func DateEqual(t1, t2 time.Time) bool {
	date1 := TruncateToDay(t1)
	date2 := TruncateToDay(t2)

	return date1.Equal(date2)
}

// IsWithinRange 檢查一個時間是否在指定的區間內
func IsWithinRange(target, start, end time.Time) bool {
	return target.After(start) && target.Before(end)
}

// IsDateWithinRange 檢查一個日期是否在指定的日期區間內
func IsDateWithinRange(target, start, end time.Time) bool {
	dateTarget := TruncateToDay(target)
	dateStart := TruncateToDay(start)
	dateEnd := TruncateToDay(end)

	return dateTarget.After(dateStart) && dateTarget.Before(dateEnd)
}

func FormatDateString(dateStr string, layout string, format string) (string, error) {
	var parsedTime time.Time
	var err error

	parsedTime, err = time.Parse(layout, dateStr)

	if err != nil {
		return "", fmt.Errorf("Cannot parse date format: %s", dateStr)
	}

	// 返回 MySQL DATE 格式
	return parsedTime.Format(format), nil
}
