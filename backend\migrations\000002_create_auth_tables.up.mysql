-- 創建訪問令牌表
CREATE TABLE IF NOT EXISTS `access_tokens` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `jwt_id` VARCHAR(100) NOT NULL COMMENT 'JWT ID (jti)',
    `access_token` TEXT NOT NULL COMMENT '可選存儲完整token',
    `user_agent` VARCHAR(255) NOT NULL,
    `ip_address` VARCHAR(50) NOT NULL,
    `last_used_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `expires_at` DATETIME NOT NULL,
    `is_revoked` BOOLEAN NOT NULL DEFAULT FALSE,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `access_tokens_jwt_id_unique` (`jwt_id`),
    INDEX `access_tokens_user_id_index` (`user_id`),
    INDEX `access_tokens_is_revoked_expires_at_index` (`is_revoked`, `expires_at`),
    CONSTRAINT `access_tokens_user_id_foreign` FOREIGN KEY (`user_id`) 
        REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 創建刷新令牌表
CREATE TABLE IF NOT EXISTS `refresh_tokens` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL,
    `access_token_id` BIGINT NOT NULL,
    `refresh_token` TEXT NOT NULL,
    `expires_at` DATETIME NOT NULL,
    `is_revoked` BOOLEAN NOT NULL DEFAULT FALSE,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `refresh_tokens_user_id_index` (`user_id`),
    INDEX `refresh_tokens_access_token_id_index` (`access_token_id`),
    INDEX `refresh_tokens_is_revoked_expires_at_index` (`is_revoked`, `expires_at`),
    CONSTRAINT `refresh_tokens_user_id_foreign` FOREIGN KEY (`user_id`) 
        REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `refresh_tokens_access_token_id_foreign` FOREIGN KEY (`access_token_id`) 
        REFERENCES `access_tokens` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;