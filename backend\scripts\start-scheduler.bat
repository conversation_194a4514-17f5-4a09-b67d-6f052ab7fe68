@echo off
setlocal enabledelayedexpansion

REM WooCommerce to Xero Invoice Scheduler 啟動腳本 (Windows)

echo [INFO] Starting WooCommerce to Xero Invoice Scheduler...

REM 檢查是否存在 .env.scheduler 文件
if not exist ".env.scheduler" (
    echo [WARN] .env.scheduler file not found. Creating from example...
    if exist ".env.scheduler.example" (
        copy ".env.scheduler.example" ".env.scheduler"
        echo [INFO] Please edit .env.scheduler file with your configuration
        pause
        exit /b 1
    ) else (
        echo [ERROR] .env.scheduler.example file not found
        pause
        exit /b 1
    )
)

REM 載入環境變量
for /f "usebackq tokens=1,2 delims==" %%a in (".env.scheduler") do (
    if not "%%a"=="" if not "%%a:~0,1%"=="#" (
        set "%%a=%%b"
    )
)

REM 檢查必要的環境變量
if "%DB_PASSWORD%"=="" (
    echo [ERROR] DB_PASSWORD is not set in .env.scheduler
    pause
    exit /b 1
)

REM 設置默認值
if "%DB_HOST%"=="" set "DB_HOST=localhost"
if "%DB_PORT%"=="" set "DB_PORT=3306"
if "%DB_USER%"=="" set "DB_USER=root"
if "%DB_NAME%"=="" set "DB_NAME=cx_pos"

echo [INFO] Database configuration:
echo   Host: %DB_HOST%
echo   Port: %DB_PORT%
echo   User: %DB_USER%
echo   Database: %DB_NAME%

REM 構建調度器
echo [INFO] Building scheduler...
go build -o scheduler.exe ./cmd/scheduler

if errorlevel 1 (
    echo [ERROR] Failed to build scheduler
    pause
    exit /b 1
)

echo [INFO] Scheduler built successfully

REM 運行調度器
echo [INFO] Starting scheduler...
echo [INFO] Press Ctrl+C to stop the scheduler

scheduler.exe

pause
