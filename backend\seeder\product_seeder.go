package seeder

import (
	"context"
	"cx/config"
	"cx/domain"
	"cx/repository"
	"cx/service"
	"cx/utils"
	"net/http"
	"time"

	"github.com/brianvoe/gofakeit/v7"
	"gorm.io/gorm"
)

type ProductSeeder struct {
	seed int
}

func NewProductSeeder(seed int) *ProductSeeder {
	return &ProductSeeder{seed}
}

func (s *ProductSeeder) Seed(db *gorm.DB) error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	c, _ := config.LoadConfig()
	wpDB, err := config.ConnectWpDB(c)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := config.CloseDB(db); err != nil {
			utils.ErrorLog(domain.HttpResponse{
				Status:  http.StatusInternalServerError,
				Message: "failed to close database",
				Error:   err.Error(),
			})
		}
	}()

	categoryRepo := repository.NewProductCategoryRepository(db)
	categoryService := service.NewProductCategoryService(db, wpDB, categoryRepo)

	productRepo := repository.NewProductRepository(db)
	productService := service.NewProductService(db, wpDB, productRepo)

	for i := 0; i < s.seed; i++ {
		category := domain.ProductCategory{
			Name:      gofakeit.Name(),
			CreatedAt: gofakeit.DateRange(time.Now().AddDate(0, 0, -30), time.Now()),
		}

		categoryID, err := categoryService.Create(ctx, &category)
		if err != nil {
			continue
		}

		for j := 0; j < gofakeit.Number(10, 20); j++ {
			product := domain.Product{
				CategoryID:       categoryID,
				Barcode:          gofakeit.StreetNumber(),
				Name:             gofakeit.Name(),
				Description:      gofakeit.Sentence(10),
				Unit:             gofakeit.RandomString([]string{"pcs", "pack", "bottle", "can", "box", "carton", "bag", "strip", "slice", "stick", "set"}), // unit
				Price:            gofakeit.Price(100, 1000),
				Cost:             gofakeit.Price(50, 500),
				MinStockQuantity: gofakeit.Number(1, 10),
				IsActive:         true,
				CreatedAt:        gofakeit.DateRange(time.Now().AddDate(0, 0, -30), time.Now()),
			}

			productService.Create(ctx, &product)
		}
	}

	return nil
}
