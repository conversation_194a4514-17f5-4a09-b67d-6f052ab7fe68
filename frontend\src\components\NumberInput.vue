<template>
  <div class="number-input">
    <q-input
      v-model="inputValue"
      :label="label"
      :step="step"
      :hint="hint"
      :rules="validationRules"
      :disable="disable"
      :readonly="readonly"
      :mask="mask"
      :prefix="prefix"
      :suffix="suffix"
      stack-label
      inputmode="numeric"
      pattern="[0-9]*"
      :square="square"
      :dense="dense"
      :outlined="outlined"
      :input-class="inputClass"
      @update:model-value="handleInput"
      @keypress="validateKeyPress"
      @click.stop=""
      hide-bottom-space
      lazy-rules
    >
      <template v-if="prepend" v-slot:prepend>
        <q-icon :name="prepend" />
      </template>
      <template v-if="append" v-slot:append>
        <q-icon :name="append" />
      </template>
    </q-input>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: 0,
  },
  label: {
    type: String,
    default: undefined,
  },
  hint: {
    type: String,
    default: undefined,
  },
  min: {
    type: Number,
    default: Number.NEGATIVE_INFINITY,
  },
  max: {
    type: Number,
    default: Number.POSITIVE_INFINITY,
  },
  step: {
    type: Number,
    default: 1,
  },
  precision: {
    type: Number,
    default: 0,
  },
  mask: {
    type: String,
    default: undefined,
  },
  prefix: {
    type: String,
    default: undefined,
  },
  suffix: {
    type: String,
    default: undefined,
  },
  prepend: {
    type: String,
    default: undefined,
  },
  append: {
    type: String,
    default: undefined,
  },
  disable: {
    type: Boolean,
    default: false,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
  square: {
    type: Boolean,
    default: false,
  },
  dense: {
    type: Boolean,
    default: false,
  },
  outlined: {
    type: Boolean,
    default: false,
  },
  inputClass: {
    type: [String, Array, Object],
    default: '',
  },
});

const emit = defineEmits(['update:modelValue', 'change']);

const inputValue = ref(props.modelValue);

// 驗證規則
const validationRules = computed(() => [
  (val: string) =>
    (val !== null && val !== '') || !props.required || t('error.required'),
  (val: number) => val >= props.min || t('error.minNumber', { min: props.min }),
  (val: number) => val <= props.max || t('error.maxNumber', { max: props.max }),
]);

// 驗證按鍵輸入
const validateKeyPress = (event: KeyboardEvent) => {
  // 允許的按鍵：數字、退格、刪除、方向鍵
  const allowedKeys = [
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '.',
    '-',
  ];
  const allowedKeyCodes = [8, 46, 37, 39]; // backspace, delete, left, right

  if (
    !allowedKeys.includes(event.key) &&
    !allowedKeyCodes.includes(event.keyCode)
  ) {
    event.preventDefault();
  }

  // 特別處理小數點和負號
  if (event.key === '.') {
    // 如果已經有小數點或不允許小數，則阻止輸入
    if (inputValue.value.toString().includes('.') || props.step % 1 === 0) {
      event.preventDefault();
    }
  }

  if (event.key === '-') {
    // 如果最小值大於等於0或已經有負號，則阻止輸入
    if (props.min >= 0 || inputValue.value.toString().includes('-')) {
      event.preventDefault();
    }
    // 只允許在第一個位置輸入負號
    else if (event.target instanceof HTMLInputElement) {
      if (inputValue.value !== 0) {
        if (event.target.selectionStart !== 0) {
          event.preventDefault();
        }
      } else {
        handleInput('-');
        event.preventDefault();
      }
    }
  }
};

// 處理輸入值變更
const handleInput = (value: string | number | null) => {
  let numValue = Number(value);

  if (value === '-') {
    inputValue.value = '-';
    emit('update:modelValue', 0);
    emit('change', 0);
    return;
  }

  if (isNaN(numValue)) {
    numValue = 0;
  } else if (!isFinite(numValue)) {
    inputValue.value = '';
    emit('update:modelValue', 0);
    emit('change', 0);
    return;
  }

  // 確保數值在範圍內
  numValue = Math.max(props.min, Math.min(props.max, numValue));
  // 限制小數位數
  numValue = Number(numValue.toFixed(props.precision));

  inputValue.value = numValue;

  emit('update:modelValue', numValue);
  emit('change', numValue);
};

// 監聽外部值變更
watch(
  () => props.modelValue,
  (newVal) => {
    inputValue.value = newVal;
  }
);
</script>
