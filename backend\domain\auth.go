package domain

import "errors"

var (
	ErrTokenInvalid   = errors.New("token is invalid")
	ErrTokenRevoked   = errors.New("token has been revoked")
	ErrTokenExpired   = errors.New("token has expired")
	ErrRefreshInvalid = errors.New("refresh token is invalid")
)

type LoginRequest struct {
	Username string `form:"username" json:"username" binding:"required"`
	Password string `form:"password" json:"password" binding:"required"`
}

type LoginResponse struct {
	TokenPair `gorm:"embedded"`
	User      UserInfo `json:"user"`
}

type UserInfo struct {
	ID    int64  `json:"-"`
	UUID  string `json:"uuid"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

func (u *UserInfo) TableName() string {
	return "users"
}

type RefreshTokenPayload struct {
	RefreshToken string
	UserAgent    string
	ClientIP     string
}
