package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func setupWpDBConfig(v *viper.Viper) *DBConfig {
	return &DBConfig{
		Host:         v.GetString("DB_HOST"),
		Port:         v.GetString("DB_PORT"),
		User:         v.GetString("DB_USER"),
		Password:     v.GetString("DB_PASSWORD"),
		DatabaseName: v.GetString("WP_DB_NAME"),
		MaxIdleConns: v.GetInt("DB_MAX_IDLE_CONNS"),
		MaxOpenConns: v.GetInt("DB_MAX_OPEN_CONNS"),
		MaxLifetime:  v.GetInt("DB_MAX_LIFETIME"),
	}
}

func ConnectWpDB(config *Config) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local&collation=utf8mb4_unicode_ci&multiStatements=true",
		config.WpDB.User,
		config.WpDB.Password,
		config.WpDB.Host,
		config.WpDB.Port,
		config.WpDB.DatabaseName,
	)

	var logLevel logger.LogLevel
	if config.Server.ServerEnv == "production" {
		logLevel = logger.Silent
	} else {
		logLevel = logger.Info
	}

	// 設定 GORM 配置
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
		// 禁用默認事務
		SkipDefaultTransaction: true,
		// 準備語句緩存
		PrepareStmt: true,
	}

	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	// 取得底層的 *sql.DB 物件
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying *sql.DB: %v", err)
	}

	// 設定連線池參數
	sqlDB.SetMaxIdleConns(config.DB.MaxIdleConns)                                  // 設定最大空閒連線數，建議設定為 MaxOpenConns 的 25%-50%
	sqlDB.SetMaxOpenConns(config.DB.MaxOpenConns)                                  // 設定最大開啟連線數，根據服務器資源和預期負載來設定
	sqlDB.SetConnMaxLifetime(time.Duration(config.WpDB.MaxLifetime) * time.Minute) // 設定連線最大存活時間，建議設定為 5-10 分鐘

	// 測試資料庫連線
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %v", err)
	}

	return db, nil
}
