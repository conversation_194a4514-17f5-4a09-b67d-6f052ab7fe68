import { Notify, Dialog, Loading } from 'quasar';
import { i18n } from '@/boot/i18n';
import { formatDate } from '@/utils';

interface VersionInfo {
  version: string;
  buildTime: string;
  hash: string;
}

export class UniversalUpdateService {
  private currentVersion = '';
  private checkInterval: number = 3 * 60 * 1000; // 3分鐘檢查一次
  private intervalId: number | null = null;
  private isChecking = false;
  private hasShownUpdateDialog = false;

  constructor() {
    this.initialize();
  }

  private async initialize() {
    // 取得當前版本資訊
    this.currentVersion = await this.getCurrentVersion();
    this.startPeriodicCheck();

    // 監聽頁面可見性變化，當頁面重新可見時檢查更新
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && !this.hasShownUpdateDialog) {
        setTimeout(() => this.checkForUpdates(), 1000);
      }
    });
  }

  private async getCurrentVersion(): Promise<string> {
    try {
      // 方法1：從 meta tag 取得版本
      const metaVersion = document
        .querySelector('meta[name="version"]')
        ?.getAttribute('content');
      if (metaVersion) return metaVersion;

      // 方法2：從 localStorage 取得之前儲存的版本
      const storedVersion = localStorage.getItem('app_version');
      if (storedVersion) return storedVersion;

      // 方法3：從版本檔案取得
      const response = await fetch('/version.json?' + Date.now());
      const versionInfo = await response.json();
      localStorage.setItem('app_version', versionInfo.version);
      return versionInfo.version;
    } catch (error) {
      console.warn('Cannot get the latest version:', error);
      return Date.now().toString(); // 使用時間戳作為後備
    }
  }

  private async getRemoteVersion(): Promise<VersionInfo | null> {
    try {
      const response = await fetch('/version.json?' + Date.now(), {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) throw new Error('Cannot get the remote version info.');

      return await response.json();
    } catch (error) {
      console.error('Wrong to get the remote version:', error);
      return null;
    }
  }

  private async checkForUpdates(): Promise<boolean> {
    if (this.isChecking) return false;

    this.isChecking = true;

    try {
      const remoteVersion = await this.getRemoteVersion();

      if (!remoteVersion) {
        this.isChecking = false;
        return false;
      }

      const hasUpdate = remoteVersion.version !== this.currentVersion;

      if (hasUpdate && !this.hasShownUpdateDialog) {
        this.showUpdateDialog(remoteVersion);
        return true;
      }

      return hasUpdate;
    } catch (error) {
      console.error('Wrong to check update:', error);
      return false;
    } finally {
      this.isChecking = false;
    }
  }

  private showUpdateDialog(versionInfo: VersionInfo) {
    this.hasShownUpdateDialog = true;

    Dialog.create({
      title: '🚀 ' + i18n.global.t('updateService.updateNotification.title'),
      message: `
        <div style="text-align: left;">
          <p>
            <strong>
              ${i18n.global.t('updateService.updateNotification.newVersion')}：
            </strong>
            v${versionInfo.version}
          </p>
          <p>
            <strong>
              ${i18n.global.t(
                'updateService.updateNotification.currentVersion'
              )}：
            </strong>
            v${this.currentVersion}
          </p>
          <p>
            <strong>
              ${i18n.global.t('updateService.updateNotification.buildTime')}：
            </strong>
            ${formatDate(versionInfo.buildTime, 'YYYY-MM-DD HH:mm:ss')}
          </p>
          <br>
          <p style="color: #f56565;">
            ⚠️ ${i18n.global.t(
              'updateService.updateNotification.recommendUpdate'
            )}
          </p>
          <p style="color: #666; font-size: 0.9em;">
            ${i18n.global.t('updateService.updateNotification.doNotClose')}
          </p>
        </div>
      `,
      html: true,
      cancel: {
        label: i18n.global.t('updateService.updateNotification.updateLater'),
        color: 'grey',
        flat: true,
      },
      ok: {
        label: i18n.global.t('updateService.updateNotification.updateNow'),
        color: 'primary',
      },
      persistent: true,
    })
      .onOk(() => {
        this.performUpdate();
      })
      .onCancel(() => {
        this.hasShownUpdateDialog = false;
        // 10分鐘後再次提醒
        setTimeout(() => {
          this.hasShownUpdateDialog = false;
        }, 10 * 60 * 1000);

        Notify.create({
          type: 'info',
          message:
            '💡 ' +
            i18n.global.t('updateService.updateNotification.laterReminder'),
          timeout: 3000,
          position: 'top',
        });
      });
  }

  private async performUpdate() {
    Loading.show({
      message: i18n.global.t('updateService.updateProcess.updating'),
      spinnerColor: 'primary',
      spinnerSize: 50,
      backgroundColor: 'rgba(0,0,0,0.8)',
    });

    try {
      // 清除所有快取
      await this.clearAllCaches();

      // 預載新資源
      await this.preloadNewResources();

      // 更新版本資訊
      const newVersion = await this.getRemoteVersion();
      if (newVersion) {
        localStorage.setItem('app_version', newVersion.version);
      }

      // 顯示成功訊息
      Notify.create({
        type: 'positive',
        message: '✅ ' + i18n.global.t('updateService.updateProcess.success'),
        timeout: 2000,
      });

      // 延遲重新載入讓使用者看到成功訊息
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error('Update failed:', error);
      Loading.hide();

      Notify.create({
        type: 'negative',
        message: '❌ ' + i18n.global.t('updateService.updateProcess.failed'),
        timeout: 3000,
      });

      // 3秒後強制重新載入
      setTimeout(() => {
        window.location.href = window.location.href;
      }, 3000);
    }
  }

  private async clearAllCaches() {
    // 清除瀏覽器快取
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map((cacheName) => caches.delete(cacheName))
      );
    }

    // 清除 localStorage 中的過期資料（保留重要設定）
    const keysToKeep = ['user_settings', 'auth_token', 'language_preference'];
    const allKeys = Object.keys(localStorage);

    allKeys.forEach((key) => {
      if (!keysToKeep.some((keepKey) => key.includes(keepKey))) {
        localStorage.removeItem(key);
      }
    });

    // 清除 sessionStorage
    sessionStorage.clear();
  }

  private async preloadNewResources() {
    try {
      // 預載主要資源
      const criticalResources = ['/', '/css/app.css', '/js/app.js'];

      await Promise.all(
        criticalResources.map((url) =>
          fetch(url + '?' + Date.now(), { cache: 'no-cache' }).catch((err) =>
            console.warn(`Failed to preload ${url}:`, err)
          )
        )
      );
    } catch (error) {
      console.warn('Failed to preload resources:', error);
    }
  }

  private startPeriodicCheck() {
    this.stopPeriodicCheck(); // 確保沒有重複的定時器

    this.intervalId = window.setInterval(() => {
      if (!document.hidden) {
        // 只在頁面可見時檢查
        this.checkForUpdates();
      }
    }, this.checkInterval);
  }

  private stopPeriodicCheck() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  // 公開方法
  public async manualCheckUpdate(): Promise<void> {
    this.hasShownUpdateDialog = false;
    await this.checkForUpdates();
  }

  public destroy() {
    this.stopPeriodicCheck();
    document.removeEventListener('visibilitychange', this.checkForUpdates);
  }
}
