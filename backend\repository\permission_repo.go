package repository

import (
	"context"
	"cx/domain"

	"gorm.io/gorm"
)

type PermissionRepository interface {
	Fetch(ctx context.Context, filter *domain.PermissionFilter) []domain.Permission
	GetByID(ctx context.Context, id uint) domain.Permission
	GetByCode(ctx context.Context, code domain.PermissionCode) domain.Permission

	HasPermission(ctx context.Context, code domain.PermissionCode, groupID int64) (bool, error)
	BatchCreateGroupPermission(ctx context.Context, permissions []domain.GroupPermission) error
	DeleteGroupPermission(ctx context.Context, groupID int64) error
}

type permissionRepository struct {
	db *gorm.DB
}

func NewPermissionRepository(db *gorm.DB) PermissionRepository {
	return &permissionRepository{db}
}

func (r *permissionRepository) Fetch(ctx context.Context, filter *domain.PermissionFilter) []domain.Permission {
	var permissions []domain.Permission

	tx := r.db.WithContext(ctx)

	if filter != nil {
		if filter.GroupID > 0 {
			tx = tx.Joins("JOIN group_permissions ON group_permissions.permission_id = permissions.id").
				Where("group_permissions.group_id = ?", filter.GroupID)
		}
	}

	tx.Where("is_active = TRUE").Find(&permissions)
	return permissions
}

func (r *permissionRepository) GetByID(ctx context.Context, id uint) domain.Permission {
	var permission domain.Permission

	tx := r.db.WithContext(ctx)
	tx.Where("is_active = TRUE").First(&permission, id)
	return permission
}

func (r *permissionRepository) GetByCode(ctx context.Context, code domain.PermissionCode) domain.Permission {
	var permission domain.Permission

	tx := r.db.WithContext(ctx)
	tx.Where("code = ? AND is_active = TRUE", code).First(&permission)
	return permission
}

func (r *permissionRepository) HasPermission(ctx context.Context, code domain.PermissionCode, groupID int64) (bool, error) {
	var groupPermission domain.GroupPermission

	tx := r.db.WithContext(ctx)

	err := tx.Where("group_id = ? AND permission_id = (SELECT id FROM permissions WHERE code = ?)", groupID, code).First(&groupPermission).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		return false, err
	}

	return true, nil
}

// user group permission
func (r *permissionRepository) BatchCreateGroupPermission(ctx context.Context, permissions []domain.GroupPermission) error {
	tx := r.db.WithContext(ctx)
	return tx.Model(&domain.GroupPermission{}).Create(&permissions).Error
}

func (r *permissionRepository) DeleteGroupPermission(ctx context.Context, groupID int64) error {
	tx := r.db.WithContext(ctx)
	tx.Where("group_id = ?", groupID).Delete(&domain.GroupPermission{})
	return nil
}
