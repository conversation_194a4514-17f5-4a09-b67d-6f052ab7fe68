package repository

import (
	"testing"
)

func TestParseAustraliaPostShippingMethod(t *testing.T) {
	repo := &wcOrderRepository{}

	tests := []struct {
		name        string
		methodID    string
		methodTitle string
		expected    string
	}{
		{
			name:        "Australia Post Express Post",
			methodID:    "australia_post:express_post",
			methodTitle: "Australia Post Shipping Express Post",
			expected:    "australia_post_express",
		},
		{
			name:        "Australia Post Parcel Post",
			methodID:    "australia_post:parcel_post",
			methodTitle: "Australia Post Shipping Parcel Post",
			expected:    "australia_post_parcel",
		},
		{
			name:        "Express in title only",
			methodID:    "australia_post:shipping",
			methodTitle: "Express Delivery",
			expected:    "australia_post_express",
		},
		{
			name:        "Parcel in title only",
			methodID:    "australia_post:shipping",
			methodTitle: "Parcel Delivery Service",
			expected:    "australia_post_parcel",
		},
		{
			name:        "Express in method_id",
			methodID:    "australia_post_express:1",
			methodTitle: "Australia Post Shipping",
			expected:    "australia_post_express",
		},
		{
			name:        "Parcel in method_id",
			methodID:    "australia_post_parcel:1",
			methodTitle: "Australia Post Shipping",
			expected:    "australia_post_parcel",
		},
		{
			name:        "Generic Australia Post",
			methodID:    "australia_post:shipping",
			methodTitle: "Australia Post Shipping",
			expected:    "australia_post",
		},
		{
			name:        "Non-Australia Post method",
			methodID:    "flat_rate:1",
			methodTitle: "Flat Rate Shipping",
			expected:    "flat_rate:1",
		},
		{
			name:        "Empty method",
			methodID:    "",
			methodTitle: "",
			expected:    "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := repo.parseAustraliaPostShippingMethod(tt.methodID, tt.methodTitle)
			if result != tt.expected {
				t.Errorf("parseAustraliaPostShippingMethod() = %v, want %v", result, tt.expected)
			}
		})
	}
}
