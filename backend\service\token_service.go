package service

import (
	"context"
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/spf13/viper"
	"gorm.io/gorm"

	"cx/config"
	"cx/domain"
	"cx/repository"
	"cx/utils"
)

var (
	accessTokenTTL  = 15 * time.Minute
	refreshTokenTTL = 7 * 24 * time.Hour
)

type TokenClaims struct {
	UserUUID string `json:"user_uuid"`
	IsAdmin  bool   `json:"is_admin"`
	jwt.RegisteredClaims
}

type TokenService interface {
	GenerateTokenPair(ctx context.Context, user *domain.User, userAgent, ipAddress string) (*domain.TokenPair, error)
	ParseToken(tokenString string) (*TokenClaims, bool, error)
	GetTokenByJwtID(ctx context.Context, jwtID string) (*domain.AccessToken, error)
	GetRefreshToken(ctx context.Context, refreshToken string) (*domain.RefreshToken, error)
	UpdateLastUsedAt(ctx context.Context, jwtID string) error
	RevokeToken(ctx context.Context, id int64) error
	RevokeTokenByJwtID(ctx context.Context, jwtID string) error
	RevokeUserTokens(ctx context.Context, userID int64) error
	RevokeRefreshToken(ctx context.Context, id int64) error
}

type tokenService struct {
	db        *gorm.DB
	tokenRepo repository.TokenRepository
}

func NewTokenService(db *gorm.DB, tokenRepo repository.TokenRepository) TokenService {
	return &tokenService{db, tokenRepo}
}

func (s *tokenService) GenerateTokenPair(ctx context.Context, user *domain.User, userAgent, ipAddress string) (*domain.TokenPair, error) {
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	tokenRepo := s.tokenRepo.WithTx(tx)

	// 生成唯一的 JWT ID
	jti := utils.GenerateUUID()
	issuer := viper.GetString("APP_DOMAIN")

	// 生成訪問令牌
	accessClaims := TokenClaims{
		UserUUID: user.UUID,
		IsAdmin:  user.IsAdmin,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(accessTokenTTL)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    issuer,
			Subject:   user.Username,
			ID:        jti,
		},
	}

	accessToken, err := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims).SignedString(getSecretKey())
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	dbAccessToken := domain.AccessToken{
		UserID:      user.ID,
		JwtID:       jti,
		AccessToken: accessToken,
		UserAgent:   userAgent,
		IPAddress:   ipAddress,
		ExpiresAt:   accessClaims.ExpiresAt.Time,
		LastUsedAt:  time.Now(),
	}

	if err := tokenRepo.CreateToken(ctx, &dbAccessToken); err != nil {
		tx.Rollback()
		return nil, err
	}

	// 生成刷新令牌
	refreshClaims := jwt.RegisteredClaims{
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(refreshTokenTTL)),
		IssuedAt:  jwt.NewNumericDate(time.Now()),
		NotBefore: jwt.NewNumericDate(time.Now()),
		Issuer:    issuer,
		Subject:   user.Username,
		ID:        utils.GenerateUUID(),
	}

	refreshToken, err := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims).SignedString(getRefreshKey())
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	if err := tokenRepo.CreateRefreshToken(ctx, &domain.RefreshToken{
		UserID:        user.ID,
		AccessTokenID: dbAccessToken.ID,
		RefreshToken:  refreshToken,
		ExpiresAt:     refreshClaims.ExpiresAt.Time,
	}); err != nil {
		tx.Rollback()
		return nil, err
	}

	if tx.Commit().Error != nil {
		return nil, tx.Error
	}

	return &domain.TokenPair{
		AccessToken:      accessToken,
		ExpiresAt:        accessClaims.ExpiresAt.Time,
		RefreshToken:     refreshToken,
		RefreshExpiresAt: refreshClaims.ExpiresAt.Time,
	}, nil
}

func (s *tokenService) ParseToken(tokenString string) (*TokenClaims, bool, error) {
	claims := &TokenClaims{}
	isExpired := false

	_, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, domain.ErrTokenInvalid
		}
		return getSecretKey(), nil
	})

	if err != nil {
		if errors.Is(err, jwt.ErrTokenExpired) {
			isExpired = true
		} else {
			return nil, isExpired, err
		}
	}

	return claims, isExpired, nil
}

func (s *tokenService) GetTokenByJwtID(ctx context.Context, jwtID string) (*domain.AccessToken, error) {
	return s.tokenRepo.GetTokenByJwtID(ctx, jwtID)
}

func (s *tokenService) GetRefreshToken(ctx context.Context, refreshToken string) (*domain.RefreshToken, error) {
	return s.tokenRepo.GetRefreshToken(ctx, refreshToken)
}

func (s *tokenService) UpdateLastUsedAt(ctx context.Context, jwtID string) error {
	return s.tokenRepo.UpdateLastUsedAt(ctx, jwtID)
}

func (s *tokenService) RevokeToken(ctx context.Context, id int64) error {
	return s.tokenRepo.RevokeToken(ctx, id)
}

func (s *tokenService) RevokeTokenByJwtID(ctx context.Context, jwtID string) error {
	return s.tokenRepo.RevokeTokenByJwtID(ctx, jwtID)
}

func (s *tokenService) RevokeUserTokens(ctx context.Context, userID int64) error {
	return s.tokenRepo.RevokeUserTokens(ctx, userID)
}

func (s *tokenService) RevokeRefreshToken(ctx context.Context, id int64) error {
	return s.tokenRepo.RevokeRefreshToken(ctx, id)
}

func getSecretKey() []byte {
	return []byte(config.AppConfig.Auth.SecretKey)
}

func getRefreshKey() []byte {
	return []byte(config.AppConfig.Auth.RefreshSecretKey)
}
