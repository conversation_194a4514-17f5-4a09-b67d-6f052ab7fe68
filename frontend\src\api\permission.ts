import { apiWrapper } from '@/boot/axios';
export interface Permission {
  id: number;
  code: string;
}

export interface PermissionResponse {
  permissions: Permission[];
  group_permissions: Permission[];
}

export interface PermissionFilter {
  group_id?: number;
}

export const PermissionApi = {
  fetch: (filter?: PermissionFilter) =>
    apiWrapper.get<PermissionResponse>('v1/permissions', {
      params: {
        ...filter,
      },
    }),
};
