.stock-requisition-page {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
  animation: pageTransition 0.4s ease-out;

  .page-header {
    margin-bottom: 30px;
    position: relative;
    padding-bottom: 15px;
    text-align: center;

    h1 {
      font-size: 2.3rem;
      font-weight: 700;
      margin: 0 auto 25px;
      display: block;
      width: fit-content;
      position: relative;
      padding-bottom: 15px;
      animation: titleFade 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      opacity: 0;

      // 深藍到青藍漸層
      background: linear-gradient(135deg, #2d3436 0%, #0984e3 100%);
      
      /* 標準屬性優先 */
      background-clip: text;
      -webkit-background-clip: text; /* 瀏覽器前綴次之 */
      
      /* 文字透明處理 */
      color: transparent;
      -webkit-text-fill-color: transparent; /* 兼容舊版WebKit核心 */

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0;
        width: 60px;
        height: 3px;
        background: #0984e3;
        border-radius: 2px;
        animation: underlineExpand 0.6s 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      }

      &:hover::after {
        width: 80px;
        background: linear-gradient(90deg, #0984e3 0%, #00cec9 100%);
        transition: all 0.3s ease;
      }
    }

    + .q-card {
      margin-top: 30px;
    }
  }

  .basic-info-card,
  .products-card {
    margin-bottom: 16px;
    border-radius: 8px;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
  }

  .requisition-table {
    .q-table__container {
      background-color: white;
      border-radius: 8px;
    }
  }

  .summary-section {
    .summary-card {
      padding: 16px;
      background-color: #f5f5f5;
      border-radius: 8px;
    }

    .total-quantity {
      font-size: 1.2em;
      color: var(--q-primary);
    }
  }

  .status-badge {
    &.pending {
      background-color: #ffd700;
    }
    &.approved {
      background-color: #4caf50;
    }
    &.rejected {
      background-color: #f44336;
    }
    &.completed {
      background-color: #2196f3;
    }
  }

  .department-section {
    margin-bottom: 16px;
  }

  .remarks-field {
    width: 100%;
  }

  @media (max-width: 600px) {
    .page-header h1 {
      font-size: 1.8rem;

      &::after {
        width: 40px;
        height: 2px;
      }

      &:hover::after {
        width: 50px;
      }
    }
  }
}

// 共用動畫定義
@keyframes pageTransition {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes titleFade {
  0% {
    opacity: 0;
    transform: translateY(-15px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes underlineExpand {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 60px;
    opacity: 1;
  }
} 