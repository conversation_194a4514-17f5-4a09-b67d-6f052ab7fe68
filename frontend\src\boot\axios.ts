import { boot } from 'quasar/wrappers';
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { useAuthStore } from '@/stores/auth-store';
import { ApiResponse } from '@/api/modules/response';
import { AuthApi } from '@/api/auth';
import { handleError } from '@/utils';

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $axios: AxiosInstance;
    $api: AxiosInstance;
  }
}

// Be careful when using SSR for cross-request state pollution
// due to creating a Singleton instance here;
// If any client changes this (global) instance, it might be a
// good idea to move this instance creation inside of the
// "export default () => {}" function below (which runs individually
// for each client)
const api = axios.create({
  baseURL: process.env.VUE_API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

export default boot(({ app, router }) => {
  // for use inside Vue files (Options API) through this.$axios and this.$api

  app.config.globalProperties.$axios = axios;
  // ^ ^ ^ this will allow you to use this.$axios (for Vue Options API form)
  //       so you won't necessarily have to import axios in each vue file

  app.config.globalProperties.$api = api;
  // ^ ^ ^ this will allow you to use this.$api (for Vue Options API form)
  //       so you can easily perform requests against your app's API

  let isRefreshing = false;
  // 待處理請求佇列
  let failedQueue: Array<{
    resolve: (value: unknown) => void;
    reject: (reason?: unknown) => void;
  }> = [];

  // 處理佇列中的請求
  const processQueue = (error: unknown, token: string | null = null) => {
    failedQueue.forEach((prom) => {
      if (error) {
        prom.reject(error);
      } else {
        prom.resolve(token);
      }
    });

    // 清空佇列
    failedQueue = [];
  };

  // 请求拦截器
  api.interceptors.request.use(
    (config) => {
      // 如果header沒有Authorization，則設定
      if (!config.headers?.Authorization) {
        const authStore = useAuthStore();

        if (authStore.accessToken) {
          config.headers.Authorization = `Bearer ${authStore.accessToken}`;
          config.headers['X-Refresh-Token'] = authStore.refreshToken;
        }
      }

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  api.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;

      switch (error.response?.status) {
        case 401:
          return handle401(originalRequest);
        case 403:
          router.push('/index');
          break;
        default:
          break;
      }

      return Promise.reject(error);
    }
  );

  const handle401 = async (
    originalRequest: AxiosRequestConfig & { _retry?: boolean }
  ) => {
    // 不包含的url
    const excludeUrls = ['v1/auth/logout', 'v1/auth/refresh-token'];

    // 如果是 401 錯誤且不是刷新 token 的請求
    if (
      !originalRequest._retry &&
      !excludeUrls.some((url) => originalRequest.url?.includes(url))
    ) {
      originalRequest._retry = true;
      const authStore = useAuthStore();

      // 如果目前沒有正在進行的重新整理請求
      if (!isRefreshing) {
        isRefreshing = true;

        try {
          // 嘗試刷新 token
          const refreshToken = localStorage.getItem('refreshToken');
          if (!refreshToken) {
            throw new Error('No refresh token');
          }

          const response = await AuthApi.refreshToken();
          const newToken = response.result;

          // 更新 token
          authStore.updateToken(newToken);

          // 設置重新整理狀態為 false
          isRefreshing = false;

          // 處理所有排隊的請求
          processQueue(null, newToken.access_token);

          // 重試原始請求
          if (!originalRequest.headers) {
            originalRequest.headers = {};
          }
          originalRequest.headers[
            'Authorization'
          ] = `Bearer ${newToken.access_token}`;
          originalRequest.headers['X-Refresh-Token'] = newToken.refresh_token;
          return api(originalRequest);
        } catch (refreshError) {
          // 刷新失敗，處理所有排隊的請求（拒絕）
          isRefreshing = false;
          processQueue(refreshError, null);

          // 登出並跳轉到登入頁
          authStore.logout();
          router.push('/login');
          return Promise.reject(refreshError);
        }
      } else {
        // 已經有一個重新整理 token 的請求正在進行中
        // 將這個請求放入佇列中，等待重新整理完成後再處理
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            if (!originalRequest.headers) {
              originalRequest.headers = {};
            }
            originalRequest.headers['Authorization'] = `Bearer ${token}`;
            originalRequest.headers['X-Refresh-Token'] = authStore.refreshToken;
            return api(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }
    }
  };
});

// 請求方法包裝器
export const apiWrapper = {
  async get<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await api.get<ApiResponse<T>>(url, config);
      return response.data;
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },

  async post<T>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await api.post<ApiResponse<T>>(url, data, config);
      return response?.data;
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },

  async put<T>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await api.put<ApiResponse<T>>(url, data, config);
      return response.data;
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },

  async patch<T>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await api.patch<ApiResponse<T>>(url, data, config);
      return response.data;
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },

  async delete<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await api.delete<ApiResponse<T>>(url, config);
      return response.data;
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },

  async uploadFile<T>(
    url: string,
    file: File,
    additionalData?: Record<string, string | Blob>,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      // 添加任何其他數據到 FormData
      if (additionalData) {
        Object.entries(additionalData).forEach(([key, value]) => {
          formData.append(key, value);
        });
      }

      // 創建一個新的配置，確保不覆蓋 Content-Type
      // Axios 會自動為 FormData 設置正確的 Content-Type 和邊界
      const uploadConfig: AxiosRequestConfig = {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      };

      const response = await api.post<ApiResponse<T>>(
        url,
        formData,
        uploadConfig
      );
      return response.data;
    } catch (error) {
      handleError(error);
      return Promise.reject(error);
    }
  },
};

export { api };
