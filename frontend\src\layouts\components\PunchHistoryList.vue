<template>
  <div class="punch-history-list q-py-md">
    <q-list bordered separator>
      <q-item v-if="items.length === 0">
        <q-item-section>
          {{ t('error.noData') }}
        </q-item-section>
      </q-item>
      <q-item v-for="item in items" :key="item.uuid">
        <q-item-section>
          {{ item.user.name }}
        </q-item-section>
        <q-item-section>
          {{ formatDate(item.clock_time, 'YYYY-MM-DD HH:mm') }}
        </q-item-section>
        <q-item-section side>
          <q-icon
            name="arrow_upward"
            color="positive"
            v-if="item.type === 'clock_in'"
          />
          <q-icon name="arrow_downward" color="negative" v-else />
        </q-item-section>
      </q-item>
    </q-list>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { AttendanceApi, Clock } from '@/api/attendance';
import { UserInfo } from '@/api/user';
import { formatDate } from '@/utils';

const { t } = useI18n();

const props = defineProps<{
  userInfo: UserInfo;
}>();

const isLoading = ref(false);
const items = ref<Clock[]>([]);
const fetchData = async () => {
  try {
    isLoading.value = true;
    const response = await AttendanceApi.fetch({
      filter: {
        user_uuid: props.userInfo.uuid,
        // 只顯示7日內打卡記錄
        start_date: formatDate(
          new Date().setDate(new Date().getDate() - 7),
          'YYYY-MM-DD'
        ),
      },
    });

    items.value = response.result.data;
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  fetchData();
});
</script>
