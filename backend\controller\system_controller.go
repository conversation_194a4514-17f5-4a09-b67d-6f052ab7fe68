package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type SystemController struct {
	systemService service.SystemService
}

func NewSystemController(r *gin.RouterGroup, systemService service.SystemService) {
	sysController := SystemController{systemService}

	v1 := r.Group("/v1/systems")
	{
		v1.GET("", sysController.GetSystemOptionsHandler)
		v1.PUT("", sysController.UpdateSystemOptionsHandler)
	}
}

func (ctr *SystemController) GetSystemOptionsHandler(c *gin.Context) {
	systemOptions, err := ctr.systemService.Get(c)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to fetch system options")
		return
	}

	utils.HandleSuccess(c, systemOptions)
}

func (ctr *SystemController) UpdateSystemOptionsHandler(c *gin.Context) {
	var payload domain.SystemOptionUpdatePayload
	if err := c.ShouldBind(&payload); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request payload")
		return
	}

	if err := ctr.systemService.Update(c, &payload); err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update system options")
		return
	}

	utils.HandleSuccess(c, nil)
}
