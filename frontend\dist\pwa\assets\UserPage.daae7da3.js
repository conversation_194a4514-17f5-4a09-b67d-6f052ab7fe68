import{Q as R}from"./QItemLabel.88fb9012.js";import{a as M,Q as A}from"./QItemSection.a2ef2d56.js";import{bh as S,d as K,u as O,c as ue,r as p,s as W,w as E,o as m,a as q,i as s,b as t,f as i,p as U,t as n,aS as B,q as d,g as ee,Q,aI as se,l as G,B as F,k as D,h as T,m as I,y as te,F as z,a$ as L,j as H,bF as X,bG as Y,z as Z,bH as de,bB as ie,v as ne}from"./index.b4716878.js";import{Q as J}from"./QScrollArea.3ec8d45a.js";import{Q as re}from"./QPage.cc993543.js";import{u as le}from"./use-quasar.59b22ad6.js";import{U as N}from"./user.8e7275ff.js";import{Q as ce}from"./QSelect.c42fe000.js";import{Q as oe}from"./QForm.b737edd8.js";import{u as me}from"./dialog.ba2bf79c.js";import{_ as ae}from"./plugin-vue_export-helper.21dcd24c.js";import"./QScrollObserver.3b8f1bba.js";import"./TouchPan.202cd306.js";import"./selection.7371c306.js";import"./format.054b8074.js";import"./QMenu.45353d3c.js";const j={fetch:()=>S.get("v1/user-groups"),get:v=>S.get(`v1/user-groups/${v}`),create:(v,b)=>S.post("v1/user-groups",{user_group:v,permission_codes:b}),update:(v,b)=>S.put(`v1/user-groups/${v.id}`,{user_group:v,permission_codes:b}),delete:v=>S.delete(`v1/user-groups/${v}`)};const pe={class:"col-1"},ve={class:"column fit"},_e={class:"row q-mb-md"},fe={class:"col-12 col-sm-2 text-subtitle1 text-md-center"},ge={class:"col-12 col-sm-10"},be={class:"row"},he={class:"col-12 col-sm-2 text-subtitle1 text-md-center"},ye={class:"col-12 col-sm-10"},we={class:"row q-mb-md"},xe={class:"col-12 col-sm-2 text-subtitle1 text-md-center"},qe={class:"col-12 col-sm-10"},$e={class:"row q-mb-md"},Ve={class:"col-12 col-sm-2 text-subtitle1 text-md-center"},Ue={class:"col-12 col-sm-10"},ke={class:"row q-mb-md"},Ce={class:"col-12 col-sm-2 text-subtitle1 text-md-center"},Qe={class:"col-12 col-sm-10"},ze={class:"row q-mb-md"},Pe={class:"col-12 col-sm-2 text-subtitle1 text-md-center"},Se={class:"col-12 col-sm-10"},De={key:0,class:"row q-mb-md"},Ge={class:"col-12 col-sm-2 text-subtitle1 text-md-center"},Be={class:"col-12 col-sm-10"},Ie={class:"row q-mb-md"},Ne={class:"col-12 col-sm-2 text-subtitle1 text-md-center"},Re={class:"col-12 col-sm-10"},Me={class:"col-1"},Ae=K({__name:"UserProfile",props:{id:{},groups:{}},emits:["dataUpdated","close"],setup(v,{emit:b}){const{t:a}=O(),$=le(),V=me(),g=ue(()=>e.value.uuid?`${a("user.label")} - ${e.value.name}`:a("createUser")),_=p(!1),y=p(null),e=p({uuid:"",name:"",group_id:0,username:"",password:"",email:"",is_active:!0,is_admin:!1,note:""}),h=p([]),w=p(""),f=v,x=b;W(()=>{C(f.id),h.value=f.groups}),E(()=>f.id,l=>{C(l)});const C=async l=>{var u;(u=y.value)==null||u.resetValidation(),e.value={uuid:"",name:"",group_id:0,username:"",password:"",email:"",is_active:!0,is_admin:!1,note:""},l&&await P()};E(()=>f.groups,l=>{h.value=l});const P=async()=>{const l=await N.get(f.id);e.value=l.result},c=async()=>{try{if(_.value=!0,e.value.uuid)await N.update(e.value);else{const l=await N.create({...e.value,password:e.value.password});e.value.uuid=l.result.uuid}L.create({message:a("success"),position:"top",color:"positive"}),x("close")}finally{_.value=!1,e.value.password="",w.value="",x("dataUpdated")}},r=async()=>{V.showMessage({message:"",title:a("confirmDelete"),timeout:0,ok:async()=>{try{_.value=!0,e.value.uuid&&await N.delete(e.value.uuid),L.create({message:a("success"),position:"top",color:"positive"}),x("close")}finally{_.value=!1,x("dataUpdated")}}})};return(l,u)=>(m(),q(z,null,[s("div",pe,[t(A,{dense:""},{default:i(()=>[t(M,null,{default:i(()=>[t(R,{header:"",class:"text-h6 text-weight-bold q-pa-sm"},{default:i(()=>[U(n(g.value),1)]),_:1})]),_:1})]),_:1}),t(B)]),t(te,{class:"col-10"},{default:i(()=>[t(d(oe),{ref_key:"formRef",ref:y,onSubmit:ee(c,["prevent"]),greedy:"",autocomplete:"off",class:"fit q-px-md-xl"},{default:i(()=>[s("div",ve,[t(J,{visible:"",class:"col-11 q-pr-md q-px-sm-xl"},{default:i(()=>[s("div",_e,[s("div",fe,n(d(a)("name")),1),s("div",ge,[t(Q,{type:"text",modelValue:e.value.name,"onUpdate:modelValue":u[0]||(u[0]=o=>e.value.name=o),outlined:"",dense:"","hide-bottom-space":"",rules:[o=>!!o||d(a)("error.required")],"lazy-rules":"",class:"q-px-none"},null,8,["modelValue","rules"])])]),s("div",be,[s("div",he,n(d(a)("group")),1),s("div",ye,[t(ce,{modelValue:e.value.group_id,"onUpdate:modelValue":u[1]||(u[1]=o=>e.value.group_id=o),options:h.value,"option-label":"name","option-value":"id",outlined:"",dense:"","hide-bottom-space":"","emit-value":"","map-options":"",rules:[o=>!!o||d(a)("error.required")],"lazy-rules":""},null,8,["modelValue","options","rules"])])]),t(B,{class:"q-my-lg"}),s("div",we,[s("div",xe,n(d(a)("account")),1),s("div",qe,[t(Q,{type:"text",name:"username",modelValue:e.value.username,"onUpdate:modelValue":u[2]||(u[2]=o=>e.value.username=o),maxlength:"50",outlined:"",dense:"","hide-bottom-space":"",disable:e.value.uuid!=="",rules:[o=>!!o||d(a)("error.required")],"lazy-rules":""},null,8,["modelValue","disable","rules"])])]),s("div",$e,[s("div",Ve,n(d(a)("password")),1),s("div",Ue,[t(Q,{type:"password",name:"password",modelValue:e.value.password,"onUpdate:modelValue":u[3]||(u[3]=o=>e.value.password=o),maxlength:"50",outlined:"",dense:"","hide-bottom-space":"",rules:[o=>!!o||e.value.uuid!==""||d(a)("error.required")],"lazy-rules":""},null,8,["modelValue","rules"])])]),s("div",ke,[s("div",Ce,n(d(a)("confirmPassword")),1),s("div",Qe,[t(Q,{type:"password",name:"confirmPassword",modelValue:w.value,"onUpdate:modelValue":u[4]||(u[4]=o=>w.value=o),maxlength:"50",outlined:"",dense:"","hide-bottom-space":"",rules:[o=>o===e.value.password||!e.value.password||d(a)("error.passwordNotMatch")],"lazy-rules":""},null,8,["modelValue","rules"])])]),t(B,{class:"q-my-lg"}),s("div",ze,[s("div",Pe,n(d(a)("email.label")),1),s("div",Se,[t(Q,{type:"email",name:"email",modelValue:e.value.email,"onUpdate:modelValue":u[5]||(u[5]=o=>e.value.email=o),maxlength:"50",outlined:"",dense:"","hide-bottom-space":"","lazy-rules":""},null,8,["modelValue"])])]),e.value.uuid?(m(),q("div",De,[s("div",Ge,n(d(a)("status")),1),s("div",Be,[t(se,{modelValue:e.value.is_active,"onUpdate:modelValue":u[6]||(u[6]=o=>e.value.is_active=o),color:"positive"},null,8,["modelValue"])])])):G("",!0),s("div",Ie,[s("div",Ne,n(d(a)("note.label")),1),s("div",Re,[t(Q,{type:"textarea",name:"note",modelValue:e.value.note,"onUpdate:modelValue":u[7]||(u[7]=o=>e.value.note=o),maxlength:"255",outlined:"",dense:"","hide-bottom-space":"",rows:"10"},null,8,["modelValue"])])])]),_:1}),s("div",Me,[s("div",{class:F(["row q-mt-md q-pr-md",{"justify-between":!!e.value.uuid,"justify-end":!e.value.uuid}])},[e.value.uuid?(m(),D(I,{key:0,type:"button",color:"negative",onClick:r,size:d($).screen.lt.md?"sm":"md"},{default:i(()=>[t(T,{name:"delete"})]),_:1},8,["size"])):G("",!0),t(I,{type:"submit",label:d(a)("submit"),color:"submit",size:d($).screen.lt.md?"sm":"md",loading:_.value},null,8,["label","size","loading"])],2)])])]),_:1},512)]),_:1})],64))}});var Fe=ae(Ae,[["__scopeId","data-v-656d8864"]]);const Te={fetch:v=>S.get("v1/permissions",{params:{...v}})},je={class:"col-1"},Ee={class:"column fit"},Le={class:"row q-mb-md"},He={class:"col-12 col-sm-2 text-subtitle1 text-md-center"},Ke={class:"col-12 col-sm-10"},Oe={class:"row q-mt-lg q-mb-md"},We={class:"col-12 text-h6 text-weight-bold"},Je={class:"row q-mb-md q-mt-lg"},Xe={class:"col-12 text-h6"},Ye={class:"col-12 col-sm-2 text-subtitle1 text-md-center"},Ze={class:"col-12 col-sm-10"},es={class:"row col-1"},ss={class:"offset-7 offset-md-10"},ts=K({__name:"UserGroup",props:{id:{}},emits:["dataUpdated","close"],setup(v,{emit:b}){const{t:a}=O(),$=v,V=b,g=p(""),_=p(!1),y=p(null),e=p({id:0,name:""}),h=p([]),w=p([]),f=p({});W(()=>{x($.id)});const x=async c=>{var r;(r=y.value)==null||r.resetValidation(),g.value=a("createGroup"),e.value={id:0,name:""},c&&(e.value.id=c,await C())};E(()=>$.id,c=>{x(c)});const C=async()=>{const c=await Promise.all([j.get(e.value.id),Te.fetch({group_id:e.value.id})]),r=c[0].result,l=c[1].result;e.value=r,h.value=l.permissions,w.value=l.group_permissions;for(const u of h.value)f.value[u.code]=w.value?w.value.some(o=>o.code===u.code):!1;g.value=`${a("group")} - ${e.value.name}`},P=async()=>{try{_.value=!0;const c=h.value.filter(r=>f.value[r.code]).map(r=>r.code);if($.id)await j.update(e.value,c);else{const r=await j.create(e.value,c);e.value.id=r.result.id}C(),L.create({message:a("success"),position:"top",color:"positive"})}finally{_.value=!1,V("dataUpdated")}};return(c,r)=>(m(),q(z,null,[s("div",je,[t(A,{dense:""},{default:i(()=>[t(M,null,{default:i(()=>[t(R,{header:"",class:"text-h6 text-weight-bold q-pa-sm"},{default:i(()=>[U(n(g.value),1)]),_:1})]),_:1})]),_:1}),t(B)]),t(te,{class:"col-10"},{default:i(()=>[t(d(oe),{ref_key:"formRef",ref:y,onSubmit:ee(P,["prevent"]),greedy:"",autocomplete:"off",class:"fit q-px-md-xl"},{default:i(()=>[s("div",Ee,[t(J,{visible:"",class:"col-11"},{default:i(()=>[s("div",Le,[s("div",He,n(d(a)("group")),1),s("div",Ke,[t(Q,{type:"text",modelValue:e.value.name,"onUpdate:modelValue":r[0]||(r[0]=l=>e.value.name=l),maxlength:"50",outlined:"",dense:"","hide-bottom-space":"",rules:[l=>!!l||d(a)("error.required")],"lazy-rules":""},null,8,["modelValue","rules"])])]),s("div",Oe,[s("div",We,n(d(a)("permissions")),1)]),s("div",Je,[s("div",Xe,[U(n(d(a)("order.label"))+" ",1),t(B,{class:"q-my-sm"})])]),(m(!0),q(z,null,H(h.value.filter(l=>l.code.includes("order")),l=>(m(),q("div",{class:"row q-mb-md",key:l.code},[s("div",Ye,n(d(a)(`permission.${l.code}`)),1),s("div",Ze,[t(se,{dense:"",modelValue:f.value[l.code],"onUpdate:modelValue":u=>f.value[l.code]=u,color:"positive",size:"lg"},null,8,["modelValue","onUpdate:modelValue"])])]))),128))]),_:1}),s("div",es,[s("div",ss,[t(I,{type:"submit",label:d(a)("submit"),color:"submit",class:"q-mt-md",loading:_.value},null,8,["label","loading"])])])])]),_:1},512)]),_:1})],64))}});const ls={class:"col-1"},os={class:"row items-center"},as={key:0,class:"col-auto q-px-sm"},us={class:"col-2 col-md-1"},ds={class:"column q-col-gutter-md q-pa-sm"},is={class:"col"},ns={class:"col"},rs=K({__name:"UserPage",setup(v){const{t:b}=O(),a=le(),$=p(),V=p([]),g=p(""),_=X(null),y=X({id:"",groups:[]}),e=()=>{_.value=null,y.value={id:"",groups:[]},g.value=""},h=p(),w={profile:Y(Fe),group:Y(ts)},f=l=>{_.value=w.profile,y.value={id:l,groups:V.value},h.value=P,l?g.value=l:g.value="new"},x=l=>{_.value=w.group,y.value={id:l},h.value=c,l?g.value=l:g.value="new"},C=async()=>{try{await Promise.all([P(),c()])}catch(l){ne(l)}},P=()=>N.fetch().then(l=>{$.value=l.result}),c=()=>j.fetch().then(l=>{V.value=l.result});W(()=>{C()});const r=l=>{var u;return((u=$.value)==null?void 0:u.filter(o=>o.group_id===l.id))||[]};return(l,u)=>(m(),D(re,{class:"row"},{default:i(()=>[t(Z,{flat:"",square:"",bordered:"",class:"col-4 col-md-3 column"},{default:i(()=>[s("div",ls,[s("div",os,[t(A,{dense:"",class:"col-grow"},{default:i(()=>[t(M,null,{default:i(()=>[t(R,{header:"",class:"text-h6 text-weight-bold q-pa-sm"},{default:i(()=>[U(n(d(b)("user.menu")),1)]),_:1})]),_:1})]),_:1}),d(a).screen.gt.sm&&V.value.length>0?(m(),q("div",as,[t(I,{type:"button",onClick:u[0]||(u[0]=o=>f("")),icon:"add",color:"create",size:"sm",class:"q-pa-sm"})])):G("",!0)]),t(B)]),t(J,{visible:"",class:"col-8 col-md-9"},{default:i(()=>[(m(!0),q(z,null,H(V.value,o=>(m(),q(z,{key:o.id},[t(A,{clickable:"",onClick:k=>x(o.id),class:F(["group-item",{active:g.value===o.id}])},{default:i(()=>[t(M,null,{default:i(()=>[t(R,null,{default:i(()=>[U(n(o.name),1)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onClick","class"]),(m(!0),q(z,null,H(r(o),k=>(m(),D(A,{key:k.uuid,clickable:"",onClick:cs=>f(k.uuid),class:F(["user-item",{active:g.value===k.uuid}])},{default:i(()=>[t(M,null,{default:i(()=>[t(R,{class:F({"text-negative":!k.is_active})},{default:i(()=>[k.is_admin?(m(),D(T,{key:0,name:"star",size:"xs"})):G("",!0),U(" "+n(k.name)+" ",1),k.is_active?G("",!0):(m(),q(z,{key:1},[U(" ("+n(d(b)("disabled"))+") ",1)],64))]),_:2},1032,["class"])]),_:2},1024)]),_:2},1032,["onClick","class"]))),128))],64))),128))]),_:1}),s("div",us,[s("div",ds,[s("div",is,[d(a).screen.lt.md&&V.value.length>0?(m(),D(I,{key:0,type:"button",onClick:u[1]||(u[1]=o=>f("")),color:"create",size:d(a).screen.lt.md?"sm":"md"},{default:i(()=>[t(T,{name:"add"}),U(" "+n(d(b)("account")),1)]),_:1},8,["size"])):G("",!0)]),s("div",ns,[t(I,{type:"button",onClick:u[2]||(u[2]=o=>x(0)),color:"create",size:d(a).screen.lt.md?"sm":"md"},{default:i(()=>[t(T,{name:"add"}),U(" "+n(d(b)("group")),1)]),_:1},8,["size"])])])])]),_:1}),t(Z,{flat:"",square:"",bordered:"",class:"col-8 col-md-9 column"},{default:i(()=>[(m(),D(de(_.value),ie(y.value,{onDataUpdated:h.value,onClose:e}),null,16,["onDataUpdated"]))]),_:1})]),_:1}))}});var Cs=ae(rs,[["__scopeId","data-v-79bc92ac"]]);export{Cs as default};
