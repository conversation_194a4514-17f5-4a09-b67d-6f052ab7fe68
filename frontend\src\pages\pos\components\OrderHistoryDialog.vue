<template>
  <q-dialog v-model="visible" class="card-dialog" no-refocus>
    <q-card class="column full-width">
      <!-- header -->
      <q-card-section class="col-1 q-py-none">
        <div class="row q-pt-sm">
          <!-- title -->
          <q-tabs
            v-model="tab"
            active-color="primary"
            narrow-indicator
            class="text-bold"
          >
            <q-tab
              name="salesStatistics"
              :label="t('order.salesStatistics')"
              no-caps
            />
            <q-tab name="history" :label="t('order.history')" no-caps />
          </q-tabs>
          <q-space />
          <!-- close -->
          <q-btn
            type="button"
            icon="close"
            flat
            round
            dense
            @click="emit('update:modelValue', false)"
          />
        </div>
      </q-card-section>

      <q-card-section class="col-11 q-pa-none">
        <q-tab-panels v-model="tab" class="full-height">
          <!-- Sales Statistics -->
          <q-tab-panel name="salesStatistics" class="q-pa-sm">
            <q-scroll-area class="full-height">
              <div class="q-mx-md">
                <!-- Order Date -->
                <div class="row q-mb-md">
                  <div class="col-12">
                    <DateRangePicker
                      v-model="salesDate"
                      :from-label="t('orderDate')"
                      :show-separator="false"
                      :show-to-date="false"
                      :show-clear-button="false"
                      @update:model-value="getStatsOrders"
                    />
                  </div>
                </div>

                <!-- 訂單統計 -->
                <div class="row q-col-gutter-md q-mb-md">
                  <div class="col-12 col-md-4">
                    <q-card class="shadow-1">
                      <q-card-section>
                        <div
                          class="text-subtitle1 text-weight-medium text-grey-8"
                        >
                          {{ t('order.label') }}
                        </div>
                        <div class="row justify-between items-end q-mt-sm">
                          <div>
                            <div class="text-h4 text-weight-bold text-green">
                              {{ stats.effectiveOrders }}
                            </div>
                            <div class="text-caption text-grey">
                              {{ t('order.qty') }}
                            </div>
                          </div>
                          <div class="text-h6 text-weight-medium text-green">
                            AU$ {{ formatNumber(stats.effectiveAmount, 2) }}
                          </div>
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>

                  <div class="col-12 col-md-4">
                    <q-card class="shadow-1">
                      <q-card-section>
                        <div
                          class="text-subtitle1 text-weight-medium text-grey-8"
                        >
                          {{ t('order.void') }}
                        </div>
                        <div class="row justify-between items-end q-mt-sm">
                          <div>
                            <div class="text-h4 text-weight-bold text-negative">
                              {{ stats.voidOrders }}
                            </div>
                            <div class="text-caption text-grey">
                              {{ t('order.qty') }}
                            </div>
                          </div>
                          <div class="text-h6 text-weight-medium text-negative">
                            AU$ {{ formatNumber(stats.voidAmount, 2) }}
                          </div>
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>

                  <div class="col-12 col-md-4">
                    <q-card class="shadow-1">
                      <q-card-section>
                        <div
                          class="text-subtitle1 text-weight-medium text-grey-8"
                        >
                          {{ t('total') }}
                        </div>
                        <div class="row justify-between items-end q-mt-sm">
                          <div>
                            <div class="text-h4 text-weight-bold text-primary">
                              {{ stats.totalOrders }}
                            </div>
                            <div class="text-caption text-grey">
                              {{ t('order.total') }}
                            </div>
                          </div>
                          <div class="text-h6 text-weight-medium text-primary">
                            AU$ {{ formatNumber(stats.totalAmount, 2) }}
                          </div>
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>
                </div>

                <!-- 付款方式統計 -->
                <div class="row q-mb-md">
                  <div class="col-12">
                    <q-card class="shadow-1 full-width">
                      <q-card-section>
                        <div
                          class="text-subtitle1 text-weight-medium text-grey-8 q-mb-md"
                        >
                          {{ t('payment.label') }}
                        </div>
                        <div class="row q-col-gutter-md">
                          <div
                            v-for="payment in stats.paymentStats"
                            :key="payment.type"
                            class="col-12 col-md-3"
                          >
                            <q-card>
                              <q-card-section>
                                <div class="row items-center">
                                  <q-badge
                                    :color="payment.color"
                                    class="q-mr-md"
                                  />
                                  <span class="text-h6 q-mr-xl">
                                    {{ payment.label }}
                                    <div class="text-subtitle1 text-grey">
                                      {{
                                        t('order.count', {
                                          count: payment.count,
                                        })
                                      }}
                                    </div>
                                  </span>
                                  <span class="text-h6 text-weight-medium">
                                    AU$ {{ formatNumber(payment.amount, 2) }}
                                  </span>
                                </div>
                              </q-card-section>
                            </q-card>
                          </div>
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>
                </div>

                <!-- 訂單列表 -->
                <div class="row q-mb-md">
                  <div class="col-12">
                    <q-card class="shadow-1 full-width">
                      <q-card-section>
                        <div
                          class="text-subtitle1 text-weight-medium text-grey-8 q-mb-md"
                        >
                          {{ t('order.list') }}
                        </div>
                        <q-table
                          :rows="statsOrders"
                          :columns="statsColumns"
                          row-key="uuid"
                          :rows-per-page-options="[10, 20, 50]"
                          :pagination="{ rowsPerPage: 10 }"
                          table-header-class="bg-grey-3"
                          flat
                          bordered
                        >
                          <!-- Header -->
                          <template v-slot:header="props">
                            <q-tr
                              :props="props"
                            >
                              <q-th
                                v-for="col in props.cols"
                                :key="col.name"
                                :props="props"
                              >
                                <span class="text-subtitle2 text-bold">
                                  {{ col.label }}
                                </span>
                              </q-th>
                            </q-tr>
                          </template>
                          <!-- Body -->
                          <template v-slot:body="props">
                            <q-tr
                              :props="props"
                              @click="showOrderDetail(props.row.uuid)"
                              :class="
                                props.row.status === 'void' ? 'bg-red-1' : ''
                              "
                            >
                              <q-td key="order_no" :props="props">
                                <div class="text-subtitle1">
                                  {{ props.row.order_no }}
                                </div>
                              </q-td>
                              <q-td key="order_at" :props="props">
                                <div class="text-subtitle1">
                                  {{
                                    formatDate(
                                      props.row.order_at,
                                      'YYYY-MM-DD HH:mm'
                                    )
                                  }}
                                </div>
                              </q-td>
                              <q-td key="invoice_number" :props="props">
                                <div class="text-subtitle1">
                                  {{ props.row.xero_sync?.xero_invoice_no || '-' }}
                                </div>
                              </q-td>
                              <q-td
                                key="total"
                                :props="props"
                                class="text-weight-medium"
                              >
                                <div class="text-h6">
                                  AU$ {{ formatNumber(props.row.total, 2) }}
                                </div>
                              </q-td>
                              <q-td key="pay_type" :props="props">
                                <q-badge
                                  :color="getPaymentColor(props.row.pay_type)"
                                  text-color="white"
                                  :label="getPaymentLabel(props.row.pay_type)"
                                  class="text-subtitle2"
                                />
                              </q-td>
                              <q-td key="status" :props="props">
                                <q-badge
                                  :color="getOrderStatusColor(props.row.status)"
                                  text-color="white"
                                  :label="getOrderStatusLabel(props.row.status)"
                                  class="text-subtitle2"
                                />
                              </q-td>
                            </q-tr>
                          </template>
                        </q-table>
                      </q-card-section>
                    </q-card>
                  </div>
                </div>
              </div>
            </q-scroll-area>
          </q-tab-panel>
          <!-- History -->
          <q-tab-panel name="history">
            <q-scroll-area class="full-height">
              <q-table
                virtual-scroll
                :rows="items"
                :columns="columns"
                row-key="uuid"
                :rows-per-page-options="[20]"
                table-header-class="bg-grey-2"
              >
                <!-- Top -->
                <template v-slot:top>
                  <div class="row">
                    <DateRangePicker
                      v-model="dateRange"
                      @update:model-value="getHistories"
                    />
                  </div>
                </template>
                <!-- Body -->
                <template v-slot:body="props">
                  <q-tr
                    :props="props"
                    @click="showOrderDetail(props.row.uuid)"
                    :class="getOrderStatusColor(props.row.status)"
                  >
                    <q-td :props="props" key="order_no">
                      {{ props.row.order_no }}
                    </q-td>
                    <q-td :props="props" key="order_at">
                      {{ formatDate(props.row.order_at, 'YYYY-MM-DD HH:mm') }}
                    </q-td>
                    <q-td :props="props" key="invoice_number">
                      {{ props.row.xero_sync?.xero_invoice_no || '-' }}
                    </q-td>
                    <q-td :props="props" key="customer">
                      <template v-if="props.row.customer.name">
                        {{ props.row.customer.name }}
                      </template>
                      <template v-else>
                        {{ t('unknown.customer') }}
                      </template>
                    </q-td>
                    <q-td :props="props" key="total">
                      <div class="text-h6">
                        AU$ {{ props.row.total }}
                      </div>
                    </q-td>
                    <q-td :props="props" key="status" class="text-bold">
                      {{ getOrderStatusLabel(props.row.status) }}
                    </q-td>
                  </q-tr>
                </template>
              </q-table>
            </q-scroll-area>
          </q-tab-panel>
        </q-tab-panels>
      </q-card-section>
    </q-card>
  </q-dialog>

  <OrderDetailDialog
    v-model="showDetailDialog"
    :orderID="selectedOrderID"
    @refresh="getHistories"
  />
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { OrderApi, Order } from '@/api/order';
import {
  getOrderStatusColor,
  getOrderStatusLabel,
  getPaymentColor,
  getPaymentLabel,
} from '@/types/order';
import { formatDate, formatNumber } from '@/utils';
import OrderDetailDialog from '@/components/OrderDetailDialog.vue';
import DateRangePicker from '@/components/DateRangePicker.vue';

const { t } = useI18n();

const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits(['update:modelValue']);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const tab = ref('salesStatistics');

// Sales Statistics
interface PaymentStats {
  type: string;
  label: string;
  amount: number;
  count: number;
  color: string;
}

interface Stats {
  totalOrders: number;
  totalAmount: number;
  voidOrders: number;
  voidAmount: number;
  effectiveOrders: number;
  effectiveAmount: number;
  paymentStats: PaymentStats[];
}

const statsOrders = ref<Order[]>([]);
const salesDate = ref({
  from: '',
});
const statsColumns = computed(() => [
  {
    name: 'order_no',
    label: t('orderNo'),
    field: 'order_no',
    align: 'left' as const,
  },
  {
    name: 'order_at',
    label: t('orderDate'),
    field: 'order_at',
    align: 'left' as const,
  },
  {
    name: 'invoice_number',
    label: t('xero.invoices.invoiceNumber'),
    field: 'invoice_number',
    align: 'left' as const,
  },
  {
    name: 'total',
    label: t('total'),
    field: 'total',
    align: 'right' as const,
    sortable: true,
  },
  {
    name: 'pay_type',
    label: t('payType'),
    field: 'pay_type',
    align: 'center' as const,
  },
  {
    name: 'status',
    label: t('status'),
    field: 'status',
    align: 'center' as const,
  },
]);
const getStatsOrders = async () => {
  const response = await OrderApi.fetch({
    filter: {
      status: ['completed', 'void'],
      start_date: salesDate.value.from,
    },
  });

  statsOrders.value = response.result.data;
  calculateStats(statsOrders.value);
};

const stats = ref<Stats>({
  totalOrders: 0,
  totalAmount: 0,
  voidOrders: 0,
  voidAmount: 0,
  effectiveOrders: 0,
  effectiveAmount: 0,
  paymentStats: [],
});

// 計算統計數據
const calculateStats = (orderData: Order[]) => {
  const validOrders = orderData.filter((order) => order.status !== 'void');
  const voidOrders = orderData.filter((order) => order.status === 'void');

  // 計算付款方式統計
  const paymentMap = new Map<string, { amount: number; count: number }>();

  validOrders.forEach((order) => {
    const payType = order.pay_type;
    const current = paymentMap.get(payType) || { amount: 0, count: 0 };
    paymentMap.set(payType, {
      amount: current.amount + order.total,
      count: current.count + 1,
    });
  });

  const paymentStats: PaymentStats[] = Array.from(paymentMap.entries()).map(
    ([type, data]) => ({
      type,
      label: getPaymentLabel(type),
      amount: data.amount,
      count: data.count,
      color: getPaymentColor(type),
    })
  );

  stats.value.totalOrders = orderData.length;
  stats.value.totalAmount = validOrders.reduce(
    (sum, order) => sum + order.total,
    0
  );
  stats.value.voidOrders = voidOrders.length;
  stats.value.voidAmount = voidOrders.reduce(
    (sum, order) => sum + order.total,
    0
  );
  stats.value.effectiveOrders = validOrders.length;
  stats.value.effectiveAmount = validOrders.reduce(
    (sum, order) => sum + order.total,
    0
  );
  stats.value.paymentStats = paymentStats;
};

// History
const columns = computed(() => [
  {
    label: t('orderNo'),
    name: 'order_no',
    field: 'order_no',
    align: 'left' as const,
  },
  {
    name: 'order_at',
    label: t('orderDate'),
    field: 'order_at',
    align: 'left' as const,
  },
  {
    name: 'invoice_number',
    label: t('xero.invoices.invoiceNumber'),
    field: 'invoice_number',
    align: 'left' as const,
  },
  {
    name: 'customer',
    label: t('customer.label'),
    field: 'customer',
    align: 'left' as const,
  },
  {
    name: 'total',
    label: t('total'),
    field: 'total',
    align: 'left' as const,
  },
  {
    name: 'status',
    label: t('status'),
    field: 'status',
    align: 'left' as const,
  },
]);

const items = ref<Order[]>([]);
const selectedOrderID = ref('');
const dateRange = ref({
  from: '',
  to: '',
});
const getHistories = async () => {
  const response = await OrderApi.fetch({
    filter: {
      status: ['completed', 'void'],
      start_date: dateRange.value.from,
      end_date: dateRange.value.to,
    },
  });
  items.value = response.result.data;
};

onMounted(() => {
  salesDate.value.from = formatDate(new Date(), 'YYYY/MM/DD');
  getStatsOrders();
  getHistories();
});

const showDetailDialog = ref(false);
const showOrderDetail = (orderID: string) => {
  selectedOrderID.value = orderID;
  showDetailDialog.value = true;
};
</script>
