import{d as ee,aF as te,u as ae,r as y,aA as F,c as k,s as oe,o as g,k as w,f as t,i as m,b as e,y as V,t as i,h as le,m as h,p as r,l as $,a as L,z as O,q as B,j as se,F as ie,aH as ne,x as re}from"./index.9477d5a3.js";import{Q as ue}from"./QBanner.7a7a9f89.js";import{Q as ce,b as q}from"./QSelect.c4b20219.js";import{Q as A}from"./QTd.6cf74813.js";import{Q as de}from"./QTooltip.7e86cfcb.js";import{Q as me}from"./QTable.e4650bec.js";import{Q as ve}from"./QSpace.bd91c020.js";import{Q as u}from"./QItemLabel.3b58be08.js";import{Q as v,a as d}from"./QItemSection.7dc1f54f.js";import{Q as fe}from"./QList.f0282e16.js";import{Q as pe}from"./QScrollArea.44613085.js";import{Q as ge}from"./QPage.8bf63692.js";import{u as be}from"./use-quasar.66282a44.js";import{X as U}from"./xero.72a68f5d.js";import{f as S}from"./date.6d29930c.js";import{_ as ye}from"./DateRangePicker.7842c5b2.js";import{_ as _e}from"./TablePagination.215cbc35.js";import"./QMenu.abf49c1b.js";import"./selection.787abe58.js";import"./format.054b8074.js";import"./use-fullscreen.e0d6e2b2.js";import"./QScrollObserver.b02e8e20.js";import"./TouchPan.ddd1c5b8.js";import"./QDate.10e993fc.js";import"./QPopupProxy.1fd4617d.js";import"./ClosePopup.f68b6158.js";const xe={class:"row justify-center"},we={class:"col-12"},he={class:"text-h6 q-mb-md"},De={key:1,class:"row q-gutter-md q-mb-md"},Ie={class:"row q-pt-sm"},Qe={class:"text-h6 text-bold"},$e={class:"q-mb-md bg-white rounded-borders",style:{border:"1px solid #e0e0e0"}},Ue={class:"row"},ke={class:"q-pa-md q-mb-md bg-blue-grey-1 rounded-borders"},Ae={class:"text-h6 text-bold q-mb-md"},Se={class:"row q-gutter-sm"},lt=ee({__name:"XeroInvoicesPage",setup(Ee){const N=te(),{t:n}=ae(),p=be(),D=y(!1),E=y([]),s=y(null),I=y(!1),T=y(!1),C=y(!1),_=F({connected:!1,tenant_name:""}),b=F({status:"",dateRange:{from:"",to:""}}),c=y({sortBy:"Date",descending:!0,page:1,rowsPerPage:10,rowsNumber:0}),M=k(()=>[{label:n("xero.invoices.status.draft"),value:"DRAFT"},{label:n("xero.invoices.status.submitted"),value:"SUBMITTED"},{label:n("xero.invoices.status.authorised"),value:"AUTHORISED"},{label:n("xero.invoices.status.paid"),value:"PAID"},{label:n("xero.invoices.status.voided"),value:"VOIDED"}]),j=k(()=>[{name:"invoiceNumber",label:n("xero.invoices.invoiceNumber"),field:"invoice_number",align:"left",sortable:!0},{name:"type",label:n("xero.invoices.type.label"),field:"type",align:"center"},{name:"contact.name",label:n("xero.invoices.contact"),field:a=>{var l;return((l=a.contact)==null?void 0:l.name)||""},align:"left",sortable:!0},{name:"date",label:n("xero.invoices.date"),field:"date",align:"center",sortable:!0,format:a=>S(a)},{name:"due_date",label:n("xero.invoices.dueDate"),field:"due_date",align:"center",format:a=>S(a)},{name:"status",label:n("status"),field:"status",align:"center",sortable:!0},{name:"total",label:n("xero.invoices.total"),field:"total",align:"right",sortable:!0},{name:"actions",label:n("actions"),field:"",align:"center"}]),H=async()=>{try{const a=await U.getConnectionStatus();Object.assign(_,a.result)}catch(a){console.error("Failed to load connection status:",a)}},Q=async()=>{var a,l,o;if(!!_.connected){D.value=!0;try{const f={page:c.value.page,pageSize:c.value.rowsPerPage,status:b.status||void 0,dateFrom:b.dateRange.from||void 0,dateTo:b.dateRange.to||void 0,order:c.value.sortBy||void 0,asc:c.value.descending?"false":"true"},R=await U.getInvoices(f);E.value=((a=R.result)==null?void 0:a.invoices)||[],c.value.page=((l=R.result)==null?void 0:l.page)||1,c.value.rowsNumber=((o=R.result)==null?void 0:o.total_count)||0}catch(f){console.error("Failed to load invoices:",f),E.value=[],c.value.rowsNumber=0,p.notify({position:"top",type:"negative",message:(f==null?void 0:f.message)||n("failed")})}finally{D.value=!1}}},z=a=>{if(!a)return;const l=a,{sortBy:o,descending:f}=l.pagination;c.value.sortBy=o,c.value.descending=f,Q()},Y=async a=>{var l;try{D.value=!0;const o=await U.getInvoice(a.invoice_id);s.value=((l=o.result)==null?void 0:l.invoice)||null,s.value?I.value=!0:p.notify({position:"top",type:"negative",message:n("xero.invoices.invoiceNotFound")})}catch(o){console.error("Failed to load invoice details:",o),s.value=null,p.notify({position:"top",type:"negative",message:(o==null?void 0:o.message)||n("failed")})}finally{D.value=!1}},X=()=>{N.push("/admin/dashboard/xero/setup")},P=a=>({DRAFT:"grey",SUBMITTED:"orange",AUTHORISED:"blue",PAID:"positive",VOIDED:"negative"})[a]||"grey",x=(a,l="AUD")=>new Intl.NumberFormat("en-AU",{style:"currency",currency:l}).format(a),G=a=>({DRAFT:n("xero.invoices.status.draft"),SUBMITTED:n("xero.invoices.status.submitted"),AUTHORISED:n("xero.invoices.status.authorised"),PAID:n("xero.invoices.status.paid"),VOIDED:n("xero.invoices.status.voided")})[a]||a,W=k(()=>s.value?["AUTHORISED","PAID"].includes(s.value.status):!1),J=k(()=>{var a;return s.value?["AUTHORISED","PAID"].includes(s.value.status)&&((a=s.value.contact)==null?void 0:a.name):!1}),K=async()=>{if(!!s.value)try{T.value=!0;const a=await U.getInvoicePDF(s.value.invoice_id);if(a.size<100)throw new Error("PDF data too small, likely invalid");const l=URL.createObjectURL(a),o=window.open(l,"_blank");if(!o)throw URL.revokeObjectURL(l),new Error("Failed to open print window - popup blocked?");o.onload=()=>{setTimeout(()=>{o.print()},1e3)},setTimeout(()=>{URL.revokeObjectURL(l)},15e3),p.notify({type:"positive",message:n("printInvoiceSuccess"),position:"top"})}catch(a){console.error("Print invoice error:",a),p.notify({type:"negative",message:(a==null?void 0:a.message)||n("printInvoiceError"),position:"top"})}finally{T.value=!1}},Z=async()=>{var a;!s.value||!((a=s.value.contact)!=null&&a.name)||p.dialog({title:n("sendEmail"),message:n("xero.invoices.enterEmailAddress"),prompt:{model:"",type:"email",placeholder:"<EMAIL>"},cancel:!0,persistent:!0}).onOk(async l=>{if(!(!l||!s.value))try{C.value=!0,await U.sendInvoiceEmail(s.value.invoice_id,l),p.notify({type:"positive",message:n("sendEmailSuccess"),position:"top"})}catch(o){console.error("Send email error:",o),p.notify({type:"negative",message:(o==null?void 0:o.message)||n("sendEmailError"),position:"top"})}finally{C.value=!1}})};return oe(async()=>{await H(),_.connected&&await Q()}),(a,l)=>(g(),w(ge,{class:"q-pa-md"},{default:t(()=>[m("div",xe,[m("div",we,[e(O,null,{default:t(()=>[e(V,null,{default:t(()=>[m("div",he,i(a.$t("xero.invoices.title")),1),_.connected?$("",!0):(g(),w(ue,{key:0,class:"bg-warning text-dark q-mb-md",rounded:""},{avatar:t(()=>[e(le,{name:"warning",color:"orange"})]),action:t(()=>[e(h,{flat:"",color:"dark",label:a.$t("xero.invoices.goToSetup"),onClick:X},null,8,["label"])]),default:t(()=>[r(" "+i(a.$t("xero.invoices.notConnected"))+" ",1)]),_:1})),_.connected?(g(),L("div",De,[e(ce,{modelValue:b.status,"onUpdate:modelValue":[l[0]||(l[0]=o=>b.status=o),Q],options:M.value,label:a.$t("status"),clearable:"",style:{"min-width":"150px"},"map-options":"","emit-value":"",dense:""},null,8,["modelValue","options","label"]),e(ye,{modelValue:b.dateRange,"onUpdate:modelValue":[l[1]||(l[1]=o=>b.dateRange=o),Q],dateMask:"YYYY-MM-DD"},null,8,["modelValue"])])):$("",!0),_.connected?(g(),w(me,{key:2,rows:E.value,columns:j.value,pagination:c.value,"onUpdate:pagination":l[2]||(l[2]=o=>c.value=o),"hide-pagination":"",onRequest:z,"row-key":"invoice_id","binary-state-sort":"",loading:D.value},{"body-cell-status":t(o=>[e(A,{props:o},{default:t(()=>[e(q,{color:P(o.value),"text-color":"white",label:a.$t(`xero.invoices.status.${o.value.toLowerCase()}`)},null,8,["color","label"])]),_:2},1032,["props"])]),"body-cell-type":t(o=>[e(A,{props:o},{default:t(()=>[e(q,{color:o.value==="ACCREC"?"positive":"info","text-color":"white",label:a.$t(`xero.invoices.type.${o.value.toLowerCase()}`)},null,8,["color","label"])]),_:2},1032,["props"])]),"body-cell-total":t(o=>[e(A,{props:o},{default:t(()=>[r(" AU "+i(x(o.value,o.row.currency_code)),1)]),_:2},1032,["props"])]),"body-cell-actions":t(o=>[e(A,{props:o},{default:t(()=>[e(h,{flat:"",round:"",color:"primary",icon:"visibility",size:"sm",onClick:f=>Y(o.row)},{default:t(()=>[e(de,null,{default:t(()=>[r(i(a.$t("view")),1)]),_:1})]),_:2},1032,["onClick"])]),_:2},1032,["props"])]),_:1},8,["rows","columns","pagination","loading"])):$("",!0),e(_e,{modelValue:c.value,"onUpdate:modelValue":l[3]||(l[3]=o=>c.value=o),onGetData:Q},null,8,["modelValue"])]),_:1})]),_:1})])]),e(re,{modelValue:I.value,"onUpdate:modelValue":l[6]||(l[6]=o=>I.value=o),"no-refocus":"",class:"card-dialog"},{default:t(()=>[e(O,{class:"column"},{default:t(()=>[e(V,{class:"col-1 q-py-none"},{default:t(()=>[m("div",Ie,[m("div",Qe,i(a.$t("xero.invoices.invoiceDetails")),1),e(ve),e(h,{type:"button",icon:"close",flat:"",round:"",dense:"",onClick:l[4]||(l[4]=o=>I.value=!1)})])]),_:1}),s.value?(g(),w(V,{key:0,class:"col-10"},{default:t(()=>[e(pe,{class:"full-height"},{default:t(()=>[m("div",$e,[m("div",Ue,[s.value.invoice_number?(g(),w(v,{key:0,class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(i(a.$t("xero.invoices.invoiceNumber")),1)]),_:1}),e(u,{class:"text-subtitle1 text-grey-9"},{default:t(()=>[r(i(s.value.invoice_number),1)]),_:1})]),_:1})]),_:1})):$("",!0),e(v,{class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(i(a.$t("status")),1)]),_:1}),e(u,null,{default:t(()=>[e(q,{color:P(s.value.status),"text-color":"white",class:"text-subtitle1",size:"md"},{default:t(()=>[r(i(G(s.value.status)),1)]),_:1},8,["color"])]),_:1})]),_:1})]),_:1}),e(v,{class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(i(a.$t("xero.invoices.contact")),1)]),_:1}),e(u,{class:"text-subtitle1 text-grey-9"},{default:t(()=>{var o;return[r(i(((o=s.value.contact)==null?void 0:o.name)||""),1)]}),_:1})]),_:1})]),_:1}),e(v,{class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(i(a.$t("xero.invoices.date")),1)]),_:1}),e(u,{class:"text-subtitle1 text-grey-9"},{default:t(()=>[r(i(B(S)(s.value.date)),1)]),_:1})]),_:1})]),_:1}),e(v,{class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(i(a.$t("xero.invoices.dueDate")),1)]),_:1}),e(u,{class:"text-subtitle1 text-grey-9"},{default:t(()=>[r(i(B(S)(s.value.due_date)),1)]),_:1})]),_:1})]),_:1}),e(v,{class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(i(a.$t("xero.invoices.subTotal")),1)]),_:1}),e(u,{class:"text-subtitle1 text-grey-9"},{default:t(()=>[r("AU "+i(x(s.value.sub_total,s.value.currency_code)),1)]),_:1})]),_:1})]),_:1}),e(v,{class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(i(a.$t("xero.invoices.totalTax")),1)]),_:1}),e(u,{class:"text-subtitle1 text-grey-9"},{default:t(()=>[r("AU "+i(x(s.value.total_tax,s.value.currency_code)),1)]),_:1})]),_:1})]),_:1}),e(v,{class:"col-12"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(i(a.$t("xero.invoices.total")),1)]),_:1}),e(u,{class:"text-h6 text-primary text-weight-bold"},{default:t(()=>[r("AU "+i(x(s.value.total,s.value.currency_code)),1)]),_:1})]),_:1})]),_:1})])]),m("div",ke,[m("div",Ae,i(a.$t("xero.invoices.lineItems")),1),e(fe,{bordered:"",separator:""},{default:t(()=>[e(v,{class:"text-bold text-subtitle1 bg-grey-3"},{default:t(()=>[e(d,null,{default:t(()=>[r(i(a.$t("product.label")),1)]),_:1}),e(d,{side:""},{default:t(()=>[r(i(a.$t("price")),1)]),_:1})]),_:1}),(g(!0),L(ie,null,se(s.value.line_items,o=>(g(),w(v,{key:o.line_item_id},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-subtitle1 text-weight-bold"},{default:t(()=>[r(i(o.description),1)]),_:2},1024),e(u,{class:"text-subtitle2"},{default:t(()=>[r(i(o.quantity)+" x AU "+i(x(o.unit_amount,s.value.currency_code)),1)]),_:2},1024)]),_:2},1024),e(d,{side:""},{default:t(()=>[e(u,{class:"text-subtitle1 text-weight-bold"},{default:t(()=>[r(" AU "+i(x(o.line_amount,s.value.currency_code)),1)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1})])]),_:1})]),_:1})):$("",!0),e(ne,{align:"between",class:"col-1 bg-grey-2 q-pa-sm"},{default:t(()=>[m("div",Se,[e(h,{color:"primary",icon:"print",label:a.$t("printInvoice"),onClick:K,loading:T.value,disable:!W.value},null,8,["label","loading","disable"]),e(h,{color:"secondary",icon:"email",label:a.$t("sendEmail"),onClick:Z,loading:C.value,disable:!J.value},null,8,["label","loading","disable"])]),e(h,{color:"negative",icon:"close",label:a.$t("close"),onClick:l[5]||(l[5]=o=>I.value=!1)},null,8,["label"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}))}});export{lt as default};
