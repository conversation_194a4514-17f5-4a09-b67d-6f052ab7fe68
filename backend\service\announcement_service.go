package service

import (
	"context"
	"cx/domain"
	"cx/repository"

	"gorm.io/gorm"
)

type AnnouncementService interface {
	Get(ctx context.Context) (*domain.Announcement, error)
	Update(ctx context.Context, payload *domain.AnnouncementUpdatePayload) error
}

type announcementService struct {
	db               *gorm.DB
	announcementRepo repository.AnnouncementRepository
}

func NewAnnouncementService(db *gorm.DB, announcementRepo repository.AnnouncementRepository) AnnouncementService {
	return &announcementService{
		db,
		announcementRepo,
	}
}

func (s *announcementService) Get(ctx context.Context) (*domain.Announcement, error) {
	return s.announcementRepo.Get(ctx)
}

func (s *announcementService) Update(ctx context.Context, payload *domain.AnnouncementUpdatePayload) error {
	announcement, err := s.announcementRepo.Get(ctx)
	if err != nil {
		return err
	}

	payload.ID = announcement.ID

	return s.announcementRepo.Update(ctx, payload)
}
