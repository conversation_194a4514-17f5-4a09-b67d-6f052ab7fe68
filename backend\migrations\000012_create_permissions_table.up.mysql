-- 存儲所有可能的權限定義
CREATE TABLE IF NOT EXISTS `permissions` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(50) NOT NULL UNIQUE COMMENT '權限代碼',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'true=啟用，false=禁用',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_permissions_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 群組與權限的多對多關聯
CREATE TABLE IF NOT EXISTS `group_permissions` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `group_id` BIGINT NOT NULL COMMENT '關聯user_groups表',
    `permission_id` INT UNSIGNED NOT NULL COMMENT '關聯permissions表',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `created_by` BIGINT COMMENT '創建者ID',
    UNIQUE KEY `unique_group_permission` (`group_id`, `permission_id`),
    FOREIGN KEY (`group_id`) REFERENCES `user_groups`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`permission_id`) REFERENCES permissions(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用戶特殊權限設定（覆蓋群組權限）
CREATE TABLE IF NOT EXISTS `user_permissions` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT NOT NULL COMMENT '關聯users表',
    `permission_id` INT UNSIGNED NOT NULL COMMENT '關聯permissions表',
    `is_granted` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'true=授予權限，false=拒絕權限',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `created_by` BIGINT COMMENT '創建者ID',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `unique_user_permission` (`user_id`, `permission_id`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE CASCADE
);