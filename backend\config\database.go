package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type DBConfig struct {
	Host         string
	Port         string
	User         string
	Password     string
	DatabaseName string
	MaxIdleConns int
	MaxOpenConns int
	MaxLifetime  int
}

func setupDBConfig(v *viper.Viper) *DBConfig {
	v.SetDefault("DB_HOST", "localhost")
	v.<PERSON><PERSON><PERSON><PERSON>("DB_PORT", "3306")
	v.<PERSON><PERSON><PERSON><PERSON>("MaxIdleConns", "10")
	v.SetDefault("MaxOpenConns", "100")
	v.<PERSON>Default("MaxLifetime", "5")

	return &DBConfig{
		Host:         v.GetString("DB_HOST"),
		Port:         v.GetString("DB_PORT"),
		User:         v.GetString("DB_USER"),
		Password:     v.GetString("DB_PASSWORD"),
		DatabaseName: v.GetString("DB_NAME"),
		MaxIdleConns: v.GetInt("DB_MAX_IDLE_CONNS"),
		MaxOpenConns: v.GetInt("DB_MAX_OPEN_CONNS"),
		MaxLifetime:  v.GetInt("DB_MAX_LIFETIME"),
	}
}

func ConnectDB(config *Config) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local&collation=utf8mb4_unicode_ci&multiStatements=true",
		config.DB.User,
		config.DB.Password,
		config.DB.Host,
		config.DB.Port,
		config.DB.DatabaseName,
	)

	var logLevel logger.LogLevel
	if config.Server.ServerEnv == "production" {
		logLevel = logger.Silent
	} else {
		logLevel = logger.Info
	}

	// 設定 GORM 配置
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
		// 禁用默認事務
		SkipDefaultTransaction: true,
		// 準備語句緩存
		PrepareStmt: true,
	}

	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect database: %v", err)
	}

	// 取得底層的 *sql.DB 物件
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying *sql.DB: %v", err)
	}

	// 設定連線池參數
	sqlDB.SetMaxIdleConns(config.DB.MaxIdleConns)                                // 設定最大空閒連線數，建議設定為 MaxOpenConns 的 25%-50%
	sqlDB.SetMaxOpenConns(config.DB.MaxOpenConns)                                // 設定最大開啟連線數，根據服務器資源和預期負載來設定
	sqlDB.SetConnMaxLifetime(time.Duration(config.DB.MaxLifetime) * time.Minute) // 設定連線最大存活時間，建議設定為 5-10 分鐘

	// 測試資料庫連線
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %v", err)
	}

	return db, nil
}

// Close 關閉資料庫連線
func CloseDB(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}
