package repository

import (
	"context"
	"cx/domain"

	"gorm.io/gorm"
)

type AnnouncementRepository interface {
	Get(ctx context.Context) (*domain.Announcement, error)
	Update(ctx context.Context, payload *domain.AnnouncementUpdatePayload) error
}

type announcementRepository struct {
	db *gorm.DB
}

func NewAnnouncementRepository(db *gorm.DB) AnnouncementRepository {
	return &announcementRepository{db}
}

func (r *announcementRepository) Get(ctx context.Context) (*domain.Announcement, error) {
	res := domain.Announcement{}

	tx := r.db.WithContext(ctx)
	err := tx.FirstOrCreate(&res).Error

	return &res, err
}

func (r *announcementRepository) Update(ctx context.Context, payload *domain.AnnouncementUpdatePayload) error {
	tx := r.db.WithContext(ctx)
	return tx.Updates(payload).Error
}
