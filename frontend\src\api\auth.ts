import { api, apiWrapper } from '@/boot/axios';
import { UserInfo } from './user';

// 定義介面
interface LoginPayload {
  username: string;
  password: string;
}

export interface TokenResponse {
  access_token: string;
  expires_at: Date;
  refresh_token: string;
  refresh_expires_at: Date;
}

export interface LoginResponse extends TokenResponse {
  user: UserInfo;
}

export const AuthApi = {
  login: (loginPayload: LoginPayload) =>
    apiWrapper.post<LoginResponse>('v1/auth/login', loginPayload),
  logout: () => apiWrapper.post('v1/auth/logout'),
  refreshToken: () => apiWrapper.post<TokenResponse>('v1/auth/refresh-token'),
  verifyUser: (loginPayload: LoginPayload) =>
    apiWrapper.post<UserInfo>('v1/auth/verify-user', loginPayload),
};
