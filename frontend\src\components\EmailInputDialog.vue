<template>
  <q-dialog v-model="visible" persistent>
    <q-card style="min-width: 400px; max-width: 500px">
      <q-card-section>
        <div class="text-h6">{{ $t('sendEmail') }}</div>
        <div class="text-subtitle2 text-grey-7 q-mt-sm">
          {{ $t('emailInput.confirmEmailAddress') }}
        </div>
      </q-card-section>

      <q-card-section class="q-pt-none">
        <q-input
          v-model="emailAddress"
          filled
          type="email"
          :label="$t('emailInput.emailAddress')"
          :rules="[
            (val) => (val && val.length > 0) || $t('emailInput.emailRequired'),
            (val) => /.+@.+\..+/.test(val) || $t('emailInput.invalidEmail')
          ]"
          lazy-rules
          ref="emailInputRef"
          @keyup.enter="handleConfirm"
        >
          <template v-slot:prepend>
            <q-icon name="email" />
          </template>
        </q-input>

        <div v-if="showHint" class="text-caption text-grey-6 q-mt-sm">
          <q-icon name="info" size="xs" class="q-mr-xs" />
          {{ hintMessage }}
        </div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          :label="$t('cancel')"
          color="grey"
          @click="handleCancel"
          :disable="loading"
        />
        <q-btn
          :label="$t('sendEmail')"
          color="primary"
          @click="handleConfirm"
          :loading="loading"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { QInput } from 'quasar';

const { t } = useI18n();

interface Props {
  modelValue: boolean;
  defaultEmail?: string;
  customerEmail?: string;
  loading?: boolean;
  hintMessage?: string;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm', email: string): void;
  (e: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
  defaultEmail: '',
  customerEmail: '',
  loading: false,
  hintMessage: '',
});

const emit = defineEmits<Emits>();

const emailInputRef = ref<QInput>();
const emailAddress = ref('');

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const showHint = computed(() => {
  return props.hintMessage && props.hintMessage.length > 0;
});

// 當對話框打開時，設定預設的 email 地址
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      // 優先使用客戶的 email，如果沒有則使用預設 email
      emailAddress.value = props.customerEmail || props.defaultEmail || '';

      // 聚焦到輸入框
      nextTick(() => {
        emailInputRef.value?.focus();
      });
    }
  }
);

const handleConfirm = async () => {
  // 驗證 email 格式
  if (!emailAddress.value || !/.+@.+\..+/.test(emailAddress.value)) {
    emailInputRef.value?.focus();
    return;
  }

  emit('confirm', emailAddress.value);
};

const handleCancel = () => {
  emit('cancel');
  visible.value = false;
};

// 重置表單
const resetForm = () => {
  emailAddress.value = '';
};

// 暴露方法給父組件
defineExpose({
  resetForm,
});
</script>

<style scoped>
.q-card {
  border-radius: 8px;
}
</style>
