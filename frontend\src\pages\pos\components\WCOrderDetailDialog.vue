<template>
  <q-dialog v-model="visible" class="card-dialog" no-refocus>
    <q-card class="column">
      <!-- close -->
      <q-card-section class="col-1 q-py-none">
        <div class="row q-mt-sm">
          <div class="text-h5 text-bold">
            {{ t('orderDetail') }}
          </div>
          <q-space />
          <q-btn
            type="button"
            icon="close"
            flat
            round
            dense
            @click="visible = false"
          />
        </div>
      </q-card-section>

      <q-card-section class="col-10 text-h6 q-py-sm">
        <q-scroll-area class="full-height">
          <!-- data -->
          <div class="row q-mb-sm">
            <!-- order No -->
            <q-item class="col-12">
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('orderNo') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.id }}
              </q-item-section>
            </q-item>
            <!-- order date -->
            <q-item class="col-12 col-md-6">
              <q-item-section side>
                <q-icon name="event" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('orderDate') }}
              </q-item-section>
              <q-item-section>
                {{ formatDate(wcOrder?.date_created, 'YYYY-MM-DD HH:mm') }}
              </q-item-section>
            </q-item>
            <!-- customer -->
            <q-item class="col-12 col-md-6">
              <q-item-section side>
                <q-icon name="person" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('customer.label') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.billing_first_name }}
                {{ wcOrder?.billing_last_name }}
              </q-item-section>
            </q-item>
            <!-- email -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="email" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('email.label') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.billing_email }}
              </q-item-section>
            </q-item>
            <!-- phone -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="phone" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('phone') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.billing_phone }}
              </q-item-section>
            </q-item>
            <!-- status -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="check_circle" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('status') }}
              </q-item-section>
              <q-item-section>
                <q-select
                  v-model="selectedStatus"
                  :options="statusOptions"
                  option-value="value"
                  option-label="label"
                  emit-value
                  map-options
                  outlined
                  dense
                  @update:model-value="updateOrderStatus"
                  :loading="isLoading"
                />
              </q-item-section>
            </q-item>
            <!-- Xero 同步狀態 - 付款後即可同步 (processing, packing, shipping, completed) -->
            <q-item class="col-12" v-if="canSyncToXero">
              <q-item-section side>
                <q-icon :name="getXeroSyncIcon()" size="sm" :color="getXeroSyncColor()" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                <div class="row">
                  {{ t('xero.syncStatus') }}
                  <q-btn
                    flat
                    dense
                    @click="syncToXero"
                    icon="sync"
                    size="sm"
                    class="q-ml-xs"
                    :loading="isSyncing"
                    :disable="wcOrder?.xero_sync?.sync_status === 'success'"
                    v-if="wcOrder?.xero_sync?.sync_status !== 'syncing'"
                  />
                </div>
              </q-item-section>
              <q-item-section>
                <div>
                  {{ getXeroSyncStatusLabel() }}
                  <div v-if="wcOrder?.xero_sync?.xero_invoice_no" class="text-caption text-grey">
                    Invoice: {{ wcOrder.xero_sync.xero_invoice_no }}
                  </div>
                </div>
              </q-item-section>
            </q-item>
            <q-item class="col-12" v-else-if="wcOrder?.status === 'wc-cancelled' && wcOrder?.xero_sync?.xero_invoice_no">
              <q-item-section side>
                <q-icon name="warning" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('xero.voidedInvoice') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.xero_sync?.xero_invoice_no }}
              </q-item-section>
            </q-item>
            <!-- payment method -->
            <q-item class="col-12 col-md-6">
              <q-item-section side>
                <q-icon name="payment" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('payment.label') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.payment_method_title }}
              </q-item-section>
            </q-item>
            <!-- total -->
            <q-item class="col-12 col-md-6">
              <q-item-section side>
                <q-icon name="attach_money" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('total') }}
              </q-item-section>
              <q-item-section>
                AU$ {{ formatNumber(wcOrder?.total, 2) }}
              </q-item-section>
            </q-item>

            <q-item class="col-12">
              <q-item-section>
                <q-separator color="black" size="2px" />
              </q-item-section>
            </q-item>

            <!-- 收貨人 -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="person" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('receiver') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.shipping_first_name }}
                {{ wcOrder?.shipping_last_name }}
              </q-item-section>
            </q-item>

            <!-- 運送方式 -->
            <q-item class="col-12" v-if="wcOrder?.shipping_method || wcOrder?.shipping_method_title">
              <q-item-section side>
                <q-icon name="local_shipping" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('shippingMethod') }}
              </q-item-section>
              <q-item-section>
                {{ getShippingMethodDisplay(wcOrder?.shipping_method, wcOrder?.shipping_method_title) }}
              </q-item-section>
            </q-item>

            <!-- address -->
            <q-item class="col-12">
              <q-item-section side>
                <q-icon name="location_on" size="sm" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('address') }}
              </q-item-section>
              <q-item-section>
                {{ wcOrder?.shipping_address_1 }},
                {{ wcOrder?.shipping_city }},
                {{ wcOrder?.shipping_state }},
                {{ wcOrder?.shipping_postcode }},
                {{ wcOrder?.shipping_country }}
              </q-item-section>
            </q-item>

            <!-- 取消原因 - 只在訂單被取消時顯示 -->
            <q-item class="col-12" v-if="wcOrder?.status === 'wc-cancelled' && wcOrder?.customer_note">
              <q-item-section side>
                <q-icon name="cancel" size="sm" color="red" />
              </q-item-section>
              <q-item-section class="text-bold q-mr-sm" side>
                {{ t('wcOrder.cancel.reasonLabel') }}
              </q-item-section>
              <q-item-section>
                <span class="text-red">{{ wcOrder.customer_note }}</span>
              </q-item-section>
            </q-item>
          </div>
          <!-- items -->
          <q-list bordered separator>
            <q-item class="bg-grey-3">
              <q-item-section class="text-bold">
                {{ t('product.label') }}
              </q-item-section>

              <q-item-section class="text-bold" side>
                {{ t('price') }}
              </q-item-section>
            </q-item>
            <!-- 商品列表 -->
            <q-item v-for="item in wcOrder?.line_items" :key="item.name">
              <q-item-section>
                {{ item.name }}
              </q-item-section>
              <q-item-section class="text-subtitle1" side>
                x {{ item.quantity }}
              </q-item-section>
              <q-item-section class="text-bold" side>
                AU$ {{ formatNumber(item.total, 2) }}
              </q-item-section>
            </q-item>
            <!-- 運費 -->
            <q-item v-if="wcOrder?.shipping_total">
              <q-item-section>
                {{ t('shippingFee') }}
              </q-item-section>
              <q-item-section class="text-bold" side>
                AU$ {{ formatNumber(wcOrder?.shipping_total, 2) }}
              </q-item-section>
            </q-item>
            <!-- 總計 -->
            <q-item class="bg-grey-3">
              <q-item-section>
                <q-item-label class="text-bold">{{ t('total') }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-item-label class="text-bold">
                  AU$ {{ formatNumber(wcOrder?.total, 2) }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-scroll-area>
      </q-card-section>

      <!-- actions -->
      <q-card-actions align="between" class="col-1 bg-grey-2 q-pa-sm">
        <div class="row q-gutter-sm">
          <q-btn
            color="red"
            icon="delete"
            :label="t('wpOrder.actions.cancel')"
            no-caps
            @click="orderCancelDialog?.openDialog()"
            :loading="isLoading"
            v-if="wcOrder?.status != 'wc-cancelled'"
          />
          <q-btn
            color="primary"
            icon="print"
            :label="t('printInvoice')"
            @click="handlePrintInvoice"
            :loading="isLoading"
            v-if="canPrintInvoice"
          />
          <q-btn
            color="secondary"
            icon="email"
            :label="t('sendEmail')"
            @click="showEmailDialog"
            :loading="isLoading"
            v-if="canSendEmail"
          />
        </div>
        <q-btn :label="t('close')" color="grey" @click="visible = false" />
      </q-card-actions>
    </q-card>

    <!-- 取消訂單對話框 -->
    <WCOrderCancelDialog
      ref="orderCancelDialog"
      :order-id="props.orderID"
      @order-cancelled="handleOrderCancelled"
    />

    <!-- Email 輸入對話框 -->
    <EmailInputDialog
      v-model="emailDialogVisible"
      :default-email="xeroConfig?.default_email || ''"
      :customer-email="wcOrder?.billing_email || ''"
      :loading="isLoading"
      :hint-message="getEmailHintMessage()"
      @confirm="handleSendEmail"
      @cancel="emailDialogVisible = false"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { OrderApi, WCOrder } from '@/api/order';
import { XeroApi, XeroConfig } from '@/api/xero';
import { formatDate, formatNumber, useDialog, getShippingMethodDisplay, handleError } from '@/utils';
import { usePrintInvoice } from '@/composables/usePrintInvoice';
import WCOrderCancelDialog from './WCOrderCancelDialog.vue';
import EmailInputDialog from '@/components/EmailInputDialog.vue';

const { t } = useI18n();
const $q = useQuasar();
const dialog = useDialog();
const { printInvoice } = usePrintInvoice();

const props = defineProps<{
  modelValue: boolean;
  orderID: number;
}>();

const emit = defineEmits(['update:modelValue', 'refresh']);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

onMounted(() => {
  loadXeroConfig();
});

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      getData();
    }
  }
);

const isLoading = ref(false);
const isSyncing = ref(false);
const wcOrder = ref<WCOrder>();
const orderCancelDialog = ref();
const emailDialogVisible = ref(false);
const xeroConfig = ref<XeroConfig>();

// 訂單狀態選項
const statusOptions = computed(() => [
  { label: t('orderStatus.processing'), value: 'processing' },
  { label: t('orderStatus.on-hold'), value: 'on-hold' },
  { label: t('orderStatus.packing'), value: 'packing' },
  { label: t('orderStatus.shipping'), value: 'shipping' },
  { label: t('orderStatus.completed'), value: 'completed' },
  { label: t('orderStatus.cancelled'), value: 'cancelled' },
  { label: t('orderStatus.refunded'), value: 'refunded' },
]);

// 選中的狀態
const selectedStatus = computed({
  get: () => {
    const status = wcOrder.value?.status;
    if (!status) return '';
    return status.startsWith('wc-') ? status.substring(3) : status;
  },
  set: () => {
    // 這裡不直接設置，而是通過 updateOrderStatus 方法處理
  }
});

const getData = async () => {
  try {
    isLoading.value = true;

    const response = await OrderApi.getWCOrder(props.orderID);

    wcOrder.value = response.result;
  } finally {
    isLoading.value = false;
  }
};



// 更新訂單狀態
const updateOrderStatus = async (newStatus: string) => {
  // 確保狀態值不為空且有效
  if (!newStatus || typeof newStatus !== 'string' || newStatus.trim() === '') {
    console.error('Invalid status:', newStatus);
    return;
  }

  // 移除空白字符
  newStatus = newStatus.trim();

  if (newStatus === selectedStatus.value) {
    return;
  }

  // 如果選擇取消狀態，開啟取消對話框
  if (newStatus === 'cancelled') {
    orderCancelDialog.value?.openDialog();
    return;
  }

  dialog.showMessage({
    title: t('wcOrder.statusUpdate.title'),
    message: t('wcOrder.statusUpdate.confirm'),
    timeout: 0,
    persistent: true,
    ok: async () => {
      try {
        isLoading.value = true;
        await OrderApi.updateWCOrderStatus(props.orderID, newStatus);

        // 重新獲取訂單資料
        await getData();
      } finally {
        isLoading.value = false;
        emit('refresh');
      }
    },
  });
};

// 處理訂單取消
const handleOrderCancelled = async (cancelData: {
  orderId: number;
  reason: string;
  otherReason: string | null;
}) => {
  try {
    isLoading.value = true;

    // 準備客戶備註內容
    const customerNote = cancelData.reason === 'other' && cancelData.otherReason
      ? `${t('wcOrder.cancel.title')}: ${cancelData.otherReason}`
      : `${t('wcOrder.cancel.title')}: ${cancelData.reason}`;

    // 先更新客戶備註，再更新訂單狀態
    await OrderApi.updateWCOrderCustomerNote(props.orderID, customerNote);
    await OrderApi.updateWCOrderStatus(props.orderID, 'cancelled');

    // 重新獲取訂單資料
    await getData();
  } finally {
    isLoading.value = false;
    emit('refresh');
  }
};

// 載入 Xero 配置
const loadXeroConfig = async () => {
  try {
    const response = await XeroApi.getConfig();
    xeroConfig.value = response.result;
  } catch (error) {
    console.error('Failed to load Xero config:', error);
  }
};

// Xero 同步相關方法
const getXeroSyncIcon = () => {
  if (!wcOrder.value?.xero_sync) return 'cloud_off';

  switch (wcOrder.value.xero_sync.sync_status) {
    case 'success':
      return 'cloud_done';
    case 'failed':
      return 'cloud_off';
    case 'syncing':
      return 'cloud_sync';
    case 'pending':
      return 'cloud_queue';
    default:
      return 'cloud_off';
  }
};

const getXeroSyncColor = () => {
  if (!wcOrder.value?.xero_sync) return 'grey';

  switch (wcOrder.value.xero_sync.sync_status) {
    case 'success':
      return 'green';
    case 'failed':
      return 'red';
    case 'syncing':
      return 'blue';
    case 'pending':
      return 'orange';
    default:
      return 'grey';
  }
};

const getXeroSyncStatusLabel = () => {
  if (!wcOrder.value?.xero_sync) return t('xero.notSynced');

  switch (wcOrder.value.xero_sync.sync_status) {
    case 'success':
      return t('xero.syncSuccess');
    case 'failed':
      return t('xero.syncFailed');
    case 'syncing':
      return t('xero.syncing');
    case 'pending':
      return t('xero.syncPending');
    default:
      return t('xero.notSynced');
  }
};

const syncToXero = async () => {
  if (!wcOrder.value?.id) return;

  try {
    isSyncing.value = true;

    const response = await OrderApi.syncWCOrderToXero(wcOrder.value.id);

    $q.notify({
      type: 'positive',
      message: response.result.message || t('xero.syncSuccess'),
      position: 'top',
    });

    // 重新獲取訂單數據以更新同步狀態
    await getData();
    emit('refresh');
  } catch (error) {
    handleError(error);
  } finally {
    isSyncing.value = false;
  }
};

// 檢查訂單是否可以同步到 Xero（付款後的狀態）
const canSyncToXero = computed(() => {
  const status = wcOrder.value?.status;
  return !!(status && ['wc-processing', 'wc-packing', 'wc-shipping', 'wc-completed'].includes(status));
});

// 計算屬性：檢查是否可以列印發票
const canPrintInvoice = computed(() => {
  return !!(
    wcOrder.value?.xero_sync?.xero_invoice_id &&
    wcOrder.value?.xero_sync?.sync_status === 'success' &&
    canSyncToXero.value
  );
});

// 計算屬性：檢查是否可以發送 Email
const canSendEmail = computed(() => {
  return !!(
    wcOrder.value?.xero_sync?.xero_invoice_id &&
    canSyncToXero.value
  );
});

// 列印 Invoice
const handlePrintInvoice = async () => {
  if (!wcOrder.value?.xero_sync?.xero_invoice_id) return;

  try {
    isLoading.value = true;

    // 創建一個模擬的 Order 對象來使用 printInvoice
    const mockOrder = {
      uuid: `wc-${wcOrder.value.id}`,
      xero_sync: {
        xero_invoice_id: wcOrder.value.xero_sync.xero_invoice_id,
        sync_status: wcOrder.value.xero_sync.sync_status,
      }
    };

    await printInvoice(mockOrder as any);

    $q.notify({
      type: 'positive',
      message: t('printInvoiceSuccess'),
      position: 'top',
    });
  } catch (error) {
    console.error('Print invoice error:', error);
    $q.notify({
      type: 'negative',
      message: t('printInvoiceError'),
      position: 'top',
    });
  } finally {
    isLoading.value = false;
  }
};

// 顯示 Email 對話框
const showEmailDialog = () => {
  emailDialogVisible.value = true;
};

// 獲取 Email 提示訊息
const getEmailHintMessage = () => {
  if (!wcOrder.value) return '';

  const hasCustomerEmail = wcOrder.value.billing_email;
  const hasDefaultEmail = xeroConfig.value?.default_email;

  if (hasCustomerEmail) {
    return t('emailInput.usingCustomerEmail');
  } else if (hasDefaultEmail) {
    return t('emailInput.usingDefaultEmail');
  } else {
    return t('emailInput.noDefaultEmail');
  }
};

// 發送 Email
const handleSendEmail = async (email: string) => {
  if (!wcOrder.value?.xero_sync?.xero_invoice_id) return;

  // 檢查是否已同步到 Xero
  if (wcOrder.value.xero_sync.sync_status !== 'success') {
    $q.notify({
      type: 'warning',
      message: t('sendEmailNotSynced'),
      position: 'top',
    });
    emailDialogVisible.value = false;
    return;
  }

  try {
    isLoading.value = true;

    // 調用 Xero API 發送 email
    await XeroApi.sendInvoiceEmail(wcOrder.value.xero_sync.xero_invoice_id, email);

    $q.notify({
      type: 'positive',
      message: t('sendEmailSuccess'),
      position: 'top',
    });

    emailDialogVisible.value = false;
  } catch (error) {
    console.error('Send email error:', error);

    // 提供更詳細的錯誤處理
    let errorMessage = t('sendEmailError');

    if (error instanceof Error) {
      if (error.message.includes('daily email limit') || error.message.includes('Daily Email Rate Limit')) {
        errorMessage = t('sendEmailRateLimitError');

        // 顯示解決方案對話框
        $q.dialog({
          title: t('sendEmailRateLimitTitle'),
          message: t('sendEmailRateLimitMessage'),
          ok: {
            label: t('understood'),
            color: 'primary',
          },
          persistent: false,
        });

        emailDialogVisible.value = false;
        return; // 不顯示一般的錯誤通知
      } else if (error.message.includes('invalid email') || error.message.includes('Invalid email')) {
        errorMessage = t('sendEmailInvalidEmailError');
      } else if (error.message.includes('manually from Xero')) {
        errorMessage = t('sendEmailManualError');
      }
    }

    $q.notify({
      type: 'negative',
      message: errorMessage,
      position: 'top',
      timeout: 5000, // 顯示更長時間讓用戶看到完整訊息
    });
  } finally {
    isLoading.value = false;
  }
};
</script>
