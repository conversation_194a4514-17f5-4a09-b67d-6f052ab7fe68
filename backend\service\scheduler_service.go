package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"

	"cx/domain"
	"cx/repository"
	"cx/utils"
)

type SchedulerService interface {
	Start(ctx context.Context) error
	Stop() error
	ProcessPaidOrdersForXeroInvoices(ctx context.Context) error
}

type schedulerService struct {
	db             *gorm.DB
	wpDB           *gorm.DB
	wcOrderService WcOrderService
	xeroService    XeroService
	ticker         *time.Ticker
	stopChan       chan bool
	isRunning      bool
	defaultEmail   string
}

func NewSchedulerService(
	db *gorm.DB,
	wpDB *gorm.DB,
	wcOrderService WcOrderService,
	xeroService XeroService,
	defaultEmail string,
) SchedulerService {
	return &schedulerService{
		db:             db,
		wpDB:           wpDB,
		wcOrderService: wcOrderService,
		xeroService:    xeroService,
		stopChan:       make(chan bool),
		defaultEmail:   defaultEmail,
	}
}

// Start 啟動調度器，每5分鐘執行一次
func (s *schedulerService) Start(ctx context.Context) error {
	if s.isRunning {
		return fmt.Errorf("scheduler is already running")
	}

	s.isRunning = true
	s.ticker = time.NewTicker(5 * time.Minute)

	log.Println("Scheduler started: checking for paid orders without Xero invoices every 5 minutes")

	// 立即執行一次
	go func() {
		if err := s.ProcessPaidOrdersForXeroInvoices(ctx); err != nil {
			log.Printf("Error processing paid orders on startup: %v", err)
		}
	}()

	// 定期執行
	go func() {
		for {
			select {
			case <-s.ticker.C:
				if err := s.ProcessPaidOrdersForXeroInvoices(ctx); err != nil {
					log.Printf("Error processing paid orders: %v", err)
				}
			case <-s.stopChan:
				s.ticker.Stop()
				s.isRunning = false
				log.Println("Scheduler stopped")
				return
			}
		}
	}()

	return nil
}

// Stop 停止調度器
func (s *schedulerService) Stop() error {
	if !s.isRunning {
		return fmt.Errorf("scheduler is not running")
	}

	s.stopChan <- true
	return nil
}

// ProcessPaidOrdersForXeroInvoices 處理已付款但尚未創建Xero發票的訂單
func (s *schedulerService) ProcessPaidOrdersForXeroInvoices(ctx context.Context) error {
	log.Println("Starting to process paid orders for Xero invoice creation...")

	// 獲取已付款的訂單
	paidOrders, err := s.wcOrderService.GetPaidOrders(ctx)
	if err != nil {
		return fmt.Errorf("failed to get paid orders without Xero invoice: %w", err)
	}

	if len(paidOrders) == 0 {
		log.Println("No paid orders found that need Xero invoice creation")
		return nil
	}

	// 過濾掉已經同步成功的訂單
	wcXeroSyncRepo := repository.NewWcXeroOrderSyncRepository(s.db)
	var ordersToProcess []domain.WcOrder
	for _, order := range paidOrders {
		syncRecord, err := wcXeroSyncRepo.GetByWcOrderID(ctx, order.ID)
		if err != nil && err != gorm.ErrRecordNotFound {
			log.Printf("Failed to get sync record for order %d: %v", order.ID, err)
			continue
		}

		if syncRecord == nil || syncRecord.SyncStatus != domain.XeroSyncStatusSuccess {
			ordersToProcess = append(ordersToProcess, order)
		}
	}

	successCount := 0
	errorCount := 0

	for _, order := range ordersToProcess {
		if err := s.processOrderForXeroInvoice(ctx, order); err != nil {
			log.Printf("Failed to process order %d: %v", order.ID, err)
			errorCount++
		} else {
			log.Printf("Successfully processed order %d", order.ID)
			successCount++
		}

		// 添加小延遲避免API限制
		time.Sleep(1 * time.Second)
	}

	log.Printf("Completed processing: %d successful, %d errors", successCount, errorCount)
	return nil
}

// processOrderForXeroInvoice 為單個訂單創建Xero發票並發送郵件
func (s *schedulerService) processOrderForXeroInvoice(ctx context.Context, order domain.WcOrder) error {
	log.Printf("Processing order %d for Xero invoice creation", order.ID)

	// 檢查訂單是否有有效的客戶郵箱
	customerEmail := order.BillingEmail
	if customerEmail == "" {
		// 嘗試從Xero配置獲取默認郵箱
		xeroConfig, err := s.xeroService.GetConfig(ctx)
		if err == nil && xeroConfig.DefaultEmail != "" {
			customerEmail = xeroConfig.DefaultEmail
			log.Printf("Order %d has no customer email, using Xero default email: %s", order.ID, customerEmail)
		} else if s.defaultEmail != "" {
			customerEmail = s.defaultEmail
			log.Printf("Order %d has no customer email, using fallback default email: %s", order.ID, customerEmail)
		}
	}

	if customerEmail == "" {
		return fmt.Errorf("no customer email and no default email configured for order %d", order.ID)
	}

	// 創建WooCommerce Xero同步記錄
	wcXeroSyncRepo := repository.NewWcXeroOrderSyncRepository(s.db)
	syncRecord := &domain.WcXeroOrderSync{
		UUID:       utils.GenerateUUID(),
		WcOrderID:  order.ID,
		SyncStatus: domain.XeroSyncStatusPending,
	}

	if err := wcXeroSyncRepo.Create(ctx, syncRecord); err != nil {
		return fmt.Errorf("failed to create WC Xero sync record: %w", err)
	}

	// 更新同步狀態為進行中
	syncRecord.SyncStatus = domain.XeroSyncStatusSyncing
	if err := wcXeroSyncRepo.Update(ctx, syncRecord); err != nil {
		log.Printf("Failed to update sync status to syncing for order %d: %v", order.ID, err)
	}

	// 創建Xero發票
	invoiceResponse, err := s.createXeroInvoiceForWCOrder(ctx, order)
	if err != nil {
		// 更新同步狀態為失敗
		syncRecord.SyncStatus = domain.XeroSyncStatusFailed
		syncRecord.ErrorMessage = err.Error()
		if updateErr := wcXeroSyncRepo.Update(ctx, syncRecord); updateErr != nil {
			log.Printf("Failed to update sync status to failed for order %d: %v", order.ID, updateErr)
		}
		return fmt.Errorf("failed to create Xero invoice: %w", err)
	}

	// 更新同步記錄為成功
	syncRecord.SyncStatus = domain.XeroSyncStatusSuccess
	syncRecord.XeroInvoiceID = invoiceResponse.Invoices[0].InvoiceID
	syncRecord.XeroInvoiceNo = invoiceResponse.Invoices[0].InvoiceNumber
	now := time.Now()
	syncRecord.LastSyncAt = &now

	if err := wcXeroSyncRepo.Update(ctx, syncRecord); err != nil {
		log.Printf("Failed to update sync record for order %d: %v", order.ID, err)
	}

	// 發送發票郵件
	if err := s.xeroService.SendInvoiceEmail(ctx, invoiceResponse.Invoices[0].InvoiceID, customerEmail); err != nil {
		log.Printf("Failed to send invoice email for order %d to %s: %v", order.ID, customerEmail, err)
		// 郵件發送失敗不影響發票創建成功的狀態
	} else {
		log.Printf("Successfully sent invoice email for order %d to %s", order.ID, customerEmail)
	}

	return nil
}

// createXeroInvoiceForWCOrder 為WooCommerce訂單創建Xero發票
func (s *schedulerService) createXeroInvoiceForWCOrder(ctx context.Context, order domain.WcOrder) (*domain.XeroInvoiceResponse, error) {
	// 構建聯絡人資訊 - 根據Xero API，Contact只需要Name和EmailAddress
	customerName := fmt.Sprintf("%s %s", order.BillingFirstName, order.BillingLastName)
	if customerName == " " {
		customerName = "WooCommerce Customer"
	}

	contact := domain.XeroContact{
		Name:         customerName,
		EmailAddress: order.BillingEmail,
	}

	// 構建發票行項目
	lineItems := []domain.XeroLineItem{
		{
			Description: fmt.Sprintf("WooCommerce Order #%d", order.ID),
			Quantity:    1,
			UnitAmount:  order.Total,
			AccountCode: "200", // 預設收入帳戶代碼
		},
	}

	// 構建發票
	invoice := domain.XeroInvoice{
		Type:            "ACCREC",
		Contact:         contact,
		Date:            domain.XeroDate{Time: order.DateCreated},
		DueDate:         domain.XeroDate{Time: order.DateCreated.AddDate(0, 0, 30)},
		Status:          "AUTHORISED",
		LineAmountTypes: "Inclusive",
		LineItems:       lineItems,
		Reference:       fmt.Sprintf("WC-%d", order.ID),
	}

	// 創建發票請求
	invoiceRequest := &domain.XeroInvoiceRequest{
		Invoices: []domain.XeroInvoice{invoice},
	}

	// 調用Xero服務創建發票
	response, err := s.xeroService.CreateInvoice(ctx, invoiceRequest)
	if err != nil {
		return nil, err
	}

	if len(response.Invoices) == 0 {
		return nil, fmt.Errorf("no invoice returned from Xero API")
	}

	return response, nil
}
