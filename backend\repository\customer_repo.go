package repository

import (
	"context"
	"cx/domain"
	"cx/utils"

	"gorm.io/gorm"
)

type CustomerRepository interface {
	WithTx(tx *gorm.DB) CustomerRepository

	Create(ctx context.Context, customer *domain.Customer) error
	Update(ctx context.Context, uuid string, customer *domain.CustomerUpdatePayload) error
	Fetch(ctx context.Context, filter *domain.CustomerFilter, pagination *domain.Pagination) ([]domain.Customer, error)
	GetByID(ctx context.Context, id int64) (*domain.Customer, error)
	GetByUUID(ctx context.Context, uuid string) (*domain.Customer, error)
	Delete(ctx context.Context, uuid string) error
}

type customerRepository struct {
	db *gorm.DB
}

func NewCustomerRepository(db *gorm.DB) CustomerRepository {
	return &customerRepository{db}
}

func (r *customerRepository) WithTx(tx *gorm.DB) CustomerRepository {
	return &customerRepository{tx}
}

func (r *customerRepository) Create(ctx context.Context, customer *domain.Customer) error {
	customer.UUID = utils.GenerateUUID()
	return r.db.WithContext(ctx).Create(customer).Error
}

func (r *customerRepository) Update(ctx context.Context, uuid string, customer *domain.CustomerUpdatePayload) error {
	tx := r.db.WithContext(ctx)

	err := tx.Where("uuid = ?", uuid).Updates(customer).Error
	if err != nil {
		return err
	}

	if !*customer.IsVip || customer.VipEndDate.IsZero() {
		err = tx.Model(&domain.Customer{}).Where("uuid = ?", uuid).Update("vip_end_date", nil).Error
		if err != nil {
			return err
		}
	}

	return nil
}

func (r *customerRepository) Fetch(ctx context.Context, filter *domain.CustomerFilter, pagination *domain.Pagination) ([]domain.Customer, error) {
	var customers []domain.Customer

	tx := r.db.WithContext(ctx).Model(&domain.Customer{})

	if filter.Search != "" {
		search := "%" + filter.Search + "%"
		tx = tx.Where("name LIKE ? OR email LIKE ? OR phone LIKE ? OR tax_id LIKE ?",
			search, search, search, search)
	}

	if filter.Name != "" {
		tx = tx.Where("name LIKE ?", "%"+filter.Name+"%")
	}

	if filter.Email != "" {
		tx = tx.Where("email = ?", filter.Email)
	}

	if filter.Phone != "" {
		tx = tx.Where("phone = ?", filter.Phone)
	}

	if filter.Address != "" {
		tx = tx.Where("address LIKE ?", "%"+filter.Address+"%")
	}

	if filter.TaxID != "" {
		tx = tx.Where("tax_id = ?", filter.TaxID)
	}

	if filter.IsSupplier != nil {
		tx = tx.Where("is_supplier = ?", filter.IsSupplier)
	}

	if filter.IsActive != nil {
		tx = tx.Where("is_active = ?", filter.IsActive)
	}

	err := tx.Count(&pagination.RowsNumber).Error
	if err != nil {
		return nil, err
	}

	tx = pagination.SetPaginationToDB(tx, "created_at", true)

	err = tx.Preload("Products").Find(&customers).Error
	return customers, err
}

func (r *customerRepository) GetByID(ctx context.Context, id int64) (*domain.Customer, error) {
	var customer domain.Customer
	err := r.db.WithContext(ctx).Preload("Products").Where("id = ?", id).First(&customer).Error
	return &customer, err
}

func (r *customerRepository) GetByUUID(ctx context.Context, uuid string) (*domain.Customer, error) {
	var customer domain.Customer
	err := r.db.WithContext(ctx).Preload("Products").Where("uuid = ?", uuid).First(&customer).Error
	return &customer, err
}

func (r *customerRepository) Delete(ctx context.Context, uuid string) error {
	return r.db.WithContext(ctx).Where("uuid = ?", uuid).Delete(&domain.Customer{}).Error
}
