import{c as l,E,r as s,ac as q,aw as H,s as M,J as n,al as F,T as O,ay as P,w as W,W as h,K as A}from"./index.b4716878.js";const J={ratio:[String,Number]};function K(e,o){return l(()=>{const u=Number(e.ratio||(o!==void 0?o.value:void 0));return isNaN(u)!==!0&&u>0?{paddingBottom:`${100/u}%`}:null})}const x=1.7778;var U=E({name:"QImg",props:{...J,src:String,srcset:String,sizes:String,alt:String,crossorigin:String,decoding:String,referrerpolicy:String,draggable:Boolean,loading:{type:String,default:"lazy"},loadingShowDelay:{type:[Number,String],default:0},fetchpriority:{type:String,default:"auto"},width:String,height:String,initialRatio:{type:[Number,String],default:x},placeholderSrc:String,errorSrc:String,fit:{type:String,default:"cover"},position:{type:String,default:"50% 50%"},imgClass:String,imgStyle:Object,noSpinner:Boolean,noNativeMenu:Boolean,noTransition:Boolean,spinnerColor:String,spinnerSize:String},emits:["load","error"],setup(e,{slots:o,emit:u}){const y=s(e.initialRatio),_=K(e,y),m=A(),{registerTimeout:C,removeTimeout:v}=q(),{registerTimeout:z,removeTimeout:b}=q(),f=l(()=>e.placeholderSrc!==void 0?{src:e.placeholderSrc}:null),L=l(()=>e.errorSrc!==void 0?{src:e.errorSrc,__qerror:!0}:null),t=[s(null),s(f.value)],a=s(0),c=s(!1),g=s(!1),N=l(()=>`q-img q-img--${e.noNativeMenu===!0?"no-":""}menu`),k=l(()=>({width:e.width,height:e.height})),I=l(()=>`q-img__image ${e.imgClass!==void 0?e.imgClass+" ":""}q-img__image--with${e.noTransition===!0?"out":""}-transition q-img__image--`),R=l(()=>({...e.imgStyle,objectFit:e.fit,objectPosition:e.position}));function B(){if(b(),e.loadingShowDelay===0){c.value=!0;return}z(()=>{c.value=!0},e.loadingShowDelay)}function S(){b(),c.value=!1}function j({target:i}){h(m)===!1&&(v(),y.value=i.naturalHeight===0?.5:i.naturalWidth/i.naturalHeight,w(i,1))}function w(i,r){r===1e3||h(m)===!0||(i.complete===!0?D(i):C(()=>{w(i,r+1)},50))}function D(i){h(m)!==!0&&(a.value=a.value^1,t[a.value].value=null,S(),i.getAttribute("__qerror")!=="true"&&(g.value=!1),u("load",i.currentSrc||i.src))}function Q(i){v(),S(),g.value=!0,t[a.value].value=L.value,t[a.value^1].value=f.value,u("error",i)}function T(i){const r=t[i].value,d={key:"img_"+i,class:I.value,style:R.value,alt:e.alt,crossorigin:e.crossorigin,decoding:e.decoding,referrerpolicy:e.referrerpolicy,height:e.height,width:e.width,loading:e.loading,fetchpriority:e.fetchpriority,"aria-hidden":"true",draggable:e.draggable,...r};return a.value===i?Object.assign(d,{class:d.class+"current",onLoad:j,onError:Q}):d.class+="loaded",n("div",{class:"q-img__container absolute-full",key:"img"+i},n("img",d))}function $(){return c.value===!1?n("div",{key:"content",class:"q-img__content absolute-full q-anchor--skip"},O(o[g.value===!0?"error":"default"])):n("div",{key:"loading",class:"q-img__loading absolute-full flex flex-center"},o.loading!==void 0?o.loading():e.noSpinner===!0?void 0:[n(P,{color:e.spinnerColor,size:e.spinnerSize})])}{let i=function(){W(()=>e.src||e.srcset||e.sizes?{src:e.src,srcset:e.srcset,sizes:e.sizes}:null,r=>{v(),g.value=!1,r===null?(S(),t[a.value^1].value=f.value):B(),t[a.value].value=r},{immediate:!0})};H.value===!0?M(i):i()}return()=>{const i=[];return _.value!==null&&i.push(n("div",{key:"filler",style:_.value})),t[0].value!==null&&i.push(T(0)),t[1].value!==null&&i.push(T(1)),i.push(n(F,{name:"q-transition--fade"},$)),n("div",{key:"main",class:N.value,style:k.value,role:"img","aria-label":e.alt},i)}}});export{U as Q};
