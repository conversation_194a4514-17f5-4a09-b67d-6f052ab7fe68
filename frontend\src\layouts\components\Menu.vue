<template>
  <q-list bordered separator>
    <template v-for="link in modelValue" :key="link.title">
      <template v-if="!link.requireAdmin || authStore.isAdmin()">
        <q-expansion-item
          v-if="link.subMenus && link.subMenus.length > 0"
          :label="link.title"
          :icon="link.icon"
          expand-separator
        >
          <MenuItem
            v-for="subMenu in link.subMenus"
            :key="subMenu.title"
            :isSubmenu="true"
            v-bind="subMenu"
          />
        </q-expansion-item>

        <MenuItem v-else v-bind="link" />
      </template>
    </template>
  </q-list>
</template>

<script setup lang="ts">
import MenuItem from './MenuItem.vue';
import { MenuLinkProps } from './models/Menu';
import { useAuthStore } from '@/stores/auth-store';

const authStore = useAuthStore();

defineOptions({
  name: 'MenuLink',
});

defineProps<{
  modelValue: MenuLinkProps[];
}>();
</script>
