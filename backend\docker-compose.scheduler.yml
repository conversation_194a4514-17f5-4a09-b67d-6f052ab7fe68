version: '3.8'

services:
  wc-xero-scheduler:
    build:
      context: .
      dockerfile: Dockerfile.scheduler
    container_name: wc-xero-scheduler
    environment:
      - DB_HOST=${DB_HOST:-localhost}
      - DB_PORT=${DB_PORT:-3306}
      - DB_USER=${DB_USER:-root}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME:-cx_pos}
      - DEFAULT_EMAIL=${DEFAULT_EMAIL}
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - cx-pos-network

  mysql:
    image: mysql:8.0
    container_name: cx-pos-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME:-cx_pos}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - cx-pos-network

volumes:
  mysql_data:

networks:
  cx-pos-network:
    driver: bridge
