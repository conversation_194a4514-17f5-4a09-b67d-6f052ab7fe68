<template>
  <!-- title -->
  <div class="col-1">
    <q-item dense>
      <q-item-section>
        <q-item-label header class="text-h6 text-weight-bold q-pa-sm">
          {{ panelTitle }}
        </q-item-label>
      </q-item-section>
    </q-item>
    <q-separator />
  </div>

  <!-- form -->
  <q-card-section class="col-10">
    <q-form
      ref="formRef"
      @submit.prevent="onSubmit"
      greedy
      autocomplete="off"
      class="fit q-px-md-xl"
    >
      <div class="column fit">
        <!-- fields -->
        <q-scroll-area visible class="col-11 q-pr-md q-px-sm-xl">
          <!-- name -->
          <div class="row q-mb-md">
            <div class="col-12 col-sm-2 text-subtitle1 text-md-center">
              {{ t('name') }}
            </div>
            <div class="col-12 col-sm-10">
              <q-input
                type="text"
                v-model="localUser.name"
                outlined
                dense
                hide-bottom-space
                :rules="[(val: string) => !!val || t('error.required')]"
                lazy-rules
                class="q-px-none"
              />
            </div>
          </div>
          <!-- Group -->
          <div class="row">
            <div class="col-12 col-sm-2 text-subtitle1 text-md-center">
              {{ t('group') }}
            </div>
            <div class="col-12 col-sm-10">
              <q-select
                v-model="localUser.group_id"
                :options="groupOpts"
                option-label="name"
                option-value="id"
                outlined
                dense
                hide-bottom-space
                emit-value
                map-options
                :rules="[(val: string) => !!val || t('error.required')]"
                lazy-rules
              />
            </div>
          </div>

          <q-separator class="q-my-lg" />

          <!-- account ID / username -->
          <div class="row q-mb-md">
            <div class="col-12 col-sm-2 text-subtitle1 text-md-center">
              {{ t('account') }}
            </div>
            <div class="col-12 col-sm-10">
              <q-input
                type="text"
                name="username"
                v-model="localUser.username"
                maxlength="50"
                outlined
                dense
                hide-bottom-space
                :disable="localUser.uuid !== ''"
                :rules="[(val: string) => !!val || t('error.required')]"
                lazy-rules
              />
            </div>
          </div>
          <!-- password -->
          <div class="row q-mb-md">
            <div class="col-12 col-sm-2 text-subtitle1 text-md-center">
              {{ t('password') }}
            </div>
            <div class="col-12 col-sm-10">
              <q-input
                type="password"
                name="password"
                v-model="localUser.password"
                maxlength="50"
                outlined
                dense
                hide-bottom-space
                :rules="[(val: string) => (!!val || localUser.uuid !== '') || t('error.required')]"
                lazy-rules
              />
            </div>
          </div>
          <!-- confirmed password -->
          <div class="row q-mb-md">
            <div class="col-12 col-sm-2 text-subtitle1 text-md-center">
              {{ t('confirmPassword') }}
            </div>
            <div class="col-12 col-sm-10">
              <q-input
                type="password"
                name="confirmPassword"
                v-model="confirmPassword"
                maxlength="50"
                outlined
                dense
                hide-bottom-space
                :rules="[
                  (val: string) => (val === localUser.password || !localUser.password) || t('error.passwordNotMatch'),
                ]"
                lazy-rules
              />
            </div>
          </div>

          <q-separator class="q-my-lg" />

          <!-- email -->
          <div class="row q-mb-md">
            <div class="col-12 col-sm-2 text-subtitle1 text-md-center">
              {{ t('email.label') }}
            </div>
            <div class="col-12 col-sm-10">
              <q-input
                type="email"
                name="email"
                v-model="localUser.email"
                maxlength="50"
                outlined
                dense
                hide-bottom-space
                lazy-rules
              />
            </div>
          </div>

          <!-- status -->
          <div class="row q-mb-md" v-if="localUser.uuid">
            <div class="col-12 col-sm-2 text-subtitle1 text-md-center">
              {{ t('status') }}
            </div>
            <div class="col-12 col-sm-10">
              <q-toggle v-model="localUser.is_active" color="positive" />
            </div>
          </div>

          <!-- note -->
          <div class="row q-mb-md">
            <div class="col-12 col-sm-2 text-subtitle1 text-md-center">
              {{ t('note.label') }}
            </div>
            <div class="col-12 col-sm-10">
              <q-input
                type="textarea"
                name="note"
                v-model="localUser.note"
                maxlength="255"
                outlined
                dense
                hide-bottom-space
                rows="10"
              />
            </div>
          </div>
        </q-scroll-area>

        <!-- actions -->
        <div class="col-1">
          <div
            class="row q-mt-md q-pr-md"
            :class="{
              'justify-between': !!localUser.uuid,
              'justify-end': !localUser.uuid,
            }"
          >
            <template v-if="!!localUser.uuid">
              <q-btn
                type="button"
                color="negative"
                @click="onDelete"
                :size="$q.screen.lt.md ? 'sm' : 'md'"
              >
                <q-icon name="delete" />
              </q-btn>
            </template>
            <q-btn
              type="submit"
              :label="t('submit')"
              color="submit"
              :size="$q.screen.lt.md ? 'sm' : 'md'"
              :loading="isSubmit"
            />
          </div>
        </div>
      </div>
    </q-form>
  </q-card-section>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useQuasar, QForm, Notify } from 'quasar';
import { useI18n } from 'vue-i18n';
import { User, UserApi } from '@/api/user';
import { UserGroup } from '@/api/userGroup';
import { useDialog } from '@/utils';

const { t } = useI18n();
const $q = useQuasar();
const dialog = useDialog();

const panelTitle = computed(() => {
  if (localUser.value.uuid) {
    return `${t('user.label')} - ${localUser.value.name}`;
  }
  return t('createUser');
});
const isSubmit = ref(false);
const formRef = ref<InstanceType<typeof QForm> | null>(null);
const localUser = ref<User>({
  uuid: '',
  name: '',
  group_id: 0,
  username: '',
  password: '',
  email: '',
  is_active: true,
  is_admin: false,
  note: '',
});
const groupOpts = ref<UserGroup[]>([]);
const confirmPassword = ref('');

const props = defineProps<{
  id: string;
  groups: UserGroup[];
}>();

const emit = defineEmits(['dataUpdated', 'close']);

onMounted(() => {
  initUserData(props.id);
  groupOpts.value = props.groups;
});

watch(
  () => props.id,
  (newVal) => {
    initUserData(newVal);
  }
);

const initUserData = async (id: string) => {
  formRef.value?.resetValidation();

  localUser.value = {
    uuid: '',
    name: '',
    group_id: 0,
    username: '',
    password: '',
    email: '',
    is_active: true,
    is_admin: false,
    note: '',
  };

  if (id) {
    await getData();
  }
};

watch(
  () => props.groups,
  (newVal) => {
    groupOpts.value = newVal;
  }
);

const getData = async () => {
  const response = await UserApi.get(props.id);
  localUser.value = response.result;
};

const onSubmit = async () => {
  try {
    isSubmit.value = true;

    if (localUser.value.uuid) {
      // update
      await UserApi.update(localUser.value);
    } else {
      // create
      const response = await UserApi.create({
        ...localUser.value,
        password: localUser.value.password,
      });
      localUser.value.uuid = response.result.uuid;
    }

    Notify.create({
      message: t('success'),
      position: 'top',
      color: 'positive',
    });
    emit('close');
  } finally {
    isSubmit.value = false;
    localUser.value.password = '';
    confirmPassword.value = '';

    emit('dataUpdated');
  }
};

const onDelete = async () => {
  dialog.showMessage({
    message: '',
    title: t('confirmDelete'),
    timeout: 0,
    ok: async () => {
      try {
        isSubmit.value = true;

        if (localUser.value.uuid) {
          await UserApi.delete(localUser.value.uuid);
        }

        Notify.create({
          message: t('success'),
          position: 'top',
          color: 'positive',
        });
        emit('close');
      } finally {
        isSubmit.value = false;
        emit('dataUpdated');
      }
    },
  });
};
</script>

<style lang="scss" scoped>
.row {
  align-items: center;
}
</style>
