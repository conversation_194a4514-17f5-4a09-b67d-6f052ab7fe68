package domain

import (
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type Customer struct {
	ID           int64          `gorm:"primaryKey" json:"-"`
	UUID         string         `gorm:"<-:create" json:"uuid"`
	Name         string         `json:"name"`
	Email        string         `json:"email"`
	Phone        string         `json:"phone"`
	LicensePlate string         `json:"license_plate"`
	Country      string         `json:"country"`
	State        string         `json:"state"`
	City         string         `json:"city"`
	Zipcode      string         `json:"zipcode"`
	Address      string         `json:"address"`
	TaxID        string         `json:"tax_id"`      // 統一編號
	IsSupplier   bool           `json:"is_supplier"` // 是否為供應商
	IsVip        bool           `json:"is_vip"`
	VipStartDate null.Time      `json:"-"`
	VipEndDate   null.Time      `json:"vip_end_date,omitempty"`
	IsActive     bool           `json:"is_active"`
	Note         string         `json:"note"`
	CreatedAt    time.Time      `gorm:"<-:create" json:"created_at"`
	UpdatedAt    time.Time      `json:"-"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`

	Products []Product `gorm:"many2many:product_suppliers;foreignKey:ID;joinForeignKey:CustomerID;References:ID;joinReferences:ProductID" json:"products"`
}

type CustomerFilter struct {
	Search     string  `form:"search"`
	Name       string  `form:"name"`
	Email      string  `form:"email"`
	Phone      string  `form:"phone"`
	Address    string  `form:"address"`
	TaxID      string  `form:"tax_id"`
	IsSupplier *bool   `form:"is_supplier"`
	IsActive   *bool   `form:"is_active"`
	Offset     int     `form:"offset"`
	Limit      int     `form:"limit"`
	Order      *string `form:"order"`
}

type CustomerInfo struct {
	ID           int64  `json:"-"`
	UUID         string `json:"uuid"`
	Name         string `json:"name"`
	Email        string `json:"email"`
	Phone        string `json:"phone"`
	LicensePlate string `json:"license_plate"`
	TaxID        string `json:"tax_id"`
}

func (c CustomerInfo) TableName() string {
	return "customers"
}

type CustomerUpdatePayload struct {
	Name         string    `json:"name"`
	Email        *string   `json:"email"`
	Phone        *string   `json:"phone"`
	LicensePlate *string   `json:"license_plate"`
	Country      *string   `json:"country"`
	State        *string   `json:"state"`
	City         *string   `json:"city"`
	Zipcode      *string   `json:"zipcode"`
	Address      *string   `json:"address"`
	TaxID        *string   `json:"tax_id"`      // 統一編號
	IsSupplier   *bool     `json:"is_supplier"` // 是否為供應商
	IsVip        *bool     `json:"is_vip"`
	VipStartDate null.Time `json:"-"`
	VipEndDate   null.Time `json:"vip_end_date,omitempty"`
	IsActive     *bool     `json:"is_active"`
	Note         *string   `json:"note"`
	UpdatedAt    time.Time `json:"-"`
}

func (c CustomerUpdatePayload) TableName() string {
	return "customers"
}
