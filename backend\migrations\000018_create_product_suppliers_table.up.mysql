CREATE TABLE `product_suppliers` (
    `product_id` BIGINT NOT NULL,
    `customer_id` BIGINT NOT NULL,
    UNIQUE (`product_id`, `customer_id`),
    CONSTRAINT `fk_product_suppliers_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_product_suppliers_customer_id` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;