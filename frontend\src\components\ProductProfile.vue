<template>
  <q-dialog
    v-model="open"
    persistent
    no-refocus
    class="card-dialog q-px-md q-pb-lg"
  >
    <q-card class="q-mx-sm q-my-md q-py-sm">
      <!-- data form -->
      <q-form
        ref="formRef"
        @submit.prevent="onSubmit"
        greedy
        class="column full-height"
      >
        <!-- title and close btn -->
        <q-card-section class="col-1 q-py-none">
          <div class="row items-center">
            <div class="text-h6">
              <template v-if="localProduct.uuid">
                {{ t('editProduct') }} - {{ title }}
              </template>
              <template v-else>
                {{ t('createProduct') }}
              </template>
            </div>
            <q-space />
            <q-btn type="button" icon="close" flat round dense @click="close" />
          </div>
        </q-card-section>

        <!-- data -->
        <q-card-section class="col-10 q-pt-xs q-pr-md">
          <q-scroll-area visible class="fit q-pr-md q-pb-md">
            <!-- images / 產品圖片 -->
            <div class="row q-mb-md">
              <div class="col-12 text-h6 text-bold text-black">
                <div class="row items-center">
                  {{ t('images') }}
                  <q-btn
                    round
                    icon="add"
                    color="positive"
                    size="sm"
                    class="q-ma-sm"
                    @click="triggerFileInput"
                    :loading="isUpload"
                  />
                  <input
                    type="file"
                    ref="fileInput"
                    accept="image/*"
                    @change="handleFileUpload"
                    style="display: none"
                  />
                </div>
                <q-separator color="black" size="2px" class="q-mb-sm" />
              </div>
              <div class="col-12">
                <!-- image -->
                <draggable
                  v-model="localProduct.images"
                  @end="onDragEnd"
                  item-key="id"
                  class="row q-col-gutter-sm"
                  v-if="localProduct.images?.length > 0"
                >
                  <template #item="{ element }">
                    <div class="col-4 col-sm-3">
                      <q-img
                        :src="`/api/${element.image_path}`"
                        ratio="1"
                        fit="fill"
                        class="item"
                      >
                        <div
                          class="absolute-top-right"
                          style="background: none; padding: 8px 8px"
                        >
                          <q-btn
                            type="button"
                            round
                            color="negative"
                            icon="delete"
                            size="sm"
                            @click.stop="confirmDeleteImage(element.uuid)"
                            :loading="isDeleteImage || isUpload"
                          />
                        </div>
                      </q-img>
                    </div>
                  </template>
                </draggable>
                <!-- temp images -->
                <draggable
                  v-model="productImages"
                  item-key="id"
                  class="row"
                  v-else-if="productImages.length > 0"
                >
                  <template #item="{ element }">
                    <div class="col-4 col-sm-3">
                      <q-img
                        :src="element.url"
                        ratio="1"
                        fit="fill"
                        class="item"
                      >
                        <div
                          class="absolute-top-right"
                          style="background: none; padding: 8px 8px"
                        >
                          <q-btn
                            type="button"
                            round
                            color="negative"
                            icon="delete"
                            size="sm"
                            @click.stop="deleteTempImage(element)"
                          />
                        </div>
                      </q-img>
                    </div>
                  </template>
                </draggable>
                <!-- no image -->
                <div class="col-12" v-else>
                  <div class="row items-center text-h6 text-bold">
                    {{ t('error.noImage') }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Product Data / 產品資料 -->
            <div class="row q-mb-sm">
              <div class="col-12 text-h6 text-bold text-black">
                {{ t('product.data') }}
                <q-separator color="black" size="2px" />
              </div>
            </div>

            <!-- name -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('name') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model="localProduct.name"
                  maxlength="50"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[
                      (val: string) => !!val || t('error.required'),
                          (val: string) => val.length <= 50 || t('error.max', { max: 50 })
                          ]"
                  lazy-rules
                />
              </div>
            </div>
            <!-- barcode -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('barcode') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model="localProduct.barcode"
                  @keydown.enter.prevent
                  maxlength="50"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[(val: string) => val.length <= 50 || t('error.max', { max: 50 })]"
                  lazy-rules
                >
                  <template v-slot:append>
                    <q-icon
                      name="sym_o_barcode_scanner"
                      class="cursor-pointer"
                      @click="openBarcodeScanner"
                    />
                  </template>
                </q-input>
              </div>
            </div>
            <!-- unit -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('unit') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model="localProduct.unit"
                  maxlength="20"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[(val: string) => val.length <= 20 || t('error.max', { max: 20 })]"
                  lazy-rules
                />
              </div>
            </div>
            <!-- product type -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('productType') }}
              </div>
              <div class="col-12 col-md-10">
                <q-option-group
                  v-model="localProduct.is_flexible_price"
                  :options="productTypeOptions"
                  color="primary"
                  inline
                />
              </div>
            </div>
            <!-- price -->
            <div class="row items-center q-mb-md" v-if="!localProduct.is_flexible_price">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('price') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model.number="localProduct.price"
                  type="number"
                  min="0"
                  step="0.01"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[(val: string) => val !== '' || t('error.required'), (val: number) => val >= 0 || t('error.minNumber', { min: 0 })]"
                  lazy-rules
                />
              </div>
            </div>
            <!-- sale price -->
            <div class="row items-center q-mb-md" v-if="!localProduct.is_flexible_price">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('salePrice') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model.number="localProduct.sale_price"
                  type="number"
                  min="0"
                  step="0.01"
                  outlined
                  dense
                  hide-bottom-space
                />
              </div>
            </div>
            <!-- cost -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('cost') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model.number="localProduct.cost"
                  type="number"
                  min="0"
                  step="0.01"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[(val: number) => val >= 0 || t('error.minNumber', { min: 0 })]"
                  lazy-rules
                />
              </div>
            </div>
            <!-- stock quantity -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('stockQuantity') }}
              </div>
              <div class="col-12 col-md-10 text-subtitle1 q-pl-md q-pl-md-sm">
                {{ localProduct.stock_quantity }}
              </div>
            </div>
            <!-- min stock quantity -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('minStockQuantity') }}
              </div>
              <div class="col-12 col-md-10">
                <q-input
                  v-model.number="localProduct.min_stock_quantity"
                  type="number"
                  min="0"
                  outlined
                  dense
                  hide-bottom-space
                  :rules="[(val: number) => val >= 0 || t('error.minNumber', { min: 0 })]"
                  lazy-rules
                />
              </div>
            </div>

            <!-- Description -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('description') }}
              </div>
              <div class="col-12 col-md-10">
                <q-editor
                  v-model="localProduct.description"
                  :toolbar="toolbar"
                  min-height="7rem"
                />
              </div>
            </div>
            <!-- Short Description -->
            <div class="row items-center q-mb-md">
              <div class="col-12 col-md-2 text-subtitle1 text-md-center">
                {{ t('shortDescription') }}
              </div>
              <div class="col-12 col-md-10">
                <q-editor
                  v-model="localProduct.short_description"
                  :toolbar="toolbar"
                  min-height="6rem"
                />
              </div>
            </div>

            <!-- Suppliers / 供應商 -->
            <div class="row q-mb-sm">
              <div class="col-12 text-h6 text-bold text-black">
                {{ t('supplier') }}
                <q-separator color="black" size="2px" />
              </div>
            </div>
            <div class="row q-mb-sm">
              <div class="col-12 flex">
                <q-select
                  v-model="addSupplier"
                  :options="supplierOptions"
                  option-value="uuid"
                  option-label="name"
                  map-options
                  emit-value
                  :label="t('supplier')"
                  stack-label
                  use-input
                  hide-selected
                  fill-input
                  input-debounce="0"
                  @filter="supplierFilterFn"
                  clearable
                  outlined
                  dense
                  hide-bottom-space
                />
                <q-btn
                  icon="add"
                  color="positive"
                  size="sm"
                  class="q-pa-sm q-ml-sm"
                  @click="addSupplierToProduct"
                  :loading="isUpload"
                />
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <q-table
                  :rows="localProduct.suppliers"
                  :columns="supplierColumns"
                  hide-pagination
                >
                  <template v-slot:body-cell-actions="props">
                    <q-td :props="props">
                      <q-btn
                        type="button"
                        icon="delete"
                        color="negative"
                        size="sm"
                        class="q-pa-sm"
                        @click="confirmRemoveSupplier(props.row.uuid)"
                        :loading="isUpload"
                      />
                    </q-td>
                  </template>
                </q-table>
              </div>
            </div>
          </q-scroll-area>
        </q-card-section>

        <!-- actions -->
        <q-card-actions
          class="col-1"
          :align="!!localProduct.uuid ? 'between' : 'right'"
        >
          <template v-if="!!localProduct.uuid">
            <q-btn
              type="button"
              @click="onDelete"
              icon="delete"
              color="negative"
              :size="$q.screen.lt.md ? 'sm' : 'md'"
              :loading="isSubmit"
            />
          </template>

          <q-btn
            type="submit"
            color="create"
            :size="$q.screen.lt.md ? 'sm' : 'md'"
            :loading="isSubmit"
          >
            <template v-if="localProduct.uuid">
              {{ t('submit') }}
            </template>
            <template v-else>
              {{ t('create') }}
            </template>
          </q-btn>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>

  <!-- 圖片確認刪除對話框 -->
  <q-dialog v-model="deleteImageDialog" no-refocus>
    <q-card>
      <q-card-section class="row items-center">
        <q-avatar icon="warning" color="negative" text-color="white" />
        <span class="q-ml-sm">
          {{ t('confirmDelete') }}
        </span>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat :label="t('cancel')" color="primary" v-close-popup />
        <q-btn
          flat
          :label="t('delete')"
          color="negative"
          @click="deleteImage"
          v-close-popup
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- 刪除供應商確認刪除對話 -->
  <q-dialog v-model="removeSupplierDialog" no-refocus>
    <q-card>
      <q-card-section class="row items-center">
        <q-avatar icon="warning" color="negative" text-color="white" />
        <span class="q-ml-sm">
          {{ t('confirmDelete') }}
        </span>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat :label="t('cancel')" color="primary" v-close-popup />
        <q-btn
          flat
          :label="t('delete')"
          color="negative"
          @click="removeSupplier"
          v-close-popup
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <QrcodeScanner v-model="showScanner" v-model:result="localProduct.barcode" />
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar, Notify, QForm } from 'quasar';
import draggable from 'vuedraggable';
import { ProductApi, Product } from '@/api/product';
import { CustomerApi, Customer } from '@/api/customer';

import { useDialog } from '@/utils';

const { t } = useI18n();
const $q = useQuasar();
const dialog = useDialog();

const props = defineProps<{
  modelValue: boolean;
  productUUID: string;
  categoryID: number | undefined;
}>();

const emit = defineEmits(['update:modelValue', 'refreshData']);

const open = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const formRef = ref<InstanceType<typeof QForm> | null>(null);
const isSubmit = ref(false);
const localProduct = ref<Product>({
  uuid: '',
  name: '',
  barcode: '',
  price: 0,
  sale_price: 0,
  cost: 0,
  unit: '',
  stock_quantity: 0,
  min_stock_quantity: 0,
  description: '',
  short_description: '',
  images: [],
  suppliers: [],
  is_active: true,
  is_flexible_price: false,
});
const title = ref('');
const toolbar = [
  [
    {
      label: $q.lang.editor.formatting,
      list: 'no-icons',
      options: ['p', 'h3', 'h4', 'h5', 'h6', 'code'],
    },
  ],
  ['left', 'center', 'right', 'justify'],
  ['bold', 'italic', 'underline', 'strike'],
];

// 產品類型選項
const productTypeOptions = computed(() => [
  {
    label: t('fixedPriceProduct'),
    value: false,
  },
  {
    label: t('flexiblePriceProduct'),
    value: true,
  },
]);

onMounted(() => {
  showScanner.value = false;

  if (props.productUUID) {
    localProduct.value.uuid = props.productUUID;
    getData();
  }
});

watch(
  () => props.modelValue,
  (newVal) => {
    if (!newVal) return;

    formRef.value?.resetValidation();
    showScanner.value = false;

    if (props.productUUID) {
      localProduct.value.uuid = props.productUUID;
      getData();
    } else {
      initLocalProduct();
      localProduct.value.category_id = props.categoryID;
      title.value = '';
    }

    getSuppliers();
  }
);

// 觸發檔案選擇
const fileInput = ref();
const triggerFileInput = () => {
  if (!fileInput.value) return;

  fileInput.value.click();
};

// 處理檔案上傳
const productImages = ref<
  {
    file: File;
    url: string;
  }[]
>([]);
const isUpload = ref(false);
const handleFileUpload = (event: Event | null) => {
  if (!event?.target) return;

  const input = event.target as HTMLInputElement;
  if (!input.files || input.files?.length === 0) return;

  const file = input.files[0];

  if (!file) return;

  // 遍歷選擇的檔案並添加到圖片陣列
  const reader = new FileReader();

  reader.onload = async (e: ProgressEvent<FileReader>) => {
    if (!e.target?.result) return;

    if (!localProduct.value.uuid) {
      // for create
      productImages.value.push({
        file: file,
        url: e.target.result as string, // 這是 DataURL
      });
    } else {
      // upload
      try {
        isUpload.value = true;
        await ProductApi.uploadImage(localProduct.value.uuid, file);
        await ProductApi.syncWcImages(localProduct.value.uuid);
      } finally {
        isUpload.value = false;
        getData();
      }
    }
  };

  reader.readAsDataURL(file);

  // 清空 input，確保同一檔案能夠再次上傳
  input.value = '';
};

const onDragEnd = () => {
  if (localProduct.value.uuid) {
    updateImageSortOrder();
  }
};

const updateImageSortOrder = async () => {
  try {
    isUpload.value = true;
    localProduct.value.images.forEach((image, index) => {
      image.sort_order = index + 1;
    });

    await ProductApi.updateImageSortOrder(localProduct.value.images);
    await ProductApi.syncWcImages(localProduct.value.uuid);

    Notify.create({
      type: 'positive',
      message: t('success'),
      position: 'top',
    });
  } finally {
    isUpload.value = false;
    getData();
  }
};

const deleteImageDialog = ref(false);
const deletedImageID = ref('');
const confirmDeleteImage = (imageID: string) => {
  deletedImageID.value = imageID;
  deleteImageDialog.value = true;
};

const isDeleteImage = ref(false);
const deleteImage = async () => {
  try {
    isDeleteImage.value = true;
    await ProductApi.deleteImage(localProduct.value.uuid, deletedImageID.value);

    Notify.create({
      type: 'positive',
      message: t('success'),
      position: 'top',
    });
  } finally {
    isDeleteImage.value = false;
    deleteImageDialog.value = false;
    getData();
  }
};

const deleteTempImage = (image: { file: File; url: string }) => {
  productImages.value = productImages.value.filter((item) => item !== image);
};

const addSupplier = ref<string>();
const suppliers = ref<Customer[]>([]);
const supplierOptions = ref<Customer[]>([]);
const supplierColumns = computed(() => [
  {
    name: 'name',
    label: t('name'),
    field: 'name',
    align: 'left' as const,
  },
  {
    name: 'phone',
    label: t('phone'),
    field: 'phone',
    align: 'left' as const,
  },
  {
    name: 'actions',
    label: t('actions'),
    field: 'actions',
    align: 'center' as const,
  },
]);

const addSupplierToProduct = async () => {
  if (!addSupplier.value) return;

  try {
    isUpload.value = true;
    await ProductApi.addSupplier(localProduct.value.uuid, addSupplier.value);
  } finally {
    isUpload.value = false;
    addSupplier.value = '';
    getData();
  }
};

const removeSupplierDialog = ref(false);
const removeSupplierUUID = ref('');
const confirmRemoveSupplier = (supplierUUID: string) => {
  removeSupplierUUID.value = supplierUUID;
  removeSupplierDialog.value = true;
};

const removeSupplier = async () => {
  try {
    isUpload.value = true;
    await ProductApi.removeSupplier(
      localProduct.value.uuid,
      removeSupplierUUID.value
    );
  } finally {
    isUpload.value = false;
    removeSupplierDialog.value = false;
    getData();
  }
};

const getSuppliers = async () => {
  const response = await CustomerApi.fetch({
    filter: {
      is_supplier: true,
    },
  });

  suppliers.value = response.result.data;
};

const supplierFilterFn = (
  val: string,
  update: (callback: () => void) => void
) => {
  if (val === '') {
    update(() => {
      supplierOptions.value = suppliers.value.filter(
        (v: Customer) =>
          !localProduct.value.suppliers.some((s) => s.uuid === v.uuid)
      );
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    supplierOptions.value = suppliers.value.filter(
      (v: Customer) => v.name.toLowerCase().indexOf(needle) > -1
    );
  });
};

const getData = async () => {
  try {
    isUpload.value = true;
    const response = await ProductApi.get(localProduct.value.uuid);
    localProduct.value = response.result;
    title.value = response.result.name;
  } finally {
    isUpload.value = false;
  }
};

const onSubmit = async () => {
  try {
    isSubmit.value = true;

    handleData();

    if (localProduct.value.uuid) {
      // update
      await ProductApi.update(localProduct.value);
    } else {
      // create
      const response = await ProductApi.create(localProduct.value);
      localProduct.value.uuid = response.result.uuid;

      for (const image of productImages.value) {
        if (image.file) {
          await ProductApi.uploadImage(localProduct.value.uuid, image.file);
        }
      }
    }

    await ProductApi.syncWcImages(localProduct.value.uuid);

    Notify.create({
      message: t('success'),
      position: 'top',
      color: 'positive',
    });

    emit('refreshData');
    close();
  } finally {
    isSubmit.value = false;

    productImages.value = [];
  }
};

const handleData = () => {
  if (!localProduct.value.sale_price && localProduct.value.sale_price !== 0) {
    // 如果 sale_price 沒有設定，則設為 0
    localProduct.value.sale_price = 0;
  }
};

const onDelete = async () => {
  dialog.showMessage({
    title: t('confirmDelete'),
    message: '',
    timeout: 0,
    ok: async () => {
      try {
        isSubmit.value = true;
        if (localProduct.value.uuid) {
          await ProductApi.delete(localProduct.value.uuid);
        }

        Notify.create({
          message: t('success'),
          position: 'top',
          color: 'positive',
        });

        emit('refreshData');
        close();
      } finally {
        isSubmit.value = false;
      }
    },
  });
};

const initLocalProduct = () => {
  localProduct.value = {
    uuid: '',
    name: '',
    barcode: '',
    price: 0,
    sale_price: 0,
    cost: 0,
    unit: '',
    stock_quantity: 0,
    min_stock_quantity: 0,
    description: '',
    short_description: '',
    images: [],
    suppliers: [],
    is_active: true,
    is_flexible_price: false,
  };
};

const close = () => {
  initLocalProduct();
  productImages.value = [];
  title.value = '';
  emit('update:modelValue', false);
};

const showScanner = ref(false);
const openBarcodeScanner = () => {
  showScanner.value = true;
};
</script>

<style lang="scss" scope>
.image-container {
  position: relative;

  &:hover {
    .image-overlay {
      opacity: 1;
    }
  }
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
