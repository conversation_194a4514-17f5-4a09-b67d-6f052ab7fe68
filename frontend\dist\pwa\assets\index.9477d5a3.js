var pp=Object.defineProperty;var gp=(e,t,n)=>t in e?pp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Ns=(e,t,n)=>(gp(e,typeof t!="symbol"?t+"":t,n),n);const _p=function(){const t=document.createElement("link").relList;return t&&t.supports&&t.supports("modulepreload")?"modulepreload":"preload"}(),zu={},vp="/au-pos/",Fe=function(t,n){return!n||n.length===0?t():Promise.all(n.map(r=>{if(r=`${vp}${r}`,r in zu)return;zu[r]=!0;const s=r.endsWith(".css"),o=s?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${r}"]${o}`))return;const i=document.createElement("link");if(i.rel=s?"stylesheet":_p,s||(i.as="script",i.crossOrigin=""),i.href=r,document.head.appendChild(i),s)return new Promise((a,u)=>{i.addEventListener("load",a),i.addEventListener("error",()=>u(new Error(`Unable to preload CSS for ${r}`)))})})).then(()=>t())};/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Xo(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ve={},yr=[],bt=()=>{},yp=()=>!1,Ts=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),nu=e=>e.startsWith("onUpdate:"),xe=Object.assign,ru=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},bp=Object.prototype.hasOwnProperty,Ce=(e,t)=>bp.call(e,t),ie=Array.isArray,br=e=>Nr(e)==="[object Map]",Qn=e=>Nr(e)==="[object Set]",Gu=e=>Nr(e)==="[object Date]",Ep=e=>Nr(e)==="[object RegExp]",ce=e=>typeof e=="function",Re=e=>typeof e=="string",Bt=e=>typeof e=="symbol",ke=e=>e!==null&&typeof e=="object",su=e=>(ke(e)||ce(e))&&ce(e.then)&&ce(e.catch),Of=Object.prototype.toString,Nr=e=>Of.call(e),Cp=e=>Nr(e).slice(8,-1),zo=e=>Nr(e)==="[object Object]",ou=e=>Re(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Er=Xo(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Go=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ap=/-(\w)/g,ot=Go(e=>e.replace(Ap,(t,n)=>n?n.toUpperCase():"")),Sp=/\B([A-Z])/g,_t=Go(e=>e.replace(Sp,"-$1").toLowerCase()),Yo=Go(e=>e.charAt(0).toUpperCase()+e.slice(1)),Js=Go(e=>e?`on${Yo(e)}`:""),ct=(e,t)=>!Object.is(e,t),Cr=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Rf=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},mo=e=>{const t=parseFloat(e);return isNaN(t)?e:t},po=e=>{const t=Re(e)?Number(e):NaN;return isNaN(t)?e:t};let Yu;const Qo=()=>Yu||(Yu=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:typeof global!="undefined"?global:{}),wp="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error,Symbol",Tp=Xo(wp);function Jo(e){if(ie(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=Re(r)?Rp(r):Jo(r);if(s)for(const o in s)t[o]=s[o]}return t}else if(Re(e)||ke(e))return e}const kp=/;(?![^(]*\))/g,xp=/:([^]+)/,Op=/\/\*[^]*?\*\//g;function Rp(e){const t={};return e.replace(Op,"").split(kp).forEach(n=>{if(n){const r=n.split(xp);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Zo(e){let t="";if(Re(e))t=e;else if(ie(e))for(let n=0;n<e.length;n++){const r=Zo(e[n]);r&&(t+=r+" ")}else if(ke(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function VS(e){if(!e)return null;let{class:t,style:n}=e;return t&&!Re(t)&&(e.class=Zo(t)),n&&(e.style=Jo(n)),e}const Pp="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Fp=Xo(Pp);function Pf(e){return!!e||e===""}function Dp(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=kn(e[r],t[r]);return n}function kn(e,t){if(e===t)return!0;let n=Gu(e),r=Gu(t);if(n||r)return n&&r?e.getTime()===t.getTime():!1;if(n=Bt(e),r=Bt(t),n||r)return e===t;if(n=ie(e),r=ie(t),n||r)return n&&r?Dp(e,t):!1;if(n=ke(e),r=ke(t),n||r){if(!n||!r)return!1;const s=Object.keys(e).length,o=Object.keys(t).length;if(s!==o)return!1;for(const i in e){const a=e.hasOwnProperty(i),u=t.hasOwnProperty(i);if(a&&!u||!a&&u||!kn(e[i],t[i]))return!1}}return String(e)===String(t)}function ei(e,t){return e.findIndex(n=>kn(n,t))}const Ff=e=>!!(e&&e.__v_isRef===!0),Np=e=>Re(e)?e:e==null?"":ie(e)||ke(e)&&(e.toString===Of||!ce(e.toString))?Ff(e)?Np(e.value):JSON.stringify(e,Df,2):String(e),Df=(e,t)=>Ff(t)?Df(e,t.value):br(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],o)=>(n[Ai(r,o)+" =>"]=s,n),{})}:Qn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Ai(n))}:Bt(t)?Ai(t):ke(t)&&!ie(t)&&!zo(t)?String(t):t,Ai=(e,t="")=>{var n;return Bt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let lt;class Nf{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=lt,!t&&lt&&(this.index=(lt.scopes||(lt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=lt;try{return lt=this,t()}finally{lt=n}}}on(){lt=this}off(){lt=this.parent}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function iu(e){return new Nf(e)}function Lf(){return lt}function Lp(e,t=!1){lt&&lt.cleanups.push(e)}let Oe;const Si=new WeakSet;class go{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,lt&&lt.active&&lt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Si.has(this)&&(Si.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Bf(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Qu(this),Mf(this);const t=Oe,n=Nt;Oe=this,Nt=!0;try{return this.fn()}finally{$f(this),Oe=t,Nt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)lu(t);this.deps=this.depsTail=void 0,Qu(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Si.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ra(this)&&this.run()}get dirty(){return ra(this)}}let If=0,Zr,es;function Bf(e,t=!1){if(e.flags|=8,t){e.next=es,es=e;return}e.next=Zr,Zr=e}function au(){If++}function uu(){if(--If>0)return;if(es){let t=es;for(es=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Zr;){let t=Zr;for(Zr=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function Mf(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function $f(e){let t,n=e.depsTail,r=n;for(;r;){const s=r.prevDep;r.version===-1?(r===n&&(n=s),lu(r),Ip(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=s}e.deps=t,e.depsTail=n}function ra(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Uf(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Uf(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===us))return;e.globalVersion=us;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!ra(e)){e.flags&=-3;return}const n=Oe,r=Nt;Oe=e,Nt=!0;try{Mf(e);const s=e.fn(e._value);(t.version===0||ct(s,e._value))&&(e._value=s,t.version++)}catch(s){throw t.version++,s}finally{Oe=n,Nt=r,$f(e),e.flags&=-3}}function lu(e,t=!1){const{dep:n,prevSub:r,nextSub:s}=e;if(r&&(r.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)lu(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Ip(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}function qS(e,t){e.effect instanceof go&&(e=e.effect.fn);const n=new go(e);t&&xe(n,t);try{n.run()}catch(s){throw n.stop(),s}const r=n.run.bind(n);return r.effect=n,r}function HS(e){e.effect.stop()}let Nt=!0;const Vf=[];function Fn(){Vf.push(Nt),Nt=!1}function Dn(){const e=Vf.pop();Nt=e===void 0?!0:e}function Qu(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=Oe;Oe=void 0;try{t()}finally{Oe=n}}}let us=0;class Bp{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ti{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!Oe||!Nt||Oe===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Oe)n=this.activeLink=new Bp(Oe,this),Oe.deps?(n.prevDep=Oe.depsTail,Oe.depsTail.nextDep=n,Oe.depsTail=n):Oe.deps=Oe.depsTail=n,qf(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=Oe.depsTail,n.nextDep=void 0,Oe.depsTail.nextDep=n,Oe.depsTail=n,Oe.deps===n&&(Oe.deps=r)}return n}trigger(t){this.version++,us++,this.notify(t)}notify(t){au();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{uu()}}}function qf(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)qf(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const _o=new WeakMap,Hn=Symbol(""),sa=Symbol(""),ls=Symbol("");function tt(e,t,n){if(Nt&&Oe){let r=_o.get(e);r||_o.set(e,r=new Map);let s=r.get(n);s||(r.set(n,s=new ti),s.map=r,s.key=n),s.track()}}function en(e,t,n,r,s,o){const i=_o.get(e);if(!i){us++;return}const a=u=>{u&&u.trigger()};if(au(),t==="clear")i.forEach(a);else{const u=ie(e),l=u&&ou(n);if(u&&n==="length"){const c=Number(r);i.forEach((f,d)=>{(d==="length"||d===ls||!Bt(d)&&d>=c)&&a(f)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),l&&a(i.get(ls)),t){case"add":u?l&&a(i.get("length")):(a(i.get(Hn)),br(e)&&a(i.get(sa)));break;case"delete":u||(a(i.get(Hn)),br(e)&&a(i.get(sa)));break;case"set":br(e)&&a(i.get(Hn));break}}uu()}function Mp(e,t){const n=_o.get(e);return n&&n.get(t)}function sr(e){const t=me(e);return t===e?t:(tt(t,"iterate",ls),wt(e)?t:t.map(nt))}function ni(e){return tt(e=me(e),"iterate",ls),e}const $p={__proto__:null,[Symbol.iterator](){return wi(this,Symbol.iterator,nt)},concat(...e){return sr(this).concat(...e.map(t=>ie(t)?sr(t):t))},entries(){return wi(this,"entries",e=>(e[1]=nt(e[1]),e))},every(e,t){return Gt(this,"every",e,t,void 0,arguments)},filter(e,t){return Gt(this,"filter",e,t,n=>n.map(nt),arguments)},find(e,t){return Gt(this,"find",e,t,nt,arguments)},findIndex(e,t){return Gt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Gt(this,"findLast",e,t,nt,arguments)},findLastIndex(e,t){return Gt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Gt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ti(this,"includes",e)},indexOf(...e){return Ti(this,"indexOf",e)},join(e){return sr(this).join(e)},lastIndexOf(...e){return Ti(this,"lastIndexOf",e)},map(e,t){return Gt(this,"map",e,t,void 0,arguments)},pop(){return Ur(this,"pop")},push(...e){return Ur(this,"push",e)},reduce(e,...t){return Ju(this,"reduce",e,t)},reduceRight(e,...t){return Ju(this,"reduceRight",e,t)},shift(){return Ur(this,"shift")},some(e,t){return Gt(this,"some",e,t,void 0,arguments)},splice(...e){return Ur(this,"splice",e)},toReversed(){return sr(this).toReversed()},toSorted(e){return sr(this).toSorted(e)},toSpliced(...e){return sr(this).toSpliced(...e)},unshift(...e){return Ur(this,"unshift",e)},values(){return wi(this,"values",nt)}};function wi(e,t,n){const r=ni(e),s=r[t]();return r!==e&&!wt(e)&&(s._next=s.next,s.next=()=>{const o=s._next();return o.value&&(o.value=n(o.value)),o}),s}const Up=Array.prototype;function Gt(e,t,n,r,s,o){const i=ni(e),a=i!==e&&!wt(e),u=i[t];if(u!==Up[t]){const f=u.apply(e,o);return a?nt(f):f}let l=n;i!==e&&(a?l=function(f,d){return n.call(this,nt(f),d,e)}:n.length>2&&(l=function(f,d){return n.call(this,f,d,e)}));const c=u.call(i,l,r);return a&&s?s(c):c}function Ju(e,t,n,r){const s=ni(e);let o=n;return s!==e&&(wt(e)?n.length>3&&(o=function(i,a,u){return n.call(this,i,a,u,e)}):o=function(i,a,u){return n.call(this,i,nt(a),u,e)}),s[t](o,...r)}function Ti(e,t,n){const r=me(e);tt(r,"iterate",ls);const s=r[t](...n);return(s===-1||s===!1)&&cu(n[0])?(n[0]=me(n[0]),r[t](...n)):s}function Ur(e,t,n=[]){Fn(),au();const r=me(e)[t].apply(e,n);return uu(),Dn(),r}const Vp=Xo("__proto__,__v_isRef,__isVue"),Hf=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Bt));function qp(e){Bt(e)||(e=String(e));const t=me(this);return tt(t,"has",e),t.hasOwnProperty(e)}class jf{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(s?o?Yf:Gf:o?zf:Xf).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=ie(t);if(!s){let u;if(i&&(u=$p[n]))return u;if(n==="hasOwnProperty")return qp}const a=Reflect.get(t,n,Le(t)?t:r);return(Bt(n)?Hf.has(n):Vp(n))||(s||tt(t,"get",n),o)?a:Le(a)?i&&ou(n)?a:a.value:ke(a)?s?Jf(a):Jn(a):a}}class Wf extends jf{constructor(t=!1){super(!1,t)}set(t,n,r,s){let o=t[n];if(!this._isShallow){const u=Xn(o);if(!wt(r)&&!Xn(r)&&(o=me(o),r=me(r)),!ie(t)&&Le(o)&&!Le(r))return u?!1:(o.value=r,!0)}const i=ie(t)&&ou(n)?Number(n)<t.length:Ce(t,n),a=Reflect.set(t,n,r,Le(t)?t:s);return t===me(s)&&(i?ct(r,o)&&en(t,"set",n,r):en(t,"add",n,r)),a}deleteProperty(t,n){const r=Ce(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&en(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!Bt(n)||!Hf.has(n))&&tt(t,"has",n),r}ownKeys(t){return tt(t,"iterate",ie(t)?"length":Hn),Reflect.ownKeys(t)}}class Kf extends jf{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Hp=new Wf,jp=new Kf,Wp=new Wf(!0),Kp=new Kf(!0),oa=e=>e,Ls=e=>Reflect.getPrototypeOf(e);function Xp(e,t,n){return function(...r){const s=this.__v_raw,o=me(s),i=br(o),a=e==="entries"||e===Symbol.iterator&&i,u=e==="keys"&&i,l=s[e](...r),c=n?oa:t?ia:nt;return!t&&tt(o,"iterate",u?sa:Hn),{next(){const{value:f,done:d}=l.next();return d?{value:f,done:d}:{value:a?[c(f[0]),c(f[1])]:c(f),done:d}},[Symbol.iterator](){return this}}}}function Is(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function zp(e,t){const n={get(s){const o=this.__v_raw,i=me(o),a=me(s);e||(ct(s,a)&&tt(i,"get",s),tt(i,"get",a));const{has:u}=Ls(i),l=t?oa:e?ia:nt;if(u.call(i,s))return l(o.get(s));if(u.call(i,a))return l(o.get(a));o!==i&&o.get(s)},get size(){const s=this.__v_raw;return!e&&tt(me(s),"iterate",Hn),Reflect.get(s,"size",s)},has(s){const o=this.__v_raw,i=me(o),a=me(s);return e||(ct(s,a)&&tt(i,"has",s),tt(i,"has",a)),s===a?o.has(s):o.has(s)||o.has(a)},forEach(s,o){const i=this,a=i.__v_raw,u=me(a),l=t?oa:e?ia:nt;return!e&&tt(u,"iterate",Hn),a.forEach((c,f)=>s.call(o,l(c),l(f),i))}};return xe(n,e?{add:Is("add"),set:Is("set"),delete:Is("delete"),clear:Is("clear")}:{add(s){!t&&!wt(s)&&!Xn(s)&&(s=me(s));const o=me(this);return Ls(o).has.call(o,s)||(o.add(s),en(o,"add",s,s)),this},set(s,o){!t&&!wt(o)&&!Xn(o)&&(o=me(o));const i=me(this),{has:a,get:u}=Ls(i);let l=a.call(i,s);l||(s=me(s),l=a.call(i,s));const c=u.call(i,s);return i.set(s,o),l?ct(o,c)&&en(i,"set",s,o):en(i,"add",s,o),this},delete(s){const o=me(this),{has:i,get:a}=Ls(o);let u=i.call(o,s);u||(s=me(s),u=i.call(o,s)),a&&a.call(o,s);const l=o.delete(s);return u&&en(o,"delete",s,void 0),l},clear(){const s=me(this),o=s.size!==0,i=s.clear();return o&&en(s,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=Xp(s,e,t)}),n}function ri(e,t){const n=zp(e,t);return(r,s,o)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(Ce(n,s)&&s in r?n:r,s,o)}const Gp={get:ri(!1,!1)},Yp={get:ri(!1,!0)},Qp={get:ri(!0,!1)},Jp={get:ri(!0,!0)},Xf=new WeakMap,zf=new WeakMap,Gf=new WeakMap,Yf=new WeakMap;function Zp(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function eg(e){return e.__v_skip||!Object.isExtensible(e)?0:Zp(Cp(e))}function Jn(e){return Xn(e)?e:si(e,!1,Hp,Gp,Xf)}function Qf(e){return si(e,!1,Wp,Yp,zf)}function Jf(e){return si(e,!0,jp,Qp,Gf)}function jS(e){return si(e,!0,Kp,Jp,Yf)}function si(e,t,n,r,s){if(!ke(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=s.get(e);if(o)return o;const i=eg(e);if(i===0)return e;const a=new Proxy(e,i===2?r:n);return s.set(e,a),a}function rn(e){return Xn(e)?rn(e.__v_raw):!!(e&&e.__v_isReactive)}function Xn(e){return!!(e&&e.__v_isReadonly)}function wt(e){return!!(e&&e.__v_isShallow)}function cu(e){return e?!!e.__v_raw:!1}function me(e){const t=e&&e.__v_raw;return t?me(t):e}function Zn(e){return!Ce(e,"__v_skip")&&Object.isExtensible(e)&&Rf(e,"__v_skip",!0),e}const nt=e=>ke(e)?Jn(e):e,ia=e=>ke(e)?Jf(e):e;function Le(e){return e?e.__v_isRef===!0:!1}function ge(e){return Zf(e,!1)}function fu(e){return Zf(e,!0)}function Zf(e,t){return Le(e)?e:new tg(e,t)}class tg{constructor(t,n){this.dep=new ti,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:me(t),this._value=n?t:nt(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||wt(t)||Xn(t);t=r?t:me(t),ct(t,n)&&(this._rawValue=t,this._value=r?t:nt(t),this.dep.trigger())}}function WS(e){e.dep&&e.dep.trigger()}function sn(e){return Le(e)?e.value:e}function KS(e){return ce(e)?e():sn(e)}const ng={get:(e,t,n)=>t==="__v_raw"?e:sn(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return Le(s)&&!Le(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function ed(e){return rn(e)?e:new Proxy(e,ng)}class rg{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new ti,{get:r,set:s}=t(n.track.bind(n),n.trigger.bind(n));this._get=r,this._set=s}get value(){return this._value=this._get()}set value(t){this._set(t)}}function sg(e){return new rg(e)}function og(e){const t=ie(e)?new Array(e.length):{};for(const n in e)t[n]=td(e,n);return t}class ig{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Mp(me(this._object),this._key)}}class ag{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function XS(e,t,n){return Le(e)?e:ce(e)?new ag(e):ke(e)&&arguments.length>1?td(e,t,n):ge(e)}function td(e,t,n){const r=e[t];return Le(r)?r:new ig(e,t,n)}class ug{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new ti(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=us-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&Oe!==this)return Bf(this,!0),!0}get value(){const t=this.dep.track();return Uf(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function lg(e,t,n=!1){let r,s;return ce(e)?r=e:(r=e.get,s=e.set),new ug(r,s,n)}const zS={GET:"get",HAS:"has",ITERATE:"iterate"},GS={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},Bs={},vo=new WeakMap;let vn;function YS(){return vn}function cg(e,t=!1,n=vn){if(n){let r=vo.get(n);r||vo.set(n,r=[]),r.push(e)}}function fg(e,t,n=ve){const{immediate:r,deep:s,once:o,scheduler:i,augmentJob:a,call:u}=n,l=p=>s?p:wt(p)||s===!1||s===0?tn(p,1):tn(p);let c,f,d,m,g=!1,C=!1;if(Le(e)?(f=()=>e.value,g=wt(e)):rn(e)?(f=()=>l(e),g=!0):ie(e)?(C=!0,g=e.some(p=>rn(p)||wt(p)),f=()=>e.map(p=>{if(Le(p))return p.value;if(rn(p))return l(p);if(ce(p))return u?u(p,2):p()})):ce(e)?t?f=u?()=>u(e,2):e:f=()=>{if(d){Fn();try{d()}finally{Dn()}}const p=vn;vn=c;try{return u?u(e,3,[m]):e(m)}finally{vn=p}}:f=bt,t&&s){const p=f,b=s===!0?1/0:s;f=()=>tn(p(),b)}const w=Lf(),R=()=>{c.stop(),w&&w.active&&ru(w.effects,c)};if(o&&t){const p=t;t=(...b)=>{p(...b),R()}}let E=C?new Array(e.length).fill(Bs):Bs;const h=p=>{if(!(!(c.flags&1)||!c.dirty&&!p))if(t){const b=c.run();if(s||g||(C?b.some((S,x)=>ct(S,E[x])):ct(b,E))){d&&d();const S=vn;vn=c;try{const x=[b,E===Bs?void 0:C&&E[0]===Bs?[]:E,m];u?u(t,3,x):t(...x),E=b}finally{vn=S}}}else c.run()};return a&&a(h),c=new go(f),c.scheduler=i?()=>i(h,!1):h,m=p=>cg(p,!1,c),d=c.onStop=()=>{const p=vo.get(c);if(p){if(u)u(p,4);else for(const b of p)b();vo.delete(c)}},t?r?h(!0):E=c.run():i?i(h.bind(null,!0),!0):c.run(),R.pause=c.pause.bind(c),R.resume=c.resume.bind(c),R.stop=R,R}function tn(e,t=1/0,n){if(t<=0||!ke(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Le(e))tn(e.value,t,n);else if(ie(e))for(let r=0;r<e.length;r++)tn(e[r],t,n);else if(Qn(e)||br(e))e.forEach(r=>{tn(r,t,n)});else if(zo(e)){for(const r in e)tn(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&tn(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const nd=[];function dg(e){nd.push(e)}function hg(){nd.pop()}function QS(e,t){}const JS={SETUP_FUNCTION:0,0:"SETUP_FUNCTION",RENDER_FUNCTION:1,1:"RENDER_FUNCTION",NATIVE_EVENT_HANDLER:5,5:"NATIVE_EVENT_HANDLER",COMPONENT_EVENT_HANDLER:6,6:"COMPONENT_EVENT_HANDLER",VNODE_HOOK:7,7:"VNODE_HOOK",DIRECTIVE_HOOK:8,8:"DIRECTIVE_HOOK",TRANSITION_HOOK:9,9:"TRANSITION_HOOK",APP_ERROR_HANDLER:10,10:"APP_ERROR_HANDLER",APP_WARN_HANDLER:11,11:"APP_WARN_HANDLER",FUNCTION_REF:12,12:"FUNCTION_REF",ASYNC_COMPONENT_LOADER:13,13:"ASYNC_COMPONENT_LOADER",SCHEDULER:14,14:"SCHEDULER",COMPONENT_UPDATE:15,15:"COMPONENT_UPDATE",APP_UNMOUNT_CLEANUP:16,16:"APP_UNMOUNT_CLEANUP"},mg={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",[0]:"setup function",[1]:"render function",[2]:"watcher getter",[3]:"watcher callback",[4]:"watcher cleanup function",[5]:"native event handler",[6]:"component event handler",[7]:"vnode hook",[8]:"directive hook",[9]:"transition hook",[10]:"app errorHandler",[11]:"app warnHandler",[12]:"ref function",[13]:"async component loader",[14]:"scheduler flush",[15]:"component update",[16]:"app unmount cleanup function"};function ks(e,t,n,r){try{return r?e(...r):e()}catch(s){Lr(s,t,n)}}function Mt(e,t,n,r){if(ce(e)){const s=ks(e,t,n,r);return s&&su(s)&&s.catch(o=>{Lr(o,t,n)}),s}if(ie(e)){const s=[];for(let o=0;o<e.length;o++)s.push(Mt(e[o],t,n,r));return s}}function Lr(e,t,n,r=!0){const s=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ve;if(t){let a=t.parent;const u=t.proxy,l=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const c=a.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,u,l)===!1)return}a=a.parent}if(o){Fn(),ks(o,null,10,[e,u,l]),Dn();return}}pg(e,n,s,r,i)}function pg(e,t,n,r=!0,s=!1){if(s)throw e;console.error(e)}const ft=[];let Wt=-1;const Ar=[];let yn=null,fr=0;const rd=Promise.resolve();let yo=null;function rt(e){const t=yo||rd;return e?t.then(this?e.bind(this):e):t}function gg(e){let t=Wt+1,n=ft.length;for(;t<n;){const r=t+n>>>1,s=ft[r],o=cs(s);o<e||o===e&&s.flags&2?t=r+1:n=r}return t}function du(e){if(!(e.flags&1)){const t=cs(e),n=ft[ft.length-1];!n||!(e.flags&2)&&t>=cs(n)?ft.push(e):ft.splice(gg(t),0,e),e.flags|=1,sd()}}function sd(){yo||(yo=rd.then(od))}function bo(e){ie(e)?Ar.push(...e):yn&&e.id===-1?yn.splice(fr+1,0,e):e.flags&1||(Ar.push(e),e.flags|=1),sd()}function Zu(e,t,n=Wt+1){for(;n<ft.length;n++){const r=ft[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;ft.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Eo(e){if(Ar.length){const t=[...new Set(Ar)].sort((n,r)=>cs(n)-cs(r));if(Ar.length=0,yn){yn.push(...t);return}for(yn=t,fr=0;fr<yn.length;fr++){const n=yn[fr];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}yn=null,fr=0}}const cs=e=>e.id==null?e.flags&2?-1:1/0:e.id;function od(e){const t=bt;try{for(Wt=0;Wt<ft.length;Wt++){const n=ft[Wt];n&&!(n.flags&8)&&(n.flags&4&&(n.flags&=-2),ks(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;Wt<ft.length;Wt++){const n=ft[Wt];n&&(n.flags&=-2)}Wt=-1,ft.length=0,Eo(),yo=null,(ft.length||Ar.length)&&od()}}let dr,Ms=[];function id(e,t){var n,r;dr=e,dr?(dr.enabled=!0,Ms.forEach(({event:s,args:o})=>dr.emit(s,...o)),Ms=[]):typeof window!="undefined"&&window.HTMLElement&&!((r=(n=window.navigator)==null?void 0:n.userAgent)!=null&&r.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(o=>{id(o,t)}),setTimeout(()=>{dr||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,Ms=[])},3e3)):Ms=[]}let Ke=null,oi=null;function fs(e){const t=Ke;return Ke=e,oi=e&&e.type.__scopeId||null,t}function ZS(e){oi=e}function ew(){oi=null}const tw=e=>ad;function ad(e,t=Ke,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&hl(-1);const o=fs(t);let i;try{i=e(...s)}finally{fs(o),r._d&&hl(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function ud(e,t){if(Ke===null)return e;const n=Rs(Ke),r=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[o,i,a,u=ve]=t[s];o&&(ce(o)&&(o={mounted:o,updated:o}),o.deep&&tn(i),r.push({dir:o,instance:n,value:i,oldValue:void 0,arg:a,modifiers:u}))}return e}function Kt(e,t,n,r){const s=e.dirs,o=t&&t.dirs;for(let i=0;i<s.length;i++){const a=s[i];o&&(a.oldValue=o[i].value);let u=a.dir[r];u&&(Fn(),Mt(u,n,8,[e.el,a,e,t]),Dn())}}const ld=Symbol("_vte"),cd=e=>e.__isTeleport,ts=e=>e&&(e.disabled||e.disabled===""),el=e=>e&&(e.defer||e.defer===""),tl=e=>typeof SVGElement!="undefined"&&e instanceof SVGElement,nl=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,aa=(e,t)=>{const n=e&&e.to;return Re(n)?t?t(n):null:n},fd={name:"Teleport",__isTeleport:!0,process(e,t,n,r,s,o,i,a,u,l){const{mc:c,pc:f,pbc:d,o:{insert:m,querySelector:g,createText:C,createComment:w}}=l,R=ts(t.props);let{shapeFlag:E,children:h,dynamicChildren:p}=t;if(e==null){const b=t.el=C(""),S=t.anchor=C("");m(b,n,r),m(S,n,r);const x=(v,O)=>{E&16&&(s&&s.isCE&&(s.ce._teleportTarget=v),c(h,v,O,s,o,i,a,u))},F=()=>{const v=t.target=aa(t.props,g),O=dd(v,t,C,m);v&&(i!=="svg"&&tl(v)?i="svg":i!=="mathml"&&nl(v)&&(i="mathml"),R||(x(v,O),Zs(t,!1)))};R&&(x(n,S),Zs(t,!0)),el(t.props)?qe(()=>{F(),t.el.__isMounted=!0},o):F()}else{if(el(t.props)&&!e.el.__isMounted){qe(()=>{fd.process(e,t,n,r,s,o,i,a,u,l),delete e.el.__isMounted},o);return}t.el=e.el,t.targetStart=e.targetStart;const b=t.anchor=e.anchor,S=t.target=e.target,x=t.targetAnchor=e.targetAnchor,F=ts(e.props),v=F?n:S,O=F?b:x;if(i==="svg"||tl(S)?i="svg":(i==="mathml"||nl(S))&&(i="mathml"),p?(d(e.dynamicChildren,p,v,s,o,i,a),Cu(e,t,!0)):u||f(e,t,v,O,s,o,i,a,!1),R)F?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):$s(t,n,b,l,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const I=t.target=aa(t.props,g);I&&$s(t,I,null,l,0)}else F&&$s(t,S,x,l,1);Zs(t,R)}},remove(e,t,n,{um:r,o:{remove:s}},o){const{shapeFlag:i,children:a,anchor:u,targetStart:l,targetAnchor:c,target:f,props:d}=e;if(f&&(s(l),s(c)),o&&s(u),i&16){const m=o||!ts(d);for(let g=0;g<a.length;g++){const C=a[g];r(C,t,n,m,!!C.dynamicChildren)}}},move:$s,hydrate:_g};function $s(e,t,n,{o:{insert:r},m:s},o=2){o===0&&r(e.targetAnchor,t,n);const{el:i,anchor:a,shapeFlag:u,children:l,props:c}=e,f=o===2;if(f&&r(i,t,n),(!f||ts(c))&&u&16)for(let d=0;d<l.length;d++)s(l[d],t,n,2);f&&r(a,t,n)}function _g(e,t,n,r,s,o,{o:{nextSibling:i,parentNode:a,querySelector:u,insert:l,createText:c}},f){const d=t.target=aa(t.props,u);if(d){const m=ts(t.props),g=d._lpa||d.firstChild;if(t.shapeFlag&16)if(m)t.anchor=f(i(e),t,a(e),n,r,s,o),t.targetStart=g,t.targetAnchor=g&&i(g);else{t.anchor=i(e);let C=g;for(;C;){if(C&&C.nodeType===8){if(C.data==="teleport start anchor")t.targetStart=C;else if(C.data==="teleport anchor"){t.targetAnchor=C,d._lpa=t.targetAnchor&&i(t.targetAnchor);break}}C=i(C)}t.targetAnchor||dd(d,t,c,l),f(g&&i(g),t,d,n,r,s,o)}Zs(t,m)}return t.anchor&&i(t.anchor)}const vg=fd;function Zs(e,t){const n=e.ctx;if(n&&n.ut){let r,s;for(t?(r=e.el,s=e.anchor):(r=e.targetStart,s=e.targetAnchor);r&&r!==s;)r.nodeType===1&&r.setAttribute("data-v-owner",n.uid),r=r.nextSibling;n.ut()}}function dd(e,t,n,r){const s=t.targetStart=n(""),o=t.targetAnchor=n("");return s[ld]=o,e&&(r(s,e),r(o,e)),o}const bn=Symbol("_leaveCb"),Us=Symbol("_enterCb");function hd(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Vt(()=>{e.isMounted=!0}),xt(()=>{e.isUnmounting=!0}),e}const Ct=[Function,Array],md={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ct,onEnter:Ct,onAfterEnter:Ct,onEnterCancelled:Ct,onBeforeLeave:Ct,onLeave:Ct,onAfterLeave:Ct,onLeaveCancelled:Ct,onBeforeAppear:Ct,onAppear:Ct,onAfterAppear:Ct,onAppearCancelled:Ct},pd=e=>{const t=e.subTree;return t.component?pd(t.component):t},yg={name:"BaseTransition",props:md,setup(e,{slots:t}){const n=Ee(),r=hd();return()=>{const s=t.default&&hu(t.default(),!0);if(!s||!s.length)return;const o=gd(s),i=me(e),{mode:a}=i;if(r.isLeaving)return ki(o);const u=rl(o);if(!u)return ki(o);let l=ds(u,i,r,n,f=>l=f);u.type!==je&&xn(u,l);let c=n.subTree&&rl(n.subTree);if(c&&c.type!==je&&!Pt(u,c)&&pd(n).type!==je){let f=ds(c,i,r,n);if(xn(c,f),a==="out-in"&&u.type!==je)return r.isLeaving=!0,f.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,c=void 0},ki(o);a==="in-out"&&u.type!==je?f.delayLeave=(d,m,g)=>{const C=_d(r,c);C[String(c.key)]=c,d[bn]=()=>{m(),d[bn]=void 0,delete l.delayedLeave,c=void 0},l.delayedLeave=()=>{g(),delete l.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return o}}};function gd(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==je){t=n;break}}return t}const bg=yg;function _d(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function ds(e,t,n,r,s){const{appear:o,mode:i,persisted:a=!1,onBeforeEnter:u,onEnter:l,onAfterEnter:c,onEnterCancelled:f,onBeforeLeave:d,onLeave:m,onAfterLeave:g,onLeaveCancelled:C,onBeforeAppear:w,onAppear:R,onAfterAppear:E,onAppearCancelled:h}=t,p=String(e.key),b=_d(n,e),S=(v,O)=>{v&&Mt(v,r,9,O)},x=(v,O)=>{const I=O[1];S(v,O),ie(v)?v.every(T=>T.length<=1)&&I():v.length<=1&&I()},F={mode:i,persisted:a,beforeEnter(v){let O=u;if(!n.isMounted)if(o)O=w||u;else return;v[bn]&&v[bn](!0);const I=b[p];I&&Pt(e,I)&&I.el[bn]&&I.el[bn](),S(O,[v])},enter(v){let O=l,I=c,T=f;if(!n.isMounted)if(o)O=R||l,I=E||c,T=h||f;else return;let H=!1;const L=v[Us]=Z=>{H||(H=!0,Z?S(T,[v]):S(I,[v]),F.delayedLeave&&F.delayedLeave(),v[Us]=void 0)};O?x(O,[v,L]):L()},leave(v,O){const I=String(e.key);if(v[Us]&&v[Us](!0),n.isUnmounting)return O();S(d,[v]);let T=!1;const H=v[bn]=L=>{T||(T=!0,O(),L?S(C,[v]):S(g,[v]),v[bn]=void 0,b[I]===e&&delete b[I])};b[I]=e,m?x(m,[v,H]):H()},clone(v){const O=ds(v,t,n,r,s);return s&&s(O),O}};return F}function ki(e){if(xs(e))return e=an(e),e.children=null,e}function rl(e){if(!xs(e))return cd(e.type)&&e.children?gd(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&ce(n.default))return n.default()}}function xn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,xn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function hu(e,t=!1,n){let r=[],s=0;for(let o=0;o<e.length;o++){let i=e[o];const a=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===He?(i.patchFlag&128&&s++,r=r.concat(hu(i.children,t,a))):(t||i.type!==je)&&r.push(a!=null?an(i,{key:a}):i)}if(s>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function ln(e,t){return ce(e)?(()=>xe({name:e.name},t,{setup:e}))():e}function nw(){const e=Ee();return e?(e.appContext.config.idPrefix||"v")+"-"+e.ids[0]+e.ids[1]++:""}function mu(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function rw(e){const t=Ee(),n=fu(null);if(t){const s=t.refs===ve?t.refs={}:t.refs;Object.defineProperty(s,e,{enumerable:!0,get:()=>n.value,set:o=>n.value=o})}return n}function hs(e,t,n,r,s=!1){if(ie(e)){e.forEach((g,C)=>hs(g,t&&(ie(t)?t[C]:t),n,r,s));return}if(wn(r)&&!s){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&hs(e,t,n,r.component.subTree);return}const o=r.shapeFlag&4?Rs(r.component):r.el,i=s?null:o,{i:a,r:u}=e,l=t&&t.r,c=a.refs===ve?a.refs={}:a.refs,f=a.setupState,d=me(f),m=f===ve?()=>!1:g=>Ce(d,g);if(l!=null&&l!==u&&(Re(l)?(c[l]=null,m(l)&&(f[l]=null)):Le(l)&&(l.value=null)),ce(u))ks(u,a,12,[i,c]);else{const g=Re(u),C=Le(u);if(g||C){const w=()=>{if(e.f){const R=g?m(u)?f[u]:c[u]:u.value;s?ie(R)&&ru(R,o):ie(R)?R.includes(o)||R.push(o):g?(c[u]=[o],m(u)&&(f[u]=c[u])):(u.value=[o],e.k&&(c[e.k]=u.value))}else g?(c[u]=i,m(u)&&(f[u]=i)):C&&(u.value=i,e.k&&(c[e.k]=i))};i?(w.id=-1,qe(w,n)):w()}}}let sl=!1;const or=()=>{sl||(console.error("Hydration completed but contains mismatches."),sl=!0)},Eg=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",Cg=e=>e.namespaceURI.includes("MathML"),Vs=e=>{if(e.nodeType===1){if(Eg(e))return"svg";if(Cg(e))return"mathml"}},gr=e=>e.nodeType===8;function Ag(e){const{mt:t,p:n,o:{patchProp:r,createText:s,nextSibling:o,parentNode:i,remove:a,insert:u,createComment:l}}=e,c=(h,p)=>{if(!p.hasChildNodes()){n(null,h,p),Eo(),p._vnode=h;return}f(p.firstChild,h,null,null,null),Eo(),p._vnode=h},f=(h,p,b,S,x,F=!1)=>{F=F||!!p.dynamicChildren;const v=gr(h)&&h.data==="[",O=()=>C(h,p,b,S,x,v),{type:I,ref:T,shapeFlag:H,patchFlag:L}=p;let Z=h.nodeType;p.el=h,L===-2&&(F=!1,p.dynamicChildren=null);let W=null;switch(I){case Tn:Z!==3?p.children===""?(u(p.el=s(""),i(h),h),W=h):W=O():(h.data!==p.children&&(or(),h.data=p.children),W=o(h));break;case je:E(h)?(W=o(h),R(p.el=h.content.firstChild,h,b)):Z!==8||v?W=O():W=o(h);break;case wr:if(v&&(h=o(h),Z=h.nodeType),Z===1||Z===3){W=h;const Q=!p.children.length;for(let V=0;V<p.staticCount;V++)Q&&(p.children+=W.nodeType===1?W.outerHTML:W.data),V===p.staticCount-1&&(p.anchor=W),W=o(W);return v?o(W):W}else O();break;case He:v?W=g(h,p,b,S,x,F):W=O();break;default:if(H&1)(Z!==1||p.type.toLowerCase()!==h.tagName.toLowerCase())&&!E(h)?W=O():W=d(h,p,b,S,x,F);else if(H&6){p.slotScopeIds=x;const Q=i(h);if(v?W=w(h):gr(h)&&h.data==="teleport start"?W=w(h,h.data,"teleport end"):W=o(h),t(p,Q,null,b,S,Vs(Q),F),wn(p)&&!p.type.__asyncResolved){let V;v?(V=De(He),V.anchor=W?W.previousSibling:Q.lastChild):V=h.nodeType===3?Wd(""):De("div"),V.el=h,p.component.subTree=V}}else H&64?Z!==8?W=O():W=p.type.hydrate(h,p,b,S,x,F,e,m):H&128&&(W=p.type.hydrate(h,p,b,S,Vs(i(h)),x,F,e,f))}return T!=null&&hs(T,null,S,p),W},d=(h,p,b,S,x,F)=>{F=F||!!p.dynamicChildren;const{type:v,props:O,patchFlag:I,shapeFlag:T,dirs:H,transition:L}=p,Z=v==="input"||v==="option";if(Z||I!==-1){H&&Kt(p,null,b,"created");let W=!1;if(E(h)){W=Nd(null,L)&&b&&b.vnode.props&&b.vnode.props.appear;const V=h.content.firstChild;W&&L.beforeEnter(V),R(V,h,b),p.el=h=V}if(T&16&&!(O&&(O.innerHTML||O.textContent))){let V=m(h.firstChild,p,h,b,S,x,F);for(;V;){qs(h,1)||or();const se=V;V=V.nextSibling,a(se)}}else if(T&8){let V=p.children;V[0]===`
`&&(h.tagName==="PRE"||h.tagName==="TEXTAREA")&&(V=V.slice(1)),h.textContent!==V&&(qs(h,0)||or(),h.textContent=p.children)}if(O){if(Z||!F||I&48){const V=h.tagName.includes("-");for(const se in O)(Z&&(se.endsWith("value")||se==="indeterminate")||Ts(se)&&!Er(se)||se[0]==="."||V)&&r(h,se,null,O[se],void 0,b)}else if(O.onClick)r(h,"onClick",null,O.onClick,void 0,b);else if(I&4&&rn(O.style))for(const V in O.style)O.style[V]}let Q;(Q=O&&O.onVnodeBeforeMount)&&mt(Q,b,p),H&&Kt(p,null,b,"beforeMount"),((Q=O&&O.onVnodeMounted)||H||W)&&Ud(()=>{Q&&mt(Q,b,p),W&&L.enter(h),H&&Kt(p,null,b,"mounted")},S)}return h.nextSibling},m=(h,p,b,S,x,F,v)=>{v=v||!!p.dynamicChildren;const O=p.children,I=O.length;for(let T=0;T<I;T++){const H=v?O[T]:O[T]=gt(O[T]),L=H.type===Tn;h?(L&&!v&&T+1<I&&gt(O[T+1]).type===Tn&&(u(s(h.data.slice(H.children.length)),b,o(h)),h.data=H.children),h=f(h,H,S,x,F,v)):L&&!H.children?u(H.el=s(""),b):(qs(b,1)||or(),n(null,H,b,null,S,x,Vs(b),F))}return h},g=(h,p,b,S,x,F)=>{const{slotScopeIds:v}=p;v&&(x=x?x.concat(v):v);const O=i(h),I=m(o(h),p,O,b,S,x,F);return I&&gr(I)&&I.data==="]"?o(p.anchor=I):(or(),u(p.anchor=l("]"),O,I),I)},C=(h,p,b,S,x,F)=>{if(qs(h.parentElement,1)||or(),p.el=null,F){const I=w(h);for(;;){const T=o(h);if(T&&T!==I)a(T);else break}}const v=o(h),O=i(h);return a(h),n(null,p,O,v,b,S,Vs(O),x),b&&(b.vnode.el=p.el,li(b,p.el)),v},w=(h,p="[",b="]")=>{let S=0;for(;h;)if(h=o(h),h&&gr(h)&&(h.data===p&&S++,h.data===b)){if(S===0)return o(h);S--}return h},R=(h,p,b)=>{const S=p.parentNode;S&&S.replaceChild(h,p);let x=b;for(;x;)x.vnode.el===p&&(x.vnode.el=x.subTree.el=h),x=x.parent},E=h=>h.nodeType===1&&h.tagName==="TEMPLATE";return[c,f]}const ol="data-allow-mismatch",Sg={[0]:"text",[1]:"children",[2]:"class",[3]:"style",[4]:"attribute"};function qs(e,t){if(t===0||t===1)for(;e&&!e.hasAttribute(ol);)e=e.parentElement;const n=e&&e.getAttribute(ol);if(n==null)return!1;if(n==="")return!0;{const r=n.split(",");return t===0&&r.includes("children")?!0:n.split(",").includes(Sg[t])}}const wg=Qo().requestIdleCallback||(e=>setTimeout(e,1)),Tg=Qo().cancelIdleCallback||(e=>clearTimeout(e)),sw=(e=1e4)=>t=>{const n=wg(t,{timeout:e});return()=>Tg(n)};function kg(e){const{top:t,left:n,bottom:r,right:s}=e.getBoundingClientRect(),{innerHeight:o,innerWidth:i}=window;return(t>0&&t<o||r>0&&r<o)&&(n>0&&n<i||s>0&&s<i)}const ow=e=>(t,n)=>{const r=new IntersectionObserver(s=>{for(const o of s)if(!!o.isIntersecting){r.disconnect(),t();break}},e);return n(s=>{if(s instanceof Element){if(kg(s))return t(),r.disconnect(),!1;r.observe(s)}}),()=>r.disconnect()},iw=e=>t=>{if(e){const n=matchMedia(e);if(n.matches)t();else return n.addEventListener("change",t,{once:!0}),()=>n.removeEventListener("change",t)}},aw=(e=[])=>(t,n)=>{Re(e)&&(e=[e]);let r=!1;const s=i=>{r||(r=!0,o(),t(),i.target.dispatchEvent(new i.constructor(i.type,i)))},o=()=>{n(i=>{for(const a of e)i.removeEventListener(a,s)})};return n(i=>{for(const a of e)i.addEventListener(a,s,{once:!0})}),o};function xg(e,t){if(gr(e)&&e.data==="["){let n=1,r=e.nextSibling;for(;r;){if(r.nodeType===1){if(t(r)===!1)break}else if(gr(r))if(r.data==="]"){if(--n===0)break}else r.data==="["&&n++;r=r.nextSibling}}else t(e)}const wn=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function uw(e){ce(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:r,delay:s=200,hydrate:o,timeout:i,suspensible:a=!0,onError:u}=e;let l=null,c,f=0;const d=()=>(f++,l=null,m()),m=()=>{let g;return l||(g=l=t().catch(C=>{if(C=C instanceof Error?C:new Error(String(C)),u)return new Promise((w,R)=>{u(C,()=>w(d()),()=>R(C),f+1)});throw C}).then(C=>g!==l&&l?l:(C&&(C.__esModule||C[Symbol.toStringTag]==="Module")&&(C=C.default),c=C,C)))};return ln({name:"AsyncComponentWrapper",__asyncLoader:m,__asyncHydrate(g,C,w){const R=o?()=>{const E=o(w,h=>xg(g,h));E&&(C.bum||(C.bum=[])).push(E)}:w;c?R():m().then(()=>!C.isUnmounted&&R())},get __asyncResolved(){return c},setup(){const g=We;if(mu(g),c)return()=>xi(c,g);const C=h=>{l=null,Lr(h,g,13,!r)};if(a&&g.suspense||Tr)return m().then(h=>()=>xi(h,g)).catch(h=>(C(h),()=>r?De(r,{error:h}):null));const w=ge(!1),R=ge(),E=ge(!!s);return s&&setTimeout(()=>{E.value=!1},s),i!=null&&setTimeout(()=>{if(!w.value&&!R.value){const h=new Error(`Async component timed out after ${i}ms.`);C(h),R.value=h}},i),m().then(()=>{w.value=!0,g.parent&&xs(g.parent.vnode)&&g.parent.update()}).catch(h=>{C(h),R.value=h}),()=>{if(w.value&&c)return xi(c,g);if(R.value&&r)return De(r,{error:R.value});if(n&&!E.value)return De(n)}}})}function xi(e,t){const{ref:n,props:r,children:s,ce:o}=t.vnode,i=De(e,r,s);return i.ref=n,i.ce=o,delete t.vnode.ce,i}const xs=e=>e.type.__isKeepAlive,Og={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=Ee(),r=n.ctx;if(!r.renderer)return()=>{const E=t.default&&t.default();return E&&E.length===1?E[0]:E};const s=new Map,o=new Set;let i=null;const a=n.suspense,{renderer:{p:u,m:l,um:c,o:{createElement:f}}}=r,d=f("div");r.activate=(E,h,p,b,S)=>{const x=E.component;l(E,h,p,0,a),u(x.vnode,E,h,p,x,a,b,E.slotScopeIds,S),qe(()=>{x.isDeactivated=!1,x.a&&Cr(x.a);const F=E.props&&E.props.onVnodeMounted;F&&mt(F,x.parent,E)},a)},r.deactivate=E=>{const h=E.component;Ao(h.m),Ao(h.a),l(E,d,null,1,a),qe(()=>{h.da&&Cr(h.da);const p=E.props&&E.props.onVnodeUnmounted;p&&mt(p,h.parent,E),h.isDeactivated=!0},a)};function m(E){Oi(E),c(E,n,a,!0)}function g(E){s.forEach((h,p)=>{const b=va(h.type);b&&!E(b)&&C(p)})}function C(E){const h=s.get(E);h&&(!i||!Pt(h,i))?m(h):i&&Oi(i),s.delete(E),o.delete(E)}Ae(()=>[e.include,e.exclude],([E,h])=>{E&&g(p=>Gr(E,p)),h&&g(p=>!Gr(h,p))},{flush:"post",deep:!0});let w=null;const R=()=>{w!=null&&(So(n.subTree.type)?qe(()=>{s.set(w,Hs(n.subTree))},n.subTree.suspense):s.set(w,Hs(n.subTree)))};return Vt(R),gu(R),xt(()=>{s.forEach(E=>{const{subTree:h,suspense:p}=n,b=Hs(h);if(E.type===b.type&&E.key===b.key){Oi(b);const S=b.component.da;S&&qe(S,p);return}m(E)})}),()=>{if(w=null,!t.default)return i=null;const E=t.default(),h=E[0];if(E.length>1)return i=null,E;if(!On(h)||!(h.shapeFlag&4)&&!(h.shapeFlag&128))return i=null,h;let p=Hs(h);if(p.type===je)return i=null,p;const b=p.type,S=va(wn(p)?p.type.__asyncResolved||{}:b),{include:x,exclude:F,max:v}=e;if(x&&(!S||!Gr(x,S))||F&&S&&Gr(F,S))return p.shapeFlag&=-257,i=p,h;const O=p.key==null?b:p.key,I=s.get(O);return p.el&&(p=an(p),h.shapeFlag&128&&(h.ssContent=p)),w=O,I?(p.el=I.el,p.component=I.component,p.transition&&xn(p,p.transition),p.shapeFlag|=512,o.delete(O),o.add(O)):(o.add(O),v&&o.size>parseInt(v,10)&&C(o.values().next().value)),p.shapeFlag|=256,i=p,So(h.type)?h:p}}},lw=Og;function Gr(e,t){return ie(e)?e.some(n=>Gr(n,t)):Re(e)?e.split(",").includes(t):Ep(e)?(e.lastIndex=0,e.test(t)):!1}function vd(e,t){yd(e,"a",t)}function ii(e,t){yd(e,"da",t)}function yd(e,t,n=We){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(ai(t,r,n),n){let s=n.parent;for(;s&&s.parent;)xs(s.parent.vnode)&&Rg(r,t,n,s),s=s.parent}}function Rg(e,t,n,r){const s=ai(t,e,r,!0);Ir(()=>{ru(r[t],s)},n)}function Oi(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Hs(e){return e.shapeFlag&128?e.ssContent:e}function ai(e,t,n=We,r=!1){if(n){const s=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{Fn();const a=Gn(n),u=Mt(t,n,e,i);return a(),Dn(),u});return r?s.unshift(o):s.push(o),o}}const cn=e=>(t,n=We)=>{(!Tr||e==="sp")&&ai(e,(...r)=>t(...r),n)},Pg=cn("bm"),Vt=cn("m"),pu=cn("bu"),gu=cn("u"),xt=cn("bum"),Ir=cn("um"),Fg=cn("sp"),Dg=cn("rtg"),Ng=cn("rtc");function Lg(e,t=We){ai("ec",e,t)}const _u="components",Ig="directives";function Bg(e,t){return vu(_u,e,!0,t)||e}const bd=Symbol.for("v-ndc");function cw(e){return Re(e)?vu(_u,e,!1)||e:e||bd}function fw(e){return vu(Ig,e)}function vu(e,t,n=!0,r=!1){const s=Ke||We;if(s){const o=s.type;if(e===_u){const a=va(o,!1);if(a&&(a===t||a===ot(t)||a===Yo(ot(t))))return o}const i=il(s[e]||o[e],t)||il(s.appContext[e],t);return!i&&r?o:i}}function il(e,t){return e&&(e[t]||e[ot(t)]||e[Yo(ot(t))])}function dw(e,t,n,r){let s;const o=n&&n[r],i=ie(e);if(i||Re(e)){const a=i&&rn(e);let u=!1;a&&(u=!wt(e),e=ni(e)),s=new Array(e.length);for(let l=0,c=e.length;l<c;l++)s[l]=t(u?nt(e[l]):e[l],l,void 0,o&&o[l])}else if(typeof e=="number"){s=new Array(e);for(let a=0;a<e;a++)s[a]=t(a+1,a,void 0,o&&o[a])}else if(ke(e))if(e[Symbol.iterator])s=Array.from(e,(a,u)=>t(a,u,void 0,o&&o[u]));else{const a=Object.keys(e);s=new Array(a.length);for(let u=0,l=a.length;u<l;u++){const c=a[u];s[u]=t(e[c],c,u,o&&o[u])}}else s=[];return n&&(n[r]=s),s}function hw(e,t){for(let n=0;n<t.length;n++){const r=t[n];if(ie(r))for(let s=0;s<r.length;s++)e[r[s].name]=r[s].fn;else r&&(e[r.name]=r.key?(...s)=>{const o=r.fn(...s);return o&&(o.key=r.key),o}:r.fn)}return e}function mw(e,t,n={},r,s){if(Ke.ce||Ke.parent&&wn(Ke.parent)&&Ke.parent.ce)return t!=="default"&&(n.name=t),gs(),wo(He,null,[De("slot",n,r&&r())],64);let o=e[t];o&&o._c&&(o._d=!1),gs();const i=o&&yu(o(n)),a=n.key||i&&i.key,u=wo(He,{key:(a&&!Bt(a)?a:`_${t}`)+(!i&&r?"_fb":"")},i||(r?r():[]),i&&e._===1?64:-2);return!s&&u.scopeId&&(u.slotScopeIds=[u.scopeId+"-s"]),o&&o._c&&(o._d=!0),u}function yu(e){return e.some(t=>On(t)?!(t.type===je||t.type===He&&!yu(t.children)):!0)?e:null}function pw(e,t){const n={};for(const r in e)n[t&&/[A-Z]/.test(r)?`on:${r}`:Js(r)]=e[r];return n}const ua=e=>e?Xd(e)?Rs(e):ua(e.parent):null,ns=xe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ua(e.parent),$root:e=>ua(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>bu(e),$forceUpdate:e=>e.f||(e.f=()=>{du(e.update)}),$nextTick:e=>e.n||(e.n=rt.bind(e.proxy)),$watch:e=>o_.bind(e)}),Ri=(e,t)=>e!==ve&&!e.__isScriptSetup&&Ce(e,t),la={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:o,accessCache:i,type:a,appContext:u}=e;let l;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return o[t]}else{if(Ri(r,t))return i[t]=1,r[t];if(s!==ve&&Ce(s,t))return i[t]=2,s[t];if((l=e.propsOptions[0])&&Ce(l,t))return i[t]=3,o[t];if(n!==ve&&Ce(n,t))return i[t]=4,n[t];ca&&(i[t]=0)}}const c=ns[t];let f,d;if(c)return t==="$attrs"&&tt(e.attrs,"get",""),c(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==ve&&Ce(n,t))return i[t]=4,n[t];if(d=u.config.globalProperties,Ce(d,t))return d[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:o}=e;return Ri(s,t)?(s[t]=n,!0):r!==ve&&Ce(r,t)?(r[t]=n,!0):Ce(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:o}},i){let a;return!!n[i]||e!==ve&&Ce(e,i)||Ri(t,i)||(a=o[0])&&Ce(a,i)||Ce(r,i)||Ce(ns,i)||Ce(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Ce(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}},Mg=xe({},la,{get(e,t){if(t!==Symbol.unscopables)return la.get(e,t,e)},has(e,t){return t[0]!=="_"&&!Tp(t)}});function gw(){return null}function _w(){return null}function vw(e){}function yw(e){}function bw(){return null}function Ew(){}function Cw(e,t){return null}function Aw(){return Ed().slots}function Sw(){return Ed().attrs}function Ed(){const e=Ee();return e.setupContext||(e.setupContext=Yd(e))}function ms(e){return ie(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}function ww(e,t){const n=ms(e);for(const r in t){if(r.startsWith("__skip"))continue;let s=n[r];s?ie(s)||ce(s)?s=n[r]={type:s,default:t[r]}:s.default=t[r]:s===null&&(s=n[r]={default:t[r]}),s&&t[`__skip_${r}`]&&(s.skipFactory=!0)}return n}function Tw(e,t){return!e||!t?e||t:ie(e)&&ie(t)?e.concat(t):xe({},ms(e),ms(t))}function kw(e,t){const n={};for(const r in e)t.includes(r)||Object.defineProperty(n,r,{enumerable:!0,get:()=>e[r]});return n}function xw(e){const t=Ee();let n=e();return pa(),su(n)&&(n=n.catch(r=>{throw Gn(t),r})),[n,()=>Gn(t)]}let ca=!0;function $g(e){const t=bu(e),n=e.proxy,r=e.ctx;ca=!1,t.beforeCreate&&al(t.beforeCreate,e,"bc");const{data:s,computed:o,methods:i,watch:a,provide:u,inject:l,created:c,beforeMount:f,mounted:d,beforeUpdate:m,updated:g,activated:C,deactivated:w,beforeDestroy:R,beforeUnmount:E,destroyed:h,unmounted:p,render:b,renderTracked:S,renderTriggered:x,errorCaptured:F,serverPrefetch:v,expose:O,inheritAttrs:I,components:T,directives:H,filters:L}=t;if(l&&Ug(l,r,null),i)for(const Q in i){const V=i[Q];ce(V)&&(r[Q]=V.bind(n))}if(s){const Q=s.call(n,n);ke(Q)&&(e.data=Jn(Q))}if(ca=!0,o)for(const Q in o){const V=o[Q],se=ce(V)?V.bind(n,n):ce(V.get)?V.get.bind(n,n):bt,he=!ce(V)&&ce(V.set)?V.set.bind(n):bt,de=$({get:se,set:he});Object.defineProperty(r,Q,{enumerable:!0,configurable:!0,get:()=>de.value,set:z=>de.value=z})}if(a)for(const Q in a)Cd(a[Q],r,n,Q);if(u){const Q=ce(u)?u.call(n):u;Reflect.ownKeys(Q).forEach(V=>{eo(V,Q[V])})}c&&al(c,e,"c");function W(Q,V){ie(V)?V.forEach(se=>Q(se.bind(n))):V&&Q(V.bind(n))}if(W(Pg,f),W(Vt,d),W(pu,m),W(gu,g),W(vd,C),W(ii,w),W(Lg,F),W(Ng,S),W(Dg,x),W(xt,E),W(Ir,p),W(Fg,v),ie(O))if(O.length){const Q=e.exposed||(e.exposed={});O.forEach(V=>{Object.defineProperty(Q,V,{get:()=>n[V],set:se=>n[V]=se})})}else e.exposed||(e.exposed={});b&&e.render===bt&&(e.render=b),I!=null&&(e.inheritAttrs=I),T&&(e.components=T),H&&(e.directives=H),v&&mu(e)}function Ug(e,t,n=bt){ie(e)&&(e=fa(e));for(const r in e){const s=e[r];let o;ke(s)?"default"in s?o=vt(s.from||r,s.default,!0):o=vt(s.from||r):o=vt(s),Le(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[r]=o}}function al(e,t,n){Mt(ie(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Cd(e,t,n,r){let s=r.includes(".")?Id(n,r):()=>n[r];if(Re(e)){const o=t[e];ce(o)&&Ae(s,o)}else if(ce(e))Ae(s,e.bind(n));else if(ke(e))if(ie(e))e.forEach(o=>Cd(o,t,n,r));else{const o=ce(e.handler)?e.handler.bind(n):t[e.handler];ce(o)&&Ae(s,o,e)}}function bu(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,a=o.get(t);let u;return a?u=a:!s.length&&!n&&!r?u=t:(u={},s.length&&s.forEach(l=>Co(u,l,i,!0)),Co(u,t,i)),ke(t)&&o.set(t,u),u}function Co(e,t,n,r=!1){const{mixins:s,extends:o}=t;o&&Co(e,o,n,!0),s&&s.forEach(i=>Co(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const a=Vg[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const Vg={data:ul,props:ll,emits:ll,methods:Yr,computed:Yr,beforeCreate:at,created:at,beforeMount:at,mounted:at,beforeUpdate:at,updated:at,beforeDestroy:at,beforeUnmount:at,destroyed:at,unmounted:at,activated:at,deactivated:at,errorCaptured:at,serverPrefetch:at,components:Yr,directives:Yr,watch:Hg,provide:ul,inject:qg};function ul(e,t){return t?e?function(){return xe(ce(e)?e.call(this,this):e,ce(t)?t.call(this,this):t)}:t:e}function qg(e,t){return Yr(fa(e),fa(t))}function fa(e){if(ie(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function at(e,t){return e?[...new Set([].concat(e,t))]:t}function Yr(e,t){return e?xe(Object.create(null),e,t):t}function ll(e,t){return e?ie(e)&&ie(t)?[...new Set([...e,...t])]:xe(Object.create(null),ms(e),ms(t!=null?t:{})):t}function Hg(e,t){if(!e)return t;if(!t)return e;const n=xe(Object.create(null),e);for(const r in t)n[r]=at(e[r],t[r]);return n}function Ad(){return{app:null,config:{isNativeTag:yp,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let jg=0;function Wg(e,t){return function(r,s=null){ce(r)||(r=xe({},r)),s!=null&&!ke(s)&&(s=null);const o=Ad(),i=new WeakSet,a=[];let u=!1;const l=o.app={_uid:jg++,_component:r,_props:s,_container:null,_context:o,_instance:null,version:T_,get config(){return o.config},set config(c){},use(c,...f){return i.has(c)||(c&&ce(c.install)?(i.add(c),c.install(l,...f)):ce(c)&&(i.add(c),c(l,...f))),l},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),l},component(c,f){return f?(o.components[c]=f,l):o.components[c]},directive(c,f){return f?(o.directives[c]=f,l):o.directives[c]},mount(c,f,d){if(!u){const m=l._ceVNode||De(r,s);return m.appContext=o,d===!0?d="svg":d===!1&&(d=void 0),f&&t?t(m,c):e(m,c,d),u=!0,l._container=c,c.__vue_app__=l,Rs(m.component)}},onUnmount(c){a.push(c)},unmount(){u&&(Mt(a,l._instance,16),e(null,l._container),delete l._container.__vue_app__)},provide(c,f){return o.provides[c]=f,l},runWithContext(c){const f=jn;jn=l;try{return c()}finally{jn=f}}};return l}}let jn=null;function eo(e,t){if(We){let n=We.provides;const r=We.parent&&We.parent.provides;r===n&&(n=We.provides=Object.create(r)),n[e]=t}}function vt(e,t,n=!1){const r=We||Ke;if(r||jn){const s=jn?jn._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&ce(t)?t.call(r&&r.proxy):t}}function Kg(){return!!(We||Ke||jn)}const Sd={},wd=()=>Object.create(Sd),Td=e=>Object.getPrototypeOf(e)===Sd;function Xg(e,t,n,r=!1){const s={},o=wd();e.propsDefaults=Object.create(null),kd(e,t,s,o);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=r?s:Qf(s):e.type.props?e.props=s:e.props=o,e.attrs=o}function zg(e,t,n,r){const{props:s,attrs:o,vnode:{patchFlag:i}}=e,a=me(s),[u]=e.propsOptions;let l=!1;if((r||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let d=c[f];if(ui(e.emitsOptions,d))continue;const m=t[d];if(u)if(Ce(o,d))m!==o[d]&&(o[d]=m,l=!0);else{const g=ot(d);s[g]=da(u,a,g,m,e,!1)}else m!==o[d]&&(o[d]=m,l=!0)}}}else{kd(e,t,s,o)&&(l=!0);let c;for(const f in a)(!t||!Ce(t,f)&&((c=_t(f))===f||!Ce(t,c)))&&(u?n&&(n[f]!==void 0||n[c]!==void 0)&&(s[f]=da(u,a,f,void 0,e,!0)):delete s[f]);if(o!==a)for(const f in o)(!t||!Ce(t,f)&&!0)&&(delete o[f],l=!0)}l&&en(e.attrs,"set","")}function kd(e,t,n,r){const[s,o]=e.propsOptions;let i=!1,a;if(t)for(let u in t){if(Er(u))continue;const l=t[u];let c;s&&Ce(s,c=ot(u))?!o||!o.includes(c)?n[c]=l:(a||(a={}))[c]=l:ui(e.emitsOptions,u)||(!(u in r)||l!==r[u])&&(r[u]=l,i=!0)}if(o){const u=me(n),l=a||ve;for(let c=0;c<o.length;c++){const f=o[c];n[f]=da(s,u,f,l[f],e,!Ce(l,f))}}return i}function da(e,t,n,r,s,o){const i=e[n];if(i!=null){const a=Ce(i,"default");if(a&&r===void 0){const u=i.default;if(i.type!==Function&&!i.skipFactory&&ce(u)){const{propsDefaults:l}=s;if(n in l)r=l[n];else{const c=Gn(s);r=l[n]=u.call(null,t),c()}}else r=u;s.ce&&s.ce._setProp(n,r)}i[0]&&(o&&!a?r=!1:i[1]&&(r===""||r===_t(n))&&(r=!0))}return r}const Gg=new WeakMap;function xd(e,t,n=!1){const r=n?Gg:t.propsCache,s=r.get(e);if(s)return s;const o=e.props,i={},a=[];let u=!1;if(!ce(e)){const c=f=>{u=!0;const[d,m]=xd(f,t,!0);xe(i,d),m&&a.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!u)return ke(e)&&r.set(e,yr),yr;if(ie(o))for(let c=0;c<o.length;c++){const f=ot(o[c]);cl(f)&&(i[f]=ve)}else if(o)for(const c in o){const f=ot(c);if(cl(f)){const d=o[c],m=i[f]=ie(d)||ce(d)?{type:d}:xe({},d),g=m.type;let C=!1,w=!0;if(ie(g))for(let R=0;R<g.length;++R){const E=g[R],h=ce(E)&&E.name;if(h==="Boolean"){C=!0;break}else h==="String"&&(w=!1)}else C=ce(g)&&g.name==="Boolean";m[0]=C,m[1]=w,(C||Ce(m,"default"))&&a.push(f)}}const l=[i,a];return ke(e)&&r.set(e,l),l}function cl(e){return e[0]!=="$"&&!Er(e)}const Od=e=>e[0]==="_"||e==="$stable",Eu=e=>ie(e)?e.map(gt):[gt(e)],Yg=(e,t,n)=>{if(t._n)return t;const r=ad((...s)=>Eu(t(...s)),n);return r._c=!1,r},Rd=(e,t,n)=>{const r=e._ctx;for(const s in e){if(Od(s))continue;const o=e[s];if(ce(o))t[s]=Yg(s,o,r);else if(o!=null){const i=Eu(o);t[s]=()=>i}}},Pd=(e,t)=>{const n=Eu(t);e.slots.default=()=>n},Fd=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},Qg=(e,t,n)=>{const r=e.slots=wd();if(e.vnode.shapeFlag&32){const s=t._;s?(Fd(r,t,n),n&&Rf(r,"_",s,!0)):Rd(t,r)}else t&&Pd(e,t)},Jg=(e,t,n)=>{const{vnode:r,slots:s}=e;let o=!0,i=ve;if(r.shapeFlag&32){const a=t._;a?n&&a===1?o=!1:Fd(s,t,n):(o=!t.$stable,Rd(t,s)),i=t}else t&&(Pd(e,t),i={default:1});if(o)for(const a in s)!Od(a)&&i[a]==null&&delete s[a]},qe=Ud;function Zg(e){return Dd(e)}function e_(e){return Dd(e,Ag)}function Dd(e,t){const n=Qo();n.__VUE__=!0;const{insert:r,remove:s,patchProp:o,createElement:i,createText:a,createComment:u,setText:l,setElementText:c,parentNode:f,nextSibling:d,setScopeId:m=bt,insertStaticContent:g}=e,C=(A,k,N,K=null,j=null,G=null,ne=void 0,_=null,y=!!k.dynamicChildren)=>{if(A===k)return;A&&!Pt(A,k)&&(K=B(A),z(A,j,G,!0),A=null),k.patchFlag===-2&&(y=!1,k.dynamicChildren=null);const{type:P,ref:X,shapeFlag:J}=k;switch(P){case Tn:w(A,k,N,K);break;case je:R(A,k,N,K);break;case wr:A==null&&E(k,N,K,ne);break;case He:T(A,k,N,K,j,G,ne,_,y);break;default:J&1?b(A,k,N,K,j,G,ne,_,y):J&6?H(A,k,N,K,j,G,ne,_,y):(J&64||J&128)&&P.process(A,k,N,K,j,G,ne,_,y,re)}X!=null&&j&&hs(X,A&&A.ref,G,k||A,!k)},w=(A,k,N,K)=>{if(A==null)r(k.el=a(k.children),N,K);else{const j=k.el=A.el;k.children!==A.children&&l(j,k.children)}},R=(A,k,N,K)=>{A==null?r(k.el=u(k.children||""),N,K):k.el=A.el},E=(A,k,N,K)=>{[A.el,A.anchor]=g(A.children,k,N,K,A.el,A.anchor)},h=({el:A,anchor:k},N,K)=>{let j;for(;A&&A!==k;)j=d(A),r(A,N,K),A=j;r(k,N,K)},p=({el:A,anchor:k})=>{let N;for(;A&&A!==k;)N=d(A),s(A),A=N;s(k)},b=(A,k,N,K,j,G,ne,_,y)=>{k.type==="svg"?ne="svg":k.type==="math"&&(ne="mathml"),A==null?S(k,N,K,j,G,ne,_,y):v(A,k,j,G,ne,_,y)},S=(A,k,N,K,j,G,ne,_)=>{let y,P;const{props:X,shapeFlag:J,transition:Y,dirs:D}=A;if(y=A.el=i(A.type,G,X&&X.is,X),J&8?c(y,A.children):J&16&&F(A.children,y,null,K,j,Pi(A,G),ne,_),D&&Kt(A,null,K,"created"),x(y,A,A.scopeId,ne,K),X){for(const ue in X)ue!=="value"&&!Er(ue)&&o(y,ue,null,X[ue],G,K);"value"in X&&o(y,"value",null,X.value,G),(P=X.onVnodeBeforeMount)&&mt(P,K,A)}D&&Kt(A,null,K,"beforeMount");const U=Nd(j,Y);U&&Y.beforeEnter(y),r(y,k,N),((P=X&&X.onVnodeMounted)||U||D)&&qe(()=>{P&&mt(P,K,A),U&&Y.enter(y),D&&Kt(A,null,K,"mounted")},j)},x=(A,k,N,K,j)=>{if(N&&m(A,N),K)for(let G=0;G<K.length;G++)m(A,K[G]);if(j){let G=j.subTree;if(k===G||So(G.type)&&(G.ssContent===k||G.ssFallback===k)){const ne=j.vnode;x(A,ne,ne.scopeId,ne.slotScopeIds,j.parent)}}},F=(A,k,N,K,j,G,ne,_,y=0)=>{for(let P=y;P<A.length;P++){const X=A[P]=_?En(A[P]):gt(A[P]);C(null,X,k,N,K,j,G,ne,_)}},v=(A,k,N,K,j,G,ne)=>{const _=k.el=A.el;let{patchFlag:y,dynamicChildren:P,dirs:X}=k;y|=A.patchFlag&16;const J=A.props||ve,Y=k.props||ve;let D;if(N&&Ln(N,!1),(D=Y.onVnodeBeforeUpdate)&&mt(D,N,k,A),X&&Kt(k,A,N,"beforeUpdate"),N&&Ln(N,!0),(J.innerHTML&&Y.innerHTML==null||J.textContent&&Y.textContent==null)&&c(_,""),P?O(A.dynamicChildren,P,_,N,K,Pi(k,j),G):ne||V(A,k,_,null,N,K,Pi(k,j),G,!1),y>0){if(y&16)I(_,J,Y,N,j);else if(y&2&&J.class!==Y.class&&o(_,"class",null,Y.class,j),y&4&&o(_,"style",J.style,Y.style,j),y&8){const U=k.dynamicProps;for(let ue=0;ue<U.length;ue++){const le=U[ue],Be=J[le],$e=Y[le];($e!==Be||le==="value")&&o(_,le,Be,$e,j,N)}}y&1&&A.children!==k.children&&c(_,k.children)}else!ne&&P==null&&I(_,J,Y,N,j);((D=Y.onVnodeUpdated)||X)&&qe(()=>{D&&mt(D,N,k,A),X&&Kt(k,A,N,"updated")},K)},O=(A,k,N,K,j,G,ne)=>{for(let _=0;_<k.length;_++){const y=A[_],P=k[_],X=y.el&&(y.type===He||!Pt(y,P)||y.shapeFlag&70)?f(y.el):N;C(y,P,X,null,K,j,G,ne,!0)}},I=(A,k,N,K,j)=>{if(k!==N){if(k!==ve)for(const G in k)!Er(G)&&!(G in N)&&o(A,G,k[G],null,j,K);for(const G in N){if(Er(G))continue;const ne=N[G],_=k[G];ne!==_&&G!=="value"&&o(A,G,_,ne,j,K)}"value"in N&&o(A,"value",k.value,N.value,j)}},T=(A,k,N,K,j,G,ne,_,y)=>{const P=k.el=A?A.el:a(""),X=k.anchor=A?A.anchor:a("");let{patchFlag:J,dynamicChildren:Y,slotScopeIds:D}=k;D&&(_=_?_.concat(D):D),A==null?(r(P,N,K),r(X,N,K),F(k.children||[],N,X,j,G,ne,_,y)):J>0&&J&64&&Y&&A.dynamicChildren?(O(A.dynamicChildren,Y,N,j,G,ne,_),(k.key!=null||j&&k===j.subTree)&&Cu(A,k,!0)):V(A,k,N,X,j,G,ne,_,y)},H=(A,k,N,K,j,G,ne,_,y)=>{k.slotScopeIds=_,A==null?k.shapeFlag&512?j.ctx.activate(k,N,K,ne,y):L(k,N,K,j,G,ne,y):Z(A,k,y)},L=(A,k,N,K,j,G,ne)=>{const _=A.component=Kd(A,K,j);if(xs(A)&&(_.ctx.renderer=re),zd(_,!1,ne),_.asyncDep){if(j&&j.registerDep(_,W,ne),!A.el){const y=_.subTree=De(je);R(null,y,k,N)}}else W(_,A,k,N,j,G,ne)},Z=(A,k,N)=>{const K=k.component=A.component;if(c_(A,k,N))if(K.asyncDep&&!K.asyncResolved){Q(K,k,N);return}else K.next=k,K.update();else k.el=A.el,K.vnode=k},W=(A,k,N,K,j,G,ne)=>{const _=()=>{if(A.isMounted){let{next:J,bu:Y,u:D,parent:U,vnode:ue}=A;{const it=Ld(A);if(it){J&&(J.el=ue.el,Q(A,J,ne)),it.asyncDep.then(()=>{A.isUnmounted||_()});return}}let le=J,Be;Ln(A,!1),J?(J.el=ue.el,Q(A,J,ne)):J=ue,Y&&Cr(Y),(Be=J.props&&J.props.onVnodeBeforeUpdate)&&mt(Be,U,J,ue),Ln(A,!0);const $e=to(A),Ge=A.subTree;A.subTree=$e,C(Ge,$e,f(Ge.el),B(Ge),A,j,G),J.el=$e.el,le===null&&li(A,$e.el),D&&qe(D,j),(Be=J.props&&J.props.onVnodeUpdated)&&qe(()=>mt(Be,U,J,ue),j)}else{let J;const{el:Y,props:D}=k,{bm:U,m:ue,parent:le,root:Be,type:$e}=A,Ge=wn(k);if(Ln(A,!1),U&&Cr(U),!Ge&&(J=D&&D.onVnodeBeforeMount)&&mt(J,le,k),Ln(A,!0),Y&&Te){const it=()=>{A.subTree=to(A),Te(Y,A.subTree,A,j,null)};Ge&&$e.__asyncHydrate?$e.__asyncHydrate(Y,A,it):it()}else{Be.ce&&Be.ce._injectChildStyle($e);const it=A.subTree=to(A);C(null,it,N,K,A,j,G),k.el=it.el}if(ue&&qe(ue,j),!Ge&&(J=D&&D.onVnodeMounted)){const it=k;qe(()=>mt(J,le,it),j)}(k.shapeFlag&256||le&&wn(le.vnode)&&le.vnode.shapeFlag&256)&&A.a&&qe(A.a,j),A.isMounted=!0,k=N=K=null}};A.scope.on();const y=A.effect=new go(_);A.scope.off();const P=A.update=y.run.bind(y),X=A.job=y.runIfDirty.bind(y);X.i=A,X.id=A.uid,y.scheduler=()=>du(X),Ln(A,!0),P()},Q=(A,k,N)=>{k.component=A;const K=A.vnode.props;A.vnode=k,A.next=null,zg(A,k.props,K,N),Jg(A,k.children,N),Fn(),Zu(A),Dn()},V=(A,k,N,K,j,G,ne,_,y=!1)=>{const P=A&&A.children,X=A?A.shapeFlag:0,J=k.children,{patchFlag:Y,shapeFlag:D}=k;if(Y>0){if(Y&128){he(P,J,N,K,j,G,ne,_,y);return}else if(Y&256){se(P,J,N,K,j,G,ne,_,y);return}}D&8?(X&16&&oe(P,j,G),J!==P&&c(N,J)):X&16?D&16?he(P,J,N,K,j,G,ne,_,y):oe(P,j,G,!0):(X&8&&c(N,""),D&16&&F(J,N,K,j,G,ne,_,y))},se=(A,k,N,K,j,G,ne,_,y)=>{A=A||yr,k=k||yr;const P=A.length,X=k.length,J=Math.min(P,X);let Y;for(Y=0;Y<J;Y++){const D=k[Y]=y?En(k[Y]):gt(k[Y]);C(A[Y],D,N,null,j,G,ne,_,y)}P>X?oe(A,j,G,!0,!1,J):F(k,N,K,j,G,ne,_,y,J)},he=(A,k,N,K,j,G,ne,_,y)=>{let P=0;const X=k.length;let J=A.length-1,Y=X-1;for(;P<=J&&P<=Y;){const D=A[P],U=k[P]=y?En(k[P]):gt(k[P]);if(Pt(D,U))C(D,U,N,null,j,G,ne,_,y);else break;P++}for(;P<=J&&P<=Y;){const D=A[J],U=k[Y]=y?En(k[Y]):gt(k[Y]);if(Pt(D,U))C(D,U,N,null,j,G,ne,_,y);else break;J--,Y--}if(P>J){if(P<=Y){const D=Y+1,U=D<X?k[D].el:K;for(;P<=Y;)C(null,k[P]=y?En(k[P]):gt(k[P]),N,U,j,G,ne,_,y),P++}}else if(P>Y)for(;P<=J;)z(A[P],j,G,!0),P++;else{const D=P,U=P,ue=new Map;for(P=U;P<=Y;P++){const yt=k[P]=y?En(k[P]):gt(k[P]);yt.key!=null&&ue.set(yt.key,P)}let le,Be=0;const $e=Y-U+1;let Ge=!1,it=0;const rr=new Array($e);for(P=0;P<$e;P++)rr[P]=0;for(P=D;P<=J;P++){const yt=A[P];if(Be>=$e){z(yt,j,G,!0);continue}let Ht;if(yt.key!=null)Ht=ue.get(yt.key);else for(le=U;le<=Y;le++)if(rr[le-U]===0&&Pt(yt,k[le])){Ht=le;break}Ht===void 0?z(yt,j,G,!0):(rr[Ht-U]=P+1,Ht>=it?it=Ht:Ge=!0,C(yt,k[Ht],N,null,j,G,ne,_,y),Be++)}const Ku=Ge?t_(rr):yr;for(le=Ku.length-1,P=$e-1;P>=0;P--){const yt=U+P,Ht=k[yt],Xu=yt+1<X?k[yt+1].el:K;rr[P]===0?C(null,Ht,N,Xu,j,G,ne,_,y):Ge&&(le<0||P!==Ku[le]?de(Ht,N,Xu,2):le--)}}},de=(A,k,N,K,j=null)=>{const{el:G,type:ne,transition:_,children:y,shapeFlag:P}=A;if(P&6){de(A.component.subTree,k,N,K);return}if(P&128){A.suspense.move(k,N,K);return}if(P&64){ne.move(A,k,N,re);return}if(ne===He){r(G,k,N);for(let J=0;J<y.length;J++)de(y[J],k,N,K);r(A.anchor,k,N);return}if(ne===wr){h(A,k,N);return}if(K!==2&&P&1&&_)if(K===0)_.beforeEnter(G),r(G,k,N),qe(()=>_.enter(G),j);else{const{leave:J,delayLeave:Y,afterLeave:D}=_,U=()=>r(G,k,N),ue=()=>{J(G,()=>{U(),D&&D()})};Y?Y(G,U,ue):ue()}else r(G,k,N)},z=(A,k,N,K=!1,j=!1)=>{const{type:G,props:ne,ref:_,children:y,dynamicChildren:P,shapeFlag:X,patchFlag:J,dirs:Y,cacheIndex:D}=A;if(J===-2&&(j=!1),_!=null&&hs(_,null,N,A,!0),D!=null&&(k.renderCache[D]=void 0),X&256){k.ctx.deactivate(A);return}const U=X&1&&Y,ue=!wn(A);let le;if(ue&&(le=ne&&ne.onVnodeBeforeUnmount)&&mt(le,k,A),X&6)Ie(A.component,N,K);else{if(X&128){A.suspense.unmount(N,K);return}U&&Kt(A,null,k,"beforeUnmount"),X&64?A.type.remove(A,k,N,re,K):P&&!P.hasOnce&&(G!==He||J>0&&J&64)?oe(P,k,N,!1,!0):(G===He&&J&384||!j&&X&16)&&oe(y,k,N),K&&_e(A)}(ue&&(le=ne&&ne.onVnodeUnmounted)||U)&&qe(()=>{le&&mt(le,k,A),U&&Kt(A,null,k,"unmounted")},N)},_e=A=>{const{type:k,el:N,anchor:K,transition:j}=A;if(k===He){Pe(N,K);return}if(k===wr){p(A);return}const G=()=>{s(N),j&&!j.persisted&&j.afterLeave&&j.afterLeave()};if(A.shapeFlag&1&&j&&!j.persisted){const{leave:ne,delayLeave:_}=j,y=()=>ne(N,G);_?_(A.el,G,y):y()}else G()},Pe=(A,k)=>{let N;for(;A!==k;)N=d(A),s(A),A=N;s(k)},Ie=(A,k,N)=>{const{bum:K,scope:j,job:G,subTree:ne,um:_,m:y,a:P}=A;Ao(y),Ao(P),K&&Cr(K),j.stop(),G&&(G.flags|=8,z(ne,A,k,N)),_&&qe(_,k),qe(()=>{A.isUnmounted=!0},k),k&&k.pendingBranch&&!k.isUnmounted&&A.asyncDep&&!A.asyncResolved&&A.suspenseId===k.pendingId&&(k.deps--,k.deps===0&&k.resolve())},oe=(A,k,N,K=!1,j=!1,G=0)=>{for(let ne=G;ne<A.length;ne++)z(A[ne],k,N,K,j)},B=A=>{if(A.shapeFlag&6)return B(A.component.subTree);if(A.shapeFlag&128)return A.suspense.next();const k=d(A.anchor||A.el),N=k&&k[ld];return N?d(N):k};let te=!1;const ee=(A,k,N)=>{A==null?k._vnode&&z(k._vnode,null,null,!0):C(k._vnode||null,A,k,null,null,null,N),k._vnode=A,te||(te=!0,Zu(),Eo(),te=!1)},re={p:C,um:z,m:de,r:_e,mt:L,mc:F,pc:V,pbc:O,n:B,o:e};let ye,Te;return t&&([ye,Te]=t(re)),{render:ee,hydrate:ye,createApp:Wg(ee,ye)}}function Pi({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Ln({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Nd(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Cu(e,t,n=!1){const r=e.children,s=t.children;if(ie(r)&&ie(s))for(let o=0;o<r.length;o++){const i=r[o];let a=s[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=s[o]=En(s[o]),a.el=i.el),!n&&a.patchFlag!==-2&&Cu(i,a)),a.type===Tn&&(a.el=i.el)}}function t_(e){const t=e.slice(),n=[0];let r,s,o,i,a;const u=e.length;for(r=0;r<u;r++){const l=e[r];if(l!==0){if(s=n[n.length-1],e[s]<l){t[r]=s,n.push(r);continue}for(o=0,i=n.length-1;o<i;)a=o+i>>1,e[n[a]]<l?o=a+1:i=a;l<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Ld(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ld(t)}function Ao(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const n_=Symbol.for("v-scx"),r_=()=>vt(n_);function Ow(e,t){return Os(e,null,t)}function Rw(e,t){return Os(e,null,{flush:"post"})}function s_(e,t){return Os(e,null,{flush:"sync"})}function Ae(e,t,n){return Os(e,t,n)}function Os(e,t,n=ve){const{immediate:r,deep:s,flush:o,once:i}=n,a=xe({},n),u=t&&r||!t&&o!=="post";let l;if(Tr){if(o==="sync"){const m=r_();l=m.__watcherHandles||(m.__watcherHandles=[])}else if(!u){const m=()=>{};return m.stop=bt,m.resume=bt,m.pause=bt,m}}const c=We;a.call=(m,g,C)=>Mt(m,c,g,C);let f=!1;o==="post"?a.scheduler=m=>{qe(m,c&&c.suspense)}:o!=="sync"&&(f=!0,a.scheduler=(m,g)=>{g?m():du(m)}),a.augmentJob=m=>{t&&(m.flags|=4),f&&(m.flags|=2,c&&(m.id=c.uid,m.i=c))};const d=fg(e,t,a);return Tr&&(l?l.push(d):u&&d()),d}function o_(e,t,n){const r=this.proxy,s=Re(e)?e.includes(".")?Id(r,e):()=>r[e]:e.bind(r,r);let o;ce(t)?o=t:(o=t.handler,n=t);const i=Gn(this),a=Os(s,o.bind(r),n);return i(),a}function Id(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}function Pw(e,t,n=ve){const r=Ee(),s=ot(t),o=_t(t),i=Bd(e,s),a=sg((u,l)=>{let c,f=ve,d;return s_(()=>{const m=e[s];ct(c,m)&&(c=m,l())}),{get(){return u(),n.get?n.get(c):c},set(m){const g=n.set?n.set(m):m;if(!ct(g,c)&&!(f!==ve&&ct(m,f)))return;const C=r.vnode.props;C&&(t in C||s in C||o in C)&&(`onUpdate:${t}`in C||`onUpdate:${s}`in C||`onUpdate:${o}`in C)||(c=m,l()),r.emit(`update:${t}`,g),ct(m,g)&&ct(m,f)&&!ct(g,d)&&l(),f=m,d=g}}});return a[Symbol.iterator]=()=>{let u=0;return{next(){return u<2?{value:u++?i||ve:a,done:!1}:{done:!0}}}},a}const Bd=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ot(t)}Modifiers`]||e[`${_t(t)}Modifiers`];function i_(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||ve;let s=n;const o=t.startsWith("update:"),i=o&&Bd(r,t.slice(7));i&&(i.trim&&(s=n.map(c=>Re(c)?c.trim():c)),i.number&&(s=n.map(mo)));let a,u=r[a=Js(t)]||r[a=Js(ot(t))];!u&&o&&(u=r[a=Js(_t(t))]),u&&Mt(u,e,6,s);const l=r[a+"Once"];if(l){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Mt(l,e,6,s)}}function Md(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const o=e.emits;let i={},a=!1;if(!ce(e)){const u=l=>{const c=Md(l,t,!0);c&&(a=!0,xe(i,c))};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}return!o&&!a?(ke(e)&&r.set(e,null),null):(ie(o)?o.forEach(u=>i[u]=null):xe(i,o),ke(e)&&r.set(e,i),i)}function ui(e,t){return!e||!Ts(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ce(e,t[0].toLowerCase()+t.slice(1))||Ce(e,_t(t))||Ce(e,t))}function to(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[o],slots:i,attrs:a,emit:u,render:l,renderCache:c,props:f,data:d,setupState:m,ctx:g,inheritAttrs:C}=e,w=fs(e);let R,E;try{if(n.shapeFlag&4){const p=s||r,b=p;R=gt(l.call(b,p,c,f,m,d,g)),E=a}else{const p=t;R=gt(p.length>1?p(f,{attrs:a,slots:i,emit:u}):p(f,null)),E=t.props?a:u_(a)}}catch(p){rs.length=0,Lr(p,e,1),R=De(je)}let h=R;if(E&&C!==!1){const p=Object.keys(E),{shapeFlag:b}=h;p.length&&b&7&&(o&&p.some(nu)&&(E=l_(E,o)),h=an(h,E,!1,!0))}return n.dirs&&(h=an(h,null,!1,!0),h.dirs=h.dirs?h.dirs.concat(n.dirs):n.dirs),n.transition&&xn(h,n.transition),R=h,fs(w),R}function a_(e,t=!0){let n;for(let r=0;r<e.length;r++){const s=e[r];if(On(s)){if(s.type!==je||s.children==="v-if"){if(n)return;n=s}}else return}return n}const u_=e=>{let t;for(const n in e)(n==="class"||n==="style"||Ts(n))&&((t||(t={}))[n]=e[n]);return t},l_=(e,t)=>{const n={};for(const r in e)(!nu(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function c_(e,t,n){const{props:r,children:s,component:o}=e,{props:i,children:a,patchFlag:u}=t,l=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&u>=0){if(u&1024)return!0;if(u&16)return r?fl(r,i,l):!!i;if(u&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const d=c[f];if(i[d]!==r[d]&&!ui(l,d))return!0}}}else return(s||a)&&(!a||!a.$stable)?!0:r===i?!1:r?i?fl(r,i,l):!0:!!i;return!1}function fl(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const o=r[s];if(t[o]!==e[o]&&!ui(n,o))return!0}return!1}function li({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const So=e=>e.__isSuspense;let ha=0;const f_={name:"Suspense",__isSuspense:!0,process(e,t,n,r,s,o,i,a,u,l){if(e==null)d_(t,n,r,s,o,i,a,u,l);else{if(o&&o.deps>0&&!e.suspense.isInFallback){t.suspense=e.suspense,t.suspense.vnode=t,t.el=e.el;return}h_(e,t,n,r,s,i,a,u,l)}},hydrate:m_,normalize:p_},Fw=f_;function ps(e,t){const n=e.props&&e.props[t];ce(n)&&n()}function d_(e,t,n,r,s,o,i,a,u){const{p:l,o:{createElement:c}}=u,f=c("div"),d=e.suspense=$d(e,s,r,t,f,n,o,i,a,u);l(null,d.pendingBranch=e.ssContent,f,null,r,d,o,i),d.deps>0?(ps(e,"onPending"),ps(e,"onFallback"),l(null,e.ssFallback,t,n,r,null,o,i),Sr(d,e.ssFallback)):d.resolve(!1,!0)}function h_(e,t,n,r,s,o,i,a,{p:u,um:l,o:{createElement:c}}){const f=t.suspense=e.suspense;f.vnode=t,t.el=e.el;const d=t.ssContent,m=t.ssFallback,{activeBranch:g,pendingBranch:C,isInFallback:w,isHydrating:R}=f;if(C)f.pendingBranch=d,Pt(d,C)?(u(C,d,f.hiddenContainer,null,s,f,o,i,a),f.deps<=0?f.resolve():w&&(R||(u(g,m,n,r,s,null,o,i,a),Sr(f,m)))):(f.pendingId=ha++,R?(f.isHydrating=!1,f.activeBranch=C):l(C,s,f),f.deps=0,f.effects.length=0,f.hiddenContainer=c("div"),w?(u(null,d,f.hiddenContainer,null,s,f,o,i,a),f.deps<=0?f.resolve():(u(g,m,n,r,s,null,o,i,a),Sr(f,m))):g&&Pt(d,g)?(u(g,d,n,r,s,f,o,i,a),f.resolve(!0)):(u(null,d,f.hiddenContainer,null,s,f,o,i,a),f.deps<=0&&f.resolve()));else if(g&&Pt(d,g))u(g,d,n,r,s,f,o,i,a),Sr(f,d);else if(ps(t,"onPending"),f.pendingBranch=d,d.shapeFlag&512?f.pendingId=d.component.suspenseId:f.pendingId=ha++,u(null,d,f.hiddenContainer,null,s,f,o,i,a),f.deps<=0)f.resolve();else{const{timeout:E,pendingId:h}=f;E>0?setTimeout(()=>{f.pendingId===h&&f.fallback(m)},E):E===0&&f.fallback(m)}}function $d(e,t,n,r,s,o,i,a,u,l,c=!1){const{p:f,m:d,um:m,n:g,o:{parentNode:C,remove:w}}=l;let R;const E=g_(e);E&&t&&t.pendingBranch&&(R=t.pendingId,t.deps++);const h=e.props?po(e.props.timeout):void 0,p=o,b={vnode:e,parent:t,parentComponent:n,namespace:i,container:r,hiddenContainer:s,deps:0,pendingId:ha++,timeout:typeof h=="number"?h:-1,activeBranch:null,pendingBranch:null,isInFallback:!c,isHydrating:c,isUnmounted:!1,effects:[],resolve(S=!1,x=!1){const{vnode:F,activeBranch:v,pendingBranch:O,pendingId:I,effects:T,parentComponent:H,container:L}=b;let Z=!1;b.isHydrating?b.isHydrating=!1:S||(Z=v&&O.transition&&O.transition.mode==="out-in",Z&&(v.transition.afterLeave=()=>{I===b.pendingId&&(d(O,L,o===p?g(v):o,0),bo(T))}),v&&(C(v.el)===L&&(o=g(v)),m(v,H,b,!0)),Z||d(O,L,o,0)),Sr(b,O),b.pendingBranch=null,b.isInFallback=!1;let W=b.parent,Q=!1;for(;W;){if(W.pendingBranch){W.effects.push(...T),Q=!0;break}W=W.parent}!Q&&!Z&&bo(T),b.effects=[],E&&t&&t.pendingBranch&&R===t.pendingId&&(t.deps--,t.deps===0&&!x&&t.resolve()),ps(F,"onResolve")},fallback(S){if(!b.pendingBranch)return;const{vnode:x,activeBranch:F,parentComponent:v,container:O,namespace:I}=b;ps(x,"onFallback");const T=g(F),H=()=>{!b.isInFallback||(f(null,S,O,T,v,null,I,a,u),Sr(b,S))},L=S.transition&&S.transition.mode==="out-in";L&&(F.transition.afterLeave=H),b.isInFallback=!0,m(F,v,null,!0),L||H()},move(S,x,F){b.activeBranch&&d(b.activeBranch,S,x,F),b.container=S},next(){return b.activeBranch&&g(b.activeBranch)},registerDep(S,x,F){const v=!!b.pendingBranch;v&&b.deps++;const O=S.vnode.el;S.asyncDep.catch(I=>{Lr(I,S,0)}).then(I=>{if(S.isUnmounted||b.isUnmounted||b.pendingId!==S.suspenseId)return;S.asyncResolved=!0;const{vnode:T}=S;ga(S,I,!1),O&&(T.el=O);const H=!O&&S.subTree.el;x(S,T,C(O||S.subTree.el),O?null:g(S.subTree),b,i,F),H&&w(H),li(S,T.el),v&&--b.deps===0&&b.resolve()})},unmount(S,x){b.isUnmounted=!0,b.activeBranch&&m(b.activeBranch,n,S,x),b.pendingBranch&&m(b.pendingBranch,n,S,x)}};return b}function m_(e,t,n,r,s,o,i,a,u){const l=t.suspense=$d(t,r,n,e.parentNode,document.createElement("div"),null,s,o,i,a,!0),c=u(e,l.pendingBranch=t.ssContent,n,l,o,i);return l.deps===0&&l.resolve(!1,!0),c}function p_(e){const{shapeFlag:t,children:n}=e,r=t&32;e.ssContent=dl(r?n.default:n),e.ssFallback=r?dl(n.fallback):De(je)}function dl(e){let t;if(ce(e)){const n=zn&&e._c;n&&(e._d=!1,gs()),e=e(),n&&(e._d=!0,t=st,Vd())}return ie(e)&&(e=a_(e)),e=gt(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(n=>n!==e)),e}function Ud(e,t){t&&t.pendingBranch?ie(e)?t.effects.push(...e):t.effects.push(e):bo(e)}function Sr(e,t){e.activeBranch=t;const{vnode:n,parentComponent:r}=e;let s=t.el;for(;!s&&t.component;)t=t.component.subTree,s=t.el;n.el=s,r&&r.subTree===n&&(r.vnode.el=s,li(r,s))}function g_(e){const t=e.props&&e.props.suspensible;return t!=null&&t!==!1}const He=Symbol.for("v-fgt"),Tn=Symbol.for("v-txt"),je=Symbol.for("v-cmt"),wr=Symbol.for("v-stc"),rs=[];let st=null;function gs(e=!1){rs.push(st=e?null:[])}function Vd(){rs.pop(),st=rs[rs.length-1]||null}let zn=1;function hl(e,t=!1){zn+=e,e<0&&st&&t&&(st.hasOnce=!0)}function qd(e){return e.dynamicChildren=zn>0?st||yr:null,Vd(),zn>0&&st&&st.push(e),e}function Dw(e,t,n,r,s,o){return qd(jd(e,t,n,r,s,o,!0))}function wo(e,t,n,r,s){return qd(De(e,t,n,r,s,!0))}function On(e){return e?e.__v_isVNode===!0:!1}function Pt(e,t){return e.type===t.type&&e.key===t.key}function Nw(e){}const Hd=({key:e})=>e!=null?e:null,no=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Re(e)||Le(e)||ce(e)?{i:Ke,r:e,k:t,f:!!n}:e:null);function jd(e,t=null,n=null,r=0,s=null,o=e===He?0:1,i=!1,a=!1){const u={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Hd(t),ref:t&&no(t),scopeId:oi,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:Ke};return a?(Au(u,n),o&128&&e.normalize(u)):n&&(u.shapeFlag|=Re(n)?8:16),zn>0&&!i&&st&&(u.patchFlag>0||o&6)&&u.patchFlag!==32&&st.push(u),u}const De=__;function __(e,t=null,n=null,r=0,s=null,o=!1){if((!e||e===bd)&&(e=je),On(e)){const a=an(e,t,!0);return n&&Au(a,n),zn>0&&!o&&st&&(a.shapeFlag&6?st[st.indexOf(e)]=a:st.push(a)),a.patchFlag=-2,a}if(S_(e)&&(e=e.__vccOpts),t){t=v_(t);let{class:a,style:u}=t;a&&!Re(a)&&(t.class=Zo(a)),ke(u)&&(cu(u)&&!ie(u)&&(u=xe({},u)),t.style=Jo(u))}const i=Re(e)?1:So(e)?128:cd(e)?64:ke(e)?4:ce(e)?2:0;return jd(e,t,n,r,s,i,o,!0)}function v_(e){return e?cu(e)||Td(e)?xe({},e):e:null}function an(e,t,n=!1,r=!1){const{props:s,ref:o,patchFlag:i,children:a,transition:u}=e,l=t?y_(s||{},t):s,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:l,key:l&&Hd(l),ref:t&&t.ref?n&&o?ie(o)?o.concat(no(t)):[o,no(t)]:no(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==He?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:u,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&an(e.ssContent),ssFallback:e.ssFallback&&an(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return u&&r&&xn(c,u.clone(c)),c}function Wd(e=" ",t=0){return De(Tn,null,e,t)}function Lw(e,t){const n=De(wr,null,e);return n.staticCount=t,n}function Iw(e="",t=!1){return t?(gs(),wo(je,null,e)):De(je,null,e)}function gt(e){return e==null||typeof e=="boolean"?De(je):ie(e)?De(He,null,e.slice()):On(e)?En(e):De(Tn,null,String(e))}function En(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:an(e)}function Au(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(ie(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),Au(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!Td(t)?t._ctx=Ke:s===3&&Ke&&(Ke.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else ce(t)?(t={default:t,_ctx:Ke},n=32):(t=String(t),r&64?(n=16,t=[Wd(t)]):n=8);e.children=t,e.shapeFlag|=n}function y_(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=Zo([t.class,r.class]));else if(s==="style")t.style=Jo([t.style,r.style]);else if(Ts(s)){const o=t[s],i=r[s];i&&o!==i&&!(ie(o)&&o.includes(i))&&(t[s]=o?[].concat(o,i):i)}else s!==""&&(t[s]=r[s])}return t}function mt(e,t,n,r=null){Mt(e,t,7,[n,r])}const b_=Ad();let E_=0;function Kd(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||b_,o={uid:E_++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Nf(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:xd(r,s),emitsOptions:Md(r,s),emit:null,emitted:null,propsDefaults:ve,inheritAttrs:r.inheritAttrs,ctx:ve,data:ve,props:ve,attrs:ve,slots:ve,refs:ve,setupState:ve,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=i_.bind(null,o),e.ce&&e.ce(o),o}let We=null;const Ee=()=>We||Ke;let To,ma;{const e=Qo(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),o=>{s.length>1?s.forEach(i=>i(o)):s[0](o)}};To=t("__VUE_INSTANCE_SETTERS__",n=>We=n),ma=t("__VUE_SSR_SETTERS__",n=>Tr=n)}const Gn=e=>{const t=We;return To(e),e.scope.on(),()=>{e.scope.off(),To(t)}},pa=()=>{We&&We.scope.off(),To(null)};function Xd(e){return e.vnode.shapeFlag&4}let Tr=!1;function zd(e,t=!1,n=!1){t&&ma(t);const{props:r,children:s}=e.vnode,o=Xd(e);Xg(e,r,o,t),Qg(e,s,n);const i=o?C_(e,t):void 0;return t&&ma(!1),i}function C_(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,la);const{setup:r}=n;if(r){Fn();const s=e.setupContext=r.length>1?Yd(e):null,o=Gn(e),i=ks(r,e,0,[e.props,s]),a=su(i);if(Dn(),o(),(a||e.sp)&&!wn(e)&&mu(e),a){if(i.then(pa,pa),t)return i.then(u=>{ga(e,u,t)}).catch(u=>{Lr(u,e,0)});e.asyncDep=i}else ga(e,i,t)}else Gd(e,t)}function ga(e,t,n){ce(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ke(t)&&(e.setupState=ed(t)),Gd(e,n)}let ko,_a;function Bw(e){ko=e,_a=t=>{t.render._rc&&(t.withProxy=new Proxy(t.ctx,Mg))}}const Mw=()=>!ko;function Gd(e,t,n){const r=e.type;if(!e.render){if(!t&&ko&&!r.render){const s=r.template||bu(e).template;if(s){const{isCustomElement:o,compilerOptions:i}=e.appContext.config,{delimiters:a,compilerOptions:u}=r,l=xe(xe({isCustomElement:o,delimiters:a},i),u);r.render=ko(s,l)}}e.render=r.render||bt,_a&&_a(e)}{const s=Gn(e);Fn();try{$g(e)}finally{Dn(),s()}}}const A_={get(e,t){return tt(e,"get",""),e[t]}};function Yd(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,A_),slots:e.slots,emit:e.emit,expose:t}}function Rs(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ed(Zn(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in ns)return ns[n](e)},has(t,n){return n in t||n in ns}})):e.proxy}function va(e,t=!0){return ce(e)?e.displayName||e.name:e.name||t&&e.__name}function S_(e){return ce(e)&&"__vccOpts"in e}const $=(e,t)=>lg(e,t,Tr);function q(e,t,n){const r=arguments.length;return r===2?ke(t)&&!ie(t)?On(t)?De(e,null,[t]):De(e,t):De(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&On(n)&&(n=[n]),De(e,t,n))}function $w(){}function Uw(e,t,n,r){const s=n[r];if(s&&w_(s,e))return s;const o=t();return o.memo=e.slice(),o.cacheIndex=r,n[r]=o}function w_(e,t){const n=e.memo;if(n.length!=t.length)return!1;for(let r=0;r<n.length;r++)if(ct(n[r],t[r]))return!1;return zn>0&&st&&st.push(e),!0}const T_="3.5.13",Vw=bt,qw=mg,Hw=dr,jw=id,k_={createComponentInstance:Kd,setupComponent:zd,renderComponentRoot:to,setCurrentRenderingInstance:fs,isVNode:On,normalizeVNode:gt,getComponentPublicInstance:Rs,ensureValidVNode:yu,pushWarningContext:dg,popWarningContext:hg},Ww=k_,Kw=null,Xw=null,zw=null;/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ya;const ml=typeof window!="undefined"&&window.trustedTypes;if(ml)try{ya=ml.createPolicy("vue",{createHTML:e=>e})}catch{}const Qd=ya?e=>ya.createHTML(e):e=>e,x_="http://www.w3.org/2000/svg",O_="http://www.w3.org/1998/Math/MathML",Zt=typeof document!="undefined"?document:null,pl=Zt&&Zt.createElement("template"),R_={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?Zt.createElementNS(x_,e):t==="mathml"?Zt.createElementNS(O_,e):n?Zt.createElement(e,{is:n}):Zt.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>Zt.createTextNode(e),createComment:e=>Zt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Zt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,o){const i=n?n.previousSibling:t.lastChild;if(s&&(s===o||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===o||!(s=s.nextSibling)););else{pl.innerHTML=Qd(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const a=pl.content;if(r==="svg"||r==="mathml"){const u=a.firstChild;for(;u.firstChild;)a.appendChild(u.firstChild);a.removeChild(u)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},dn="transition",Vr="animation",kr=Symbol("_vtc"),Jd={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Zd=xe({},md,Jd),P_=e=>(e.displayName="Transition",e.props=Zd,e),xo=P_((e,{slots:t})=>q(bg,eh(e),t)),In=(e,t=[])=>{ie(e)?e.forEach(n=>n(...t)):e&&e(...t)},gl=e=>e?ie(e)?e.some(t=>t.length>1):e.length>1:!1;function eh(e){const t={};for(const T in e)T in Jd||(t[T]=e[T]);if(e.css===!1)return t;const{name:n="v",type:r,duration:s,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:u=o,appearActiveClass:l=i,appearToClass:c=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:m=`${n}-leave-to`}=e,g=F_(s),C=g&&g[0],w=g&&g[1],{onBeforeEnter:R,onEnter:E,onEnterCancelled:h,onLeave:p,onLeaveCancelled:b,onBeforeAppear:S=R,onAppear:x=E,onAppearCancelled:F=h}=t,v=(T,H,L,Z)=>{T._enterCancelled=Z,gn(T,H?c:a),gn(T,H?l:i),L&&L()},O=(T,H)=>{T._isLeaving=!1,gn(T,f),gn(T,m),gn(T,d),H&&H()},I=T=>(H,L)=>{const Z=T?x:E,W=()=>v(H,T,L);In(Z,[H,W]),_l(()=>{gn(H,T?u:o),jt(H,T?c:a),gl(Z)||vl(H,r,C,W)})};return xe(t,{onBeforeEnter(T){In(R,[T]),jt(T,o),jt(T,i)},onBeforeAppear(T){In(S,[T]),jt(T,u),jt(T,l)},onEnter:I(!1),onAppear:I(!0),onLeave(T,H){T._isLeaving=!0;const L=()=>O(T,H);jt(T,f),T._enterCancelled?(jt(T,d),ba()):(ba(),jt(T,d)),_l(()=>{!T._isLeaving||(gn(T,f),jt(T,m),gl(p)||vl(T,r,w,L))}),In(p,[T,L])},onEnterCancelled(T){v(T,!1,void 0,!0),In(h,[T])},onAppearCancelled(T){v(T,!0,void 0,!0),In(F,[T])},onLeaveCancelled(T){O(T),In(b,[T])}})}function F_(e){if(e==null)return null;if(ke(e))return[Fi(e.enter),Fi(e.leave)];{const t=Fi(e);return[t,t]}}function Fi(e){return po(e)}function jt(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[kr]||(e[kr]=new Set)).add(t)}function gn(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[kr];n&&(n.delete(t),n.size||(e[kr]=void 0))}function _l(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let D_=0;function vl(e,t,n,r){const s=e._endId=++D_,o=()=>{s===e._endId&&r()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:a,propCount:u}=th(e,t);if(!i)return r();const l=i+"end";let c=0;const f=()=>{e.removeEventListener(l,d),o()},d=m=>{m.target===e&&++c>=u&&f()};setTimeout(()=>{c<u&&f()},a+1),e.addEventListener(l,d)}function th(e,t){const n=window.getComputedStyle(e),r=g=>(n[g]||"").split(", "),s=r(`${dn}Delay`),o=r(`${dn}Duration`),i=yl(s,o),a=r(`${Vr}Delay`),u=r(`${Vr}Duration`),l=yl(a,u);let c=null,f=0,d=0;t===dn?i>0&&(c=dn,f=i,d=o.length):t===Vr?l>0&&(c=Vr,f=l,d=u.length):(f=Math.max(i,l),c=f>0?i>l?dn:Vr:null,d=c?c===dn?o.length:u.length:0);const m=c===dn&&/\b(transform|all)(,|$)/.test(r(`${dn}Property`).toString());return{type:c,timeout:f,propCount:d,hasTransform:m}}function yl(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>bl(n)+bl(e[r])))}function bl(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function ba(){return document.body.offsetHeight}function N_(e,t,n){const r=e[kr];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Oo=Symbol("_vod"),nh=Symbol("_vsh"),L_={beforeMount(e,{value:t},{transition:n}){e[Oo]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):qr(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),qr(e,!0),r.enter(e)):r.leave(e,()=>{qr(e,!1)}):qr(e,t))},beforeUnmount(e,{value:t}){qr(e,t)}};function qr(e,t){e.style.display=t?e[Oo]:"none",e[nh]=!t}function I_(){L_.getSSRProps=({value:e})=>{if(!e)return{style:{display:"none"}}}}const rh=Symbol("");function Gw(e){const t=Ee();if(!t)return;const n=t.ut=(s=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach(o=>Ro(o,s))},r=()=>{const s=e(t.proxy);t.ce?Ro(t.ce,s):Ea(t.subTree,s),n(s)};pu(()=>{bo(r)}),Vt(()=>{Ae(r,bt,{flush:"post"});const s=new MutationObserver(r);s.observe(t.subTree.el.parentNode,{childList:!0}),Ir(()=>s.disconnect())})}function Ea(e,t){if(e.shapeFlag&128){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push(()=>{Ea(n.activeBranch,t)})}for(;e.component;)e=e.component.subTree;if(e.shapeFlag&1&&e.el)Ro(e.el,t);else if(e.type===He)e.children.forEach(n=>Ea(n,t));else if(e.type===wr){let{el:n,anchor:r}=e;for(;n&&(Ro(n,t),n!==r);)n=n.nextSibling}}function Ro(e,t){if(e.nodeType===1){const n=e.style;let r="";for(const s in t)n.setProperty(`--${s}`,t[s]),r+=`--${s}: ${t[s]};`;n[rh]=r}}const B_=/(^|;)\s*display\s*:/;function M_(e,t,n){const r=e.style,s=Re(n);let o=!1;if(n&&!s){if(t)if(Re(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&ro(r,a,"")}else for(const i in t)n[i]==null&&ro(r,i,"");for(const i in n)i==="display"&&(o=!0),ro(r,i,n[i])}else if(s){if(t!==n){const i=r[rh];i&&(n+=";"+i),r.cssText=n,o=B_.test(n)}}else t&&e.removeAttribute("style");Oo in e&&(e[Oo]=o?r.display:"",e[nh]&&(r.display="none"))}const El=/\s*!important$/;function ro(e,t,n){if(ie(n))n.forEach(r=>ro(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=$_(e,t);El.test(n)?e.setProperty(_t(r),n.replace(El,""),"important"):e[r]=n}}const Cl=["Webkit","Moz","ms"],Di={};function $_(e,t){const n=Di[t];if(n)return n;let r=ot(t);if(r!=="filter"&&r in e)return Di[t]=r;r=Yo(r);for(let s=0;s<Cl.length;s++){const o=Cl[s]+r;if(o in e)return Di[t]=o}return t}const Al="http://www.w3.org/1999/xlink";function Sl(e,t,n,r,s,o=Fp(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Al,t.slice(6,t.length)):e.setAttributeNS(Al,t,n):n==null||o&&!Pf(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Bt(n)?String(n):n)}function wl(e,t,n,r,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Qd(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,u=n==null?e.type==="checkbox"?"on":"":String(n);(a!==u||!("_value"in e))&&(e.value=u),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=Pf(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(s||t)}function nn(e,t,n,r){e.addEventListener(t,n,r)}function U_(e,t,n,r){e.removeEventListener(t,n,r)}const Tl=Symbol("_vei");function V_(e,t,n,r,s=null){const o=e[Tl]||(e[Tl]={}),i=o[t];if(r&&i)i.value=r;else{const[a,u]=q_(t);if(r){const l=o[t]=W_(r,s);nn(e,a,l,u)}else i&&(U_(e,a,i,u),o[t]=void 0)}}const kl=/(?:Once|Passive|Capture)$/;function q_(e){let t;if(kl.test(e)){t={};let r;for(;r=e.match(kl);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):_t(e.slice(2)),t]}let Ni=0;const H_=Promise.resolve(),j_=()=>Ni||(H_.then(()=>Ni=0),Ni=Date.now());function W_(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;Mt(K_(r,n.value),t,5,[r])};return n.value=e,n.attached=j_(),n}function K_(e,t){if(ie(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const xl=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,X_=(e,t,n,r,s,o)=>{const i=s==="svg";t==="class"?N_(e,r,i):t==="style"?M_(e,n,r):Ts(t)?nu(t)||V_(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):z_(e,t,r,i))?(wl(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Sl(e,t,r,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Re(r))?wl(e,ot(t),r,o,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Sl(e,t,r,i))};function z_(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&xl(t)&&ce(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return xl(t)&&Re(n)?!1:t in e}const Ol={};/*! #__NO_SIDE_EFFECTS__ */function G_(e,t,n){const r=ln(e,t);zo(r)&&xe(r,t);class s extends Su{constructor(i){super(r,i,n)}}return s.def=r,s}/*! #__NO_SIDE_EFFECTS__ */const Yw=(e,t)=>G_(e,t,hv),Y_=typeof HTMLElement!="undefined"?HTMLElement:class{};class Su extends Y_{constructor(t,n={},r=Fo){super(),this._def=t,this._props=n,this._createApp=r,this._isVueCE=!0,this._instance=null,this._app=null,this._nonce=this._def.nonce,this._connected=!1,this._resolved=!1,this._numberProps=null,this._styleChildren=new WeakSet,this._ob=null,this.shadowRoot&&r!==Fo?this._root=this.shadowRoot:t.shadowRoot!==!1?(this.attachShadow({mode:"open"}),this._root=this.shadowRoot):this._root=this,this._def.__asyncLoader||this._resolveProps(this._def)}connectedCallback(){if(!this.isConnected)return;this.shadowRoot||this._parseSlots(),this._connected=!0;let t=this;for(;t=t&&(t.parentNode||t.host);)if(t instanceof Su){this._parent=t;break}this._instance||(this._resolved?(this._setParent(),this._update()):t&&t._pendingResolve?this._pendingResolve=t._pendingResolve.then(()=>{this._pendingResolve=void 0,this._resolveDef()}):this._resolveDef())}_setParent(t=this._parent){t&&(this._instance.parent=t._instance,this._instance.provides=t._instance.provides)}disconnectedCallback(){this._connected=!1,rt(()=>{this._connected||(this._ob&&(this._ob.disconnect(),this._ob=null),this._app&&this._app.unmount(),this._instance&&(this._instance.ce=void 0),this._app=this._instance=null)})}_resolveDef(){if(this._pendingResolve)return;for(let r=0;r<this.attributes.length;r++)this._setAttr(this.attributes[r].name);this._ob=new MutationObserver(r=>{for(const s of r)this._setAttr(s.attributeName)}),this._ob.observe(this,{attributes:!0});const t=(r,s=!1)=>{this._resolved=!0,this._pendingResolve=void 0;const{props:o,styles:i}=r;let a;if(o&&!ie(o))for(const u in o){const l=o[u];(l===Number||l&&l.type===Number)&&(u in this._props&&(this._props[u]=po(this._props[u])),(a||(a=Object.create(null)))[ot(u)]=!0)}this._numberProps=a,s&&this._resolveProps(r),this.shadowRoot&&this._applyStyles(i),this._mount(r)},n=this._def.__asyncLoader;n?this._pendingResolve=n().then(r=>t(this._def=r,!0)):t(this._def)}_mount(t){this._app=this._createApp(t),t.configureApp&&t.configureApp(this._app),this._app._ceVNode=this._createVNode(),this._app.mount(this._root);const n=this._instance&&this._instance.exposed;if(!!n)for(const r in n)Ce(this,r)||Object.defineProperty(this,r,{get:()=>sn(n[r])})}_resolveProps(t){const{props:n}=t,r=ie(n)?n:Object.keys(n||{});for(const s of Object.keys(this))s[0]!=="_"&&r.includes(s)&&this._setProp(s,this[s]);for(const s of r.map(ot))Object.defineProperty(this,s,{get(){return this._getProp(s)},set(o){this._setProp(s,o,!0,!0)}})}_setAttr(t){if(t.startsWith("data-v-"))return;const n=this.hasAttribute(t);let r=n?this.getAttribute(t):Ol;const s=ot(t);n&&this._numberProps&&this._numberProps[s]&&(r=po(r)),this._setProp(s,r,!1,!0)}_getProp(t){return this._props[t]}_setProp(t,n,r=!0,s=!1){if(n!==this._props[t]&&(n===Ol?delete this._props[t]:(this._props[t]=n,t==="key"&&this._app&&(this._app._ceVNode.key=n)),s&&this._instance&&this._update(),r)){const o=this._ob;o&&o.disconnect(),n===!0?this.setAttribute(_t(t),""):typeof n=="string"||typeof n=="number"?this.setAttribute(_t(t),n+""):n||this.removeAttribute(_t(t)),o&&o.observe(this,{attributes:!0})}}_update(){dv(this._createVNode(),this._root)}_createVNode(){const t={};this.shadowRoot||(t.onVnodeMounted=t.onVnodeUpdated=this._renderSlots.bind(this));const n=De(this._def,xe(t,this._props));return this._instance||(n.ce=r=>{this._instance=r,r.ce=this,r.isCE=!0;const s=(o,i)=>{this.dispatchEvent(new CustomEvent(o,zo(i[0])?xe({detail:i},i[0]):{detail:i}))};r.emit=(o,...i)=>{s(o,i),_t(o)!==o&&s(_t(o),i)},this._setParent()}),n}_applyStyles(t,n){if(!t)return;if(n){if(n===this._def||this._styleChildren.has(n))return;this._styleChildren.add(n)}const r=this._nonce;for(let s=t.length-1;s>=0;s--){const o=document.createElement("style");r&&o.setAttribute("nonce",r),o.textContent=t[s],this.shadowRoot.prepend(o)}}_parseSlots(){const t=this._slots={};let n;for(;n=this.firstChild;){const r=n.nodeType===1&&n.getAttribute("slot")||"default";(t[r]||(t[r]=[])).push(n),this.removeChild(n)}}_renderSlots(){const t=(this._teleportTarget||this).querySelectorAll("slot"),n=this._instance.type.__scopeId;for(let r=0;r<t.length;r++){const s=t[r],o=s.getAttribute("name")||"default",i=this._slots[o],a=s.parentNode;if(i)for(const u of i){if(n&&u.nodeType===1){const l=n+"-s",c=document.createTreeWalker(u,1);u.setAttribute(l,"");let f;for(;f=c.nextNode();)f.setAttribute(l,"")}a.insertBefore(u,s)}else for(;s.firstChild;)a.insertBefore(s.firstChild,s);a.removeChild(s)}}_injectChildStyle(t){this._applyStyles(t.styles,t)}_removeChildStyle(t){}}function Q_(e){const t=Ee(),n=t&&t.ce;return n||null}function Qw(){const e=Q_();return e&&e.shadowRoot}function Jw(e="$style"){{const t=Ee();if(!t)return ve;const n=t.type.__cssModules;if(!n)return ve;const r=n[e];return r||ve}}const sh=new WeakMap,oh=new WeakMap,Po=Symbol("_moveCb"),Rl=Symbol("_enterCb"),J_=e=>(delete e.props.mode,e),Z_=J_({name:"TransitionGroup",props:xe({},Zd,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=Ee(),r=hd();let s,o;return gu(()=>{if(!s.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!sv(s[0].el,n.vnode.el,i))return;s.forEach(tv),s.forEach(nv);const a=s.filter(rv);ba(),a.forEach(u=>{const l=u.el,c=l.style;jt(l,i),c.transform=c.webkitTransform=c.transitionDuration="";const f=l[Po]=d=>{d&&d.target!==l||(!d||/transform$/.test(d.propertyName))&&(l.removeEventListener("transitionend",f),l[Po]=null,gn(l,i))};l.addEventListener("transitionend",f)})}),()=>{const i=me(e),a=eh(i);let u=i.tag||He;if(s=[],o)for(let l=0;l<o.length;l++){const c=o[l];c.el&&c.el instanceof Element&&(s.push(c),xn(c,ds(c,a,r,n)),sh.set(c,c.el.getBoundingClientRect()))}o=t.default?hu(t.default()):[];for(let l=0;l<o.length;l++){const c=o[l];c.key!=null&&xn(c,ds(c,a,r,n))}return De(u,null,o)}}}),ev=Z_;function tv(e){const t=e.el;t[Po]&&t[Po](),t[Rl]&&t[Rl]()}function nv(e){oh.set(e,e.el.getBoundingClientRect())}function rv(e){const t=sh.get(e),n=oh.get(e),r=t.left-n.left,s=t.top-n.top;if(r||s){const o=e.el.style;return o.transform=o.webkitTransform=`translate(${r}px,${s}px)`,o.transitionDuration="0s",e}}function sv(e,t,n){const r=e.cloneNode(),s=e[kr];s&&s.forEach(a=>{a.split(/\s+/).forEach(u=>u&&r.classList.remove(u))}),n.split(/\s+/).forEach(a=>a&&r.classList.add(a)),r.style.display="none";const o=t.nodeType===1?t:t.parentNode;o.appendChild(r);const{hasTransform:i}=th(r);return o.removeChild(r),i}const Rn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return ie(t)?n=>Cr(t,n):t};function ov(e){e.target.composing=!0}function Pl(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Tt=Symbol("_assign"),Ca={created(e,{modifiers:{lazy:t,trim:n,number:r}},s){e[Tt]=Rn(s);const o=r||s.props&&s.props.type==="number";nn(e,t?"change":"input",i=>{if(i.target.composing)return;let a=e.value;n&&(a=a.trim()),o&&(a=mo(a)),e[Tt](a)}),n&&nn(e,"change",()=>{e.value=e.value.trim()}),t||(nn(e,"compositionstart",ov),nn(e,"compositionend",Pl),nn(e,"change",Pl))},mounted(e,{value:t}){e.value=t==null?"":t},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:s,number:o}},i){if(e[Tt]=Rn(i),e.composing)return;const a=(o||e.type==="number")&&!/^0\d/.test(e.value)?mo(e.value):e.value,u=t==null?"":t;a!==u&&(document.activeElement===e&&e.type!=="range"&&(r&&t===n||s&&e.value.trim()===u)||(e.value=u))}},ih={deep:!0,created(e,t,n){e[Tt]=Rn(n),nn(e,"change",()=>{const r=e._modelValue,s=xr(e),o=e.checked,i=e[Tt];if(ie(r)){const a=ei(r,s),u=a!==-1;if(o&&!u)i(r.concat(s));else if(!o&&u){const l=[...r];l.splice(a,1),i(l)}}else if(Qn(r)){const a=new Set(r);o?a.add(s):a.delete(s),i(a)}else i(uh(e,o))})},mounted:Fl,beforeUpdate(e,t,n){e[Tt]=Rn(n),Fl(e,t,n)}};function Fl(e,{value:t,oldValue:n},r){e._modelValue=t;let s;if(ie(t))s=ei(t,r.props.value)>-1;else if(Qn(t))s=t.has(r.props.value);else{if(t===n)return;s=kn(t,uh(e,!0))}e.checked!==s&&(e.checked=s)}const ah={created(e,{value:t},n){e.checked=kn(t,n.props.value),e[Tt]=Rn(n),nn(e,"change",()=>{e[Tt](xr(e))})},beforeUpdate(e,{value:t,oldValue:n},r){e[Tt]=Rn(r),t!==n&&(e.checked=kn(t,r.props.value))}},iv={deep:!0,created(e,{value:t,modifiers:{number:n}},r){const s=Qn(t);nn(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?mo(xr(i)):xr(i));e[Tt](e.multiple?s?new Set(o):o:o[0]),e._assigning=!0,rt(()=>{e._assigning=!1})}),e[Tt]=Rn(r)},mounted(e,{value:t}){Dl(e,t)},beforeUpdate(e,t,n){e[Tt]=Rn(n)},updated(e,{value:t}){e._assigning||Dl(e,t)}};function Dl(e,t){const n=e.multiple,r=ie(t);if(!(n&&!r&&!Qn(t))){for(let s=0,o=e.options.length;s<o;s++){const i=e.options[s],a=xr(i);if(n)if(r){const u=typeof a;u==="string"||u==="number"?i.selected=t.some(l=>String(l)===String(a)):i.selected=ei(t,a)>-1}else i.selected=t.has(a);else if(kn(xr(i),t)){e.selectedIndex!==s&&(e.selectedIndex=s);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function xr(e){return"_value"in e?e._value:e.value}function uh(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const av={created(e,t,n){js(e,t,n,null,"created")},mounted(e,t,n){js(e,t,n,null,"mounted")},beforeUpdate(e,t,n,r){js(e,t,n,r,"beforeUpdate")},updated(e,t,n,r){js(e,t,n,r,"updated")}};function lh(e,t){switch(e){case"SELECT":return iv;case"TEXTAREA":return Ca;default:switch(t){case"checkbox":return ih;case"radio":return ah;default:return Ca}}}function js(e,t,n,r,s){const i=lh(e.tagName,n.props&&n.props.type)[s];i&&i(e,t,n,r)}function uv(){Ca.getSSRProps=({value:e})=>({value:e}),ah.getSSRProps=({value:e},t)=>{if(t.props&&kn(t.props.value,e))return{checked:!0}},ih.getSSRProps=({value:e},t)=>{if(ie(e)){if(t.props&&ei(e,t.props.value)>-1)return{checked:!0}}else if(Qn(e)){if(t.props&&e.has(t.props.value))return{checked:!0}}else if(e)return{checked:!0}},av.getSSRProps=(e,t)=>{if(typeof t.type!="string")return;const n=lh(t.type.toUpperCase(),t.props&&t.props.type);if(n.getSSRProps)return n.getSSRProps(e,t)}}const lv=["ctrl","shift","alt","meta"],cv={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>lv.some(n=>e[`${n}Key`]&&!t.includes(n))},Zw=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(s,...o)=>{for(let i=0;i<t.length;i++){const a=cv[t[i]];if(a&&a(s,t))return}return e(s,...o)})},fv={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},e1=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=s=>{if(!("key"in s))return;const o=_t(s.key);if(t.some(i=>i===o||fv[i]===o))return e(s)})},ch=xe({patchProp:X_},R_);let ss,Nl=!1;function fh(){return ss||(ss=Zg(ch))}function dh(){return ss=Nl?ss:e_(ch),Nl=!0,ss}const dv=(...e)=>{fh().render(...e)},t1=(...e)=>{dh().hydrate(...e)},Fo=(...e)=>{const t=fh().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=mh(r);if(!s)return;const o=t._component;!ce(o)&&!o.render&&!o.template&&(o.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const i=n(s,!1,hh(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t},hv=(...e)=>{const t=dh().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=mh(r);if(s)return n(s,!0,hh(s))},t};function hh(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function mh(e){return Re(e)?document.querySelector(e):e}let Ll=!1;const n1=()=>{Ll||(Ll=!0,uv(),I_())};function er(e,t,n,r){return Object.defineProperty(e,t,{get:n,set:r,enumerable:!0}),e}function r1(e,t){for(const n in t)er(e,n,t[n]);return e}const Pn=ge(!1);let Aa;function mv(e,t){const n=/(edg|edge|edga|edgios)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(vivaldi)[\/]([\w.]+)/.exec(e)||/(chrome|crios)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+).*(version)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(firefox|fxios)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[\/]([\w.]+)/.exec(e)||[];return{browser:n[5]||n[3]||n[1]||"",version:n[4]||n[2]||"0",platform:t[0]||""}}function pv(e){return/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[]}const ph="ontouchstart"in window||window.navigator.maxTouchPoints>0;function gv(e){const t=e.toLowerCase(),n=pv(t),r=mv(t,n),s={};r.browser&&(s[r.browser]=!0,s.version=r.version,s.versionNumber=parseInt(r.version,10)),r.platform&&(s[r.platform]=!0);const o=s.android||s.ios||s.bb||s.blackberry||s.ipad||s.iphone||s.ipod||s.kindle||s.playbook||s.silk||s["windows phone"];if(o===!0||t.indexOf("mobile")!==-1?s.mobile=!0:s.desktop=!0,s["windows phone"]&&(s.winphone=!0,delete s["windows phone"]),s.edga||s.edgios||s.edg?(s.edge=!0,r.browser="edge"):s.crios?(s.chrome=!0,r.browser="chrome"):s.fxios&&(s.firefox=!0,r.browser="firefox"),(s.ipod||s.ipad||s.iphone)&&(s.ios=!0),s.vivaldi&&(r.browser="vivaldi",s.vivaldi=!0),(s.chrome||s.opr||s.safari||s.vivaldi||s.mobile===!0&&s.ios!==!0&&o!==!0)&&(s.webkit=!0),s.opr&&(r.browser="opera",s.opera=!0),s.safari&&(s.blackberry||s.bb?(r.browser="blackberry",s.blackberry=!0):s.playbook?(r.browser="playbook",s.playbook=!0):s.android?(r.browser="android",s.android=!0):s.kindle?(r.browser="kindle",s.kindle=!0):s.silk&&(r.browser="silk",s.silk=!0)),s.name=r.browser,s.platform=r.platform,t.indexOf("electron")!==-1)s.electron=!0;else if(document.location.href.indexOf("-extension://")!==-1)s.bex=!0;else{if(window.Capacitor!==void 0?(s.capacitor=!0,s.nativeMobile=!0,s.nativeMobileWrapper="capacitor"):(window._cordovaNative!==void 0||window.cordova!==void 0)&&(s.cordova=!0,s.nativeMobile=!0,s.nativeMobileWrapper="cordova"),Pn.value===!0&&(Aa={is:{...s}}),ph===!0&&s.mac===!0&&(s.desktop===!0&&s.safari===!0||s.nativeMobile===!0&&s.android!==!0&&s.ios!==!0&&s.ipad!==!0)){delete s.mac,delete s.desktop;const i=Math.min(window.innerHeight,window.innerWidth)>414?"ipad":"iphone";Object.assign(s,{mobile:!0,ios:!0,platform:i,[i]:!0})}s.mobile!==!0&&window.navigator.userAgentData&&window.navigator.userAgentData.mobile&&(delete s.desktop,s.mobile=!0)}return s}const Il=navigator.userAgent||navigator.vendor||window.opera,_v={has:{touch:!1,webStorage:!1},within:{iframe:!1}},Xe={userAgent:Il,is:gv(Il),has:{touch:ph},within:{iframe:window.self!==window.top}},Sa={install(e){const{$q:t}=e;Pn.value===!0?(e.onSSRHydrated.push(()=>{Object.assign(t.platform,Xe),Pn.value=!1}),t.platform=Jn(this)):t.platform=this}};{let e;er(Xe.has,"webStorage",()=>{if(e!==void 0)return e;try{if(window.localStorage)return e=!0,!0}catch{}return e=!1,!1}),Object.assign(Sa,Xe),Pn.value===!0&&(Object.assign(Sa,Aa,_v),Aa=null)}function Ze(e){return Zn(ln(e))}function vv(e){return Zn(e)}const ci=(e,t)=>{const n=Jn(e);for(const r in e)er(t,r,()=>n[r],s=>{n[r]=s});return t},pt={hasPassive:!1,passiveCapture:!0,notPassiveCapture:!0};try{const e=Object.defineProperty({},"passive",{get(){Object.assign(pt,{hasPassive:!0,passive:{passive:!0},notPassive:{passive:!1},passiveCapture:{passive:!0,capture:!0},notPassiveCapture:{passive:!1,capture:!0}})}});window.addEventListener("qtest",null,e),window.removeEventListener("qtest",null,e)}catch{}function _s(){}function s1(e){return e.button===0}function yv(e){return e.touches&&e.touches[0]?e=e.touches[0]:e.changedTouches&&e.changedTouches[0]?e=e.changedTouches[0]:e.targetTouches&&e.targetTouches[0]&&(e=e.targetTouches[0]),{top:e.clientY,left:e.clientX}}function bv(e){if(e.path)return e.path;if(e.composedPath)return e.composedPath();const t=[];let n=e.target;for(;n;){if(t.push(n),n.tagName==="HTML")return t.push(document),t.push(window),t;n=n.parentElement}}function Do(e){e.stopPropagation()}function Sn(e){e.cancelable!==!1&&e.preventDefault()}function St(e){e.cancelable!==!1&&e.preventDefault(),e.stopPropagation()}function o1(e,t){if(e===void 0||t===!0&&e.__dragPrevented===!0)return;const n=t===!0?r=>{r.__dragPrevented=!0,r.addEventListener("dragstart",Sn,pt.notPassiveCapture)}:r=>{delete r.__dragPrevented,r.removeEventListener("dragstart",Sn,pt.notPassiveCapture)};e.querySelectorAll("a, img").forEach(n)}function Ev(e,t,n){const r=`__q_${t}_evt`;e[r]=e[r]!==void 0?e[r].concat(n):n,n.forEach(s=>{s[0].addEventListener(s[1],e[s[2]],pt[s[3]])})}function Cv(e,t){const n=`__q_${t}_evt`;e[n]!==void 0&&(e[n].forEach(r=>{r[0].removeEventListener(r[1],e[r[2]],pt[r[3]])}),e[n]=void 0)}function gh(e,t=250,n){let r=null;function s(){const o=arguments,i=()=>{r=null,n!==!0&&e.apply(this,o)};r!==null?clearTimeout(r):n===!0&&e.apply(this,o),r=setTimeout(i,t)}return s.cancel=()=>{r!==null&&clearTimeout(r)},s}const Li=["sm","md","lg","xl"],{passive:Bl}=pt;var Av=ci({width:0,height:0,name:"xs",sizes:{sm:600,md:1024,lg:1440,xl:1920},lt:{sm:!0,md:!0,lg:!0,xl:!0},gt:{xs:!1,sm:!1,md:!1,lg:!1},xs:!0,sm:!1,md:!1,lg:!1,xl:!1},{setSizes:_s,setDebounce:_s,install({$q:e,onSSRHydrated:t}){if(e.screen=this,this.__installed===!0){e.config.screen!==void 0&&(e.config.screen.bodyClasses===!1?document.body.classList.remove(`screen--${this.name}`):this.__update(!0));return}const{visualViewport:n}=window,r=n||window,s=document.scrollingElement||document.documentElement,o=n===void 0||Xe.is.mobile===!0?()=>[Math.max(window.innerWidth,s.clientWidth),Math.max(window.innerHeight,s.clientHeight)]:()=>[n.width*n.scale+window.innerWidth-s.clientWidth,n.height*n.scale+window.innerHeight-s.clientHeight],i=e.config.screen!==void 0&&e.config.screen.bodyClasses===!0;this.__update=f=>{const[d,m]=o();if(m!==this.height&&(this.height=m),d!==this.width)this.width=d;else if(f!==!0)return;let g=this.sizes;this.gt.xs=d>=g.sm,this.gt.sm=d>=g.md,this.gt.md=d>=g.lg,this.gt.lg=d>=g.xl,this.lt.sm=d<g.sm,this.lt.md=d<g.md,this.lt.lg=d<g.lg,this.lt.xl=d<g.xl,this.xs=this.lt.sm,this.sm=this.gt.xs===!0&&this.lt.md===!0,this.md=this.gt.sm===!0&&this.lt.lg===!0,this.lg=this.gt.md===!0&&this.lt.xl===!0,this.xl=this.gt.lg,g=this.xs===!0&&"xs"||this.sm===!0&&"sm"||this.md===!0&&"md"||this.lg===!0&&"lg"||"xl",g!==this.name&&(i===!0&&(document.body.classList.remove(`screen--${this.name}`),document.body.classList.add(`screen--${g}`)),this.name=g)};let a,u={},l=16;this.setSizes=f=>{Li.forEach(d=>{f[d]!==void 0&&(u[d]=f[d])})},this.setDebounce=f=>{l=f};const c=()=>{const f=getComputedStyle(document.body);f.getPropertyValue("--q-size-sm")&&Li.forEach(d=>{this.sizes[d]=parseInt(f.getPropertyValue(`--q-size-${d}`),10)}),this.setSizes=d=>{Li.forEach(m=>{d[m]&&(this.sizes[m]=d[m])}),this.__update(!0)},this.setDebounce=d=>{a!==void 0&&r.removeEventListener("resize",a,Bl),a=d>0?gh(this.__update,d):this.__update,r.addEventListener("resize",a,Bl)},this.setDebounce(l),Object.keys(u).length!==0?(this.setSizes(u),u=void 0):this.__update(),i===!0&&this.name==="xs"&&document.body.classList.add("screen--xs")};Pn.value===!0?t.push(c):c()}});const et=ci({isActive:!1,mode:!1},{__media:void 0,set(e){et.mode=e,e==="auto"?(et.__media===void 0&&(et.__media=window.matchMedia("(prefers-color-scheme: dark)"),et.__updateMedia=()=>{et.set("auto")},et.__media.addListener(et.__updateMedia)),e=et.__media.matches):et.__media!==void 0&&(et.__media.removeListener(et.__updateMedia),et.__media=void 0),et.isActive=e===!0,document.body.classList.remove(`body--${e===!0?"light":"dark"}`),document.body.classList.add(`body--${e===!0?"dark":"light"}`)},toggle(){et.set(et.isActive===!1)},install({$q:e,ssrContext:t}){const{dark:n}=e.config;e.dark=this,this.__installed!==!0&&this.set(n!==void 0?n:!1)}});function Sv(e,t,n=document.body){if(typeof e!="string")throw new TypeError("Expected a string as propName");if(typeof t!="string")throw new TypeError("Expected a string as value");if(!(n instanceof Element))throw new TypeError("Expected a DOM element");n.style.setProperty(`--q-${e}`,t)}let _h=!1;function wv(e){_h=e.isComposing===!0}function vh(e){return _h===!0||e!==Object(e)||e.isComposing===!0||e.qKeyEvent===!0}function vs(e,t){return vh(e)===!0?!1:[].concat(t).includes(e.keyCode)}function yh(e){if(e.ios===!0)return"ios";if(e.android===!0)return"android"}function Tv({is:e,has:t,within:n},r){const s=[e.desktop===!0?"desktop":"mobile",`${t.touch===!1?"no-":""}touch`];if(e.mobile===!0){const o=yh(e);o!==void 0&&s.push("platform-"+o)}if(e.nativeMobile===!0){const o=e.nativeMobileWrapper;s.push(o),s.push("native-mobile"),e.ios===!0&&(r[o]===void 0||r[o].iosStatusBarPadding!==!1)&&s.push("q-ios-padding")}else e.electron===!0?s.push("electron"):e.bex===!0&&s.push("bex");return n.iframe===!0&&s.push("within-iframe"),s}function kv(){const{is:e}=Xe,t=document.body.className,n=new Set(t.replace(/ {2}/g," ").split(" "));if(e.nativeMobile!==!0&&e.electron!==!0&&e.bex!==!0){if(e.desktop===!0)n.delete("mobile"),n.delete("platform-ios"),n.delete("platform-android"),n.add("desktop");else if(e.mobile===!0){n.delete("desktop"),n.add("mobile"),n.delete("platform-ios"),n.delete("platform-android");const s=yh(e);s!==void 0&&n.add(`platform-${s}`)}}Xe.has.touch===!0&&(n.delete("no-touch"),n.add("touch")),Xe.within.iframe===!0&&n.add("within-iframe");const r=Array.from(n).join(" ");t!==r&&(document.body.className=r)}function xv(e){for(const t in e)Sv(t,e[t])}var Ov={install(e){if(this.__installed!==!0){if(Pn.value===!0)kv();else{const{$q:t}=e;t.config.brand!==void 0&&xv(t.config.brand);const n=Tv(Xe,t.config);document.body.classList.add.apply(document.body.classList,n)}Xe.is.ios===!0&&document.body.addEventListener("touchstart",_s),window.addEventListener("keydown",wv,!0)}}};const bh=()=>!0;function Rv(e){return typeof e=="string"&&e!==""&&e!=="/"&&e!=="#/"}function Pv(e){return e.startsWith("#")===!0&&(e=e.substring(1)),e.startsWith("/")===!1&&(e="/"+e),e.endsWith("/")===!0&&(e=e.substring(0,e.length-1)),"#"+e}function Fv(e){if(e.backButtonExit===!1)return()=>!1;if(e.backButtonExit==="*")return bh;const t=["#/"];return Array.isArray(e.backButtonExit)===!0&&t.push(...e.backButtonExit.filter(Rv).map(Pv)),()=>t.includes(window.location.hash)}var wa={__history:[],add:_s,remove:_s,install({$q:e}){if(this.__installed===!0)return;const{cordova:t,capacitor:n}=Xe.is;if(t!==!0&&n!==!0)return;const r=e.config[t===!0?"cordova":"capacitor"];if(r!==void 0&&r.backButton===!1||n===!0&&(window.Capacitor===void 0||window.Capacitor.Plugins.App===void 0))return;this.add=i=>{i.condition===void 0&&(i.condition=bh),this.__history.push(i)},this.remove=i=>{const a=this.__history.indexOf(i);a>=0&&this.__history.splice(a,1)};const s=Fv(Object.assign({backButtonExit:!0},r)),o=()=>{if(this.__history.length){const i=this.__history[this.__history.length-1];i.condition()===!0&&(this.__history.pop(),i.handler())}else s()===!0?navigator.app.exitApp():window.history.back()};t===!0?document.addEventListener("deviceready",()=>{document.addEventListener("backbutton",o,!1)}):window.Capacitor.Plugins.App.addListener("backButton",o)}},Ta={isoName:"en-US",nativeName:"English (US)",label:{clear:"Clear",ok:"OK",cancel:"Cancel",close:"Close",set:"Set",select:"Select",reset:"Reset",remove:"Remove",update:"Update",create:"Create",search:"Search",filter:"Filter",refresh:"Refresh",expand:e=>e?`Expand "${e}"`:"Expand",collapse:e=>e?`Collapse "${e}"`:"Collapse"},date:{days:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),daysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),firstDayOfWeek:0,format24h:!1,pluralDay:"days"},table:{noData:"No data available",noResults:"No matching records found",loading:"Loading...",selectedRecords:e=>e===1?"1 record selected.":(e===0?"No":e)+" records selected.",recordsPerPage:"Records per page:",allRows:"All",pagination:(e,t,n)=>e+"-"+t+" of "+n,columns:"Columns"},editor:{url:"URL",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",unorderedList:"Unordered List",orderedList:"Ordered List",subscript:"Subscript",superscript:"Superscript",hyperlink:"Hyperlink",toggleFullscreen:"Toggle Fullscreen",quote:"Quote",left:"Left align",center:"Center align",right:"Right align",justify:"Justify align",print:"Print",outdent:"Decrease indentation",indent:"Increase indentation",removeFormat:"Remove formatting",formatting:"Formatting",fontSize:"Font Size",align:"Align",hr:"Insert Horizontal Rule",undo:"Undo",redo:"Redo",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",paragraph:"Paragraph",code:"Code",size1:"Very small",size2:"A bit small",size3:"Normal",size4:"Medium-large",size5:"Big",size6:"Very big",size7:"Maximum",defaultFont:"Default Font",viewSource:"View Source"},tree:{noNodes:"No nodes available",noResults:"No matching nodes found"}};function Ml(){const e=Array.isArray(navigator.languages)===!0&&navigator.languages.length!==0?navigator.languages[0]:navigator.language;if(typeof e=="string")return e.split(/[-_]/).map((t,n)=>n===0?t.toLowerCase():n>1||t.length<4?t.toUpperCase():t[0].toUpperCase()+t.slice(1).toLowerCase()).join("-")}const Cn=ci({__qLang:{}},{getLocale:Ml,set(e=Ta,t){const n={...e,rtl:e.rtl===!0,getLocale:Ml};{if(n.set=Cn.set,Cn.__langConfig===void 0||Cn.__langConfig.noHtmlAttrs!==!0){const r=document.documentElement;r.setAttribute("dir",n.rtl===!0?"rtl":"ltr"),r.setAttribute("lang",n.isoName)}Object.assign(Cn.__qLang,n)}},install({$q:e,lang:t,ssrContext:n}){e.lang=Cn.__qLang,Cn.__langConfig=e.config.lang,this.__installed===!0?t!==void 0&&this.set(t):(this.props=new Proxy(this.__qLang,{get(){return Reflect.get(...arguments)},ownKeys(r){return Reflect.ownKeys(r).filter(s=>s!=="set"&&s!=="getLocale")}}),this.set(t||Ta))}});var Dv={name:"material-icons",type:{positive:"check_circle",negative:"warning",info:"info",warning:"priority_high"},arrow:{up:"arrow_upward",right:"arrow_forward",down:"arrow_downward",left:"arrow_back",dropdown:"arrow_drop_down"},chevron:{left:"chevron_left",right:"chevron_right"},colorPicker:{spectrum:"gradient",tune:"tune",palette:"style"},pullToRefresh:{icon:"refresh"},carousel:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down",navigationIcon:"lens"},chip:{remove:"cancel",selected:"check"},datetime:{arrowLeft:"chevron_left",arrowRight:"chevron_right",now:"access_time",today:"today"},editor:{bold:"format_bold",italic:"format_italic",strikethrough:"strikethrough_s",underline:"format_underlined",unorderedList:"format_list_bulleted",orderedList:"format_list_numbered",subscript:"vertical_align_bottom",superscript:"vertical_align_top",hyperlink:"link",toggleFullscreen:"fullscreen",quote:"format_quote",left:"format_align_left",center:"format_align_center",right:"format_align_right",justify:"format_align_justify",print:"print",outdent:"format_indent_decrease",indent:"format_indent_increase",removeFormat:"format_clear",formatting:"text_format",fontSize:"format_size",align:"format_align_left",hr:"remove",undo:"undo",redo:"redo",heading:"format_size",code:"code",size:"format_size",font:"font_download",viewSource:"code"},expansionItem:{icon:"keyboard_arrow_down",denseIcon:"arrow_drop_down"},fab:{icon:"add",activeIcon:"close"},field:{clear:"cancel",error:"error"},pagination:{first:"first_page",prev:"keyboard_arrow_left",next:"keyboard_arrow_right",last:"last_page"},rating:{icon:"grade"},stepper:{done:"check",active:"edit",error:"warning"},tabs:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down"},table:{arrowUp:"arrow_upward",warning:"warning",firstPage:"first_page",prevPage:"chevron_left",nextPage:"chevron_right",lastPage:"last_page"},tree:{icon:"play_arrow"},uploader:{done:"done",clear:"clear",add:"add_box",upload:"cloud_upload",removeQueue:"clear_all",removeUploaded:"done_all"}};const No=ci({iconMapFn:null,__qIconSet:{}},{set(e,t){const n={...e};n.set=No.set,Object.assign(No.__qIconSet,n)},install({$q:e,iconSet:t,ssrContext:n}){e.config.iconMapFn!==void 0&&(this.iconMapFn=e.config.iconMapFn),e.iconSet=this.__qIconSet,er(e,"iconMapFn",()=>this.iconMapFn,r=>{this.iconMapFn=r}),this.__installed===!0?t!==void 0&&this.set(t):(this.props=new Proxy(this.__qIconSet,{get(){return Reflect.get(...arguments)},ownKeys(r){return Reflect.ownKeys(r).filter(s=>s!=="set")}}),this.set(t||Dv))}}),Nv="_q_",i1="_q_t_",a1="_q_l_",u1="_q_pc_",Lv="_q_fo_",l1="_q_tabs_";function c1(){}const Lo={};let Eh=!1;function Iv(){Eh=!0}function Ii(e,t){if(e===t)return!0;if(e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;let n,r;if(e.constructor===Array){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(Ii(e[r],t[r])!==!0)return!1;return!0}if(e.constructor===Map){if(e.size!==t.size)return!1;let o=e.entries();for(r=o.next();r.done!==!0;){if(t.has(r.value[0])!==!0)return!1;r=o.next()}for(o=e.entries(),r=o.next();r.done!==!0;){if(Ii(r.value[1],t.get(r.value[0]))!==!0)return!1;r=o.next()}return!0}if(e.constructor===Set){if(e.size!==t.size)return!1;const o=e.entries();for(r=o.next();r.done!==!0;){if(t.has(r.value[0])!==!0)return!1;r=o.next()}return!0}if(e.buffer!=null&&e.buffer.constructor===ArrayBuffer){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(e[r]!==t[r])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const s=Object.keys(e).filter(o=>e[o]!==void 0);if(n=s.length,n!==Object.keys(t).filter(o=>t[o]!==void 0).length)return!1;for(r=n;r--!==0;){const o=s[r];if(Ii(e[o],t[o])!==!0)return!1}return!0}return e!==e&&t!==t}function Xt(e){return e!==null&&typeof e=="object"&&Array.isArray(e)!==!0}function f1(e){return Object.prototype.toString.call(e)==="[object Date]"}function d1(e){return typeof e=="number"&&isFinite(e)}const $l=[Sa,Ov,et,Av,wa,Cn,No];function Ch(e,t){const n=Fo(e);n.config.globalProperties=t.config.globalProperties;const{reload:r,...s}=t._context;return Object.assign(n._context,s),n}function Ul(e,t){t.forEach(n=>{n.install(e),n.__installed=!0})}function Bv(e,t,n){e.config.globalProperties.$q=n.$q,e.provide(Nv,n.$q),Ul(n,$l),t.components!==void 0&&Object.values(t.components).forEach(r=>{Xt(r)===!0&&r.name!==void 0&&e.component(r.name,r)}),t.directives!==void 0&&Object.values(t.directives).forEach(r=>{Xt(r)===!0&&r.name!==void 0&&e.directive(r.name,r)}),t.plugins!==void 0&&Ul(n,Object.values(t.plugins).filter(r=>typeof r.install=="function"&&$l.includes(r)===!1)),Pn.value===!0&&(n.$q.onSSRHydrated=()=>{n.onSSRHydrated.forEach(r=>{r()}),n.$q.onSSRHydrated=()=>{}})}var Mv=function(e,t={}){const n={version:"2.16.9"};Eh===!1?(t.config!==void 0&&Object.assign(Lo,t.config),n.config={...Lo},Iv()):n.config=t.config||{},Bv(e,t,{parentApp:e,$q:n,lang:t.lang,iconSet:t.iconSet,onSSRHydrated:[]})},$v={name:"Quasar",version:"2.16.9",install:Mv,lang:Cn,iconSet:No};/*!
  * vue-router v4.4.3
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const hr=typeof document!="undefined";function Uv(e){return e.__esModule||e[Symbol.toStringTag]==="Module"}const we=Object.assign;function Bi(e,t){const n={};for(const r in t){const s=t[r];n[r]=$t(s)?s.map(e):e(s)}return n}const os=()=>{},$t=Array.isArray,Ah=/#/g,Vv=/&/g,qv=/\//g,Hv=/=/g,jv=/\?/g,Sh=/\+/g,Wv=/%5B/g,Kv=/%5D/g,wh=/%5E/g,Xv=/%60/g,Th=/%7B/g,zv=/%7C/g,kh=/%7D/g,Gv=/%20/g;function wu(e){return encodeURI(""+e).replace(zv,"|").replace(Wv,"[").replace(Kv,"]")}function Yv(e){return wu(e).replace(Th,"{").replace(kh,"}").replace(wh,"^")}function ka(e){return wu(e).replace(Sh,"%2B").replace(Gv,"+").replace(Ah,"%23").replace(Vv,"%26").replace(Xv,"`").replace(Th,"{").replace(kh,"}").replace(wh,"^")}function Qv(e){return ka(e).replace(Hv,"%3D")}function Jv(e){return wu(e).replace(Ah,"%23").replace(jv,"%3F")}function Zv(e){return e==null?"":Jv(e).replace(qv,"%2F")}function ys(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const ey=/\/$/,ty=e=>e.replace(ey,"");function Mi(e,t,n="/"){let r,s={},o="",i="";const a=t.indexOf("#");let u=t.indexOf("?");return a<u&&a>=0&&(u=-1),u>-1&&(r=t.slice(0,u),o=t.slice(u+1,a>-1?a:t.length),s=e(o)),a>-1&&(r=r||t.slice(0,a),i=t.slice(a,t.length)),r=oy(r!=null?r:t,n),{fullPath:r+(o&&"?")+o+i,path:r,query:s,hash:ys(i)}}function ny(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Vl(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function ry(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&Or(t.matched[r],n.matched[s])&&xh(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Or(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function xh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!sy(e[n],t[n]))return!1;return!0}function sy(e,t){return $t(e)?ql(e,t):$t(t)?ql(t,e):e===t}function ql(e,t){return $t(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function oy(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let o=n.length-1,i,a;for(i=0;i<r.length;i++)if(a=r[i],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(i).join("/")}const hn={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var bs;(function(e){e.pop="pop",e.push="push"})(bs||(bs={}));var is;(function(e){e.back="back",e.forward="forward",e.unknown=""})(is||(is={}));function iy(e){if(!e)if(hr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),ty(e)}const ay=/^[^#]+#/;function uy(e,t){return e.replace(ay,"#")+t}function ly(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const fi=()=>({left:window.scrollX,top:window.scrollY});function cy(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=ly(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Hl(e,t){return(history.state?history.state.position-t:-1)+e}const xa=new Map;function fy(e,t){xa.set(e,t)}function dy(e){const t=xa.get(e);return xa.delete(e),t}let hy=()=>location.protocol+"//"+location.host;function Oh(e,t){const{pathname:n,search:r,hash:s}=t,o=e.indexOf("#");if(o>-1){let a=s.includes(e.slice(o))?e.slice(o).length:1,u=s.slice(a);return u[0]!=="/"&&(u="/"+u),Vl(u,"")}return Vl(n,e)+r+s}function my(e,t,n,r){let s=[],o=[],i=null;const a=({state:d})=>{const m=Oh(e,location),g=n.value,C=t.value;let w=0;if(d){if(n.value=m,t.value=d,i&&i===g){i=null;return}w=C?d.position-C.position:0}else r(m);s.forEach(R=>{R(n.value,g,{delta:w,type:bs.pop,direction:w?w>0?is.forward:is.back:is.unknown})})};function u(){i=n.value}function l(d){s.push(d);const m=()=>{const g=s.indexOf(d);g>-1&&s.splice(g,1)};return o.push(m),m}function c(){const{history:d}=window;!d.state||d.replaceState(we({},d.state,{scroll:fi()}),"")}function f(){for(const d of o)d();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:u,listen:l,destroy:f}}function jl(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?fi():null}}function py(e){const{history:t,location:n}=window,r={value:Oh(e,n)},s={value:t.state};s.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(u,l,c){const f=e.indexOf("#"),d=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+u:hy()+e+u;try{t[c?"replaceState":"pushState"](l,"",d),s.value=l}catch(m){console.error(m),n[c?"replace":"assign"](d)}}function i(u,l){const c=we({},t.state,jl(s.value.back,u,s.value.forward,!0),l,{position:s.value.position});o(u,c,!0),r.value=u}function a(u,l){const c=we({},s.value,t.state,{forward:u,scroll:fi()});o(c.current,c,!0);const f=we({},jl(r.value,u,null),{position:c.position+1},l);o(u,f,!1),r.value=u}return{location:r,state:s,push:a,replace:i}}function gy(e){e=iy(e);const t=py(e),n=my(e,t.state,t.location,t.replace);function r(o,i=!0){i||n.pauseListeners(),history.go(o)}const s=we({location:"",base:e,go:r,createHref:uy.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function _y(e){return typeof e=="string"||e&&typeof e=="object"}function Rh(e){return typeof e=="string"||typeof e=="symbol"}const Ph=Symbol("");var Wl;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Wl||(Wl={}));function Rr(e,t){return we(new Error,{type:e,[Ph]:!0},t)}function Yt(e,t){return e instanceof Error&&Ph in e&&(t==null||!!(e.type&t))}const Kl="[^/]+?",vy={sensitive:!1,strict:!1,start:!0,end:!0},yy=/[.+*?^${}()[\]/\\]/g;function by(e,t){const n=we({},vy,t),r=[];let s=n.start?"^":"";const o=[];for(const l of e){const c=l.length?[]:[90];n.strict&&!l.length&&(s+="/");for(let f=0;f<l.length;f++){const d=l[f];let m=40+(n.sensitive?.25:0);if(d.type===0)f||(s+="/"),s+=d.value.replace(yy,"\\$&"),m+=40;else if(d.type===1){const{value:g,repeatable:C,optional:w,regexp:R}=d;o.push({name:g,repeatable:C,optional:w});const E=R||Kl;if(E!==Kl){m+=10;try{new RegExp(`(${E})`)}catch(p){throw new Error(`Invalid custom RegExp for param "${g}" (${E}): `+p.message)}}let h=C?`((?:${E})(?:/(?:${E}))*)`:`(${E})`;f||(h=w&&l.length<2?`(?:/${h})`:"/"+h),w&&(h+="?"),s+=h,m+=20,w&&(m+=-8),C&&(m+=-20),E===".*"&&(m+=-50)}c.push(m)}r.push(c)}if(n.strict&&n.end){const l=r.length-1;r[l][r[l].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&(s+="(?:/|$)");const i=new RegExp(s,n.sensitive?"":"i");function a(l){const c=l.match(i),f={};if(!c)return null;for(let d=1;d<c.length;d++){const m=c[d]||"",g=o[d-1];f[g.name]=m&&g.repeatable?m.split("/"):m}return f}function u(l){let c="",f=!1;for(const d of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const m of d)if(m.type===0)c+=m.value;else if(m.type===1){const{value:g,repeatable:C,optional:w}=m,R=g in l?l[g]:"";if($t(R)&&!C)throw new Error(`Provided param "${g}" is an array but it is not repeatable (* or + modifiers)`);const E=$t(R)?R.join("/"):R;if(!E)if(w)d.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${g}"`);c+=E}}return c||"/"}return{re:i,score:r,keys:o,parse:a,stringify:u}}function Ey(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Fh(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const o=Ey(r[n],s[n]);if(o)return o;n++}if(Math.abs(s.length-r.length)===1){if(Xl(r))return 1;if(Xl(s))return-1}return s.length-r.length}function Xl(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Cy={type:0,value:""},Ay=/[a-zA-Z0-9_]/;function Sy(e){if(!e)return[[]];if(e==="/")return[[Cy]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${l}": ${m}`)}let n=0,r=n;const s=[];let o;function i(){o&&s.push(o),o=[]}let a=0,u,l="",c="";function f(){!l||(n===0?o.push({type:0,value:l}):n===1||n===2||n===3?(o.length>1&&(u==="*"||u==="+")&&t(`A repeatable param (${l}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:l,regexp:c,repeatable:u==="*"||u==="+",optional:u==="*"||u==="?"})):t("Invalid state to consume buffer"),l="")}function d(){l+=u}for(;a<e.length;){if(u=e[a++],u==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:u==="/"?(l&&f(),i()):u===":"?(f(),n=1):d();break;case 4:d(),n=r;break;case 1:u==="("?n=2:Ay.test(u)?d():(f(),n=0,u!=="*"&&u!=="?"&&u!=="+"&&a--);break;case 2:u===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+u:n=3:c+=u;break;case 3:f(),n=0,u!=="*"&&u!=="?"&&u!=="+"&&a--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${l}"`),f(),i(),s}function wy(e,t,n){const r=by(Sy(e.path),n),s=we(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function Ty(e,t){const n=[],r=new Map;t=Yl({strict:!1,end:!0,sensitive:!1},t);function s(f){return r.get(f)}function o(f,d,m){const g=!m,C=ky(f);C.aliasOf=m&&m.record;const w=Yl(t,f),R=[C];if("alias"in f){const p=typeof f.alias=="string"?[f.alias]:f.alias;for(const b of p)R.push(we({},C,{components:m?m.record.components:C.components,path:b,aliasOf:m?m.record:C}))}let E,h;for(const p of R){const{path:b}=p;if(d&&b[0]!=="/"){const S=d.record.path,x=S[S.length-1]==="/"?"":"/";p.path=d.record.path+(b&&x+b)}if(E=wy(p,d,w),m?m.alias.push(E):(h=h||E,h!==E&&h.alias.push(E),g&&f.name&&!Gl(E)&&i(f.name)),Dh(E)&&u(E),C.children){const S=C.children;for(let x=0;x<S.length;x++)o(S[x],E,m&&m.children[x])}m=m||E}return h?()=>{i(h)}:os}function i(f){if(Rh(f)){const d=r.get(f);d&&(r.delete(f),n.splice(n.indexOf(d),1),d.children.forEach(i),d.alias.forEach(i))}else{const d=n.indexOf(f);d>-1&&(n.splice(d,1),f.record.name&&r.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function a(){return n}function u(f){const d=Ry(f,n);n.splice(d,0,f),f.record.name&&!Gl(f)&&r.set(f.record.name,f)}function l(f,d){let m,g={},C,w;if("name"in f&&f.name){if(m=r.get(f.name),!m)throw Rr(1,{location:f});w=m.record.name,g=we(zl(d.params,m.keys.filter(h=>!h.optional).concat(m.parent?m.parent.keys.filter(h=>h.optional):[]).map(h=>h.name)),f.params&&zl(f.params,m.keys.map(h=>h.name))),C=m.stringify(g)}else if(f.path!=null)C=f.path,m=n.find(h=>h.re.test(C)),m&&(g=m.parse(C),w=m.record.name);else{if(m=d.name?r.get(d.name):n.find(h=>h.re.test(d.path)),!m)throw Rr(1,{location:f,currentLocation:d});w=m.record.name,g=we({},d.params,f.params),C=m.stringify(g)}const R=[];let E=m;for(;E;)R.unshift(E.record),E=E.parent;return{name:w,path:C,params:g,matched:R,meta:Oy(R)}}e.forEach(f=>o(f));function c(){n.length=0,r.clear()}return{addRoute:o,resolve:l,removeRoute:i,clearRoutes:c,getRoutes:a,getRecordMatcher:s}}function zl(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function ky(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:xy(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function xy(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Gl(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Oy(e){return e.reduce((t,n)=>we(t,n.meta),{})}function Yl(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Ry(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;Fh(e,t[o])<0?r=o:n=o+1}const s=Py(e);return s&&(r=t.lastIndexOf(s,r-1)),r}function Py(e){let t=e;for(;t=t.parent;)if(Dh(t)&&Fh(e,t)===0)return t}function Dh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Fy(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const o=r[s].replace(Sh," "),i=o.indexOf("="),a=ys(i<0?o:o.slice(0,i)),u=i<0?null:ys(o.slice(i+1));if(a in t){let l=t[a];$t(l)||(l=t[a]=[l]),l.push(u)}else t[a]=u}return t}function Ql(e){let t="";for(let n in e){const r=e[n];if(n=Qv(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}($t(r)?r.map(o=>o&&ka(o)):[r&&ka(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Dy(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=$t(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const Ny=Symbol(""),Jl=Symbol(""),di=Symbol(""),Tu=Symbol(""),Oa=Symbol("");function Hr(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function An(e,t,n,r,s,o=i=>i()){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((a,u)=>{const l=d=>{d===!1?u(Rr(4,{from:n,to:t})):d instanceof Error?u(d):_y(d)?u(Rr(2,{from:t,to:d})):(i&&r.enterCallbacks[s]===i&&typeof d=="function"&&i.push(d),a())},c=o(()=>e.call(r&&r.instances[s],t,n,l));let f=Promise.resolve(c);e.length<3&&(f=f.then(l)),f.catch(d=>u(d))})}function $i(e,t,n,r,s=o=>o()){const o=[];for(const i of e)for(const a in i.components){let u=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(Ly(u)){const c=(u.__vccOpts||u)[t];c&&o.push(An(c,n,r,i,a,s))}else{let l=u();o.push(()=>l.then(c=>{if(!c)return Promise.reject(new Error(`Couldn't resolve component "${a}" at "${i.path}"`));const f=Uv(c)?c.default:c;i.components[a]=f;const m=(f.__vccOpts||f)[t];return m&&An(m,n,r,i,a,s)()}))}}return o}function Ly(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Zl(e){const t=vt(di),n=vt(Tu),r=$(()=>{const u=sn(e.to);return t.resolve(u)}),s=$(()=>{const{matched:u}=r.value,{length:l}=u,c=u[l-1],f=n.matched;if(!c||!f.length)return-1;const d=f.findIndex(Or.bind(null,c));if(d>-1)return d;const m=ec(u[l-2]);return l>1&&ec(c)===m&&f[f.length-1].path!==m?f.findIndex(Or.bind(null,u[l-2])):d}),o=$(()=>s.value>-1&&$y(n.params,r.value.params)),i=$(()=>s.value>-1&&s.value===n.matched.length-1&&xh(n.params,r.value.params));function a(u={}){return My(u)?t[sn(e.replace)?"replace":"push"](sn(e.to)).catch(os):Promise.resolve()}return{route:r,href:$(()=>r.value.href),isActive:o,isExactActive:i,navigate:a}}const Iy=ln({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Zl,setup(e,{slots:t}){const n=Jn(Zl(e)),{options:r}=vt(di),s=$(()=>({[tc(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[tc(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&t.default(n);return e.custom?o:q("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},o)}}}),By=Iy;function My(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function $y(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!$t(s)||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function ec(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const tc=(e,t,n)=>e!=null?e:t!=null?t:n,Uy=ln({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=vt(Oa),s=$(()=>e.route||r.value),o=vt(Jl,0),i=$(()=>{let l=sn(o);const{matched:c}=s.value;let f;for(;(f=c[l])&&!f.components;)l++;return l}),a=$(()=>s.value.matched[i.value]);eo(Jl,$(()=>i.value+1)),eo(Ny,a),eo(Oa,s);const u=ge();return Ae(()=>[u.value,a.value,e.name],([l,c,f],[d,m,g])=>{c&&(c.instances[f]=l,m&&m!==c&&l&&l===d&&(c.leaveGuards.size||(c.leaveGuards=m.leaveGuards),c.updateGuards.size||(c.updateGuards=m.updateGuards))),l&&c&&(!m||!Or(c,m)||!d)&&(c.enterCallbacks[f]||[]).forEach(C=>C(l))},{flush:"post"}),()=>{const l=s.value,c=e.name,f=a.value,d=f&&f.components[c];if(!d)return nc(n.default,{Component:d,route:l});const m=f.props[c],g=m?m===!0?l.params:typeof m=="function"?m(l):m:null,w=q(d,we({},g,t,{onVnodeUnmounted:R=>{R.component.isUnmounted&&(f.instances[c]=null)},ref:u}));return nc(n.default,{Component:w,route:l})||w}}});function nc(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Vy=Uy;function qy(e){const t=Ty(e.routes,e),n=e.parseQuery||Fy,r=e.stringifyQuery||Ql,s=e.history,o=Hr(),i=Hr(),a=Hr(),u=fu(hn);let l=hn;hr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=Bi.bind(null,B=>""+B),f=Bi.bind(null,Zv),d=Bi.bind(null,ys);function m(B,te){let ee,re;return Rh(B)?(ee=t.getRecordMatcher(B),re=te):re=B,t.addRoute(re,ee)}function g(B){const te=t.getRecordMatcher(B);te&&t.removeRoute(te)}function C(){return t.getRoutes().map(B=>B.record)}function w(B){return!!t.getRecordMatcher(B)}function R(B,te){if(te=we({},te||u.value),typeof B=="string"){const k=Mi(n,B,te.path),N=t.resolve({path:k.path},te),K=s.createHref(k.fullPath);return we(k,N,{params:d(N.params),hash:ys(k.hash),redirectedFrom:void 0,href:K})}let ee;if(B.path!=null)ee=we({},B,{path:Mi(n,B.path,te.path).path});else{const k=we({},B.params);for(const N in k)k[N]==null&&delete k[N];ee=we({},B,{params:f(k)}),te.params=f(te.params)}const re=t.resolve(ee,te),ye=B.hash||"";re.params=c(d(re.params));const Te=ny(r,we({},B,{hash:Yv(ye),path:re.path})),A=s.createHref(Te);return we({fullPath:Te,hash:ye,query:r===Ql?Dy(B.query):B.query||{}},re,{redirectedFrom:void 0,href:A})}function E(B){return typeof B=="string"?Mi(n,B,u.value.path):we({},B)}function h(B,te){if(l!==B)return Rr(8,{from:te,to:B})}function p(B){return x(B)}function b(B){return p(we(E(B),{replace:!0}))}function S(B){const te=B.matched[B.matched.length-1];if(te&&te.redirect){const{redirect:ee}=te;let re=typeof ee=="function"?ee(B):ee;return typeof re=="string"&&(re=re.includes("?")||re.includes("#")?re=E(re):{path:re},re.params={}),we({query:B.query,hash:B.hash,params:re.path!=null?{}:B.params},re)}}function x(B,te){const ee=l=R(B),re=u.value,ye=B.state,Te=B.force,A=B.replace===!0,k=S(ee);if(k)return x(we(E(k),{state:typeof k=="object"?we({},ye,k.state):ye,force:Te,replace:A}),te||ee);const N=ee;N.redirectedFrom=te;let K;return!Te&&ry(r,re,ee)&&(K=Rr(16,{to:N,from:re}),de(re,re,!0,!1)),(K?Promise.resolve(K):O(N,re)).catch(j=>Yt(j)?Yt(j,2)?j:he(j):V(j,N,re)).then(j=>{if(j){if(Yt(j,2))return x(we({replace:A},E(j.to),{state:typeof j.to=="object"?we({},ye,j.to.state):ye,force:Te}),te||N)}else j=T(N,re,!0,A,ye);return I(N,re,j),j})}function F(B,te){const ee=h(B,te);return ee?Promise.reject(ee):Promise.resolve()}function v(B){const te=Pe.values().next().value;return te&&typeof te.runWithContext=="function"?te.runWithContext(B):B()}function O(B,te){let ee;const[re,ye,Te]=Hy(B,te);ee=$i(re.reverse(),"beforeRouteLeave",B,te);for(const k of re)k.leaveGuards.forEach(N=>{ee.push(An(N,B,te))});const A=F.bind(null,B,te);return ee.push(A),oe(ee).then(()=>{ee=[];for(const k of o.list())ee.push(An(k,B,te));return ee.push(A),oe(ee)}).then(()=>{ee=$i(ye,"beforeRouteUpdate",B,te);for(const k of ye)k.updateGuards.forEach(N=>{ee.push(An(N,B,te))});return ee.push(A),oe(ee)}).then(()=>{ee=[];for(const k of Te)if(k.beforeEnter)if($t(k.beforeEnter))for(const N of k.beforeEnter)ee.push(An(N,B,te));else ee.push(An(k.beforeEnter,B,te));return ee.push(A),oe(ee)}).then(()=>(B.matched.forEach(k=>k.enterCallbacks={}),ee=$i(Te,"beforeRouteEnter",B,te,v),ee.push(A),oe(ee))).then(()=>{ee=[];for(const k of i.list())ee.push(An(k,B,te));return ee.push(A),oe(ee)}).catch(k=>Yt(k,8)?k:Promise.reject(k))}function I(B,te,ee){a.list().forEach(re=>v(()=>re(B,te,ee)))}function T(B,te,ee,re,ye){const Te=h(B,te);if(Te)return Te;const A=te===hn,k=hr?history.state:{};ee&&(re||A?s.replace(B.fullPath,we({scroll:A&&k&&k.scroll},ye)):s.push(B.fullPath,ye)),u.value=B,de(B,te,ee,A),he()}let H;function L(){H||(H=s.listen((B,te,ee)=>{if(!Ie.listening)return;const re=R(B),ye=S(re);if(ye){x(we(ye,{replace:!0}),re).catch(os);return}l=re;const Te=u.value;hr&&fy(Hl(Te.fullPath,ee.delta),fi()),O(re,Te).catch(A=>Yt(A,12)?A:Yt(A,2)?(x(A.to,re).then(k=>{Yt(k,20)&&!ee.delta&&ee.type===bs.pop&&s.go(-1,!1)}).catch(os),Promise.reject()):(ee.delta&&s.go(-ee.delta,!1),V(A,re,Te))).then(A=>{A=A||T(re,Te,!1),A&&(ee.delta&&!Yt(A,8)?s.go(-ee.delta,!1):ee.type===bs.pop&&Yt(A,20)&&s.go(-1,!1)),I(re,Te,A)}).catch(os)}))}let Z=Hr(),W=Hr(),Q;function V(B,te,ee){he(B);const re=W.list();return re.length?re.forEach(ye=>ye(B,te,ee)):console.error(B),Promise.reject(B)}function se(){return Q&&u.value!==hn?Promise.resolve():new Promise((B,te)=>{Z.add([B,te])})}function he(B){return Q||(Q=!B,L(),Z.list().forEach(([te,ee])=>B?ee(B):te()),Z.reset()),B}function de(B,te,ee,re){const{scrollBehavior:ye}=e;if(!hr||!ye)return Promise.resolve();const Te=!ee&&dy(Hl(B.fullPath,0))||(re||!ee)&&history.state&&history.state.scroll||null;return rt().then(()=>ye(B,te,Te)).then(A=>A&&cy(A)).catch(A=>V(A,B,te))}const z=B=>s.go(B);let _e;const Pe=new Set,Ie={currentRoute:u,listening:!0,addRoute:m,removeRoute:g,clearRoutes:t.clearRoutes,hasRoute:w,getRoutes:C,resolve:R,options:e,push:p,replace:b,go:z,back:()=>z(-1),forward:()=>z(1),beforeEach:o.add,beforeResolve:i.add,afterEach:a.add,onError:W.add,isReady:se,install(B){const te=this;B.component("RouterLink",By),B.component("RouterView",Vy),B.config.globalProperties.$router=te,Object.defineProperty(B.config.globalProperties,"$route",{enumerable:!0,get:()=>sn(u)}),hr&&!_e&&u.value===hn&&(_e=!0,p(s.location).catch(ye=>{}));const ee={};for(const ye in hn)Object.defineProperty(ee,ye,{get:()=>u.value[ye],enumerable:!0});B.provide(di,te),B.provide(Tu,Qf(ee)),B.provide(Oa,u);const re=B.unmount;Pe.add(B),B.unmount=function(){Pe.delete(B),Pe.size<1&&(l=hn,H&&H(),H=null,u.value=hn,_e=!1,Q=!1),re()}}};function oe(B){return B.reduce((te,ee)=>te.then(()=>v(ee)),Promise.resolve())}return Ie}function Hy(e,t){const n=[],r=[],s=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const a=t.matched[i];a&&(e.matched.find(l=>Or(l,a))?r.push(a):n.push(a));const u=e.matched[i];u&&(t.matched.find(l=>Or(l,u))||s.push(u))}return[n,r,s]}function jy(){return vt(di)}function h1(e){return vt(Tu)}const Ra={xs:18,sm:24,md:32,lg:38,xl:46},Ps={size:String};function Fs(e,t=Ra){return $(()=>e.size!==void 0?{fontSize:e.size in t?`${t[e.size]}px`:e.size}:null)}function Ft(e,t){return e!==void 0&&e()||t}function m1(e,t){if(e!==void 0){const n=e();if(n!=null)return n.slice()}return t}function Un(e,t){return e!==void 0?t.concat(e()):t}function Wy(e,t){return e===void 0?t:t!==void 0?t.concat(e()):e()}function p1(e,t,n,r,s,o){t.key=r+s;const i=q(e,t,n);return s===!0?ud(i,o()):i}const rc="0 0 24 24",sc=e=>e,Ui=e=>`ionicons ${e}`,Nh={"mdi-":e=>`mdi ${e}`,"icon-":sc,"bt-":e=>`bt ${e}`,"eva-":e=>`eva ${e}`,"ion-md":Ui,"ion-ios":Ui,"ion-logo":Ui,"iconfont ":sc,"ti-":e=>`themify-icon ${e}`,"bi-":e=>`bootstrap-icons ${e}`},Lh={o_:"-outlined",r_:"-round",s_:"-sharp"},Ih={sym_o_:"-outlined",sym_r_:"-rounded",sym_s_:"-sharp"},Ky=new RegExp("^("+Object.keys(Nh).join("|")+")"),Xy=new RegExp("^("+Object.keys(Lh).join("|")+")"),oc=new RegExp("^("+Object.keys(Ih).join("|")+")"),zy=/^[Mm]\s?[-+]?\.?\d/,Gy=/^img:/,Yy=/^svguse:/,Qy=/^ion-/,Jy=/^(fa-(sharp|solid|regular|light|brands|duotone|thin)|[lf]a[srlbdk]?) /;var un=Ze({name:"QIcon",props:{...Ps,tag:{type:String,default:"i"},name:String,color:String,left:Boolean,right:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=Ee(),r=Fs(e),s=$(()=>"q-icon"+(e.left===!0?" on-left":"")+(e.right===!0?" on-right":"")+(e.color!==void 0?` text-${e.color}`:"")),o=$(()=>{let i,a=e.name;if(a==="none"||!a)return{none:!0};if(n.iconMapFn!==null){const c=n.iconMapFn(a);if(c!==void 0)if(c.icon!==void 0){if(a=c.icon,a==="none"||!a)return{none:!0}}else return{cls:c.cls,content:c.content!==void 0?c.content:" "}}if(zy.test(a)===!0){const[c,f=rc]=a.split("|");return{svg:!0,viewBox:f,nodes:c.split("&&").map(d=>{const[m,g,C]=d.split("@@");return q("path",{style:g,d:m,transform:C})})}}if(Gy.test(a)===!0)return{img:!0,src:a.substring(4)};if(Yy.test(a)===!0){const[c,f=rc]=a.split("|");return{svguse:!0,src:c.substring(7),viewBox:f}}let u=" ";const l=a.match(Ky);if(l!==null)i=Nh[l[1]](a);else if(Jy.test(a)===!0)i=a;else if(Qy.test(a)===!0)i=`ionicons ion-${n.platform.is.ios===!0?"ios":"md"}${a.substring(3)}`;else if(oc.test(a)===!0){i="notranslate material-symbols";const c=a.match(oc);c!==null&&(a=a.substring(6),i+=Ih[c[1]]),u=a}else{i="notranslate material-icons";const c=a.match(Xy);c!==null&&(a=a.substring(2),i+=Lh[c[1]]),u=a}return{cls:i,content:u}});return()=>{const i={class:s.value,style:r.value,"aria-hidden":"true",role:"presentation"};return o.value.none===!0?q(e.tag,i,Ft(t.default)):o.value.img===!0?q(e.tag,i,Un(t.default,[q("img",{src:o.value.src})])):o.value.svg===!0?q(e.tag,i,Un(t.default,[q("svg",{viewBox:o.value.viewBox||"0 0 24 24"},o.value.nodes)])):o.value.svguse===!0?q(e.tag,i,Un(t.default,[q("svg",{viewBox:o.value.viewBox},[q("use",{"xlink:href":o.value.src})])])):(o.value.cls!==void 0&&(i.class+=" "+o.value.cls),q(e.tag,i,Un(t.default,[o.value.content])))}}}),Zy=Ze({name:"QAvatar",props:{...Ps,fontSize:String,color:String,textColor:String,icon:String,square:Boolean,rounded:Boolean},setup(e,{slots:t}){const n=Fs(e),r=$(()=>"q-avatar"+(e.color?` bg-${e.color}`:"")+(e.textColor?` text-${e.textColor} q-chip--colored`:"")+(e.square===!0?" q-avatar--square":e.rounded===!0?" rounded-borders":"")),s=$(()=>e.fontSize?{fontSize:e.fontSize}:null);return()=>{const o=e.icon!==void 0?[q(un,{name:e.icon})]:void 0;return q("div",{class:r.value,style:n.value},[q("div",{class:"q-avatar__content row flex-center overflow-hidden",style:s.value},Wy(t.default,o))])}}});const eb={size:{type:[String,Number],default:"1em"},color:String};function tb(e){return{cSize:$(()=>e.size in Ra?`${Ra[e.size]}px`:e.size),classes:$(()=>"q-spinner"+(e.color?` text-${e.color}`:""))}}var Es=Ze({name:"QSpinner",props:{...eb,thickness:{type:Number,default:5}},setup(e){const{cSize:t,classes:n}=tb(e);return()=>q("svg",{class:n.value+" q-spinner-mat",width:t.value,height:t.value,viewBox:"25 25 50 50"},[q("circle",{class:"path",cx:"50",cy:"50",r:"20",fill:"none",stroke:"currentColor","stroke-width":e.thickness,"stroke-miterlimit":"10"})])}});function Pa(e,t){const n=e.style;for(const r in t)n[r]=t[r]}function nb(e){if(e==null)return;if(typeof e=="string")try{return document.querySelector(e)||void 0}catch{return}const t=sn(e);if(t)return t.$el||t}function rb(e,t){if(e==null||e.contains(t)===!0)return!0;for(let n=e.nextElementSibling;n!==null;n=n.nextElementSibling)if(n.contains(t))return!0;return!1}function sb(e,t=250){let n=!1,r;return function(){return n===!1&&(n=!0,setTimeout(()=>{n=!1},t),r=e.apply(this,arguments)),r}}function ic(e,t,n,r){n.modifiers.stop===!0&&Do(e);const s=n.modifiers.color;let o=n.modifiers.center;o=o===!0||r===!0;const i=document.createElement("span"),a=document.createElement("span"),u=yv(e),{left:l,top:c,width:f,height:d}=t.getBoundingClientRect(),m=Math.sqrt(f*f+d*d),g=m/2,C=`${(f-m)/2}px`,w=o?C:`${u.left-l-g}px`,R=`${(d-m)/2}px`,E=o?R:`${u.top-c-g}px`;a.className="q-ripple__inner",Pa(a,{height:`${m}px`,width:`${m}px`,transform:`translate3d(${w},${E},0) scale3d(.2,.2,1)`,opacity:0}),i.className=`q-ripple${s?" text-"+s:""}`,i.setAttribute("dir","ltr"),i.appendChild(a),t.appendChild(i);const h=()=>{i.remove(),clearTimeout(p)};n.abort.push(h);let p=setTimeout(()=>{a.classList.add("q-ripple__inner--enter"),a.style.transform=`translate3d(${C},${R},0) scale3d(1,1,1)`,a.style.opacity=.2,p=setTimeout(()=>{a.classList.remove("q-ripple__inner--enter"),a.classList.add("q-ripple__inner--leave"),a.style.opacity=0,p=setTimeout(()=>{i.remove(),n.abort.splice(n.abort.indexOf(h),1)},275)},250)},50)}function ac(e,{modifiers:t,value:n,arg:r}){const s=Object.assign({},e.cfg.ripple,t,n);e.modifiers={early:s.early===!0,stop:s.stop===!0,center:s.center===!0,color:s.color||r,keyCodes:[].concat(s.keyCodes||13)}}var ob=vv({name:"ripple",beforeMount(e,t){const n=t.instance.$.appContext.config.globalProperties.$q.config||{};if(n.ripple===!1)return;const r={cfg:n,enabled:t.value!==!1,modifiers:{},abort:[],start(s){r.enabled===!0&&s.qSkipRipple!==!0&&s.type===(r.modifiers.early===!0?"pointerdown":"click")&&ic(s,e,r,s.qKeyEvent===!0)},keystart:sb(s=>{r.enabled===!0&&s.qSkipRipple!==!0&&vs(s,r.modifiers.keyCodes)===!0&&s.type===`key${r.modifiers.early===!0?"down":"up"}`&&ic(s,e,r,!0)},300)};ac(r,t),e.__qripple=r,Ev(r,"main",[[e,"pointerdown","start","passive"],[e,"click","start","passive"],[e,"keydown","keystart","passive"],[e,"keyup","keystart","passive"]])},updated(e,t){if(t.oldValue!==t.value){const n=e.__qripple;n!==void 0&&(n.enabled=t.value!==!1,n.enabled===!0&&Object(t.value)===t.value&&ac(n,t))}},beforeUnmount(e){const t=e.__qripple;t!==void 0&&(t.abort.forEach(n=>{n()}),Cv(t,"main"),delete e._qripple)}});const Bh={left:"start",center:"center",right:"end",between:"between",around:"around",evenly:"evenly",stretch:"stretch"},ib=Object.keys(Bh),Mh={align:{type:String,validator:e=>ib.includes(e)}};function $h(e){return $(()=>{const t=e.align===void 0?e.vertical===!0?"stretch":"left":e.align;return`${e.vertical===!0?"items":"justify"}-${Bh[t]}`})}function so(e){if(Object(e.$parent)===e.$parent)return e.$parent;let{parent:t}=e.$;for(;Object(t)===t;){if(Object(t.proxy)===t.proxy)return t.proxy;t=t.parent}}function Uh(e,t){typeof t.type=="symbol"?Array.isArray(t.children)===!0&&t.children.forEach(n=>{Uh(e,n)}):e.add(t)}function g1(e){const t=new Set;return e.forEach(n=>{Uh(t,n)}),Array.from(t)}function Vh(e){return e.appContext.config.globalProperties.$router!==void 0}function qh(e){return e.isUnmounted===!0||e.isDeactivated===!0}function uc(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}function lc(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ab(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(Array.isArray(s)===!1||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function cc(e,t){return Array.isArray(t)===!0?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function ub(e,t){return Array.isArray(e)===!0?cc(e,t):Array.isArray(t)===!0?cc(t,e):e===t}function lb(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(ub(e[n],t[n])===!1)return!1;return!0}const Hh={to:[String,Object],replace:Boolean,href:String,target:String,disable:Boolean},_1={...Hh,exact:Boolean,activeClass:{type:String,default:"q-router-link--active"},exactActiveClass:{type:String,default:"q-router-link--exact-active"}};function cb({fallbackTag:e,useDisableForRouterLinkProps:t=!0}={}){const n=Ee(),{props:r,proxy:s,emit:o}=n,i=Vh(n),a=$(()=>r.disable!==!0&&r.href!==void 0),u=$(t===!0?()=>i===!0&&r.disable!==!0&&a.value!==!0&&r.to!==void 0&&r.to!==null&&r.to!=="":()=>i===!0&&a.value!==!0&&r.to!==void 0&&r.to!==null&&r.to!==""),l=$(()=>u.value===!0?E(r.to):null),c=$(()=>l.value!==null),f=$(()=>a.value===!0||c.value===!0),d=$(()=>r.type==="a"||f.value===!0?"a":r.tag||e||"div"),m=$(()=>a.value===!0?{href:r.href,target:r.target}:c.value===!0?{href:l.value.href,target:r.target}:{}),g=$(()=>{if(c.value===!1)return-1;const{matched:b}=l.value,{length:S}=b,x=b[S-1];if(x===void 0)return-1;const F=s.$route.matched;if(F.length===0)return-1;const v=F.findIndex(lc.bind(null,x));if(v!==-1)return v;const O=uc(b[S-2]);return S>1&&uc(x)===O&&F[F.length-1].path!==O?F.findIndex(lc.bind(null,b[S-2])):v}),C=$(()=>c.value===!0&&g.value!==-1&&ab(s.$route.params,l.value.params)),w=$(()=>C.value===!0&&g.value===s.$route.matched.length-1&&lb(s.$route.params,l.value.params)),R=$(()=>c.value===!0?w.value===!0?` ${r.exactActiveClass} ${r.activeClass}`:r.exact===!0?"":C.value===!0?` ${r.activeClass}`:"":"");function E(b){try{return s.$router.resolve(b)}catch{}return null}function h(b,{returnRouterError:S,to:x=r.to,replace:F=r.replace}={}){if(r.disable===!0)return b.preventDefault(),Promise.resolve(!1);if(b.metaKey||b.altKey||b.ctrlKey||b.shiftKey||b.button!==void 0&&b.button!==0||r.target==="_blank")return Promise.resolve(!1);b.preventDefault();const v=s.$router[F===!0?"replace":"push"](x);return S===!0?v:v.then(()=>{}).catch(()=>{})}function p(b){if(c.value===!0){const S=x=>h(b,x);o("click",b,S),b.defaultPrevented!==!0&&S()}else o("click",b)}return{hasRouterLink:c,hasHrefLink:a,hasLink:f,linkTag:d,resolvedLink:l,linkIsActive:C,linkIsExactActive:w,linkClass:R,linkAttrs:m,getLink:E,navigateToRouterLink:h,navigateOnClick:p}}const fc={none:0,xs:4,sm:8,md:16,lg:24,xl:32},fb={xs:8,sm:10,md:14,lg:20,xl:24},db=["button","submit","reset"],hb=/[^\s]\/[^\s]/,mb=["flat","outline","push","unelevated"];function jh(e,t){return e.flat===!0?"flat":e.outline===!0?"outline":e.push===!0?"push":e.unelevated===!0?"unelevated":t}function v1(e){const t=jh(e);return t!==void 0?{[t]:!0}:{}}const pb={...Ps,...Hh,type:{type:String,default:"button"},label:[Number,String],icon:String,iconRight:String,...mb.reduce((e,t)=>(e[t]=Boolean)&&e,{}),square:Boolean,rounded:Boolean,glossy:Boolean,size:String,fab:Boolean,fabMini:Boolean,padding:String,color:String,textColor:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,tabindex:[Number,String],ripple:{type:[Boolean,Object],default:!0},align:{...Mh.align,default:"center"},stack:Boolean,stretch:Boolean,loading:{type:Boolean,default:null},disable:Boolean},gb={...pb,round:Boolean};function _b(e){const t=Fs(e,fb),n=$h(e),{hasRouterLink:r,hasLink:s,linkTag:o,linkAttrs:i,navigateOnClick:a}=cb({fallbackTag:"button"}),u=$(()=>{const w=e.fab===!1&&e.fabMini===!1?t.value:{};return e.padding!==void 0?Object.assign({},w,{padding:e.padding.split(/\s+/).map(R=>R in fc?fc[R]+"px":R).join(" "),minWidth:"0",minHeight:"0"}):w}),l=$(()=>e.rounded===!0||e.fab===!0||e.fabMini===!0),c=$(()=>e.disable!==!0&&e.loading!==!0),f=$(()=>c.value===!0?e.tabindex||0:-1),d=$(()=>jh(e,"standard")),m=$(()=>{const w={tabindex:f.value};return s.value===!0?Object.assign(w,i.value):db.includes(e.type)===!0&&(w.type=e.type),o.value==="a"?(e.disable===!0?w["aria-disabled"]="true":w.href===void 0&&(w.role="button"),r.value!==!0&&hb.test(e.type)===!0&&(w.type=e.type)):e.disable===!0&&(w.disabled="",w["aria-disabled"]="true"),e.loading===!0&&e.percentage!==void 0&&Object.assign(w,{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":e.percentage}),w}),g=$(()=>{let w;e.color!==void 0?e.flat===!0||e.outline===!0?w=`text-${e.textColor||e.color}`:w=`bg-${e.color} text-${e.textColor||"white"}`:e.textColor&&(w=`text-${e.textColor}`);const R=e.round===!0?"round":`rectangle${l.value===!0?" q-btn--rounded":e.square===!0?" q-btn--square":""}`;return`q-btn--${d.value} q-btn--${R}`+(w!==void 0?" "+w:"")+(c.value===!0?" q-btn--actionable q-focusable q-hoverable":e.disable===!0?" disabled":"")+(e.fab===!0?" q-btn--fab":e.fabMini===!0?" q-btn--fab-mini":"")+(e.noCaps===!0?" q-btn--no-uppercase":"")+(e.dense===!0?" q-btn--dense":"")+(e.stretch===!0?" no-border-radius self-stretch":"")+(e.glossy===!0?" glossy":"")+(e.square?" q-btn--square":"")}),C=$(()=>n.value+(e.stack===!0?" column":" row")+(e.noWrap===!0?" no-wrap text-no-wrap":"")+(e.loading===!0?" q-btn__content--hidden":""));return{classes:g,style:u,innerClasses:C,attributes:m,hasLink:s,linkTag:o,navigateOnClick:a,isActionable:c}}const{passiveCapture:At}=pt;let ir=null,ar=null,ur=null;var Fa=Ze({name:"QBtn",props:{...gb,percentage:Number,darkPercentage:Boolean,onTouchstart:[Function,Array]},emits:["click","keydown","mousedown","keyup"],setup(e,{slots:t,emit:n}){const{proxy:r}=Ee(),{classes:s,style:o,innerClasses:i,attributes:a,hasLink:u,linkTag:l,navigateOnClick:c,isActionable:f}=_b(e),d=ge(null),m=ge(null);let g=null,C,w=null;const R=$(()=>e.label!==void 0&&e.label!==null&&e.label!==""),E=$(()=>e.disable===!0||e.ripple===!1?!1:{keyCodes:u.value===!0?[13,32]:[13],...e.ripple===!0?{}:e.ripple}),h=$(()=>({center:e.round})),p=$(()=>{const L=Math.max(0,Math.min(100,e.percentage));return L>0?{transition:"transform 0.6s",transform:`translateX(${L-100}%)`}:{}}),b=$(()=>{if(e.loading===!0)return{onMousedown:H,onTouchstart:H,onClick:H,onKeydown:H,onKeyup:H};if(f.value===!0){const L={onClick:x,onKeydown:F,onMousedown:O};if(r.$q.platform.has.touch===!0){const Z=e.onTouchstart!==void 0?"":"Passive";L[`onTouchstart${Z}`]=v}return L}return{onClick:St}}),S=$(()=>({ref:d,class:"q-btn q-btn-item non-selectable no-outline "+s.value,style:o.value,...a.value,...b.value}));function x(L){if(d.value!==null){if(L!==void 0){if(L.defaultPrevented===!0)return;const Z=document.activeElement;if(e.type==="submit"&&Z!==document.body&&d.value.contains(Z)===!1&&Z.contains(d.value)===!1){d.value.focus();const W=()=>{document.removeEventListener("keydown",St,!0),document.removeEventListener("keyup",W,At),d.value!==null&&d.value.removeEventListener("blur",W,At)};document.addEventListener("keydown",St,!0),document.addEventListener("keyup",W,At),d.value.addEventListener("blur",W,At)}}c(L)}}function F(L){d.value!==null&&(n("keydown",L),vs(L,[13,32])===!0&&ar!==d.value&&(ar!==null&&T(),L.defaultPrevented!==!0&&(d.value.focus(),ar=d.value,d.value.classList.add("q-btn--active"),document.addEventListener("keyup",I,!0),d.value.addEventListener("blur",I,At)),St(L)))}function v(L){d.value!==null&&(n("touchstart",L),L.defaultPrevented!==!0&&(ir!==d.value&&(ir!==null&&T(),ir=d.value,g=L.target,g.addEventListener("touchcancel",I,At),g.addEventListener("touchend",I,At)),C=!0,w!==null&&clearTimeout(w),w=setTimeout(()=>{w=null,C=!1},200)))}function O(L){d.value!==null&&(L.qSkipRipple=C===!0,n("mousedown",L),L.defaultPrevented!==!0&&ur!==d.value&&(ur!==null&&T(),ur=d.value,d.value.classList.add("q-btn--active"),document.addEventListener("mouseup",I,At)))}function I(L){if(d.value!==null&&!(L!==void 0&&L.type==="blur"&&document.activeElement===d.value)){if(L!==void 0&&L.type==="keyup"){if(ar===d.value&&vs(L,[13,32])===!0){const Z=new MouseEvent("click",L);Z.qKeyEvent=!0,L.defaultPrevented===!0&&Sn(Z),L.cancelBubble===!0&&Do(Z),d.value.dispatchEvent(Z),St(L),L.qKeyEvent=!0}n("keyup",L)}T()}}function T(L){const Z=m.value;L!==!0&&(ir===d.value||ur===d.value)&&Z!==null&&Z!==document.activeElement&&(Z.setAttribute("tabindex",-1),Z.focus()),ir===d.value&&(g!==null&&(g.removeEventListener("touchcancel",I,At),g.removeEventListener("touchend",I,At)),ir=g=null),ur===d.value&&(document.removeEventListener("mouseup",I,At),ur=null),ar===d.value&&(document.removeEventListener("keyup",I,!0),d.value!==null&&d.value.removeEventListener("blur",I,At),ar=null),d.value!==null&&d.value.classList.remove("q-btn--active")}function H(L){St(L),L.qSkipRipple=!0}return xt(()=>{T(!0)}),Object.assign(r,{click:L=>{f.value===!0&&x(L)}}),()=>{let L=[];e.icon!==void 0&&L.push(q(un,{name:e.icon,left:e.stack!==!0&&R.value===!0,role:"img"})),R.value===!0&&L.push(q("span",{class:"block"},[e.label])),L=Un(t.default,L),e.iconRight!==void 0&&e.round===!1&&L.push(q(un,{name:e.iconRight,right:e.stack!==!0&&R.value===!0,role:"img"}));const Z=[q("span",{class:"q-focus-helper",ref:m})];return e.loading===!0&&e.percentage!==void 0&&Z.push(q("span",{class:"q-btn__progress absolute-full overflow-hidden"+(e.darkPercentage===!0?" q-btn__progress--dark":"")},[q("span",{class:"q-btn__progress-indicator fit block",style:p.value})])),Z.push(q("span",{class:"q-btn__content text-center col items-center q-anchor--skip "+i.value},L)),e.loading!==null&&Z.push(q(xo,{name:"q-transition--fade"},()=>e.loading===!0?[q("span",{key:"loading",class:"absolute-full flex flex-center"},t.loading!==void 0?t.loading():[q(Es)])]:null)),ud(q(l.value,S.value,Z),[[ob,E.value,void 0,h.value]])}}});let vb=1,yb=document.body;function ku(e,t){const n=document.createElement("div");if(n.id=t!==void 0?`q-portal--${t}--${vb++}`:e,Lo.globalNodes!==void 0){const r=Lo.globalNodes.class;r!==void 0&&(n.className=r)}return yb.appendChild(n),n}function Wh(e){e.remove()}let bb=0;const oo={},io={},Rt={},Kh={},Eb=/^\s*$/,Xh=[],Cb=[void 0,null,!0,!1,""],xu=["top-left","top-right","bottom-left","bottom-right","top","bottom","left","right","center"],Ab=["top-left","top-right","bottom-left","bottom-right"],mr={positive:{icon:e=>e.iconSet.type.positive,color:"positive"},negative:{icon:e=>e.iconSet.type.negative,color:"negative"},warning:{icon:e=>e.iconSet.type.warning,color:"warning",textColor:"dark"},info:{icon:e=>e.iconSet.type.info,color:"info"},ongoing:{group:!1,timeout:0,spinner:!0,color:"grey-8"}};function zh(e,t,n){if(!e)return jr("parameter required");let r;const s={textColor:"white"};if(e.ignoreDefaults!==!0&&Object.assign(s,oo),Xt(e)===!1&&(s.type&&Object.assign(s,mr[s.type]),e={message:e}),Object.assign(s,mr[e.type||s.type],e),typeof s.icon=="function"&&(s.icon=s.icon(t)),s.spinner?(s.spinner===!0&&(s.spinner=Es),s.spinner=Zn(s.spinner)):s.spinner=!1,s.meta={hasMedia:Boolean(s.spinner!==!1||s.icon||s.avatar),hasText:dc(s.message)||dc(s.caption)},s.position){if(xu.includes(s.position)===!1)return jr("wrong position",e)}else s.position="bottom";if(Cb.includes(s.timeout)===!0)s.timeout=5e3;else{const u=Number(s.timeout);if(isNaN(u)||u<0)return jr("wrong timeout",e);s.timeout=Number.isFinite(u)?u:0}s.timeout===0?s.progress=!1:s.progress===!0&&(s.meta.progressClass="q-notification__progress"+(s.progressClass?` ${s.progressClass}`:""),s.meta.progressStyle={animationDuration:`${s.timeout+1e3}ms`});const o=(Array.isArray(e.actions)===!0?e.actions:[]).concat(e.ignoreDefaults!==!0&&Array.isArray(oo.actions)===!0?oo.actions:[]).concat(mr[e.type]!==void 0&&Array.isArray(mr[e.type].actions)===!0?mr[e.type].actions:[]),{closeBtn:i}=s;if(i&&o.push({label:typeof i=="string"?i:t.lang.label.close}),s.actions=o.map(({handler:u,noDismiss:l,...c})=>({flat:!0,...c,onClick:typeof u=="function"?()=>{u(),l!==!0&&a()}:()=>{a()}})),s.multiLine===void 0&&(s.multiLine=s.actions.length>1),Object.assign(s.meta,{class:`q-notification row items-stretch q-notification--${s.multiLine===!0?"multi-line":"standard"}`+(s.color!==void 0?` bg-${s.color}`:"")+(s.textColor!==void 0?` text-${s.textColor}`:"")+(s.classes!==void 0?` ${s.classes}`:""),wrapperClass:"q-notification__wrapper col relative-position border-radius-inherit "+(s.multiLine===!0?"column no-wrap justify-center":"row items-center"),contentClass:"q-notification__content row items-center"+(s.multiLine===!0?"":" col"),leftClass:s.meta.hasText===!0?"additional":"single",attrs:{role:"alert",...s.attrs}}),s.group===!1?(s.group=void 0,s.meta.group=void 0):((s.group===void 0||s.group===!0)&&(s.group=[s.message,s.caption,s.multiline].concat(s.actions.map(u=>`${u.label}*${u.icon}`)).join("|")),s.meta.group=s.group+"|"+s.position),s.actions.length===0?s.actions=void 0:s.meta.actionsClass="q-notification__actions row items-center "+(s.multiLine===!0?"justify-end":"col-auto")+(s.meta.hasMedia===!0?" q-notification__actions--with-media":""),n!==void 0){n.notif.meta.timer&&(clearTimeout(n.notif.meta.timer),n.notif.meta.timer=void 0),s.meta.uid=n.notif.meta.uid;const u=Rt[s.position].value.indexOf(n.notif);Rt[s.position].value[u]=s}else{const u=io[s.meta.group];if(u===void 0){if(s.meta.uid=bb++,s.meta.badge=1,["left","right","center"].indexOf(s.position)!==-1)Rt[s.position].value.splice(Math.floor(Rt[s.position].value.length/2),0,s);else{const l=s.position.indexOf("top")!==-1?"unshift":"push";Rt[s.position].value[l](s)}s.group!==void 0&&(io[s.meta.group]=s)}else{if(u.meta.timer&&(clearTimeout(u.meta.timer),u.meta.timer=void 0),s.badgePosition!==void 0){if(Ab.includes(s.badgePosition)===!1)return jr("wrong badgePosition",e)}else s.badgePosition=`top-${s.position.indexOf("left")!==-1?"right":"left"}`;s.meta.uid=u.meta.uid,s.meta.badge=u.meta.badge+1,s.meta.badgeClass=`q-notification__badge q-notification__badge--${s.badgePosition}`+(s.badgeColor!==void 0?` bg-${s.badgeColor}`:"")+(s.badgeTextColor!==void 0?` text-${s.badgeTextColor}`:"")+(s.badgeClass?` ${s.badgeClass}`:"");const l=Rt[s.position].value.indexOf(u);Rt[s.position].value[l]=io[s.meta.group]=s}}const a=()=>{Sb(s),r=void 0};if(s.timeout>0&&(s.meta.timer=setTimeout(()=>{s.meta.timer=void 0,a()},s.timeout+1e3)),s.group!==void 0)return u=>{u!==void 0?jr("trying to update a grouped one which is forbidden",e):a()};if(r={dismiss:a,config:e,notif:s},n!==void 0){Object.assign(n,r);return}return u=>{if(r!==void 0)if(u===void 0)r.dismiss();else{const l=Object.assign({},r.config,u,{group:!1,position:s.position});zh(l,t,r)}}}function Sb(e){e.meta.timer&&(clearTimeout(e.meta.timer),e.meta.timer=void 0);const t=Rt[e.position].value.indexOf(e);if(t!==-1){e.group!==void 0&&delete io[e.meta.group];const n=Xh[""+e.meta.uid];if(n){const{width:r,height:s}=getComputedStyle(n);n.style.left=`${n.offsetLeft}px`,n.style.width=r,n.style.height=s}Rt[e.position].value.splice(t,1),typeof e.onDismiss=="function"&&e.onDismiss()}}function dc(e){return e!=null&&Eb.test(e)!==!0}function jr(e,t){return console.error(`Notify: ${e}`,t),!1}function wb(){return Ze({name:"QNotifications",devtools:{hide:!0},setup(){return()=>q("div",{class:"q-notifications"},xu.map(e=>q(ev,{key:e,class:Kh[e],tag:"div",name:`q-notification--${e}`},()=>Rt[e].value.map(t=>{const n=t.meta,r=[];if(n.hasMedia===!0&&(t.spinner!==!1?r.push(q(t.spinner,{class:"q-notification__spinner q-notification__spinner--"+n.leftClass,color:t.spinnerColor,size:t.spinnerSize})):t.icon?r.push(q(un,{class:"q-notification__icon q-notification__icon--"+n.leftClass,name:t.icon,color:t.iconColor,size:t.iconSize,role:"img"})):t.avatar&&r.push(q(Zy,{class:"q-notification__avatar q-notification__avatar--"+n.leftClass},()=>q("img",{src:t.avatar,"aria-hidden":"true"})))),n.hasText===!0){let o;const i={class:"q-notification__message col"};if(t.html===!0)i.innerHTML=t.caption?`<div>${t.message}</div><div class="q-notification__caption">${t.caption}</div>`:t.message;else{const a=[t.message];o=t.caption?[q("div",a),q("div",{class:"q-notification__caption"},[t.caption])]:a}r.push(q("div",i,o))}const s=[q("div",{class:n.contentClass},r)];return t.progress===!0&&s.push(q("div",{key:`${n.uid}|p|${n.badge}`,class:n.progressClass,style:n.progressStyle})),t.actions!==void 0&&s.push(q("div",{class:n.actionsClass},t.actions.map(o=>q(Fa,o)))),n.badge>1&&s.push(q("div",{key:`${n.uid}|${n.badge}`,class:t.meta.badgeClass,style:t.badgeStyle},[n.badge])),q("div",{ref:o=>{Xh[""+n.uid]=o},key:n.uid,class:n.class,...n.attrs},[q("div",{class:n.wrapperClass},s)])}))))}})}var Io={setDefaults(e){Xt(e)===!0&&Object.assign(oo,e)},registerType(e,t){Xt(t)===!0&&(mr[e]=t)},install({$q:e,parentApp:t}){if(e.notify=this.create=n=>zh(n,e),e.notify.setDefaults=this.setDefaults,e.notify.registerType=this.registerType,e.config.notify!==void 0&&this.setDefaults(e.config.notify),this.__installed!==!0){xu.forEach(r=>{Rt[r]=ge([]);const s=["left","center","right"].includes(r)===!0?"center":r.indexOf("top")!==-1?"top":"bottom",o=r.indexOf("left")!==-1?"start":r.indexOf("right")!==-1?"end":"center",i=["left","right"].includes(r)?`items-${r==="left"?"start":"end"} justify-center`:r==="center"?"flex-center":`items-${o}`;Kh[r]=`q-notifications__list q-notifications__list--${s} fixed column no-wrap ${i}`});const n=ku("q-notify");Ch(wb(),t).mount(n)}}};const vr=class{constructor(){Ns(this,"currentVersion");Ns(this,"buildVersion");this.buildVersion="v0.3.2_763417",this.currentVersion=this.extractBaseVersion(this.buildVersion)}extractBaseVersion(t){const n=t.match(/^(v\d+\.\d+\.\d+)/);return n?n[1]:t}static getInstance(){return vr.instance||(vr.instance=new vr),vr.instance}async performUpdate(){try{await this.clearAllCaches(),window.location.reload()}catch(t){throw console.error("VersionChecker: \u66F4\u65B0\u5931\u6557",t),t}}async clearAllCaches(){try{if("caches"in window){const t=await caches.keys();await Promise.all(t.map(n=>caches.delete(n)))}localStorage.removeItem("app-etag"),localStorage.removeItem("app-last-modified"),localStorage.removeItem("app-stored-version")}catch(t){console.error("VersionChecker: \u6E05\u9664\u7DE9\u5B58\u5931\u6557",t)}}destroy(){}getCurrentVersion(){return this.currentVersion}getBuildVersion(){return this.buildVersion}getVersionInfo(){return{baseVersion:this.currentVersion,buildVersion:this.buildVersion,timestamp:this.extractTimestamp(this.buildVersion)}}extractTimestamp(t){const n=t.match(/_(\d+)$/);return n?n[1]:""}getBasePath(){if(window.location.pathname.includes("/au-pos/"))return"/au-pos/";const n="/au-pos/";return n.endsWith("/")?n:n+"/"}async triggerUpdate(){return this.performUpdate()}async manualVersionCheck(){if(!sessionStorage.getItem("app-session-started"))return!1;try{return!!(await this.checkIndexChanges()||await this.checkVersionFileChanges())}catch(n){throw console.error("VersionChecker: \u624B\u52D5\u7248\u672C\u6AA2\u67E5\u5931\u6557",n),n}}async checkIndexChanges(){const n=`${this.getBasePath()}index.html?${Date.now()}`,r=await fetch(n,{method:"HEAD",cache:"no-cache"});if(!r.ok)throw new Error("\u7121\u6CD5\u7372\u53D6 index.html");const s=r.headers.get("etag"),o=r.headers.get("last-modified"),i=localStorage.getItem("app-etag"),a=localStorage.getItem("app-last-modified");return!i&&!a?(s&&localStorage.setItem("app-etag",s),o&&localStorage.setItem("app-last-modified",o),!1):!!(s&&i&&s!==i||o&&a&&o!==a)}async checkVersionFileChanges(){try{const n=`${this.getBasePath()}version.json?${Date.now()}`,r=await fetch(n,{cache:"no-cache"});if(r.ok){const o=(await r.json()).version;if(!localStorage.getItem("app-stored-version"))return localStorage.setItem("app-stored-version",this.buildVersion),!1;if(o&&o!==this.buildVersion)return!0}return!1}catch{return!1}}};let ao=vr;Ns(ao,"instance");const Vi=ao.getInstance(),Tb=ln({name:"App",__name:"App",setup(e){const t=jy(),n={init(){window.addEventListener("version-update-detected",()=>{this.handleVersionUpdate()}),t.afterEach(()=>{this.checkVersionOnRouteChange()})},async checkVersionOnRouteChange(){if(!!sessionStorage.getItem("app-session-started"))try{await Vi.manualVersionCheck()&&this.performSilentUpdate()}catch(s){s instanceof Error&&!s.message.includes("\u7121\u6CD5\u7372\u53D6")&&console.error("\u8DEF\u7531\u5207\u63DB\u7248\u672C\u6AA2\u67E5\u5931\u6557:",s)}},handleVersionUpdate(){!sessionStorage.getItem("app-session-started")||this.performSilentUpdate()},performSilentUpdate(){this.showUpdatingNotification(),setTimeout(async()=>{try{await Vi.triggerUpdate(),this.showUpdateCompletedNotification()}catch(r){console.error("\u81EA\u52D5\u66F4\u65B0\u5931\u6557:",r),window.location.reload()}},1e3)},showUpdatingNotification(){Io.create({message:"\u6B63\u5728\u66F4\u65B0\u5230\u6700\u65B0\u7248\u672C...",icon:"cloud_download",color:"primary",position:"top-right",timeout:2e3,spinner:!0})},showUpdateCompletedNotification(){Io.create({message:"\u5DF2\u66F4\u65B0\u5230\u6700\u65B0\u7248\u672C",icon:"check_circle",color:"positive",position:"top-right",timeout:3e3})}};return Vt(()=>{n.init(),sessionStorage.setItem("app-session-started","true"),"serviceWorker"in navigator&&(navigator.serviceWorker.addEventListener("message",r=>{if(r.data&&(r.data.type==="RELOAD_PAGE"||r.data.type==="FORCE_RELOAD")){if(!sessionStorage.getItem("app-session-started"))return;n.performSilentUpdate()}}),navigator.serviceWorker.addEventListener("controllerchange",()=>{!sessionStorage.getItem("app-session-started")||n.performSilentUpdate()}))}),Ir(()=>{Vi.destroy()}),(r,s)=>{const o=Bg("router-view");return gs(),wo(o)}}});function y1(e){return e}var kb=!1;/*!
 * pinia v2.3.0
 * (c) 2024 Eduardo San Martin Morote
 * @license MIT
 */let Gh;const hi=e=>Gh=e,Yh=Symbol();function Da(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var as;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(as||(as={}));function xb(){const e=iu(!0),t=e.run(()=>ge({}));let n=[],r=[];const s=Zn({install(o){hi(s),s._a=o,o.provide(Yh,s),o.config.globalProperties.$pinia=s,r.forEach(i=>n.push(i)),r=[]},use(o){return!this._a&&!kb?r.push(o):n.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return s}const Qh=()=>{};function hc(e,t,n,r=Qh){e.push(t);const s=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),r())};return!n&&Lf()&&Lp(s),s}function lr(e,...t){e.slice().forEach(n=>{n(...t)})}const Ob=e=>e(),mc=Symbol(),qi=Symbol();function Na(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,r)=>e.set(r,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],s=e[n];Da(s)&&Da(r)&&e.hasOwnProperty(n)&&!Le(r)&&!rn(r)?e[n]=Na(s,r):e[n]=r}return e}const Rb=Symbol();function Pb(e){return!Da(e)||!e.hasOwnProperty(Rb)}const{assign:_n}=Object;function Fb(e){return!!(Le(e)&&e.effect)}function Db(e,t,n,r){const{state:s,actions:o,getters:i}=t,a=n.state.value[e];let u;function l(){a||(n.state.value[e]=s?s():{});const c=og(n.state.value[e]);return _n(c,o,Object.keys(i||{}).reduce((f,d)=>(f[d]=Zn($(()=>{hi(n);const m=n._s.get(e);return i[d].call(m,m)})),f),{}))}return u=Jh(e,l,t,n,r,!0),u}function Jh(e,t,n={},r,s,o){let i;const a=_n({actions:{}},n),u={deep:!0};let l,c,f=[],d=[],m;const g=r.state.value[e];!o&&!g&&(r.state.value[e]={}),ge({});let C;function w(F){let v;l=c=!1,typeof F=="function"?(F(r.state.value[e]),v={type:as.patchFunction,storeId:e,events:m}):(Na(r.state.value[e],F),v={type:as.patchObject,payload:F,storeId:e,events:m});const O=C=Symbol();rt().then(()=>{C===O&&(l=!0)}),c=!0,lr(f,v,r.state.value[e])}const R=o?function(){const{state:v}=n,O=v?v():{};this.$patch(I=>{_n(I,O)})}:Qh;function E(){i.stop(),f=[],d=[],r._s.delete(e)}const h=(F,v="")=>{if(mc in F)return F[qi]=v,F;const O=function(){hi(r);const I=Array.from(arguments),T=[],H=[];function L(Q){T.push(Q)}function Z(Q){H.push(Q)}lr(d,{args:I,name:O[qi],store:b,after:L,onError:Z});let W;try{W=F.apply(this&&this.$id===e?this:b,I)}catch(Q){throw lr(H,Q),Q}return W instanceof Promise?W.then(Q=>(lr(T,Q),Q)).catch(Q=>(lr(H,Q),Promise.reject(Q))):(lr(T,W),W)};return O[mc]=!0,O[qi]=v,O},p={_p:r,$id:e,$onAction:hc.bind(null,d),$patch:w,$reset:R,$subscribe(F,v={}){const O=hc(f,F,v.detached,()=>I()),I=i.run(()=>Ae(()=>r.state.value[e],T=>{(v.flush==="sync"?c:l)&&F({storeId:e,type:as.direct,events:m},T)},_n({},u,v)));return O},$dispose:E},b=Jn(p);r._s.set(e,b);const x=(r._a&&r._a.runWithContext||Ob)(()=>r._e.run(()=>(i=iu()).run(()=>t({action:h}))));for(const F in x){const v=x[F];if(Le(v)&&!Fb(v)||rn(v))o||(g&&Pb(v)&&(Le(v)?v.value=g[F]:Na(v,g[F])),r.state.value[e][F]=v);else if(typeof v=="function"){const O=h(v,F);x[F]=O,a.actions[F]=v}}return _n(b,x),_n(me(b),x),Object.defineProperty(b,"$state",{get:()=>r.state.value[e],set:F=>{w(v=>{_n(v,F)})}}),r._p.forEach(F=>{_n(b,i.run(()=>F({store:b,app:r._a,pinia:r,options:a})))}),g&&o&&n.hydrate&&n.hydrate(b.$state,g),l=!0,c=!0,b}/*! #__NO_SIDE_EFFECTS__ */function Nb(e,t,n){let r,s;const o=typeof t=="function";typeof e=="string"?(r=e,s=o?n:t):(s=e,r=e.id);function i(a,u){const l=Kg();return a=a||(l?vt(Yh,null):null),a&&hi(a),a=Gh,a._s.has(r)||(o?Jh(r,t,s,a):Db(r,s,a)),a._s.get(r)}return i.$id=r,i}const Lb=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,Ib=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,Bb=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function Mb(e,t){if(e==="__proto__"||e==="constructor"&&t&&typeof t=="object"&&"prototype"in t){$b(e);return}return t}function $b(e){console.warn(`[destr] Dropping "${e}" key to prevent prototype pollution.`)}function Ub(e,t={}){if(typeof e!="string")return e;const n=e.trim();if(e[0]==='"'&&e.endsWith('"')&&!e.includes("\\"))return n.slice(1,-1);if(n.length<=9){const r=n.toLowerCase();if(r==="true")return!0;if(r==="false")return!1;if(r==="undefined")return;if(r==="null")return null;if(r==="nan")return Number.NaN;if(r==="infinity")return Number.POSITIVE_INFINITY;if(r==="-infinity")return Number.NEGATIVE_INFINITY}if(!Bb.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(Lb.test(e)||Ib.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,Mb)}return JSON.parse(e)}catch(r){if(t.strict)throw r;return e}}function Vb(e,t){if(e==null)return;let n=e;for(let r=0;r<t.length;r++){if(n==null||n[t[r]]==null)return;n=n[t[r]]}return n}function Ou(e,t,n){if(n.length===0)return t;const r=n[0];return n.length>1&&(t=Ou(typeof e!="object"||e===null||!Object.prototype.hasOwnProperty.call(e,r)?Number.isInteger(Number(n[1]))?[]:{}:e[r],t,Array.prototype.slice.call(n,1))),Number.isInteger(Number(r))&&Array.isArray(e)?e.slice()[r]:Object.assign({},e,{[r]:t})}function Zh(e,t){if(e==null||t.length===0)return e;if(t.length===1){if(e==null)return e;if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.slice.call(e,0).splice(t[0],1);const n={};for(const r in e)n[r]=e[r];return delete n[t[0]],n}if(e[t[0]]==null){if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.concat.call([],e);const n={};for(const r in e)n[r]=e[r];return n}return Ou(e,Zh(e[t[0]],Array.prototype.slice.call(t,1)),[t[0]])}function em(e,t){return t.map(n=>n.split(".")).map(n=>[n,Vb(e,n)]).filter(n=>n[1]!==void 0).reduce((n,r)=>Ou(n,r[1],r[0]),{})}function tm(e,t){return t.map(n=>n.split(".")).reduce((n,r)=>Zh(n,r),e)}function pc(e,{storage:t,serializer:n,key:r,debug:s,pick:o,omit:i,beforeHydrate:a,afterHydrate:u},l,c=!0){try{c&&(a==null||a(l));const f=t.getItem(r);if(f){const d=n.deserialize(f),m=o?em(d,o):d,g=i?tm(m,i):m;e.$patch(g)}c&&(u==null||u(l))}catch(f){s&&console.error("[pinia-plugin-persistedstate]",f)}}function gc(e,{storage:t,serializer:n,key:r,debug:s,pick:o,omit:i}){try{const a=o?em(e,o):e,u=i?tm(a,i):a,l=n.serialize(u);t.setItem(r,l)}catch(a){s&&console.error("[pinia-plugin-persistedstate]",a)}}function qb(e,t,n){const{pinia:r,store:s,options:{persist:o=n}}=e;if(!o)return;if(!(s.$id in r.state.value)){const u=r._s.get(s.$id.replace("__hot:",""));u&&Promise.resolve().then(()=>u.$persist());return}const a=(Array.isArray(o)?o:o===!0?[{}]:[o]).map(t);s.$hydrate=({runHooks:u=!0}={})=>{a.forEach(l=>{pc(s,l,e,u)})},s.$persist=()=>{a.forEach(u=>{gc(s.$state,u)})},a.forEach(u=>{pc(s,u,e),s.$subscribe((l,c)=>gc(c,u),{detached:!0})})}function Hb(e={}){return function(t){var n;qb(t,r=>{var s,o,i,a,u,l,c;return{key:(e.key?e.key:f=>f)((s=r.key)!=null?s:t.store.$id),debug:(i=(o=r.debug)!=null?o:e.debug)!=null?i:!1,serializer:(u=(a=r.serializer)!=null?a:e.serializer)!=null?u:{serialize:f=>JSON.stringify(f),deserialize:f=>Ub(f)},storage:(c=(l=r.storage)!=null?l:e.storage)!=null?c:window.localStorage,beforeHydrate:r.beforeHydrate,afterHydrate:r.afterHydrate,pick:r.pick,omit:r.omit}},(n=e.auto)!=null?n:!1)}}var jb=Hb(),Hi=()=>{const e=xb();return e.use(jb),e};function nm(e,t){return function(){return e.apply(t,arguments)}}const{toString:Wb}=Object.prototype,{getPrototypeOf:Ru}=Object,mi=(e=>t=>{const n=Wb.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),qt=e=>(e=e.toLowerCase(),t=>mi(t)===e),pi=e=>t=>typeof t===e,{isArray:Br}=Array,Cs=pi("undefined");function Kb(e){return e!==null&&!Cs(e)&&e.constructor!==null&&!Cs(e.constructor)&&Et(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const rm=qt("ArrayBuffer");function Xb(e){let t;return typeof ArrayBuffer!="undefined"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&rm(e.buffer),t}const zb=pi("string"),Et=pi("function"),sm=pi("number"),gi=e=>e!==null&&typeof e=="object",Gb=e=>e===!0||e===!1,uo=e=>{if(mi(e)!=="object")return!1;const t=Ru(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},Yb=qt("Date"),Qb=qt("File"),Jb=qt("Blob"),Zb=qt("FileList"),e0=e=>gi(e)&&Et(e.pipe),t0=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Et(e.append)&&((t=mi(e))==="formdata"||t==="object"&&Et(e.toString)&&e.toString()==="[object FormData]"))},n0=qt("URLSearchParams"),[r0,s0,o0,i0]=["ReadableStream","Request","Response","Headers"].map(qt),a0=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ds(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e=="undefined")return;let r,s;if(typeof e!="object"&&(e=[e]),Br(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let a;for(r=0;r<i;r++)a=o[r],t.call(null,e[a],a,e)}}function om(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const Vn=(()=>typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:global)(),im=e=>!Cs(e)&&e!==Vn;function La(){const{caseless:e}=im(this)&&this||{},t={},n=(r,s)=>{const o=e&&om(t,s)||s;uo(t[o])&&uo(r)?t[o]=La(t[o],r):uo(r)?t[o]=La({},r):Br(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&Ds(arguments[r],n);return t}const u0=(e,t,n,{allOwnKeys:r}={})=>(Ds(t,(s,o)=>{n&&Et(s)?e[o]=nm(s,n):e[o]=s},{allOwnKeys:r}),e),l0=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),c0=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},f0=(e,t,n,r)=>{let s,o,i;const a={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!r||r(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=n!==!1&&Ru(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},d0=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},h0=e=>{if(!e)return null;if(Br(e))return e;let t=e.length;if(!sm(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},m0=(e=>t=>e&&t instanceof e)(typeof Uint8Array!="undefined"&&Ru(Uint8Array)),p0=(e,t)=>{const r=(e&&e[Symbol.iterator]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},g0=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},_0=qt("HTMLFormElement"),v0=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),_c=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),y0=qt("RegExp"),am=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Ds(n,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(r[o]=i||s)}),Object.defineProperties(e,r)},b0=e=>{am(e,(t,n)=>{if(Et(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(!!Et(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},E0=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return Br(e)?r(e):r(String(e).split(t)),n},C0=()=>{},A0=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t,ji="abcdefghijklmnopqrstuvwxyz",vc="0123456789",um={DIGIT:vc,ALPHA:ji,ALPHA_DIGIT:ji+ji.toUpperCase()+vc},S0=(e=16,t=um.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n};function w0(e){return!!(e&&Et(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const T0=e=>{const t=new Array(10),n=(r,s)=>{if(gi(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const o=Br(r)?[]:{};return Ds(r,(i,a)=>{const u=n(i,s+1);!Cs(u)&&(o[a]=u)}),t[s]=void 0,o}}return r};return n(e,0)},k0=qt("AsyncFunction"),x0=e=>e&&(gi(e)||Et(e))&&Et(e.then)&&Et(e.catch),lm=((e,t)=>e?setImmediate:t?((n,r)=>(Vn.addEventListener("message",({source:s,data:o})=>{s===Vn&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),Vn.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Et(Vn.postMessage)),O0=typeof queueMicrotask!="undefined"?queueMicrotask.bind(Vn):typeof process!="undefined"&&process.nextTick||lm;var M={isArray:Br,isArrayBuffer:rm,isBuffer:Kb,isFormData:t0,isArrayBufferView:Xb,isString:zb,isNumber:sm,isBoolean:Gb,isObject:gi,isPlainObject:uo,isReadableStream:r0,isRequest:s0,isResponse:o0,isHeaders:i0,isUndefined:Cs,isDate:Yb,isFile:Qb,isBlob:Jb,isRegExp:y0,isFunction:Et,isStream:e0,isURLSearchParams:n0,isTypedArray:m0,isFileList:Zb,forEach:Ds,merge:La,extend:u0,trim:a0,stripBOM:l0,inherits:c0,toFlatObject:f0,kindOf:mi,kindOfTest:qt,endsWith:d0,toArray:h0,forEachEntry:p0,matchAll:g0,isHTMLForm:_0,hasOwnProperty:_c,hasOwnProp:_c,reduceDescriptors:am,freezeMethods:b0,toObjectSet:E0,toCamelCase:v0,noop:C0,toFiniteNumber:A0,findKey:om,global:Vn,isContextDefined:im,ALPHABET:um,generateString:S0,isSpecCompliantForm:w0,toJSONObject:T0,isAsyncFn:k0,isThenable:x0,setImmediate:lm,asap:O0};function pe(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s)}M.inherits(pe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:M.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const cm=pe.prototype,fm={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{fm[e]={value:e}});Object.defineProperties(pe,fm);Object.defineProperty(cm,"isAxiosError",{value:!0});pe.from=(e,t,n,r,s,o)=>{const i=Object.create(cm);return M.toFlatObject(e,i,function(u){return u!==Error.prototype},a=>a!=="isAxiosError"),pe.call(i,e.message,t,n,r,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};var R0=null;function Ia(e){return M.isPlainObject(e)||M.isArray(e)}function dm(e){return M.endsWith(e,"[]")?e.slice(0,-2):e}function yc(e,t,n){return e?e.concat(t).map(function(s,o){return s=dm(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function P0(e){return M.isArray(e)&&!e.some(Ia)}const F0=M.toFlatObject(M,{},null,function(t){return/^is[A-Z]/.test(t)});function _i(e,t,n){if(!M.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=M.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(C,w){return!M.isUndefined(w[C])});const r=n.metaTokens,s=n.visitor||c,o=n.dots,i=n.indexes,u=(n.Blob||typeof Blob!="undefined"&&Blob)&&M.isSpecCompliantForm(t);if(!M.isFunction(s))throw new TypeError("visitor must be a function");function l(g){if(g===null)return"";if(M.isDate(g))return g.toISOString();if(!u&&M.isBlob(g))throw new pe("Blob is not supported. Use a Buffer instead.");return M.isArrayBuffer(g)||M.isTypedArray(g)?u&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function c(g,C,w){let R=g;if(g&&!w&&typeof g=="object"){if(M.endsWith(C,"{}"))C=r?C:C.slice(0,-2),g=JSON.stringify(g);else if(M.isArray(g)&&P0(g)||(M.isFileList(g)||M.endsWith(C,"[]"))&&(R=M.toArray(g)))return C=dm(C),R.forEach(function(h,p){!(M.isUndefined(h)||h===null)&&t.append(i===!0?yc([C],p,o):i===null?C:C+"[]",l(h))}),!1}return Ia(g)?!0:(t.append(yc(w,C,o),l(g)),!1)}const f=[],d=Object.assign(F0,{defaultVisitor:c,convertValue:l,isVisitable:Ia});function m(g,C){if(!M.isUndefined(g)){if(f.indexOf(g)!==-1)throw Error("Circular reference detected in "+C.join("."));f.push(g),M.forEach(g,function(R,E){(!(M.isUndefined(R)||R===null)&&s.call(t,R,M.isString(E)?E.trim():E,C,d))===!0&&m(R,C?C.concat(E):[E])}),f.pop()}}if(!M.isObject(e))throw new TypeError("data must be an object");return m(e),t}function bc(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Pu(e,t){this._pairs=[],e&&_i(e,this,t)}const hm=Pu.prototype;hm.append=function(t,n){this._pairs.push([t,n])};hm.toString=function(t){const n=t?function(r){return t.call(this,r,bc)}:bc;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function D0(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function mm(e,t,n){if(!t)return e;const r=n&&n.encode||D0,s=n&&n.serialize;let o;if(s?o=s(t,n):o=M.isURLSearchParams(t)?t.toString():new Pu(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class N0{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){M.forEach(this.handlers,function(r){r!==null&&t(r)})}}var Ec=N0,pm={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},L0=typeof URLSearchParams!="undefined"?URLSearchParams:Pu,I0=typeof FormData!="undefined"?FormData:null,B0=typeof Blob!="undefined"?Blob:null,M0={isBrowser:!0,classes:{URLSearchParams:L0,FormData:I0,Blob:B0},protocols:["http","https","file","blob","url","data"]};const Fu=typeof window!="undefined"&&typeof document!="undefined",$0=(e=>Fu&&["ReactNative","NativeScript","NS"].indexOf(e)<0)(typeof navigator!="undefined"&&navigator.product),U0=(()=>typeof WorkerGlobalScope!="undefined"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),V0=Fu&&window.location.href||"http://localhost";var q0=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Fu,hasStandardBrowserWebWorkerEnv:U0,hasStandardBrowserEnv:$0,origin:V0},Symbol.toStringTag,{value:"Module"})),Lt={...q0,...M0};function H0(e,t){return _i(e,new Lt.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,o){return Lt.isNode&&M.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function j0(e){return M.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function W0(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function gm(e){function t(n,r,s,o){let i=n[o++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),u=o>=n.length;return i=!i&&M.isArray(s)?s.length:i,u?(M.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!a):((!s[i]||!M.isObject(s[i]))&&(s[i]=[]),t(n,r,s[i],o)&&M.isArray(s[i])&&(s[i]=W0(s[i])),!a)}if(M.isFormData(e)&&M.isFunction(e.entries)){const n={};return M.forEachEntry(e,(r,s)=>{t(j0(r),s,n,0)}),n}return null}function K0(e,t,n){if(M.isString(e))try{return(t||JSON.parse)(e),M.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Du={transitional:pm,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=M.isObject(t);if(o&&M.isHTMLForm(t)&&(t=new FormData(t)),M.isFormData(t))return s?JSON.stringify(gm(t)):t;if(M.isArrayBuffer(t)||M.isBuffer(t)||M.isStream(t)||M.isFile(t)||M.isBlob(t)||M.isReadableStream(t))return t;if(M.isArrayBufferView(t))return t.buffer;if(M.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return H0(t,this.formSerializer).toString();if((a=M.isFileList(t))||r.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return _i(a?{"files[]":t}:t,u&&new u,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),K0(t)):t}],transformResponse:[function(t){const n=this.transitional||Du.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(M.isResponse(t)||M.isReadableStream(t))return t;if(t&&M.isString(t)&&(r&&!this.responseType||s)){const i=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?pe.from(a,pe.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Lt.classes.FormData,Blob:Lt.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};M.forEach(["delete","get","head","post","put","patch"],e=>{Du.headers[e]={}});var Nu=Du;const X0=M.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]);var z0=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),n=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!n||t[n]&&X0[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t};const Cc=Symbol("internals");function Wr(e){return e&&String(e).trim().toLowerCase()}function lo(e){return e===!1||e==null?e:M.isArray(e)?e.map(lo):String(e)}function G0(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Y0=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Wi(e,t,n,r,s){if(M.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!M.isString(t)){if(M.isString(r))return t.indexOf(r)!==-1;if(M.isRegExp(r))return r.test(t)}}function Q0(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function J0(e,t){const n=M.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,i){return this[r].call(this,t,s,o,i)},configurable:!0})})}class vi{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(a,u,l){const c=Wr(u);if(!c)throw new Error("header name must be a non-empty string");const f=M.findKey(s,c);(!f||s[f]===void 0||l===!0||l===void 0&&s[f]!==!1)&&(s[f||u]=lo(a))}const i=(a,u)=>M.forEach(a,(l,c)=>o(l,c,u));if(M.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(M.isString(t)&&(t=t.trim())&&!Y0(t))i(z0(t),n);else if(M.isHeaders(t))for(const[a,u]of t.entries())o(u,a,r);else t!=null&&o(n,t,r);return this}get(t,n){if(t=Wr(t),t){const r=M.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return G0(s);if(M.isFunction(n))return n.call(this,s,r);if(M.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Wr(t),t){const r=M.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Wi(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(i){if(i=Wr(i),i){const a=M.findKey(r,i);a&&(!n||Wi(r,r[a],a,n))&&(delete r[a],s=!0)}}return M.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||Wi(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return M.forEach(this,(s,o)=>{const i=M.findKey(r,o);if(i){n[i]=lo(s),delete n[o];return}const a=t?Q0(o):String(o).trim();a!==o&&delete n[o],n[a]=lo(s),r[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return M.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&M.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[Cc]=this[Cc]={accessors:{}}).accessors,s=this.prototype;function o(i){const a=Wr(i);r[a]||(J0(s,i),r[a]=!0)}return M.isArray(t)?t.forEach(o):o(t),this}}vi.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);M.reduceDescriptors(vi.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});M.freezeMethods(vi);var It=vi;function Ki(e,t){const n=this||Nu,r=t||n,s=It.from(r.headers);let o=r.data;return M.forEach(e,function(a){o=a.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function _m(e){return!!(e&&e.__CANCEL__)}function Mr(e,t,n){pe.call(this,e==null?"canceled":e,pe.ERR_CANCELED,t,n),this.name="CanceledError"}M.inherits(Mr,pe,{__CANCEL__:!0});function vm(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new pe("Request failed with status code "+n.status,[pe.ERR_BAD_REQUEST,pe.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Z0(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function eE(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(u){const l=Date.now(),c=r[o];i||(i=l),n[s]=u,r[s]=l;let f=o,d=0;for(;f!==s;)d+=n[f++],f=f%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),l-i<t)return;const m=c&&l-c;return m?Math.round(d*1e3/m):void 0}}function tE(e,t){let n=0,r=1e3/t,s,o;const i=(l,c=Date.now())=>{n=c,s=null,o&&(clearTimeout(o),o=null),e.apply(null,l)};return[(...l)=>{const c=Date.now(),f=c-n;f>=r?i(l,c):(s=l,o||(o=setTimeout(()=>{o=null,i(s)},r-f)))},()=>s&&i(s)]}const Bo=(e,t,n=3)=>{let r=0;const s=eE(50,250);return tE(o=>{const i=o.loaded,a=o.lengthComputable?o.total:void 0,u=i-r,l=s(u),c=i<=a;r=i;const f={loaded:i,total:a,progress:a?i/a:void 0,bytes:u,rate:l||void 0,estimated:l&&a&&c?(a-i)/l:void 0,event:o,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},n)},Ac=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Sc=e=>(...t)=>M.asap(()=>e(...t));var nE=Lt.hasStandardBrowserEnv?function(){const t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");let r;function s(o){let i=o;return t&&(n.setAttribute("href",i),i=n.href),n.setAttribute("href",i),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:n.pathname.charAt(0)==="/"?n.pathname:"/"+n.pathname}}return r=s(window.location.href),function(i){const a=M.isString(i)?s(i):i;return a.protocol===r.protocol&&a.host===r.host}}():function(){return function(){return!0}}(),rE=Lt.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const i=[e+"="+encodeURIComponent(t)];M.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),M.isString(r)&&i.push("path="+r),M.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function sE(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function oE(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function ym(e,t){return e&&!sE(t)?oE(e,t):t}const wc=e=>e instanceof It?{...e}:e;function Yn(e,t){t=t||{};const n={};function r(l,c,f){return M.isPlainObject(l)&&M.isPlainObject(c)?M.merge.call({caseless:f},l,c):M.isPlainObject(c)?M.merge({},c):M.isArray(c)?c.slice():c}function s(l,c,f){if(M.isUndefined(c)){if(!M.isUndefined(l))return r(void 0,l,f)}else return r(l,c,f)}function o(l,c){if(!M.isUndefined(c))return r(void 0,c)}function i(l,c){if(M.isUndefined(c)){if(!M.isUndefined(l))return r(void 0,l)}else return r(void 0,c)}function a(l,c,f){if(f in t)return r(l,c);if(f in e)return r(void 0,l)}const u={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(l,c)=>s(wc(l),wc(c),!0)};return M.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=u[c]||s,d=f(e[c],t[c],c);M.isUndefined(d)&&f!==a||(n[c]=d)}),n}var bm=e=>{const t=Yn({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:a}=t;t.headers=i=It.from(i),t.url=mm(ym(t.baseURL,t.url),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let u;if(M.isFormData(n)){if(Lt.hasStandardBrowserEnv||Lt.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((u=i.getContentType())!==!1){const[l,...c]=u?u.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([l||"multipart/form-data",...c].join("; "))}}if(Lt.hasStandardBrowserEnv&&(r&&M.isFunction(r)&&(r=r(t)),r||r!==!1&&nE(t.url))){const l=s&&o&&rE.read(o);l&&i.set(s,l)}return t};const iE=typeof XMLHttpRequest!="undefined";var aE=iE&&function(e){return new Promise(function(n,r){const s=bm(e);let o=s.data;const i=It.from(s.headers).normalize();let{responseType:a,onUploadProgress:u,onDownloadProgress:l}=s,c,f,d,m,g;function C(){m&&m(),g&&g(),s.cancelToken&&s.cancelToken.unsubscribe(c),s.signal&&s.signal.removeEventListener("abort",c)}let w=new XMLHttpRequest;w.open(s.method.toUpperCase(),s.url,!0),w.timeout=s.timeout;function R(){if(!w)return;const h=It.from("getAllResponseHeaders"in w&&w.getAllResponseHeaders()),b={data:!a||a==="text"||a==="json"?w.responseText:w.response,status:w.status,statusText:w.statusText,headers:h,config:e,request:w};vm(function(x){n(x),C()},function(x){r(x),C()},b),w=null}"onloadend"in w?w.onloadend=R:w.onreadystatechange=function(){!w||w.readyState!==4||w.status===0&&!(w.responseURL&&w.responseURL.indexOf("file:")===0)||setTimeout(R)},w.onabort=function(){!w||(r(new pe("Request aborted",pe.ECONNABORTED,e,w)),w=null)},w.onerror=function(){r(new pe("Network Error",pe.ERR_NETWORK,e,w)),w=null},w.ontimeout=function(){let p=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const b=s.transitional||pm;s.timeoutErrorMessage&&(p=s.timeoutErrorMessage),r(new pe(p,b.clarifyTimeoutError?pe.ETIMEDOUT:pe.ECONNABORTED,e,w)),w=null},o===void 0&&i.setContentType(null),"setRequestHeader"in w&&M.forEach(i.toJSON(),function(p,b){w.setRequestHeader(b,p)}),M.isUndefined(s.withCredentials)||(w.withCredentials=!!s.withCredentials),a&&a!=="json"&&(w.responseType=s.responseType),l&&([d,g]=Bo(l,!0),w.addEventListener("progress",d)),u&&w.upload&&([f,m]=Bo(u),w.upload.addEventListener("progress",f),w.upload.addEventListener("loadend",m)),(s.cancelToken||s.signal)&&(c=h=>{!w||(r(!h||h.type?new Mr(null,e,w):h),w.abort(),w=null)},s.cancelToken&&s.cancelToken.subscribe(c),s.signal&&(s.signal.aborted?c():s.signal.addEventListener("abort",c)));const E=Z0(s.url);if(E&&Lt.protocols.indexOf(E)===-1){r(new pe("Unsupported protocol "+E+":",pe.ERR_BAD_REQUEST,e));return}w.send(o||null)})};const uE=(e,t)=>{let n=new AbortController,r;const s=function(u){if(!r){r=!0,i();const l=u instanceof Error?u:this.reason;n.abort(l instanceof pe?l:new Mr(l instanceof Error?l.message:l))}};let o=t&&setTimeout(()=>{s(new pe(`timeout ${t} of ms exceeded`,pe.ETIMEDOUT))},t);const i=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(u=>{u&&(u.removeEventListener?u.removeEventListener("abort",s):u.unsubscribe(s))}),e=null)};e.forEach(u=>u&&u.addEventListener&&u.addEventListener("abort",s));const{signal:a}=n;return a.unsubscribe=i,[a,()=>{o&&clearTimeout(o),o=null}]};var lE=uE;const cE=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},fE=async function*(e,t,n){for await(const r of e)yield*cE(ArrayBuffer.isView(r)?r:await n(String(r)),t)},Tc=(e,t,n,r,s)=>{const o=fE(e,t,s);let i=0,a,u=l=>{a||(a=!0,r&&r(l))};return new ReadableStream({async pull(l){try{const{done:c,value:f}=await o.next();if(c){u(),l.close();return}let d=f.byteLength;if(n){let m=i+=d;n(m)}l.enqueue(new Uint8Array(f))}catch(c){throw u(c),c}},cancel(l){return u(l),o.return()}},{highWaterMark:2})},yi=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Em=yi&&typeof ReadableStream=="function",Ba=yi&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Cm=(e,...t)=>{try{return!!e(...t)}catch{return!1}},dE=Em&&Cm(()=>{let e=!1;const t=new Request(Lt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),kc=64*1024,Ma=Em&&Cm(()=>M.isReadableStream(new Response("").body)),Mo={stream:Ma&&(e=>e.body)};yi&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Mo[t]&&(Mo[t]=M.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new pe(`Response type '${t}' is not supported`,pe.ERR_NOT_SUPPORT,r)})})})(new Response);const hE=async e=>{if(e==null)return 0;if(M.isBlob(e))return e.size;if(M.isSpecCompliantForm(e))return(await new Request(e).arrayBuffer()).byteLength;if(M.isArrayBufferView(e)||M.isArrayBuffer(e))return e.byteLength;if(M.isURLSearchParams(e)&&(e=e+""),M.isString(e))return(await Ba(e)).byteLength},mE=async(e,t)=>{const n=M.toFiniteNumber(e.getContentLength());return n==null?hE(t):n};var pE=yi&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:a,onUploadProgress:u,responseType:l,headers:c,withCredentials:f="same-origin",fetchOptions:d}=bm(e);l=l?(l+"").toLowerCase():"text";let[m,g]=s||o||i?lE([s,o],i):[],C,w;const R=()=>{!C&&setTimeout(()=>{m&&m.unsubscribe()}),C=!0};let E;try{if(u&&dE&&n!=="get"&&n!=="head"&&(E=await mE(c,r))!==0){let S=new Request(t,{method:"POST",body:r,duplex:"half"}),x;if(M.isFormData(r)&&(x=S.headers.get("content-type"))&&c.setContentType(x),S.body){const[F,v]=Ac(E,Bo(Sc(u)));r=Tc(S.body,kc,F,v,Ba)}}M.isString(f)||(f=f?"include":"omit"),w=new Request(t,{...d,signal:m,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:f});let h=await fetch(w);const p=Ma&&(l==="stream"||l==="response");if(Ma&&(a||p)){const S={};["status","statusText","headers"].forEach(O=>{S[O]=h[O]});const x=M.toFiniteNumber(h.headers.get("content-length")),[F,v]=a&&Ac(x,Bo(Sc(a),!0))||[];h=new Response(Tc(h.body,kc,F,()=>{v&&v(),p&&R()},Ba),S)}l=l||"text";let b=await Mo[M.findKey(Mo,l)||"text"](h,e);return!p&&R(),g&&g(),await new Promise((S,x)=>{vm(S,x,{data:b,headers:It.from(h.headers),status:h.status,statusText:h.statusText,config:e,request:w})})}catch(h){throw R(),h&&h.name==="TypeError"&&/fetch/i.test(h.message)?Object.assign(new pe("Network Error",pe.ERR_NETWORK,e,w),{cause:h.cause||h}):pe.from(h,h&&h.code,e,w)}});const $a={http:R0,xhr:aE,fetch:pE};M.forEach($a,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const xc=e=>`- ${e}`,gE=e=>M.isFunction(e)||e===null||e===!1;var Am={getAdapter:e=>{e=M.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!gE(n)&&(r=$a[(i=String(n)).toLowerCase()],r===void 0))throw new pe(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([a,u])=>`adapter ${a} `+(u===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(xc).join(`
`):" "+xc(o[0]):"as no adapter specified";throw new pe("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:$a};function Xi(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Mr(null,e)}function Oc(e){return Xi(e),e.headers=It.from(e.headers),e.data=Ki.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Am.getAdapter(e.adapter||Nu.adapter)(e).then(function(r){return Xi(e),r.data=Ki.call(e,e.transformResponse,r),r.headers=It.from(r.headers),r},function(r){return _m(r)||(Xi(e),r&&r.response&&(r.response.data=Ki.call(e,e.transformResponse,r.response),r.response.headers=It.from(r.response.headers))),Promise.reject(r)})}const Sm="1.7.4",Lu={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Lu[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Rc={};Lu.transitional=function(t,n,r){function s(o,i){return"[Axios v"+Sm+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,a)=>{if(t===!1)throw new pe(s(i," has been removed"+(n?" in "+n:"")),pe.ERR_DEPRECATED);return n&&!Rc[i]&&(Rc[i]=!0,console.warn(s(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,a):!0}};function _E(e,t,n){if(typeof e!="object")throw new pe("options must be an object",pe.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],i=t[o];if(i){const a=e[o],u=a===void 0||i(a,o,e);if(u!==!0)throw new pe("option "+o+" must be "+u,pe.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new pe("Unknown option "+o,pe.ERR_BAD_OPTION)}}var Ua={assertOptions:_E,validators:Lu};const mn=Ua.validators;class $o{constructor(t){this.defaults=t,this.interceptors={request:new Ec,response:new Ec}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s;Error.captureStackTrace?Error.captureStackTrace(s={}):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Yn(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&Ua.assertOptions(r,{silentJSONParsing:mn.transitional(mn.boolean),forcedJSONParsing:mn.transitional(mn.boolean),clarifyTimeoutError:mn.transitional(mn.boolean)},!1),s!=null&&(M.isFunction(s)?n.paramsSerializer={serialize:s}:Ua.assertOptions(s,{encode:mn.function,serialize:mn.function},!0)),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&M.merge(o.common,o[n.method]);o&&M.forEach(["delete","get","head","post","put","patch","common"],g=>{delete o[g]}),n.headers=It.concat(i,o);const a=[];let u=!0;this.interceptors.request.forEach(function(C){typeof C.runWhen=="function"&&C.runWhen(n)===!1||(u=u&&C.synchronous,a.unshift(C.fulfilled,C.rejected))});const l=[];this.interceptors.response.forEach(function(C){l.push(C.fulfilled,C.rejected)});let c,f=0,d;if(!u){const g=[Oc.bind(this),void 0];for(g.unshift.apply(g,a),g.push.apply(g,l),d=g.length,c=Promise.resolve(n);f<d;)c=c.then(g[f++],g[f++]);return c}d=a.length;let m=n;for(f=0;f<d;){const g=a[f++],C=a[f++];try{m=g(m)}catch(w){C.call(this,w);break}}try{c=Oc.call(this,m)}catch(g){return Promise.reject(g)}for(f=0,d=l.length;f<d;)c=c.then(l[f++],l[f++]);return c}getUri(t){t=Yn(this.defaults,t);const n=ym(t.baseURL,t.url);return mm(n,t.params,t.paramsSerializer)}}M.forEach(["delete","get","head","options"],function(t){$o.prototype[t]=function(n,r){return this.request(Yn(r||{},{method:t,url:n,data:(r||{}).data}))}});M.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,a){return this.request(Yn(a||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}$o.prototype[t]=n(),$o.prototype[t+"Form"]=n(!0)});var co=$o;class Iu{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(a=>{r.subscribe(a),o=a}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,a){r.reason||(r.reason=new Mr(o,i,a),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}static source(){let t;return{token:new Iu(function(s){t=s}),cancel:t}}}var vE=Iu;function yE(e){return function(n){return e.apply(null,n)}}function bE(e){return M.isObject(e)&&e.isAxiosError===!0}const Va={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Va).forEach(([e,t])=>{Va[t]=e});var EE=Va;function wm(e){const t=new co(e),n=nm(co.prototype.request,t);return M.extend(n,co.prototype,t,{allOwnKeys:!0}),M.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return wm(Yn(e,s))},n}const Ve=wm(Nu);Ve.Axios=co;Ve.CanceledError=Mr;Ve.CancelToken=vE;Ve.isCancel=_m;Ve.VERSION=Sm;Ve.toFormData=_i;Ve.AxiosError=pe;Ve.Cancel=Ve.CanceledError;Ve.all=function(t){return Promise.all(t)};Ve.spread=yE;Ve.isAxiosError=bE;Ve.mergeConfig=Yn;Ve.AxiosHeaders=It;Ve.formToJSON=e=>gm(M.isHTMLForm(e)?new FormData(e):e);Ve.getAdapter=Am.getAdapter;Ve.HttpStatusCode=EE;Ve.default=Ve;var Bu=Ve;const{Axios:b1,AxiosError:CE,CanceledError:E1,isCancel:C1,CancelToken:A1,VERSION:S1,all:w1,Cancel:T1,isAxiosError:k1,spread:x1,toFormData:O1,AxiosHeaders:R1,HttpStatusCode:P1,formToJSON:F1,getAdapter:D1,mergeConfig:N1}=Bu,Tm={login:e=>Jr.post("v1/auth/login",e),logout:()=>Jr.post("v1/auth/logout"),refreshToken:()=>Jr.post("v1/auth/refresh-token"),verifyUser:e=>Jr.post("v1/auth/verify-user",e)};function AE(e,t,n){let r;function s(){r!==void 0&&(wa.remove(r),r=void 0)}return xt(()=>{e.value===!0&&s()}),{removeFromHistory:s,addToHistory(){r={condition:()=>n.value===!0,handler:t},wa.add(r)}}}function SE(){let e=null;const t=Ee();function n(){e!==null&&(clearTimeout(e),e=null)}return ii(n),xt(n),{removeTimeout:n,registerTimeout(r,s){n(),qh(t)===!1&&(e=setTimeout(()=>{e=null,r()},s))}}}function wE(){let e;const t=Ee();function n(){e=void 0}return ii(n),xt(n),{removeTick:n,registerTick(r){e=r,rt(()=>{e===r&&(qh(t)===!1&&e(),e=void 0)})}}}const TE={modelValue:{type:Boolean,default:null},"onUpdate:modelValue":[Function,Array]},kE=["beforeShow","show","beforeHide","hide"];function xE({showing:e,canShow:t,hideOnRouteChange:n,handleShow:r,handleHide:s,processOnMount:o}){const i=Ee(),{props:a,emit:u,proxy:l}=i;let c;function f(E){e.value===!0?g(E):d(E)}function d(E){if(a.disable===!0||E!==void 0&&E.qAnchorHandled===!0||t!==void 0&&t(E)!==!0)return;const h=a["onUpdate:modelValue"]!==void 0;h===!0&&(u("update:modelValue",!0),c=E,rt(()=>{c===E&&(c=void 0)})),(a.modelValue===null||h===!1)&&m(E)}function m(E){e.value!==!0&&(e.value=!0,u("beforeShow",E),r!==void 0?r(E):u("show",E))}function g(E){if(a.disable===!0)return;const h=a["onUpdate:modelValue"]!==void 0;h===!0&&(u("update:modelValue",!1),c=E,rt(()=>{c===E&&(c=void 0)})),(a.modelValue===null||h===!1)&&C(E)}function C(E){e.value!==!1&&(e.value=!1,u("beforeHide",E),s!==void 0?s(E):u("hide",E))}function w(E){a.disable===!0&&E===!0?a["onUpdate:modelValue"]!==void 0&&u("update:modelValue",!1):E===!0!==e.value&&(E===!0?m:C)(c)}Ae(()=>a.modelValue,w),n!==void 0&&Vh(i)===!0&&Ae(()=>l.$route.fullPath,()=>{n.value===!0&&e.value===!0&&g()}),o===!0&&Vt(()=>{w(a.modelValue)});const R={show:d,hide:g,toggle:f};return Object.assign(l,R),R}const OE={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function RE(e,t=()=>{},n=()=>{}){return{transitionProps:$(()=>{const r=`q-transition--${e.transitionShow||t()}`,s=`q-transition--${e.transitionHide||n()}`;return{appear:!0,enterFromClass:`${r}-enter-from`,enterActiveClass:`${r}-enter-active`,enterToClass:`${r}-enter-to`,leaveFromClass:`${s}-leave-from`,leaveActiveClass:`${s}-leave-active`,leaveToClass:`${s}-leave-to`}}),transitionStyle:$(()=>`--q-transition-duration: ${e.transitionDuration}ms`)}}let qn=[],As=[];function km(e){As=As.filter(t=>t!==e)}function PE(e){km(e),As.push(e)}function Pc(e){km(e),As.length===0&&qn.length!==0&&(qn[qn.length-1](),qn=[])}function Mu(e){As.length===0?e():qn.push(e)}function FE(e){qn=qn.filter(t=>t!==e)}const fo=[];function L1(e){return fo.find(t=>t.contentEl!==null&&t.contentEl.contains(e))}function DE(e,t){do{if(e.$options.name==="QMenu"){if(e.hide(t),e.$props.separateClosePopup===!0)return so(e)}else if(e.__qPortal===!0){const n=so(e);return n!==void 0&&n.$options.name==="QPopupProxy"?(e.hide(t),n):e}e=so(e)}while(e!=null)}function I1(e,t,n){for(;n!==0&&e!==void 0&&e!==null;){if(e.__qPortal===!0){if(n--,e.$options.name==="QMenu"){e=DE(e,t);continue}e.hide(t)}e=so(e)}}const NE=Ze({name:"QPortal",setup(e,{slots:t}){return()=>t.default()}});function LE(e){for(e=e.parent;e!=null;){if(e.type.name==="QGlobalDialog")return!0;if(e.type.name==="QDialog"||e.type.name==="QMenu")return!1;e=e.parent}return!1}function IE(e,t,n,r){const s=ge(!1),o=ge(!1);let i=null;const a={},u=r==="dialog"&&LE(e);function l(f){if(f===!0){Pc(a),o.value=!0;return}o.value=!1,s.value===!1&&(u===!1&&i===null&&(i=ku(!1,r)),s.value=!0,fo.push(e.proxy),PE(a))}function c(f){if(o.value=!1,f!==!0)return;Pc(a),s.value=!1;const d=fo.indexOf(e.proxy);d!==-1&&fo.splice(d,1),i!==null&&(Wh(i),i=null)}return Ir(()=>{c(!0)}),e.proxy.__qPortal=!0,er(e.proxy,"contentEl",()=>t.value),{showPortal:l,hidePortal:c,portalIsActive:s,portalIsAccessible:o,renderPortal:()=>u===!0?n():s.value===!0?[q(vg,{to:i},q(NE,n))]:void 0}}const B1=[Element,String],BE=[null,document,document.body,document.scrollingElement,document.documentElement];function M1(e,t){let n=nb(t);if(n===void 0){if(e==null)return window;n=e.closest(".scroll,.scroll-y,.overflow-auto")}return BE.includes(n)?window:n}function xm(e){return e===window?window.pageYOffset||window.scrollY||document.body.scrollTop||0:e.scrollTop}function Om(e){return e===window?window.pageXOffset||window.scrollX||document.body.scrollLeft||0:e.scrollLeft}function Rm(e,t,n=0){const r=arguments[3]===void 0?performance.now():arguments[3],s=xm(e);if(n<=0){s!==t&&qa(e,t);return}requestAnimationFrame(o=>{const i=o-r,a=s+(t-s)/Math.max(i,n)*i;qa(e,a),a!==t&&Rm(e,t,n-i,o)})}function Pm(e,t,n=0){const r=arguments[3]===void 0?performance.now():arguments[3],s=Om(e);if(n<=0){s!==t&&Ha(e,t);return}requestAnimationFrame(o=>{const i=o-r,a=s+(t-s)/Math.max(i,n)*i;Ha(e,a),a!==t&&Pm(e,t,n-i,o)})}function qa(e,t){if(e===window){window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,t);return}e.scrollTop=t}function Ha(e,t){if(e===window){window.scrollTo(t,window.pageYOffset||window.scrollY||document.body.scrollTop||0);return}e.scrollLeft=t}function $1(e,t,n){if(n){Rm(e,t,n);return}qa(e,t)}function U1(e,t,n){if(n){Pm(e,t,n);return}Ha(e,t)}let Ws;function V1(){if(Ws!==void 0)return Ws;const e=document.createElement("p"),t=document.createElement("div");Pa(e,{width:"100%",height:"200px"}),Pa(t,{position:"absolute",top:"0px",left:"0px",visibility:"hidden",width:"200px",height:"150px",overflow:"hidden"}),t.appendChild(e),document.body.appendChild(t);const n=e.offsetWidth;t.style.overflow="scroll";let r=e.offsetWidth;return n===r&&(r=t.clientWidth),t.remove(),Ws=n-r,Ws}function ME(e,t=!0){return!e||e.nodeType!==Node.ELEMENT_NODE?!1:t?e.scrollHeight>e.clientHeight&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-y"])):e.scrollWidth>e.clientWidth&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-x"]))}let Kr=0,zi,Gi,Qr,Yi=!1,Fc,Dc,Nc,Bn=null;function $E(e){UE(e)&&St(e)}function UE(e){if(e.target===document.body||e.target.classList.contains("q-layout__backdrop"))return!0;const t=bv(e),n=e.shiftKey&&!e.deltaX,r=!n&&Math.abs(e.deltaX)<=Math.abs(e.deltaY),s=n||r?e.deltaY:e.deltaX;for(let o=0;o<t.length;o++){const i=t[o];if(ME(i,r))return r?s<0&&i.scrollTop===0?!0:s>0&&i.scrollTop+i.clientHeight===i.scrollHeight:s<0&&i.scrollLeft===0?!0:s>0&&i.scrollLeft+i.clientWidth===i.scrollWidth}return!0}function Lc(e){e.target===document&&(document.scrollingElement.scrollTop=document.scrollingElement.scrollTop)}function Ks(e){Yi!==!0&&(Yi=!0,requestAnimationFrame(()=>{Yi=!1;const{height:t}=e.target,{clientHeight:n,scrollTop:r}=document.scrollingElement;(Qr===void 0||t!==window.innerHeight)&&(Qr=n-t,document.scrollingElement.scrollTop=r),r>Qr&&(document.scrollingElement.scrollTop-=Math.ceil((r-Qr)/8))}))}function Ic(e){const t=document.body,n=window.visualViewport!==void 0;if(e==="add"){const{overflowY:r,overflowX:s}=window.getComputedStyle(t);zi=Om(window),Gi=xm(window),Fc=t.style.left,Dc=t.style.top,Nc=window.location.href,t.style.left=`-${zi}px`,t.style.top=`-${Gi}px`,s!=="hidden"&&(s==="scroll"||t.scrollWidth>window.innerWidth)&&t.classList.add("q-body--force-scrollbar-x"),r!=="hidden"&&(r==="scroll"||t.scrollHeight>window.innerHeight)&&t.classList.add("q-body--force-scrollbar-y"),t.classList.add("q-body--prevent-scroll"),document.qScrollPrevented=!0,Xe.is.ios===!0&&(n===!0?(window.scrollTo(0,0),window.visualViewport.addEventListener("resize",Ks,pt.passiveCapture),window.visualViewport.addEventListener("scroll",Ks,pt.passiveCapture),window.scrollTo(0,0)):window.addEventListener("scroll",Lc,pt.passiveCapture))}Xe.is.desktop===!0&&Xe.is.mac===!0&&window[`${e}EventListener`]("wheel",$E,pt.notPassive),e==="remove"&&(Xe.is.ios===!0&&(n===!0?(window.visualViewport.removeEventListener("resize",Ks,pt.passiveCapture),window.visualViewport.removeEventListener("scroll",Ks,pt.passiveCapture)):window.removeEventListener("scroll",Lc,pt.passiveCapture)),t.classList.remove("q-body--prevent-scroll"),t.classList.remove("q-body--force-scrollbar-x"),t.classList.remove("q-body--force-scrollbar-y"),document.qScrollPrevented=!1,t.style.left=Fc,t.style.top=Dc,window.location.href===Nc&&window.scrollTo(zi,Gi),Qr=void 0)}function VE(e){let t="add";if(e===!0){if(Kr++,Bn!==null){clearTimeout(Bn),Bn=null;return}if(Kr>1)return}else{if(Kr===0||(Kr--,Kr>0))return;if(t="remove",Xe.is.ios===!0&&Xe.is.nativeMobile===!0){Bn!==null&&clearTimeout(Bn),Bn=setTimeout(()=>{Ic(t),Bn=null},100);return}}Ic(t)}function qE(){let e;return{preventBodyScroll(t){t!==e&&(e!==void 0||t===!0)&&(e=t,VE(t))}}}const Wn=[];let Pr;function HE(e){Pr=e.keyCode===27}function jE(){Pr===!0&&(Pr=!1)}function WE(e){Pr===!0&&(Pr=!1,vs(e,27)===!0&&Wn[Wn.length-1](e))}function Fm(e){window[e]("keydown",HE),window[e]("blur",jE),window[e]("keyup",WE),Pr=!1}function KE(e){Xe.is.desktop===!0&&(Wn.push(e),Wn.length===1&&Fm("addEventListener"))}function Bc(e){const t=Wn.indexOf(e);t!==-1&&(Wn.splice(t,1),Wn.length===0&&Fm("removeEventListener"))}const Kn=[];function Dm(e){Kn[Kn.length-1](e)}function XE(e){Xe.is.desktop===!0&&(Kn.push(e),Kn.length===1&&document.body.addEventListener("focusin",Dm))}function Mc(e){const t=Kn.indexOf(e);t!==-1&&(Kn.splice(t,1),Kn.length===0&&document.body.removeEventListener("focusin",Dm))}let Xs=0;const zE={standard:"fixed-full flex-center",top:"fixed-top justify-center",bottom:"fixed-bottom justify-center",right:"fixed-right items-center",left:"fixed-left items-center"},$c={standard:["scale","scale"],top:["slide-down","slide-up"],bottom:["slide-up","slide-down"],right:["slide-left","slide-right"],left:["slide-right","slide-left"]};var GE=Ze({name:"QDialog",inheritAttrs:!1,props:{...TE,...OE,transitionShow:String,transitionHide:String,persistent:Boolean,autoClose:Boolean,allowFocusOutside:Boolean,noEscDismiss:Boolean,noBackdropDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,noShake:Boolean,seamless:Boolean,maximized:Boolean,fullWidth:Boolean,fullHeight:Boolean,square:Boolean,backdropFilter:String,position:{type:String,default:"standard",validator:e=>["standard","top","bottom","left","right"].includes(e)}},emits:[...kE,"shake","click","escapeKey"],setup(e,{slots:t,emit:n,attrs:r}){const s=Ee(),o=ge(null),i=ge(!1),a=ge(!1);let u=null,l=null,c,f;const d=$(()=>e.persistent!==!0&&e.noRouteDismiss!==!0&&e.seamless!==!0),{preventBodyScroll:m}=qE(),{registerTimeout:g}=SE(),{registerTick:C,removeTick:w}=wE(),{transitionProps:R,transitionStyle:E}=RE(e,()=>$c[e.position][0],()=>$c[e.position][1]),h=$(()=>E.value+(e.backdropFilter!==void 0?`;backdrop-filter:${e.backdropFilter};-webkit-backdrop-filter:${e.backdropFilter}`:"")),{showPortal:p,hidePortal:b,portalIsAccessible:S,renderPortal:x}=IE(s,o,Ie,"dialog"),{hide:F}=xE({showing:i,hideOnRouteChange:d,handleShow:Z,handleHide:W,processOnMount:!0}),{addToHistory:v,removeFromHistory:O}=AE(i,F,d),I=$(()=>`q-dialog__inner flex no-pointer-events q-dialog__inner--${e.maximized===!0?"maximized":"minimized"} q-dialog__inner--${e.position} ${zE[e.position]}`+(a.value===!0?" q-dialog__inner--animating":"")+(e.fullWidth===!0?" q-dialog__inner--fullwidth":"")+(e.fullHeight===!0?" q-dialog__inner--fullheight":"")+(e.square===!0?" q-dialog__inner--square":"")),T=$(()=>i.value===!0&&e.seamless!==!0),H=$(()=>e.autoClose===!0?{onClick:z}:{}),L=$(()=>[`q-dialog fullscreen no-pointer-events q-dialog--${T.value===!0?"modal":"seamless"}`,r.class]);Ae(()=>e.maximized,oe=>{i.value===!0&&de(oe)}),Ae(T,oe=>{m(oe),oe===!0?(XE(Pe),KE(se)):(Mc(Pe),Bc(se))});function Z(oe){v(),l=e.noRefocus===!1&&document.activeElement!==null?document.activeElement:null,de(e.maximized),p(),a.value=!0,e.noFocus!==!0?(document.activeElement!==null&&document.activeElement.blur(),C(Q)):w(),g(()=>{if(s.proxy.$q.platform.is.ios===!0){if(e.seamless!==!0&&document.activeElement){const{top:B,bottom:te}=document.activeElement.getBoundingClientRect(),{innerHeight:ee}=window,re=window.visualViewport!==void 0?window.visualViewport.height:ee;B>0&&te>re/2&&(document.scrollingElement.scrollTop=Math.min(document.scrollingElement.scrollHeight-re,te>=ee?1/0:Math.ceil(document.scrollingElement.scrollTop+te-re/2))),document.activeElement.scrollIntoView()}f=!0,o.value.click(),f=!1}p(!0),a.value=!1,n("show",oe)},e.transitionDuration)}function W(oe){w(),O(),he(!0),a.value=!0,b(),l!==null&&(((oe&&oe.type.indexOf("key")===0?l.closest('[tabindex]:not([tabindex^="-"])'):void 0)||l).focus(),l=null),g(()=>{b(!0),a.value=!1,n("hide",oe)},e.transitionDuration)}function Q(oe){Mu(()=>{let B=o.value;if(B!==null){if(oe!==void 0){const te=B.querySelector(oe);if(te!==null){te.focus({preventScroll:!0});return}}B.contains(document.activeElement)!==!0&&(B=B.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||B.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||B.querySelector("[autofocus], [data-autofocus]")||B,B.focus({preventScroll:!0}))}})}function V(oe){oe&&typeof oe.focus=="function"?oe.focus({preventScroll:!0}):Q(),n("shake");const B=o.value;B!==null&&(B.classList.remove("q-animate--scale"),B.classList.add("q-animate--scale"),u!==null&&clearTimeout(u),u=setTimeout(()=>{u=null,o.value!==null&&(B.classList.remove("q-animate--scale"),Q())},170))}function se(){e.seamless!==!0&&(e.persistent===!0||e.noEscDismiss===!0?e.maximized!==!0&&e.noShake!==!0&&V():(n("escapeKey"),F()))}function he(oe){u!==null&&(clearTimeout(u),u=null),(oe===!0||i.value===!0)&&(de(!1),e.seamless!==!0&&(m(!1),Mc(Pe),Bc(se))),oe!==!0&&(l=null)}function de(oe){oe===!0?c!==!0&&(Xs<1&&document.body.classList.add("q-body--dialog"),Xs++,c=!0):c===!0&&(Xs<2&&document.body.classList.remove("q-body--dialog"),Xs--,c=!1)}function z(oe){f!==!0&&(F(oe),n("click",oe))}function _e(oe){e.persistent!==!0&&e.noBackdropDismiss!==!0?F(oe):e.noShake!==!0&&V()}function Pe(oe){e.allowFocusOutside!==!0&&S.value===!0&&rb(o.value,oe.target)!==!0&&Q('[tabindex]:not([tabindex="-1"])')}Object.assign(s.proxy,{focus:Q,shake:V,__updateRefocusTarget(oe){l=oe||null}}),xt(he);function Ie(){return q("div",{role:"dialog","aria-modal":T.value===!0?"true":"false",...r,class:L.value},[q(xo,{name:"q-transition--fade",appear:!0},()=>T.value===!0?q("div",{class:"q-dialog__backdrop fixed-full",style:h.value,"aria-hidden":"true",tabindex:-1,onClick:_e}):null),q(xo,R.value,()=>i.value===!0?q("div",{ref:o,class:I.value,style:E.value,tabindex:-1,...H.value},Ft(t.default)):null)])}return x}});const tr={dark:{type:Boolean,default:null}};function nr(e,t){return $(()=>e.dark===null?t.dark.isActive:e.dark)}var YE=Ze({name:"QCard",props:{...tr,tag:{type:String,default:"div"},square:Boolean,flat:Boolean,bordered:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=Ee(),r=nr(e,n),s=$(()=>"q-card"+(r.value===!0?" q-card--dark q-dark":"")+(e.bordered===!0?" q-card--bordered":"")+(e.square===!0?" q-card--square no-border-radius":"")+(e.flat===!0?" q-card--flat no-shadow":""));return()=>q(e.tag,{class:s.value},Ft(t.default))}}),Xr=Ze({name:"QCardSection",props:{tag:{type:String,default:"div"},horizontal:Boolean},setup(e,{slots:t}){const n=$(()=>`q-card__section q-card__section--${e.horizontal===!0?"horiz row no-wrap":"vert"}`);return()=>q(e.tag,{class:n.value},Ft(t.default))}}),QE=Ze({name:"QCardActions",props:{...Mh,vertical:Boolean},setup(e,{slots:t}){const n=$h(e),r=$(()=>`q-card__actions ${n.value} q-card__actions--${e.vertical===!0?"vert column":"horiz row"}`);return()=>q("div",{class:r.value},Ft(t.default))}});const JE={true:"inset",item:"item-inset","item-thumbnail":"item-thumbnail-inset"},Qi={xs:2,sm:4,md:8,lg:16,xl:24};var Uc=Ze({name:"QSeparator",props:{...tr,spaced:[Boolean,String],inset:[Boolean,String],vertical:Boolean,color:String,size:String},setup(e){const t=Ee(),n=nr(e,t.proxy.$q),r=$(()=>e.vertical===!0?"vertical":"horizontal"),s=$(()=>` q-separator--${r.value}`),o=$(()=>e.inset!==!1?`${s.value}-${JE[e.inset]}`:""),i=$(()=>`q-separator${s.value}${o.value}`+(e.color!==void 0?` bg-${e.color}`:"")+(n.value===!0?" q-separator--dark":"")),a=$(()=>{const u={};if(e.size!==void 0&&(u[e.vertical===!0?"width":"height"]=e.size),e.spaced!==!1){const l=e.spaced===!0?`${Qi.md}px`:e.spaced in Qi?`${Qi[e.spaced]}px`:e.spaced,c=e.vertical===!0?["Left","Right"]:["Top","Bottom"];u[`margin${c[0]}`]=u[`margin${c[1]}`]=l}return u});return()=>q("hr",{class:i.value,style:a.value,"aria-orientation":r.value})}});let Ji,zs=0;const Je=new Array(256);for(let e=0;e<256;e++)Je[e]=(e+256).toString(16).substring(1);const ZE=(()=>{const e=typeof crypto!="undefined"?crypto:typeof window!="undefined"?window.crypto||window.msCrypto:void 0;if(e!==void 0){if(e.randomBytes!==void 0)return e.randomBytes;if(e.getRandomValues!==void 0)return t=>{const n=new Uint8Array(t);return e.getRandomValues(n),n}}return t=>{const n=[];for(let r=t;r>0;r--)n.push(Math.floor(Math.random()*256));return n}})(),Vc=4096;function ja(){(Ji===void 0||zs+16>Vc)&&(zs=0,Ji=ZE(Vc));const e=Array.prototype.slice.call(Ji,zs,zs+=16);return e[6]=e[6]&15|64,e[8]=e[8]&63|128,Je[e[0]]+Je[e[1]]+Je[e[2]]+Je[e[3]]+"-"+Je[e[4]]+Je[e[5]]+"-"+Je[e[6]]+Je[e[7]]+"-"+Je[e[8]]+Je[e[9]]+"-"+Je[e[10]]+Je[e[11]]+Je[e[12]]+Je[e[13]]+Je[e[14]]+Je[e[15]]}function eC(e){return e==null?null:e}function qc(e,t){return e==null?t===!0?`f_${ja()}`:null:e}function tC({getValue:e,required:t=!0}={}){if(Pn.value===!0){const n=ge(e!==void 0?eC(e()):null);return t===!0&&n.value===null&&Vt(()=>{n.value=`f_${ja()}`}),e!==void 0&&Ae(e,r=>{n.value=qc(r,t)}),n}return e!==void 0?$(()=>qc(e(),t)):ge(`f_${ja()}`)}const Hc=/^on[A-Z]/;function nC(){const{attrs:e,vnode:t}=Ee(),n={listeners:ge({}),attributes:ge({})};function r(){const s={},o={};for(const i in e)i!=="class"&&i!=="style"&&Hc.test(i)===!1&&(s[i]=e[i]);for(const i in t.props)Hc.test(i)===!0&&(o[i]=t.props[i]);n.attributes.value=s,n.listeners.value=o}return pu(r),r(),n}function rC({validate:e,resetValidation:t,requiresQForm:n}){const r=vt(Lv,!1);if(r!==!1){const{props:s,proxy:o}=Ee();Object.assign(o,{validate:e,resetValidation:t}),Ae(()=>s.disable,i=>{i===!0?(typeof t=="function"&&t(),r.unbindComponent(o)):r.bindComponent(o)}),Vt(()=>{s.disable!==!0&&r.bindComponent(o)}),xt(()=>{s.disable!==!0&&r.unbindComponent(o)})}else n===!0&&console.error("Parent QForm not found on useFormChild()!")}const jc=/^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$/,Wc=/^#[0-9a-fA-F]{4}([0-9a-fA-F]{4})?$/,Kc=/^#([0-9a-fA-F]{3}|[0-9a-fA-F]{4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/,Gs=/^rgb\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5])\)$/,Ys=/^rgba\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/,Zi={date:e=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(e),time:e=>/^([0-1]?\d|2[0-3]):[0-5]\d$/.test(e),fulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(e),timeOrFulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/.test(e),email:e=>/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(e),hexColor:e=>jc.test(e),hexaColor:e=>Wc.test(e),hexOrHexaColor:e=>Kc.test(e),rgbColor:e=>Gs.test(e),rgbaColor:e=>Ys.test(e),rgbOrRgbaColor:e=>Gs.test(e)||Ys.test(e),hexOrRgbColor:e=>jc.test(e)||Gs.test(e),hexaOrRgbaColor:e=>Wc.test(e)||Ys.test(e),anyColor:e=>Kc.test(e)||Gs.test(e)||Ys.test(e)},sC=[!0,!1,"ondemand"],oC={modelValue:{},error:{type:Boolean,default:null},errorMessage:String,noErrorIcon:Boolean,rules:Array,reactiveRules:Boolean,lazyRules:{type:[Boolean,String],default:!1,validator:e=>sC.includes(e)}};function iC(e,t){const{props:n,proxy:r}=Ee(),s=ge(!1),o=ge(null),i=ge(!1);rC({validate:C,resetValidation:g});let a=0,u;const l=$(()=>n.rules!==void 0&&n.rules!==null&&n.rules.length!==0),c=$(()=>n.disable!==!0&&l.value===!0&&t.value===!1),f=$(()=>n.error===!0||s.value===!0),d=$(()=>typeof n.errorMessage=="string"&&n.errorMessage.length!==0?n.errorMessage:o.value);Ae(()=>n.modelValue,()=>{i.value=!0,c.value===!0&&n.lazyRules===!1&&w()});function m(){n.lazyRules!=="ondemand"&&c.value===!0&&i.value===!0&&w()}Ae(()=>n.reactiveRules,R=>{R===!0?u===void 0&&(u=Ae(()=>n.rules,m,{immediate:!0,deep:!0})):u!==void 0&&(u(),u=void 0)},{immediate:!0}),Ae(()=>n.lazyRules,m),Ae(e,R=>{R===!0?i.value=!0:c.value===!0&&n.lazyRules!=="ondemand"&&w()});function g(){a++,t.value=!1,i.value=!1,s.value=!1,o.value=null,w.cancel()}function C(R=n.modelValue){if(n.disable===!0||l.value===!1)return!0;const E=++a,h=t.value!==!0?()=>{i.value=!0}:()=>{},p=(S,x)=>{S===!0&&h(),s.value=S,o.value=x||null,t.value=!1},b=[];for(let S=0;S<n.rules.length;S++){const x=n.rules[S];let F;if(typeof x=="function"?F=x(R,Zi):typeof x=="string"&&Zi[x]!==void 0&&(F=Zi[x](R)),F===!1||typeof F=="string")return p(!0,F),!1;F!==!0&&F!==void 0&&b.push(F)}return b.length===0?(p(!1),!0):(t.value=!0,Promise.all(b).then(S=>{if(S===void 0||Array.isArray(S)===!1||S.length===0)return E===a&&p(!1),!0;const x=S.find(F=>F===!1||typeof F=="string");return E===a&&p(x!==void 0,x),x===void 0},S=>(E===a&&(console.error(S),p(!0)),!1)))}const w=gh(C,0);return xt(()=>{u!==void 0&&u(),w.cancel()}),Object.assign(r,{resetValidation:g,validate:C}),er(r,"hasError",()=>f.value),{isDirtyModel:i,hasRules:l,hasError:f,errorMessage:d,validate:C,resetValidation:g}}function Wa(e){return e!=null&&(""+e).length!==0}const aC={...tr,...oC,label:String,stackLabel:Boolean,hint:String,hideHint:Boolean,prefix:String,suffix:String,labelColor:String,color:String,bgColor:String,filled:Boolean,outlined:Boolean,borderless:Boolean,standout:[Boolean,String],square:Boolean,loading:Boolean,labelSlot:Boolean,bottomSlots:Boolean,hideBottomSpace:Boolean,rounded:Boolean,dense:Boolean,itemAligned:Boolean,counter:Boolean,clearable:Boolean,clearIcon:String,disable:Boolean,readonly:Boolean,autofocus:Boolean,for:String},uC={...aC,maxlength:[Number,String]},lC=["update:modelValue","clear","focus","blur"];function cC({requiredForAttr:e=!0,tagProp:t,changeEvent:n=!1}={}){const{props:r,proxy:s}=Ee(),o=nr(r,s.$q),i=tC({required:e,getValue:()=>r.for});return{requiredForAttr:e,changeEvent:n,tag:t===!0?$(()=>r.tag):{value:"label"},isDark:o,editable:$(()=>r.disable!==!0&&r.readonly!==!0),innerLoading:ge(!1),focused:ge(!1),hasPopupOpen:!1,splitAttrs:nC(),targetUid:i,rootRef:ge(null),targetRef:ge(null),controlRef:ge(null)}}function fC(e){const{props:t,emit:n,slots:r,attrs:s,proxy:o}=Ee(),{$q:i}=o;let a=null;e.hasValue===void 0&&(e.hasValue=$(()=>Wa(t.modelValue))),e.emitValue===void 0&&(e.emitValue=V=>{n("update:modelValue",V)}),e.controlEvents===void 0&&(e.controlEvents={onFocusin:v,onFocusout:O}),Object.assign(e,{clearValue:I,onControlFocusin:v,onControlFocusout:O,focus:x}),e.computedCounter===void 0&&(e.computedCounter=$(()=>{if(t.counter!==!1){const V=typeof t.modelValue=="string"||typeof t.modelValue=="number"?(""+t.modelValue).length:Array.isArray(t.modelValue)===!0?t.modelValue.length:0,se=t.maxlength!==void 0?t.maxlength:t.maxValues;return V+(se!==void 0?" / "+se:"")}}));const{isDirtyModel:u,hasRules:l,hasError:c,errorMessage:f,resetValidation:d}=iC(e.focused,e.innerLoading),m=e.floatingLabel!==void 0?$(()=>t.stackLabel===!0||e.focused.value===!0||e.floatingLabel.value===!0):$(()=>t.stackLabel===!0||e.focused.value===!0||e.hasValue.value===!0),g=$(()=>t.bottomSlots===!0||t.hint!==void 0||l.value===!0||t.counter===!0||t.error!==null),C=$(()=>t.filled===!0?"filled":t.outlined===!0?"outlined":t.borderless===!0?"borderless":t.standout?"standout":"standard"),w=$(()=>`q-field row no-wrap items-start q-field--${C.value}`+(e.fieldClass!==void 0?` ${e.fieldClass.value}`:"")+(t.rounded===!0?" q-field--rounded":"")+(t.square===!0?" q-field--square":"")+(m.value===!0?" q-field--float":"")+(E.value===!0?" q-field--labeled":"")+(t.dense===!0?" q-field--dense":"")+(t.itemAligned===!0?" q-field--item-aligned q-item-type":"")+(e.isDark.value===!0?" q-field--dark":"")+(e.getControl===void 0?" q-field--auto-height":"")+(e.focused.value===!0?" q-field--focused":"")+(c.value===!0?" q-field--error":"")+(c.value===!0||e.focused.value===!0?" q-field--highlighted":"")+(t.hideBottomSpace!==!0&&g.value===!0?" q-field--with-bottom":"")+(t.disable===!0?" q-field--disabled":t.readonly===!0?" q-field--readonly":"")),R=$(()=>"q-field__control relative-position row no-wrap"+(t.bgColor!==void 0?` bg-${t.bgColor}`:"")+(c.value===!0?" text-negative":typeof t.standout=="string"&&t.standout.length!==0&&e.focused.value===!0?` ${t.standout}`:t.color!==void 0?` text-${t.color}`:"")),E=$(()=>t.labelSlot===!0||t.label!==void 0),h=$(()=>"q-field__label no-pointer-events absolute ellipsis"+(t.labelColor!==void 0&&c.value!==!0?` text-${t.labelColor}`:"")),p=$(()=>({id:e.targetUid.value,editable:e.editable.value,focused:e.focused.value,floatingLabel:m.value,modelValue:t.modelValue,emitValue:e.emitValue})),b=$(()=>{const V={};return e.targetUid.value&&(V.for=e.targetUid.value),t.disable===!0&&(V["aria-disabled"]="true"),V});function S(){const V=document.activeElement;let se=e.targetRef!==void 0&&e.targetRef.value;se&&(V===null||V.id!==e.targetUid.value)&&(se.hasAttribute("tabindex")===!0||(se=se.querySelector("[tabindex]")),se&&se!==V&&se.focus({preventScroll:!0}))}function x(){Mu(S)}function F(){FE(S);const V=document.activeElement;V!==null&&e.rootRef.value.contains(V)&&V.blur()}function v(V){a!==null&&(clearTimeout(a),a=null),e.editable.value===!0&&e.focused.value===!1&&(e.focused.value=!0,n("focus",V))}function O(V,se){a!==null&&clearTimeout(a),a=setTimeout(()=>{a=null,!(document.hasFocus()===!0&&(e.hasPopupOpen===!0||e.controlRef===void 0||e.controlRef.value===null||e.controlRef.value.contains(document.activeElement)!==!1))&&(e.focused.value===!0&&(e.focused.value=!1,n("blur",V)),se!==void 0&&se())})}function I(V){St(V),i.platform.is.mobile!==!0?(e.targetRef!==void 0&&e.targetRef.value||e.rootRef.value).focus():e.rootRef.value.contains(document.activeElement)===!0&&document.activeElement.blur(),t.type==="file"&&(e.inputRef.value.value=null),n("update:modelValue",null),e.changeEvent===!0&&n("change",null),n("clear",t.modelValue),rt(()=>{const se=u.value;d(),u.value=se})}function T(V){[13,32].includes(V.keyCode)&&I(V)}function H(){const V=[];return r.prepend!==void 0&&V.push(q("div",{class:"q-field__prepend q-field__marginal row no-wrap items-center",key:"prepend",onClick:Sn},r.prepend())),V.push(q("div",{class:"q-field__control-container col relative-position row no-wrap q-anchor--skip"},L())),c.value===!0&&t.noErrorIcon===!1&&V.push(W("error",[q(un,{name:i.iconSet.field.error,color:"negative"})])),t.loading===!0||e.innerLoading.value===!0?V.push(W("inner-loading-append",r.loading!==void 0?r.loading():[q(Es,{color:t.color})])):t.clearable===!0&&e.hasValue.value===!0&&e.editable.value===!0&&V.push(W("inner-clearable-append",[q(un,{class:"q-field__focusable-action",name:t.clearIcon||i.iconSet.field.clear,tabindex:0,role:"button","aria-hidden":"false","aria-label":i.lang.label.clear,onKeyup:T,onClick:I})])),r.append!==void 0&&V.push(q("div",{class:"q-field__append q-field__marginal row no-wrap items-center",key:"append",onClick:Sn},r.append())),e.getInnerAppend!==void 0&&V.push(W("inner-append",e.getInnerAppend())),e.getControlChild!==void 0&&V.push(e.getControlChild()),V}function L(){const V=[];return t.prefix!==void 0&&t.prefix!==null&&V.push(q("div",{class:"q-field__prefix no-pointer-events row items-center"},t.prefix)),e.getShadowControl!==void 0&&e.hasShadow.value===!0&&V.push(e.getShadowControl()),e.getControl!==void 0?V.push(e.getControl()):r.rawControl!==void 0?V.push(r.rawControl()):r.control!==void 0&&V.push(q("div",{ref:e.targetRef,class:"q-field__native row",tabindex:-1,...e.splitAttrs.attributes.value,"data-autofocus":t.autofocus===!0||void 0},r.control(p.value))),E.value===!0&&V.push(q("div",{class:h.value},Ft(r.label,t.label))),t.suffix!==void 0&&t.suffix!==null&&V.push(q("div",{class:"q-field__suffix no-pointer-events row items-center"},t.suffix)),V.concat(Ft(r.default))}function Z(){let V,se;c.value===!0?f.value!==null?(V=[q("div",{role:"alert"},f.value)],se=`q--slot-error-${f.value}`):(V=Ft(r.error),se="q--slot-error"):(t.hideHint!==!0||e.focused.value===!0)&&(t.hint!==void 0?(V=[q("div",t.hint)],se=`q--slot-hint-${t.hint}`):(V=Ft(r.hint),se="q--slot-hint"));const he=t.counter===!0||r.counter!==void 0;if(t.hideBottomSpace===!0&&he===!1&&V===void 0)return;const de=q("div",{key:se,class:"q-field__messages col"},V);return q("div",{class:"q-field__bottom row items-start q-field__bottom--"+(t.hideBottomSpace!==!0?"animated":"stale"),onClick:Sn},[t.hideBottomSpace===!0?de:q(xo,{name:"q-transition--field-message"},()=>de),he===!0?q("div",{class:"q-field__counter"},r.counter!==void 0?r.counter():e.computedCounter.value):null])}function W(V,se){return se===null?null:q("div",{key:V,class:"q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"},se)}let Q=!1;return ii(()=>{Q=!0}),vd(()=>{Q===!0&&t.autofocus===!0&&o.focus()}),t.autofocus===!0&&Vt(()=>{o.focus()}),xt(()=>{a!==null&&clearTimeout(a)}),Object.assign(o,{focus:x,blur:F}),function(){const se=e.getControl===void 0&&r.control===void 0?{...e.splitAttrs.attributes.value,"data-autofocus":t.autofocus===!0||void 0,...b.value}:b.value;return q(e.tag.value,{ref:e.rootRef,class:[w.value,s.class],style:s.style,...se},[r.before!==void 0?q("div",{class:"q-field__before q-field__marginal row no-wrap items-center",onClick:Sn},r.before()):null,q("div",{class:"q-field__inner relative-position col self-stretch"},[q("div",{ref:e.controlRef,class:R.value,tabindex:-1,...e.controlEvents},H()),g.value===!0?Z():null]),r.after!==void 0?q("div",{class:"q-field__after q-field__marginal row no-wrap items-center",onClick:Sn},r.after()):null])}}const Xc={date:"####/##/##",datetime:"####/##/## ##:##",time:"##:##",fulltime:"##:##:##",phone:"(###) ### - ####",card:"#### #### #### ####"},Uo={"#":{pattern:"[\\d]",negate:"[^\\d]"},S:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]"},N:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]"},A:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleUpperCase()},a:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleLowerCase()},X:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleUpperCase()},x:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleLowerCase()}},Nm=Object.keys(Uo);Nm.forEach(e=>{Uo[e].regex=new RegExp(Uo[e].pattern)});const dC=new RegExp("\\\\([^.*+?^${}()|([\\]])|([.*+?^${}()|[\\]])|(["+Nm.join("")+"])|(.)","g"),zc=/[.*+?^${}()|[\]\\]/g,Ue=String.fromCharCode(1),hC={mask:String,reverseFillMask:Boolean,fillMask:[Boolean,String],unmaskedValue:Boolean};function mC(e,t,n,r){let s,o,i,a,u,l;const c=ge(null),f=ge(m());function d(){return e.autogrow===!0||["textarea","text","search","url","tel","password"].includes(e.type)}Ae(()=>e.type+e.autogrow,C),Ae(()=>e.mask,v=>{if(v!==void 0)w(f.value,!0);else{const O=x(f.value);C(),e.modelValue!==O&&t("update:modelValue",O)}}),Ae(()=>e.fillMask+e.reverseFillMask,()=>{c.value===!0&&w(f.value,!0)}),Ae(()=>e.unmaskedValue,()=>{c.value===!0&&w(f.value)});function m(){if(C(),c.value===!0){const v=b(x(e.modelValue));return e.fillMask!==!1?F(v):v}return e.modelValue}function g(v){if(v<s.length)return s.slice(-v);let O="",I=s;const T=I.indexOf(Ue);if(T!==-1){for(let H=v-I.length;H>0;H--)O+=Ue;I=I.slice(0,T)+O+I.slice(T)}return I}function C(){if(c.value=e.mask!==void 0&&e.mask.length!==0&&d(),c.value===!1){a=void 0,s="",o="";return}const v=Xc[e.mask]===void 0?e.mask:Xc[e.mask],O=typeof e.fillMask=="string"&&e.fillMask.length!==0?e.fillMask.slice(0,1):"_",I=O.replace(zc,"\\$&"),T=[],H=[],L=[];let Z=e.reverseFillMask===!0,W="",Q="";v.replace(dC,(de,z,_e,Pe,Ie)=>{if(Pe!==void 0){const oe=Uo[Pe];L.push(oe),Q=oe.negate,Z===!0&&(H.push("(?:"+Q+"+)?("+oe.pattern+"+)?(?:"+Q+"+)?("+oe.pattern+"+)?"),Z=!1),H.push("(?:"+Q+"+)?("+oe.pattern+")?")}else if(_e!==void 0)W="\\"+(_e==="\\"?"":_e),L.push(_e),T.push("([^"+W+"]+)?"+W+"?");else{const oe=z!==void 0?z:Ie;W=oe==="\\"?"\\\\\\\\":oe.replace(zc,"\\\\$&"),L.push(oe),T.push("([^"+W+"]+)?"+W+"?")}});const V=new RegExp("^"+T.join("")+"("+(W===""?".":"[^"+W+"]")+"+)?"+(W===""?"":"["+W+"]*")+"$"),se=H.length-1,he=H.map((de,z)=>z===0&&e.reverseFillMask===!0?new RegExp("^"+I+"*"+de):z===se?new RegExp("^"+de+"("+(Q===""?".":Q)+"+)?"+(e.reverseFillMask===!0?"$":I+"*")):new RegExp("^"+de));i=L,a=de=>{const z=V.exec(e.reverseFillMask===!0?de:de.slice(0,L.length+1));z!==null&&(de=z.slice(1).join(""));const _e=[],Pe=he.length;for(let Ie=0,oe=de;Ie<Pe;Ie++){const B=he[Ie].exec(oe);if(B===null)break;oe=oe.slice(B.shift().length),_e.push(...B)}return _e.length!==0?_e.join(""):de},s=L.map(de=>typeof de=="string"?de:Ue).join(""),o=s.split(Ue).join(O)}function w(v,O,I){const T=r.value,H=T.selectionEnd,L=T.value.length-H,Z=x(v);O===!0&&C();const W=b(Z),Q=e.fillMask!==!1?F(W):W,V=f.value!==Q;T.value!==Q&&(T.value=Q),V===!0&&(f.value=Q),document.activeElement===T&&rt(()=>{if(Q===o){const he=e.reverseFillMask===!0?o.length:0;T.setSelectionRange(he,he,"forward");return}if(I==="insertFromPaste"&&e.reverseFillMask!==!0){const he=T.selectionEnd;let de=H-1;for(let z=u;z<=de&&z<he;z++)s[z]!==Ue&&de++;E.right(T,de);return}if(["deleteContentBackward","deleteContentForward"].indexOf(I)!==-1){const he=e.reverseFillMask===!0?H===0?Q.length>W.length?1:0:Math.max(0,Q.length-(Q===o?0:Math.min(W.length,L)+1))+1:H;T.setSelectionRange(he,he,"forward");return}if(e.reverseFillMask===!0)if(V===!0){const he=Math.max(0,Q.length-(Q===o?0:Math.min(W.length,L+1)));he===1&&H===1?T.setSelectionRange(he,he,"forward"):E.rightReverse(T,he)}else{const he=Q.length-L;T.setSelectionRange(he,he,"backward")}else if(V===!0){const he=Math.max(0,s.indexOf(Ue),Math.min(W.length,H)-1);E.right(T,he)}else{const he=H-1;E.right(T,he)}});const se=e.unmaskedValue===!0?x(Q):Q;String(e.modelValue)!==se&&(e.modelValue!==null||se!=="")&&n(se,!0)}function R(v,O,I){const T=b(x(v.value));O=Math.max(0,s.indexOf(Ue),Math.min(T.length,O)),u=O,v.setSelectionRange(O,I,"forward")}const E={left(v,O){const I=s.slice(O-1).indexOf(Ue)===-1;let T=Math.max(0,O-1);for(;T>=0;T--)if(s[T]===Ue){O=T,I===!0&&O++;break}if(T<0&&s[O]!==void 0&&s[O]!==Ue)return E.right(v,0);O>=0&&v.setSelectionRange(O,O,"backward")},right(v,O){const I=v.value.length;let T=Math.min(I,O+1);for(;T<=I;T++)if(s[T]===Ue){O=T;break}else s[T-1]===Ue&&(O=T);if(T>I&&s[O-1]!==void 0&&s[O-1]!==Ue)return E.left(v,I);v.setSelectionRange(O,O,"forward")},leftReverse(v,O){const I=g(v.value.length);let T=Math.max(0,O-1);for(;T>=0;T--)if(I[T-1]===Ue){O=T;break}else if(I[T]===Ue&&(O=T,T===0))break;if(T<0&&I[O]!==void 0&&I[O]!==Ue)return E.rightReverse(v,0);O>=0&&v.setSelectionRange(O,O,"backward")},rightReverse(v,O){const I=v.value.length,T=g(I),H=T.slice(0,O+1).indexOf(Ue)===-1;let L=Math.min(I,O+1);for(;L<=I;L++)if(T[L-1]===Ue){O=L,O>0&&H===!0&&O--;break}if(L>I&&T[O-1]!==void 0&&T[O-1]!==Ue)return E.leftReverse(v,I);v.setSelectionRange(O,O,"forward")}};function h(v){t("click",v),l=void 0}function p(v){if(t("keydown",v),vh(v)===!0||v.altKey===!0)return;const O=r.value,I=O.selectionStart,T=O.selectionEnd;if(v.shiftKey||(l=void 0),v.keyCode===37||v.keyCode===39){v.shiftKey&&l===void 0&&(l=O.selectionDirection==="forward"?I:T);const H=E[(v.keyCode===39?"right":"left")+(e.reverseFillMask===!0?"Reverse":"")];if(v.preventDefault(),H(O,l===I?T:I),v.shiftKey){const L=O.selectionStart;O.setSelectionRange(Math.min(l,L),Math.max(l,L),"forward")}}else v.keyCode===8&&e.reverseFillMask!==!0&&I===T?(E.left(O,I),O.setSelectionRange(O.selectionStart,T,"backward")):v.keyCode===46&&e.reverseFillMask===!0&&I===T&&(E.rightReverse(O,T),O.setSelectionRange(I,O.selectionEnd,"forward"))}function b(v){if(v==null||v==="")return"";if(e.reverseFillMask===!0)return S(v);const O=i;let I=0,T="";for(let H=0;H<O.length;H++){const L=v[I],Z=O[H];if(typeof Z=="string")T+=Z,L===Z&&I++;else if(L!==void 0&&Z.regex.test(L))T+=Z.transform!==void 0?Z.transform(L):L,I++;else return T}return T}function S(v){const O=i,I=s.indexOf(Ue);let T=v.length-1,H="";for(let L=O.length-1;L>=0&&T!==-1;L--){const Z=O[L];let W=v[T];if(typeof Z=="string")H=Z+H,W===Z&&T--;else if(W!==void 0&&Z.regex.test(W))do H=(Z.transform!==void 0?Z.transform(W):W)+H,T--,W=v[T];while(I===L&&W!==void 0&&Z.regex.test(W));else return H}return H}function x(v){return typeof v!="string"||a===void 0?typeof v=="number"?a(""+v):v:a(v)}function F(v){return o.length-v.length<=0?v:e.reverseFillMask===!0&&v.length!==0?o.slice(0,-v.length)+v:v+o.slice(v.length)}return{innerValue:f,hasMask:c,moveCursorForPaste:R,updateMaskValue:w,onMaskedKeydown:p,onMaskedClick:h}}const $u={name:String};function q1(e){return $(()=>({type:"hidden",name:e.name,value:e.modelValue}))}function Lm(e={}){return(t,n,r)=>{t[n](q("input",{class:"hidden"+(r||""),...e.value}))}}function pC(e){return $(()=>e.name||e.for)}function gC(e,t){function n(){const r=e.modelValue;try{const s="DataTransfer"in window?new DataTransfer:"ClipboardEvent"in window?new ClipboardEvent("").clipboardData:void 0;return Object(r)===r&&("length"in r?Array.from(r):[r]).forEach(o=>{s.items.add(o)}),{files:s.files}}catch{return{files:void 0}}}return $(t===!0?()=>{if(e.type==="file")return n()}:n)}const _C=/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/,vC=/[\u4e00-\u9fff\u3400-\u4dbf\u{20000}-\u{2a6df}\u{2a700}-\u{2b73f}\u{2b740}-\u{2b81f}\u{2b820}-\u{2ceaf}\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u{2f800}-\u{2fa1f}]/u,yC=/[\u3131-\u314e\u314f-\u3163\uac00-\ud7a3]/,bC=/[a-z0-9_ -]$/i;function EC(e){return function(n){if(n.type==="compositionend"||n.type==="change"){if(n.target.qComposing!==!0)return;n.target.qComposing=!1,e(n)}else n.type==="compositionupdate"&&n.target.qComposing!==!0&&typeof n.data=="string"&&(Xe.is.firefox===!0?bC.test(n.data)===!1:_C.test(n.data)===!0||vC.test(n.data)===!0||yC.test(n.data)===!0)===!0&&(n.target.qComposing=!0)}}var CC=Ze({name:"QInput",inheritAttrs:!1,props:{...uC,...hC,...$u,modelValue:[String,Number,FileList],shadowText:String,type:{type:String,default:"text"},debounce:[String,Number],autogrow:Boolean,inputClass:[Array,String,Object],inputStyle:[Array,String,Object]},emits:[...lC,"paste","change","keydown","click","animationend"],setup(e,{emit:t,attrs:n}){const{proxy:r}=Ee(),{$q:s}=r,o={};let i=NaN,a,u,l=null,c;const f=ge(null),d=pC(e),{innerValue:m,hasMask:g,moveCursorForPaste:C,updateMaskValue:w,onMaskedKeydown:R,onMaskedClick:E}=mC(e,t,W,f),h=gC(e,!0),p=$(()=>Wa(m.value)),b=EC(L),S=cC({changeEvent:!0}),x=$(()=>e.type==="textarea"||e.autogrow===!0),F=$(()=>x.value===!0||["text","search","url","tel","password"].includes(e.type)),v=$(()=>{const z={...S.splitAttrs.listeners.value,onInput:L,onPaste:H,onChange:V,onBlur:se,onFocus:Do};return z.onCompositionstart=z.onCompositionupdate=z.onCompositionend=b,g.value===!0&&(z.onKeydown=R,z.onClick=E),e.autogrow===!0&&(z.onAnimationend=Z),z}),O=$(()=>{const z={tabindex:0,"data-autofocus":e.autofocus===!0||void 0,rows:e.type==="textarea"?6:void 0,"aria-label":e.label,name:d.value,...S.splitAttrs.attributes.value,id:S.targetUid.value,maxlength:e.maxlength,disabled:e.disable===!0,readonly:e.readonly===!0};return x.value===!1&&(z.type=e.type),e.autogrow===!0&&(z.rows=1),z});Ae(()=>e.type,()=>{f.value&&(f.value.value=e.modelValue)}),Ae(()=>e.modelValue,z=>{if(g.value===!0){if(u===!0&&(u=!1,String(z)===i))return;w(z)}else m.value!==z&&(m.value=z,e.type==="number"&&o.hasOwnProperty("value")===!0&&(a===!0?a=!1:delete o.value));e.autogrow===!0&&rt(Q)}),Ae(()=>e.autogrow,z=>{z===!0?rt(Q):f.value!==null&&n.rows>0&&(f.value.style.height="auto")}),Ae(()=>e.dense,()=>{e.autogrow===!0&&rt(Q)});function I(){Mu(()=>{const z=document.activeElement;f.value!==null&&f.value!==z&&(z===null||z.id!==S.targetUid.value)&&f.value.focus({preventScroll:!0})})}function T(){f.value!==null&&f.value.select()}function H(z){if(g.value===!0&&e.reverseFillMask!==!0){const _e=z.target;C(_e,_e.selectionStart,_e.selectionEnd)}t("paste",z)}function L(z){if(!z||!z.target)return;if(e.type==="file"){t("update:modelValue",z.target.files);return}const _e=z.target.value;if(z.target.qComposing===!0){o.value=_e;return}if(g.value===!0)w(_e,!1,z.inputType);else if(W(_e),F.value===!0&&z.target===document.activeElement){const{selectionStart:Pe,selectionEnd:Ie}=z.target;Pe!==void 0&&Ie!==void 0&&rt(()=>{z.target===document.activeElement&&_e.indexOf(z.target.value)===0&&z.target.setSelectionRange(Pe,Ie)})}e.autogrow===!0&&Q()}function Z(z){t("animationend",z),Q()}function W(z,_e){c=()=>{l=null,e.type!=="number"&&o.hasOwnProperty("value")===!0&&delete o.value,e.modelValue!==z&&i!==z&&(i=z,_e===!0&&(u=!0),t("update:modelValue",z),rt(()=>{i===z&&(i=NaN)})),c=void 0},e.type==="number"&&(a=!0,o.value=z),e.debounce!==void 0?(l!==null&&clearTimeout(l),o.value=z,l=setTimeout(c,e.debounce)):c()}function Q(){requestAnimationFrame(()=>{const z=f.value;if(z!==null){const _e=z.parentNode.style,{scrollTop:Pe}=z,{overflowY:Ie,maxHeight:oe}=s.platform.is.firefox===!0?{}:window.getComputedStyle(z),B=Ie!==void 0&&Ie!=="scroll";B===!0&&(z.style.overflowY="hidden"),_e.marginBottom=z.scrollHeight-1+"px",z.style.height="1px",z.style.height=z.scrollHeight+"px",B===!0&&(z.style.overflowY=parseInt(oe,10)<z.scrollHeight?"auto":"hidden"),_e.marginBottom="",z.scrollTop=Pe}})}function V(z){b(z),l!==null&&(clearTimeout(l),l=null),c!==void 0&&c(),t("change",z.target.value)}function se(z){z!==void 0&&Do(z),l!==null&&(clearTimeout(l),l=null),c!==void 0&&c(),a=!1,u=!1,delete o.value,e.type!=="file"&&setTimeout(()=>{f.value!==null&&(f.value.value=m.value!==void 0?m.value:"")})}function he(){return o.hasOwnProperty("value")===!0?o.value:m.value!==void 0?m.value:""}xt(()=>{se()}),Vt(()=>{e.autogrow===!0&&Q()}),Object.assign(S,{innerValue:m,fieldClass:$(()=>`q-${x.value===!0?"textarea":"input"}`+(e.autogrow===!0?" q-textarea--autogrow":"")),hasShadow:$(()=>e.type!=="file"&&typeof e.shadowText=="string"&&e.shadowText.length!==0),inputRef:f,emitValue:W,hasValue:p,floatingLabel:$(()=>p.value===!0&&(e.type!=="number"||isNaN(m.value)===!1)||Wa(e.displayValue)),getControl:()=>q(x.value===!0?"textarea":"input",{ref:f,class:["q-field__native q-placeholder",e.inputClass],style:e.inputStyle,...O.value,...v.value,...e.type!=="file"?{value:he()}:h.value}),getShadowControl:()=>q("div",{class:"q-field__native q-field__shadow absolute-bottom no-pointer-events"+(x.value===!0?"":" text-no-wrap")},[q("span",{class:"invisible"},he()),q("span",e.shadowText)])});const de=fC(S);return Object.assign(r,{focus:I,select:T,getNativeElement:()=>f.value}),er(r,"nativeEl",()=>f.value),de}});function Im(e,t){const n=ge(null),r=$(()=>e.disable===!0?null:q("span",{ref:n,class:"no-outline",tabindex:-1}));function s(o){const i=t.value;o!==void 0&&o.type.indexOf("key")===0?i!==null&&document.activeElement!==i&&i.contains(document.activeElement)===!0&&i.focus():n.value!==null&&(o===void 0||i!==null&&i.contains(o.target)===!0)&&n.value.focus()}return{refocusTargetEl:r,refocusTarget:s}}var Bm={xs:30,sm:35,md:40,lg:50,xl:60};const AC=q("svg",{key:"svg",class:"q-radio__bg absolute non-selectable",viewBox:"0 0 24 24"},[q("path",{d:"M12,22a10,10 0 0 1 -10,-10a10,10 0 0 1 10,-10a10,10 0 0 1 10,10a10,10 0 0 1 -10,10m0,-22a12,12 0 0 0 -12,12a12,12 0 0 0 12,12a12,12 0 0 0 12,-12a12,12 0 0 0 -12,-12"}),q("path",{class:"q-radio__check",d:"M12,6a6,6 0 0 0 -6,6a6,6 0 0 0 6,6a6,6 0 0 0 6,-6a6,6 0 0 0 -6,-6"})]);var SC=Ze({name:"QRadio",props:{...tr,...Ps,...$u,modelValue:{required:!0},val:{required:!0},label:String,leftLabel:Boolean,checkedIcon:String,uncheckedIcon:String,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},emits:["update:modelValue"],setup(e,{slots:t,emit:n}){const{proxy:r}=Ee(),s=nr(e,r.$q),o=Fs(e,Bm),i=ge(null),{refocusTargetEl:a,refocusTarget:u}=Im(e,i),l=$(()=>me(e.modelValue)===me(e.val)),c=$(()=>"q-radio cursor-pointer no-outline row inline no-wrap items-center"+(e.disable===!0?" disabled":"")+(s.value===!0?" q-radio--dark":"")+(e.dense===!0?" q-radio--dense":"")+(e.leftLabel===!0?" reverse":"")),f=$(()=>{const h=e.color!==void 0&&(e.keepColor===!0||l.value===!0)?` text-${e.color}`:"";return`q-radio__inner relative-position q-radio__inner--${l.value===!0?"truthy":"falsy"}${h}`}),d=$(()=>(l.value===!0?e.checkedIcon:e.uncheckedIcon)||null),m=$(()=>e.disable===!0?-1:e.tabindex||0),g=$(()=>{const h={type:"radio"};return e.name!==void 0&&Object.assign(h,{".checked":l.value===!0,"^checked":l.value===!0?"checked":void 0,name:e.name,value:e.val}),h}),C=Lm(g);function w(h){h!==void 0&&(St(h),u(h)),e.disable!==!0&&l.value!==!0&&n("update:modelValue",e.val,h)}function R(h){(h.keyCode===13||h.keyCode===32)&&St(h)}function E(h){(h.keyCode===13||h.keyCode===32)&&w(h)}return Object.assign(r,{set:w}),()=>{const h=d.value!==null?[q("div",{key:"icon",class:"q-radio__icon-container absolute-full flex flex-center no-wrap"},[q(un,{class:"q-radio__icon",name:d.value})])]:[AC];e.disable!==!0&&C(h,"unshift"," q-radio__native q-ma-none q-pa-none");const p=[q("div",{class:f.value,style:o.value,"aria-hidden":"true"},h)];a.value!==null&&p.push(a.value);const b=e.label!==void 0?Un(t.default,[e.label]):Ft(t.default);return b!==void 0&&p.push(q("div",{class:"q-radio__label q-anchor--skip"},b)),q("div",{ref:i,class:c.value,tabindex:m.value,role:"radio","aria-label":e.label,"aria-checked":l.value===!0?"true":"false","aria-disabled":e.disable===!0?"true":void 0,onClick:w,onKeydown:R,onKeyup:E},p)}}});const Mm={...tr,...Ps,...$u,modelValue:{required:!0,default:null},val:{},trueValue:{default:!0},falseValue:{default:!1},indeterminateValue:{default:null},checkedIcon:String,uncheckedIcon:String,indeterminateIcon:String,toggleOrder:{type:String,validator:e=>e==="tf"||e==="ft"},toggleIndeterminate:Boolean,label:String,leftLabel:Boolean,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},$m=["update:modelValue"];function Um(e,t){const{props:n,slots:r,emit:s,proxy:o}=Ee(),{$q:i}=o,a=nr(n,i),u=ge(null),{refocusTargetEl:l,refocusTarget:c}=Im(n,u),f=Fs(n,Bm),d=$(()=>n.val!==void 0&&Array.isArray(n.modelValue)),m=$(()=>{const T=me(n.val);return d.value===!0?n.modelValue.findIndex(H=>me(H)===T):-1}),g=$(()=>d.value===!0?m.value!==-1:me(n.modelValue)===me(n.trueValue)),C=$(()=>d.value===!0?m.value===-1:me(n.modelValue)===me(n.falseValue)),w=$(()=>g.value===!1&&C.value===!1),R=$(()=>n.disable===!0?-1:n.tabindex||0),E=$(()=>`q-${e} cursor-pointer no-outline row inline no-wrap items-center`+(n.disable===!0?" disabled":"")+(a.value===!0?` q-${e}--dark`:"")+(n.dense===!0?` q-${e}--dense`:"")+(n.leftLabel===!0?" reverse":"")),h=$(()=>{const T=g.value===!0?"truthy":C.value===!0?"falsy":"indet",H=n.color!==void 0&&(n.keepColor===!0||(e==="toggle"?g.value===!0:C.value!==!0))?` text-${n.color}`:"";return`q-${e}__inner relative-position non-selectable q-${e}__inner--${T}${H}`}),p=$(()=>{const T={type:"checkbox"};return n.name!==void 0&&Object.assign(T,{".checked":g.value,"^checked":g.value===!0?"checked":void 0,name:n.name,value:d.value===!0?n.val:n.trueValue}),T}),b=Lm(p),S=$(()=>{const T={tabindex:R.value,role:e==="toggle"?"switch":"checkbox","aria-label":n.label,"aria-checked":w.value===!0?"mixed":g.value===!0?"true":"false"};return n.disable===!0&&(T["aria-disabled"]="true"),T});function x(T){T!==void 0&&(St(T),c(T)),n.disable!==!0&&s("update:modelValue",F(),T)}function F(){if(d.value===!0){if(g.value===!0){const T=n.modelValue.slice();return T.splice(m.value,1),T}return n.modelValue.concat([n.val])}if(g.value===!0){if(n.toggleOrder!=="ft"||n.toggleIndeterminate===!1)return n.falseValue}else if(C.value===!0){if(n.toggleOrder==="ft"||n.toggleIndeterminate===!1)return n.trueValue}else return n.toggleOrder!=="ft"?n.trueValue:n.falseValue;return n.indeterminateValue}function v(T){(T.keyCode===13||T.keyCode===32)&&St(T)}function O(T){(T.keyCode===13||T.keyCode===32)&&x(T)}const I=t(g,w);return Object.assign(o,{toggle:x}),()=>{const T=I();n.disable!==!0&&b(T,"unshift",` q-${e}__native absolute q-ma-none q-pa-none`);const H=[q("div",{class:h.value,style:f.value,"aria-hidden":"true"},T)];l.value!==null&&H.push(l.value);const L=n.label!==void 0?Un(r.default,[n.label]):Ft(r.default);return L!==void 0&&H.push(q("div",{class:`q-${e}__label q-anchor--skip`},L)),q("div",{ref:u,class:E.value,...S.value,onClick:x,onKeydown:v,onKeyup:O},H)}}const wC=q("div",{key:"svg",class:"q-checkbox__bg absolute"},[q("svg",{class:"q-checkbox__svg fit absolute-full",viewBox:"0 0 24 24"},[q("path",{class:"q-checkbox__truthy",fill:"none",d:"M1.73,12.91 8.1,19.28 22.79,4.59"}),q("path",{class:"q-checkbox__indet",d:"M4,14H20V10H4"})])]);var TC=Ze({name:"QCheckbox",props:Mm,emits:$m,setup(e){function t(n,r){const s=$(()=>(n.value===!0?e.checkedIcon:r.value===!0?e.indeterminateIcon:e.uncheckedIcon)||null);return()=>s.value!==null?[q("div",{key:"icon",class:"q-checkbox__icon-container absolute-full flex flex-center no-wrap"},[q(un,{class:"q-checkbox__icon",name:s.value})])]:[wC]}return Um("checkbox",t)}}),kC=Ze({name:"QToggle",props:{...Mm,icon:String,iconColor:String},emits:$m,setup(e){function t(n,r){const s=$(()=>(n.value===!0?e.checkedIcon:r.value===!0?e.indeterminateIcon:e.uncheckedIcon)||e.icon),o=$(()=>n.value===!0?e.iconColor:null);return()=>[q("div",{class:"q-toggle__track"}),q("div",{class:"q-toggle__thumb absolute flex flex-center no-wrap"},s.value!==void 0?[q(un,{name:s.value,color:o.value})]:void 0)]}return Um("toggle",t)}});const Vm={radio:SC,checkbox:TC,toggle:kC},xC=Object.keys(Vm);var OC=Ze({name:"QOptionGroup",props:{...tr,modelValue:{required:!0},options:{type:Array,validator:e=>e.every(t=>"value"in t&&"label"in t)},name:String,type:{type:String,default:"radio",validator:e=>xC.includes(e)},color:String,keepColor:Boolean,dense:Boolean,size:String,leftLabel:Boolean,inline:Boolean,disable:Boolean},emits:["update:modelValue"],setup(e,{emit:t,slots:n}){const{proxy:{$q:r}}=Ee(),s=Array.isArray(e.modelValue);e.type==="radio"?s===!0&&console.error("q-option-group: model should not be array"):s===!1&&console.error("q-option-group: model should be array in your case");const o=nr(e,r),i=$(()=>Vm[e.type]),a=$(()=>"q-option-group q-gutter-x-sm"+(e.inline===!0?" q-option-group--inline":"")),u=$(()=>{const c={role:"group"};return e.type==="radio"&&(c.role="radiogroup",e.disable===!0&&(c["aria-disabled"]="true")),c});function l(c){t("update:modelValue",c)}return()=>q("div",{class:a.value,...u.value},e.options.map((c,f)=>{const d=n["label-"+f]!==void 0?()=>n["label-"+f](c):n.label!==void 0?()=>n.label(c):void 0;return q("div",[q(i.value,{modelValue:e.modelValue,val:c.value,name:c.name===void 0?e.name:c.name,disable:e.disable||c.disable,label:d===void 0?c.label:null,leftLabel:c.leftLabel===void 0?e.leftLabel:c.leftLabel,color:c.color===void 0?e.color:c.color,checkedIcon:c.checkedIcon,uncheckedIcon:c.uncheckedIcon,dark:c.dark||o.value,size:c.size===void 0?e.size:c.size,dense:e.dense,keepColor:c.keepColor===void 0?e.keepColor:c.keepColor,"onUpdate:modelValue":l},d)])}))}}),RC=Ze({name:"DialogPluginComponent",props:{...tr,title:String,message:String,prompt:Object,options:Object,progress:[Boolean,Object],html:Boolean,ok:{type:[String,Object,Boolean],default:!0},cancel:[String,Object,Boolean],focus:{type:String,default:"ok",validator:e=>["ok","cancel","none"].includes(e)},stackButtons:Boolean,color:String,cardClass:[String,Array,Object],cardStyle:[String,Array,Object]},emits:["ok","hide"],setup(e,{emit:t}){const{proxy:n}=Ee(),{$q:r}=n,s=nr(e,r),o=ge(null),i=ge(e.prompt!==void 0?e.prompt.model:e.options!==void 0?e.options.model:void 0),a=$(()=>"q-dialog-plugin"+(s.value===!0?" q-dialog-plugin--dark q-dark":"")+(e.progress!==!1?" q-dialog-plugin--progress":"")),u=$(()=>e.color||(s.value===!0?"amber":"primary")),l=$(()=>e.progress===!1?null:Xt(e.progress)===!0?{component:e.progress.spinner||Es,props:{color:e.progress.color||u.value}}:{component:Es,props:{color:u.value}}),c=$(()=>e.prompt!==void 0||e.options!==void 0),f=$(()=>{if(c.value!==!0)return{};const{model:L,isValid:Z,items:W,...Q}=e.prompt!==void 0?e.prompt:e.options;return Q}),d=$(()=>Xt(e.ok)===!0||e.ok===!0?r.lang.label.ok:e.ok),m=$(()=>Xt(e.cancel)===!0||e.cancel===!0?r.lang.label.cancel:e.cancel),g=$(()=>e.prompt!==void 0?e.prompt.isValid!==void 0&&e.prompt.isValid(i.value)!==!0:e.options!==void 0?e.options.isValid!==void 0&&e.options.isValid(i.value)!==!0:!1),C=$(()=>({color:u.value,label:d.value,ripple:!1,disable:g.value,...Xt(e.ok)===!0?e.ok:{flat:!0},"data-autofocus":e.focus==="ok"&&c.value!==!0||void 0,onClick:h})),w=$(()=>({color:u.value,label:m.value,ripple:!1,...Xt(e.cancel)===!0?e.cancel:{flat:!0},"data-autofocus":e.focus==="cancel"&&c.value!==!0||void 0,onClick:p}));Ae(()=>e.prompt&&e.prompt.model,S),Ae(()=>e.options&&e.options.model,S);function R(){o.value.show()}function E(){o.value.hide()}function h(){t("ok",me(i.value)),E()}function p(){E()}function b(){t("hide")}function S(L){i.value=L}function x(L){g.value!==!0&&e.prompt.type!=="textarea"&&vs(L,13)===!0&&h()}function F(L,Z){return e.html===!0?q(Xr,{class:L,innerHTML:Z}):q(Xr,{class:L},()=>Z)}function v(){return[q(CC,{color:u.value,dense:!0,autofocus:!0,dark:s.value,...f.value,modelValue:i.value,"onUpdate:modelValue":S,onKeyup:x})]}function O(){return[q(OC,{color:u.value,options:e.options.items,dark:s.value,...f.value,modelValue:i.value,"onUpdate:modelValue":S})]}function I(){const L=[];return e.cancel&&L.push(q(Fa,w.value)),e.ok&&L.push(q(Fa,C.value)),q(QE,{class:e.stackButtons===!0?"items-end":"",vertical:e.stackButtons,align:"right"},()=>L)}function T(){const L=[];return e.title&&L.push(F("q-dialog__title",e.title)),e.progress!==!1&&L.push(q(Xr,{class:"q-dialog__progress"},()=>q(l.value.component,l.value.props))),e.message&&L.push(F("q-dialog__message",e.message)),e.prompt!==void 0?L.push(q(Xr,{class:"scroll q-dialog-plugin__form"},v)):e.options!==void 0&&L.push(q(Uc,{dark:s.value}),q(Xr,{class:"scroll q-dialog-plugin__form"},O),q(Uc,{dark:s.value})),(e.ok||e.cancel)&&L.push(I()),L}function H(){return[q(YE,{class:[a.value,e.cardClass],style:e.cardStyle,dark:s.value},T)]}return Object.assign(n,{show:R,hide:E}),()=>q(GE,{ref:o,onHide:b},H)}});function qm(e,t){for(const n in t)n!=="spinner"&&Object(t[n])===t[n]?(e[n]=Object(e[n])!==e[n]?{}:{...e[n]},qm(e[n],t[n])):e[n]=t[n]}function PC(e,t,n){return r=>{let s,o;const i=t===!0&&r.component!==void 0;if(i===!0){const{component:E,componentProps:h}=r;s=typeof E=="string"?n.component(E):E,o=h||{}}else{const{class:E,style:h,...p}=r;s=e,o=p,E!==void 0&&(p.cardClass=E),h!==void 0&&(p.cardStyle=h)}let a,u=!1;const l=ge(null),c=ku(!1,"dialog"),f=E=>{if(l.value!==null&&l.value[E]!==void 0){l.value[E]();return}const h=a.$.subTree;if(h&&h.component){if(h.component.proxy&&h.component.proxy[E]){h.component.proxy[E]();return}if(h.component.subTree&&h.component.subTree.component&&h.component.subTree.component.proxy&&h.component.subTree.component.proxy[E]){h.component.subTree.component.proxy[E]();return}}console.error("[Quasar] Incorrectly defined Dialog component")},d=[],m=[],g={onOk(E){return d.push(E),g},onCancel(E){return m.push(E),g},onDismiss(E){return d.push(E),m.push(E),g},hide(){return f("hide"),g},update(E){if(a!==null){if(i===!0)Object.assign(o,E);else{const{class:h,style:p,...b}=E;h!==void 0&&(b.cardClass=h),p!==void 0&&(b.cardStyle=p),qm(o,b)}a.$forceUpdate()}return g}},C=E=>{u=!0,d.forEach(h=>{h(E)})},w=()=>{R.unmount(c),Wh(c),R=null,a=null,u!==!0&&m.forEach(E=>{E()})};let R=Ch({name:"QGlobalDialog",setup:()=>()=>q(s,{...o,ref:l,onOk:C,onHide:w,onVnodeMounted(...E){typeof o.onVnodeMounted=="function"&&o.onVnodeMounted(...E),rt(()=>f("show"))}})},n);return a=R.mount(c),g}}var FC={install({$q:e,parentApp:t}){e.dialog=this.create=PC(RC,!0,t)}};/*!
  * shared v9.14.0
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */const Vo=typeof window!="undefined",fn=(e,t=!1)=>t?Symbol.for(e):Symbol(e),DC=(e,t,n)=>NC({l:e,k:t,s:n}),NC=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),Qe=e=>typeof e=="number"&&isFinite(e),LC=e=>jm(e)==="[object Date]",qo=e=>jm(e)==="[object RegExp]",bi=e=>be(e)&&Object.keys(e).length===0,dt=Object.assign;let Gc;const Ho=()=>Gc||(Gc=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:typeof global!="undefined"?global:{});function Yc(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const IC=Object.prototype.hasOwnProperty;function jo(e,t){return IC.call(e,t)}const ze=Array.isArray,Me=e=>typeof e=="function",ae=e=>typeof e=="string",Ne=e=>typeof e=="boolean",Se=e=>e!==null&&typeof e=="object",BC=e=>Se(e)&&Me(e.then)&&Me(e.catch),Hm=Object.prototype.toString,jm=e=>Hm.call(e),be=e=>{if(!Se(e))return!1;const t=Object.getPrototypeOf(e);return t===null||t.constructor===Object},MC=e=>e==null?"":ze(e)||be(e)&&e.toString===Hm?JSON.stringify(e,null,2):String(e);function $C(e,t=""){return e.reduce((n,r,s)=>s===0?n+r:n+t+r,"")}function Ei(e){let t=e;return()=>++t}function UC(e,t){typeof console!="undefined"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const Qs=e=>!Se(e)||ze(e);function ho(e,t){if(Qs(e)||Qs(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:r,des:s}=n.pop();Object.keys(r).forEach(o=>{Qs(r[o])||Qs(s[o])?s[o]=r[o]:n.push({src:r[o],des:s[o]})})}}/*!
  * message-compiler v9.14.0
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function VC(e,t,n){return{line:e,column:t,offset:n}}function Wo(e,t,n){const r={start:e,end:t};return n!=null&&(r.source=n),r}const qC=/\{([0-9a-zA-Z]+)\}/g;function Wm(e,...t){return t.length===1&&HC(t[0])&&(t=t[0]),(!t||!t.hasOwnProperty)&&(t={}),e.replace(qC,(n,r)=>t.hasOwnProperty(r)?t[r]:"")}const Km=Object.assign,Qc=e=>typeof e=="string",HC=e=>e!==null&&typeof e=="object";function Xm(e,t=""){return e.reduce((n,r,s)=>s===0?n+r:n+t+r,"")}const Uu={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2},jC={[Uu.USE_MODULO_SYNTAX]:"Use modulo before '{{0}}'."};function WC(e,t,...n){const r=Wm(jC[e]||"",...n||[]),s={message:String(r),code:e};return t&&(s.location=t),s}const fe={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},KC={[fe.EXPECTED_TOKEN]:"Expected token: '{0}'",[fe.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[fe.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[fe.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[fe.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[fe.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[fe.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[fe.EMPTY_PLACEHOLDER]:"Empty placeholder",[fe.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[fe.INVALID_LINKED_FORMAT]:"Invalid linked format",[fe.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[fe.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[fe.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[fe.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[fe.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[fe.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function $r(e,t,n={}){const{domain:r,messages:s,args:o}=n,i=Wm((s||KC)[e]||"",...o||[]),a=new SyntaxError(String(i));return a.code=e,t&&(a.location=t),a.domain=r,a}function XC(e){throw e}const Qt=" ",zC="\r",ut=`
`,GC=String.fromCharCode(8232),YC=String.fromCharCode(8233);function QC(e){const t=e;let n=0,r=1,s=1,o=0;const i=x=>t[x]===zC&&t[x+1]===ut,a=x=>t[x]===ut,u=x=>t[x]===YC,l=x=>t[x]===GC,c=x=>i(x)||a(x)||u(x)||l(x),f=()=>n,d=()=>r,m=()=>s,g=()=>o,C=x=>i(x)||u(x)||l(x)?ut:t[x],w=()=>C(n),R=()=>C(n+o);function E(){return o=0,c(n)&&(r++,s=0),i(n)&&n++,n++,s++,t[n]}function h(){return i(n+o)&&o++,o++,t[n+o]}function p(){n=0,r=1,s=1,o=0}function b(x=0){o=x}function S(){const x=n+o;for(;x!==n;)E();o=0}return{index:f,line:d,column:m,peekOffset:g,charAt:C,currentChar:w,currentPeek:R,next:E,peek:h,reset:p,resetPeek:b,skipToPeek:S}}const pn=void 0,JC=".",Jc="'",ZC="tokenizer";function eA(e,t={}){const n=t.location!==!1,r=QC(e),s=()=>r.index(),o=()=>VC(r.line(),r.column(),r.index()),i=o(),a=s(),u={currentType:14,offset:a,startLoc:i,endLoc:i,lastType:14,lastOffset:a,lastStartLoc:i,lastEndLoc:i,braceNest:0,inLinked:!1,text:""},l=()=>u,{onError:c}=t;function f(_,y,P,...X){const J=l();if(y.column+=P,y.offset+=P,c){const Y=n?Wo(J.startLoc,y):null,D=$r(_,Y,{domain:ZC,args:X});c(D)}}function d(_,y,P){_.endLoc=o(),_.currentType=y;const X={type:y};return n&&(X.loc=Wo(_.startLoc,_.endLoc)),P!=null&&(X.value=P),X}const m=_=>d(_,14);function g(_,y){return _.currentChar()===y?(_.next(),y):(f(fe.EXPECTED_TOKEN,o(),0,y),"")}function C(_){let y="";for(;_.currentPeek()===Qt||_.currentPeek()===ut;)y+=_.currentPeek(),_.peek();return y}function w(_){const y=C(_);return _.skipToPeek(),y}function R(_){if(_===pn)return!1;const y=_.charCodeAt(0);return y>=97&&y<=122||y>=65&&y<=90||y===95}function E(_){if(_===pn)return!1;const y=_.charCodeAt(0);return y>=48&&y<=57}function h(_,y){const{currentType:P}=y;if(P!==2)return!1;C(_);const X=R(_.currentPeek());return _.resetPeek(),X}function p(_,y){const{currentType:P}=y;if(P!==2)return!1;C(_);const X=_.currentPeek()==="-"?_.peek():_.currentPeek(),J=E(X);return _.resetPeek(),J}function b(_,y){const{currentType:P}=y;if(P!==2)return!1;C(_);const X=_.currentPeek()===Jc;return _.resetPeek(),X}function S(_,y){const{currentType:P}=y;if(P!==8)return!1;C(_);const X=_.currentPeek()===".";return _.resetPeek(),X}function x(_,y){const{currentType:P}=y;if(P!==9)return!1;C(_);const X=R(_.currentPeek());return _.resetPeek(),X}function F(_,y){const{currentType:P}=y;if(!(P===8||P===12))return!1;C(_);const X=_.currentPeek()===":";return _.resetPeek(),X}function v(_,y){const{currentType:P}=y;if(P!==10)return!1;const X=()=>{const Y=_.currentPeek();return Y==="{"?R(_.peek()):Y==="@"||Y==="%"||Y==="|"||Y===":"||Y==="."||Y===Qt||!Y?!1:Y===ut?(_.peek(),X()):T(_,!1)},J=X();return _.resetPeek(),J}function O(_){C(_);const y=_.currentPeek()==="|";return _.resetPeek(),y}function I(_){const y=C(_),P=_.currentPeek()==="%"&&_.peek()==="{";return _.resetPeek(),{isModulo:P,hasSpace:y.length>0}}function T(_,y=!0){const P=(J=!1,Y="",D=!1)=>{const U=_.currentPeek();return U==="{"?Y==="%"?!1:J:U==="@"||!U?Y==="%"?!0:J:U==="%"?(_.peek(),P(J,"%",!0)):U==="|"?Y==="%"||D?!0:!(Y===Qt||Y===ut):U===Qt?(_.peek(),P(!0,Qt,D)):U===ut?(_.peek(),P(!0,ut,D)):!0},X=P();return y&&_.resetPeek(),X}function H(_,y){const P=_.currentChar();return P===pn?pn:y(P)?(_.next(),P):null}function L(_){const y=_.charCodeAt(0);return y>=97&&y<=122||y>=65&&y<=90||y>=48&&y<=57||y===95||y===36}function Z(_){return H(_,L)}function W(_){const y=_.charCodeAt(0);return y>=97&&y<=122||y>=65&&y<=90||y>=48&&y<=57||y===95||y===36||y===45}function Q(_){return H(_,W)}function V(_){const y=_.charCodeAt(0);return y>=48&&y<=57}function se(_){return H(_,V)}function he(_){const y=_.charCodeAt(0);return y>=48&&y<=57||y>=65&&y<=70||y>=97&&y<=102}function de(_){return H(_,he)}function z(_){let y="",P="";for(;y=se(_);)P+=y;return P}function _e(_){w(_);const y=_.currentChar();return y!=="%"&&f(fe.EXPECTED_TOKEN,o(),0,y),_.next(),"%"}function Pe(_){let y="";for(;;){const P=_.currentChar();if(P==="{"||P==="}"||P==="@"||P==="|"||!P)break;if(P==="%")if(T(_))y+=P,_.next();else break;else if(P===Qt||P===ut)if(T(_))y+=P,_.next();else{if(O(_))break;y+=P,_.next()}else y+=P,_.next()}return y}function Ie(_){w(_);let y="",P="";for(;y=Q(_);)P+=y;return _.currentChar()===pn&&f(fe.UNTERMINATED_CLOSING_BRACE,o(),0),P}function oe(_){w(_);let y="";return _.currentChar()==="-"?(_.next(),y+=`-${z(_)}`):y+=z(_),_.currentChar()===pn&&f(fe.UNTERMINATED_CLOSING_BRACE,o(),0),y}function B(_){return _!==Jc&&_!==ut}function te(_){w(_),g(_,"'");let y="",P="";for(;y=H(_,B);)y==="\\"?P+=ee(_):P+=y;const X=_.currentChar();return X===ut||X===pn?(f(fe.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),0),X===ut&&(_.next(),g(_,"'")),P):(g(_,"'"),P)}function ee(_){const y=_.currentChar();switch(y){case"\\":case"'":return _.next(),`\\${y}`;case"u":return re(_,y,4);case"U":return re(_,y,6);default:return f(fe.UNKNOWN_ESCAPE_SEQUENCE,o(),0,y),""}}function re(_,y,P){g(_,y);let X="";for(let J=0;J<P;J++){const Y=de(_);if(!Y){f(fe.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),0,`\\${y}${X}${_.currentChar()}`);break}X+=Y}return`\\${y}${X}`}function ye(_){return _!=="{"&&_!=="}"&&_!==Qt&&_!==ut}function Te(_){w(_);let y="",P="";for(;y=H(_,ye);)P+=y;return P}function A(_){let y="",P="";for(;y=Z(_);)P+=y;return P}function k(_){const y=P=>{const X=_.currentChar();return X==="{"||X==="%"||X==="@"||X==="|"||X==="("||X===")"||!X||X===Qt?P:(P+=X,_.next(),y(P))};return y("")}function N(_){w(_);const y=g(_,"|");return w(_),y}function K(_,y){let P=null;switch(_.currentChar()){case"{":return y.braceNest>=1&&f(fe.NOT_ALLOW_NEST_PLACEHOLDER,o(),0),_.next(),P=d(y,2,"{"),w(_),y.braceNest++,P;case"}":return y.braceNest>0&&y.currentType===2&&f(fe.EMPTY_PLACEHOLDER,o(),0),_.next(),P=d(y,3,"}"),y.braceNest--,y.braceNest>0&&w(_),y.inLinked&&y.braceNest===0&&(y.inLinked=!1),P;case"@":return y.braceNest>0&&f(fe.UNTERMINATED_CLOSING_BRACE,o(),0),P=j(_,y)||m(y),y.braceNest=0,P;default:{let J=!0,Y=!0,D=!0;if(O(_))return y.braceNest>0&&f(fe.UNTERMINATED_CLOSING_BRACE,o(),0),P=d(y,1,N(_)),y.braceNest=0,y.inLinked=!1,P;if(y.braceNest>0&&(y.currentType===5||y.currentType===6||y.currentType===7))return f(fe.UNTERMINATED_CLOSING_BRACE,o(),0),y.braceNest=0,G(_,y);if(J=h(_,y))return P=d(y,5,Ie(_)),w(_),P;if(Y=p(_,y))return P=d(y,6,oe(_)),w(_),P;if(D=b(_,y))return P=d(y,7,te(_)),w(_),P;if(!J&&!Y&&!D)return P=d(y,13,Te(_)),f(fe.INVALID_TOKEN_IN_PLACEHOLDER,o(),0,P.value),w(_),P;break}}return P}function j(_,y){const{currentType:P}=y;let X=null;const J=_.currentChar();switch((P===8||P===9||P===12||P===10)&&(J===ut||J===Qt)&&f(fe.INVALID_LINKED_FORMAT,o(),0),J){case"@":return _.next(),X=d(y,8,"@"),y.inLinked=!0,X;case".":return w(_),_.next(),d(y,9,".");case":":return w(_),_.next(),d(y,10,":");default:return O(_)?(X=d(y,1,N(_)),y.braceNest=0,y.inLinked=!1,X):S(_,y)||F(_,y)?(w(_),j(_,y)):x(_,y)?(w(_),d(y,12,A(_))):v(_,y)?(w(_),J==="{"?K(_,y)||X:d(y,11,k(_))):(P===8&&f(fe.INVALID_LINKED_FORMAT,o(),0),y.braceNest=0,y.inLinked=!1,G(_,y))}}function G(_,y){let P={type:14};if(y.braceNest>0)return K(_,y)||m(y);if(y.inLinked)return j(_,y)||m(y);switch(_.currentChar()){case"{":return K(_,y)||m(y);case"}":return f(fe.UNBALANCED_CLOSING_BRACE,o(),0),_.next(),d(y,3,"}");case"@":return j(_,y)||m(y);default:{if(O(_))return P=d(y,1,N(_)),y.braceNest=0,y.inLinked=!1,P;const{isModulo:J,hasSpace:Y}=I(_);if(J)return Y?d(y,0,Pe(_)):d(y,4,_e(_));if(T(_))return d(y,0,Pe(_));break}}return P}function ne(){const{currentType:_,offset:y,startLoc:P,endLoc:X}=u;return u.lastType=_,u.lastOffset=y,u.lastStartLoc=P,u.lastEndLoc=X,u.offset=s(),u.startLoc=o(),r.currentChar()===pn?d(u,14):G(r,u)}return{nextToken:ne,currentOffset:s,currentPosition:o,context:l}}const tA="parser",nA=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function rA(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const r=parseInt(t||n,16);return r<=55295||r>=57344?String.fromCodePoint(r):"\uFFFD"}}}function sA(e={}){const t=e.location!==!1,{onError:n,onWarn:r}=e;function s(h,p,b,S,...x){const F=h.currentPosition();if(F.offset+=S,F.column+=S,n){const v=t?Wo(b,F):null,O=$r(p,v,{domain:tA,args:x});n(O)}}function o(h,p,b,S,...x){const F=h.currentPosition();if(F.offset+=S,F.column+=S,r){const v=t?Wo(b,F):null;r(WC(p,v,x))}}function i(h,p,b){const S={type:h};return t&&(S.start=p,S.end=p,S.loc={start:b,end:b}),S}function a(h,p,b,S){S&&(h.type=S),t&&(h.end=p,h.loc&&(h.loc.end=b))}function u(h,p){const b=h.context(),S=i(3,b.offset,b.startLoc);return S.value=p,a(S,h.currentOffset(),h.currentPosition()),S}function l(h,p){const b=h.context(),{lastOffset:S,lastStartLoc:x}=b,F=i(5,S,x);return F.index=parseInt(p,10),h.nextToken(),a(F,h.currentOffset(),h.currentPosition()),F}function c(h,p,b){const S=h.context(),{lastOffset:x,lastStartLoc:F}=S,v=i(4,x,F);return v.key=p,b===!0&&(v.modulo=!0),h.nextToken(),a(v,h.currentOffset(),h.currentPosition()),v}function f(h,p){const b=h.context(),{lastOffset:S,lastStartLoc:x}=b,F=i(9,S,x);return F.value=p.replace(nA,rA),h.nextToken(),a(F,h.currentOffset(),h.currentPosition()),F}function d(h){const p=h.nextToken(),b=h.context(),{lastOffset:S,lastStartLoc:x}=b,F=i(8,S,x);return p.type!==12?(s(h,fe.UNEXPECTED_EMPTY_LINKED_MODIFIER,b.lastStartLoc,0),F.value="",a(F,S,x),{nextConsumeToken:p,node:F}):(p.value==null&&s(h,fe.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,Ot(p)),F.value=p.value||"",a(F,h.currentOffset(),h.currentPosition()),{node:F})}function m(h,p){const b=h.context(),S=i(7,b.offset,b.startLoc);return S.value=p,a(S,h.currentOffset(),h.currentPosition()),S}function g(h){const p=h.context(),b=i(6,p.offset,p.startLoc);let S=h.nextToken();if(S.type===9){const x=d(h);b.modifier=x.node,S=x.nextConsumeToken||h.nextToken()}switch(S.type!==10&&s(h,fe.UNEXPECTED_LEXICAL_ANALYSIS,p.lastStartLoc,0,Ot(S)),S=h.nextToken(),S.type===2&&(S=h.nextToken()),S.type){case 11:S.value==null&&s(h,fe.UNEXPECTED_LEXICAL_ANALYSIS,p.lastStartLoc,0,Ot(S)),b.key=m(h,S.value||"");break;case 5:S.value==null&&s(h,fe.UNEXPECTED_LEXICAL_ANALYSIS,p.lastStartLoc,0,Ot(S)),b.key=c(h,S.value||"");break;case 6:S.value==null&&s(h,fe.UNEXPECTED_LEXICAL_ANALYSIS,p.lastStartLoc,0,Ot(S)),b.key=l(h,S.value||"");break;case 7:S.value==null&&s(h,fe.UNEXPECTED_LEXICAL_ANALYSIS,p.lastStartLoc,0,Ot(S)),b.key=f(h,S.value||"");break;default:{s(h,fe.UNEXPECTED_EMPTY_LINKED_KEY,p.lastStartLoc,0);const x=h.context(),F=i(7,x.offset,x.startLoc);return F.value="",a(F,x.offset,x.startLoc),b.key=F,a(b,x.offset,x.startLoc),{nextConsumeToken:S,node:b}}}return a(b,h.currentOffset(),h.currentPosition()),{node:b}}function C(h){const p=h.context(),b=p.currentType===1?h.currentOffset():p.offset,S=p.currentType===1?p.endLoc:p.startLoc,x=i(2,b,S);x.items=[];let F=null,v=null;do{const T=F||h.nextToken();switch(F=null,T.type){case 0:T.value==null&&s(h,fe.UNEXPECTED_LEXICAL_ANALYSIS,p.lastStartLoc,0,Ot(T)),x.items.push(u(h,T.value||""));break;case 6:T.value==null&&s(h,fe.UNEXPECTED_LEXICAL_ANALYSIS,p.lastStartLoc,0,Ot(T)),x.items.push(l(h,T.value||""));break;case 4:v=!0;break;case 5:T.value==null&&s(h,fe.UNEXPECTED_LEXICAL_ANALYSIS,p.lastStartLoc,0,Ot(T)),x.items.push(c(h,T.value||"",!!v)),v&&(o(h,Uu.USE_MODULO_SYNTAX,p.lastStartLoc,0,Ot(T)),v=null);break;case 7:T.value==null&&s(h,fe.UNEXPECTED_LEXICAL_ANALYSIS,p.lastStartLoc,0,Ot(T)),x.items.push(f(h,T.value||""));break;case 8:{const H=g(h);x.items.push(H.node),F=H.nextConsumeToken||null;break}}}while(p.currentType!==14&&p.currentType!==1);const O=p.currentType===1?p.lastOffset:h.currentOffset(),I=p.currentType===1?p.lastEndLoc:h.currentPosition();return a(x,O,I),x}function w(h,p,b,S){const x=h.context();let F=S.items.length===0;const v=i(1,p,b);v.cases=[],v.cases.push(S);do{const O=C(h);F||(F=O.items.length===0),v.cases.push(O)}while(x.currentType!==14);return F&&s(h,fe.MUST_HAVE_MESSAGES_IN_PLURAL,b,0),a(v,h.currentOffset(),h.currentPosition()),v}function R(h){const p=h.context(),{offset:b,startLoc:S}=p,x=C(h);return p.currentType===14?x:w(h,b,S,x)}function E(h){const p=eA(h,Km({},e)),b=p.context(),S=i(0,b.offset,b.startLoc);return t&&S.loc&&(S.loc.source=h),S.body=R(p),e.onCacheKey&&(S.cacheKey=e.onCacheKey(h)),b.currentType!==14&&s(p,fe.UNEXPECTED_LEXICAL_ANALYSIS,b.lastStartLoc,0,h[b.offset]||""),a(S,p.currentOffset(),p.currentPosition()),S}return{parse:E}}function Ot(e){if(e.type===14)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"\u2026":t}function oA(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:o=>(n.helpers.add(o),o)}}function Zc(e,t){for(let n=0;n<e.length;n++)Vu(e[n],t)}function Vu(e,t){switch(e.type){case 1:Zc(e.cases,t),t.helper("plural");break;case 2:Zc(e.items,t);break;case 6:{Vu(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function iA(e,t={}){const n=oA(e);n.helper("normalize"),e.body&&Vu(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function aA(e){const t=e.body;return t.type===2?ef(t):t.cases.forEach(n=>ef(n)),e}function ef(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(!(r.type===3||r.type===9)||r.value==null)break;t.push(r.value)}if(t.length===e.items.length){e.static=Xm(t);for(let n=0;n<e.items.length;n++){const r=e.items[n];(r.type===3||r.type===9)&&delete r.value}}}}const uA="minifier";function pr(e){switch(e.t=e.type,e.type){case 0:{const t=e;pr(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let r=0;r<n.length;r++)pr(n[r]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let r=0;r<n.length;r++)pr(n[r]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;pr(t.key),t.k=t.key,delete t.key,t.modifier&&(pr(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}default:throw $r(fe.UNHANDLED_MINIFIER_NODE_TYPE,null,{domain:uA,args:[e.type]})}delete e.type}const lA="parser";function cA(e,t){const{sourceMap:n,filename:r,breakLineCode:s,needIndent:o}=t,i=t.location!==!1,a={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:s,needIndent:o,indentLevel:0};i&&e.loc&&(a.source=e.loc.source);const u=()=>a;function l(w,R){a.code+=w}function c(w,R=!0){const E=R?s:"";l(o?E+"  ".repeat(w):E)}function f(w=!0){const R=++a.indentLevel;w&&c(R)}function d(w=!0){const R=--a.indentLevel;w&&c(R)}function m(){c(a.indentLevel)}return{context:u,push:l,indent:f,deindent:d,newline:m,helper:w=>`_${w}`,needIndent:()=>a.needIndent}}function fA(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),Fr(e,t.key),t.modifier?(e.push(", "),Fr(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function dA(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const s=t.items.length;for(let o=0;o<s&&(Fr(e,t.items[o]),o!==s-1);o++)e.push(", ");e.deindent(r()),e.push("])")}function hA(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const s=t.cases.length;for(let o=0;o<s&&(Fr(e,t.cases[o]),o!==s-1);o++)e.push(", ");e.deindent(r()),e.push("])")}}function mA(e,t){t.body?Fr(e,t.body):e.push("null")}function Fr(e,t){const{helper:n}=e;switch(t.type){case 0:mA(e,t);break;case 1:hA(e,t);break;case 2:dA(e,t);break;case 6:fA(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break;default:throw $r(fe.UNHANDLED_CODEGEN_NODE_TYPE,null,{domain:lA,args:[t.type]})}}const pA=(e,t={})=>{const n=Qc(t.mode)?t.mode:"normal",r=Qc(t.filename)?t.filename:"message.intl",s=!!t.sourceMap,o=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,i=t.needIndent?t.needIndent:n!=="arrow",a=e.helpers||[],u=cA(e,{mode:n,filename:r,sourceMap:s,breakLineCode:o,needIndent:i});u.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),u.indent(i),a.length>0&&(u.push(`const { ${Xm(a.map(f=>`${f}: _${f}`),", ")} } = ctx`),u.newline()),u.push("return "),Fr(u,e),u.deindent(i),u.push("}"),delete e.helpers;const{code:l,map:c}=u.context();return{ast:e,code:l,map:c?c.toJSON():void 0}};function gA(e,t={}){const n=Km({},t),r=!!n.jit,s=!!n.minify,o=n.optimize==null?!0:n.optimize,a=sA(n).parse(e);return r?(o&&aA(a),s&&pr(a),{ast:a,code:""}):(iA(a,n),pA(a,n))}/*!
  * core-base v9.14.0
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function _A(){typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(Ho().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Ho().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}const Nn=[];Nn[0]={w:[0],i:[3,0],["["]:[4],o:[7]};Nn[1]={w:[1],["."]:[2],["["]:[4],o:[7]};Nn[2]={w:[2],i:[3,0],[0]:[3,0]};Nn[3]={i:[3,0],[0]:[3,0],w:[1,1],["."]:[2,1],["["]:[4,1],o:[7,1]};Nn[4]={["'"]:[5,0],['"']:[6,0],["["]:[4,2],["]"]:[1,3],o:8,l:[4,0]};Nn[5]={["'"]:[4,0],o:8,l:[5,0]};Nn[6]={['"']:[4,0],o:8,l:[6,0]};const vA=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function yA(e){return vA.test(e)}function bA(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function EA(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function CA(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:yA(t)?bA(t):"*"+t}function AA(e){const t=[];let n=-1,r=0,s=0,o,i,a,u,l,c,f;const d=[];d[0]=()=>{i===void 0?i=a:i+=a},d[1]=()=>{i!==void 0&&(t.push(i),i=void 0)},d[2]=()=>{d[0](),s++},d[3]=()=>{if(s>0)s--,r=4,d[0]();else{if(s=0,i===void 0||(i=CA(i),i===!1))return!1;d[1]()}};function m(){const g=e[n+1];if(r===5&&g==="'"||r===6&&g==='"')return n++,a="\\"+g,d[0](),!0}for(;r!==null;)if(n++,o=e[n],!(o==="\\"&&m())){if(u=EA(o),f=Nn[r],l=f[u]||f.l||8,l===8||(r=l[0],l[1]!==void 0&&(c=d[l[1]],c&&(a=o,c()===!1))))return;if(r===7)return t}}const tf=new Map;function SA(e,t){return Se(e)?e[t]:null}function wA(e,t){if(!Se(e))return null;let n=tf.get(t);if(n||(n=AA(t),n&&tf.set(t,n)),!n)return null;const r=n.length;let s=e,o=0;for(;o<r;){const i=s[n[o]];if(i===void 0||Me(s))return null;s=i,o++}return s}const TA=e=>e,kA=e=>"",xA="text",OA=e=>e.length===0?"":$C(e),RA=MC;function nf(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function PA(e){const t=Qe(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(Qe(e.named.count)||Qe(e.named.n))?Qe(e.named.count)?e.named.count:Qe(e.named.n)?e.named.n:t:t}function FA(e,t){t.count||(t.count=e),t.n||(t.n=e)}function DA(e={}){const t=e.locale,n=PA(e),r=Se(e.pluralRules)&&ae(t)&&Me(e.pluralRules[t])?e.pluralRules[t]:nf,s=Se(e.pluralRules)&&ae(t)&&Me(e.pluralRules[t])?nf:void 0,o=R=>R[r(n,R.length,s)],i=e.list||[],a=R=>i[R],u=e.named||{};Qe(e.pluralIndex)&&FA(n,u);const l=R=>u[R];function c(R){const E=Me(e.messages)?e.messages(R):Se(e.messages)?e.messages[R]:!1;return E||(e.parent?e.parent.message(R):kA)}const f=R=>e.modifiers?e.modifiers[R]:TA,d=be(e.processor)&&Me(e.processor.normalize)?e.processor.normalize:OA,m=be(e.processor)&&Me(e.processor.interpolate)?e.processor.interpolate:RA,g=be(e.processor)&&ae(e.processor.type)?e.processor.type:xA,w={list:a,named:l,plural:o,linked:(R,...E)=>{const[h,p]=E;let b="text",S="";E.length===1?Se(h)?(S=h.modifier||S,b=h.type||b):ae(h)&&(S=h||S):E.length===2&&(ae(h)&&(S=h||S),ae(p)&&(b=p||b));const x=c(R)(w),F=b==="vnode"&&ze(x)&&S?x[0]:x;return S?f(S)(F,b):F},message:c,type:g,interpolate:m,normalize:d,values:dt({},i,u)};return w}const zm=Uu.__EXTEND_POINT__,Mn=Ei(zm),NA={NOT_FOUND_KEY:zm,FALLBACK_TO_TRANSLATE:Mn(),CANNOT_FORMAT_NUMBER:Mn(),FALLBACK_TO_NUMBER_FORMAT:Mn(),CANNOT_FORMAT_DATE:Mn(),FALLBACK_TO_DATE_FORMAT:Mn(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:Mn(),__EXTEND_POINT__:Mn()},Gm=fe.__EXTEND_POINT__,$n=Ei(Gm),Dt={INVALID_ARGUMENT:Gm,INVALID_DATE_ARGUMENT:$n(),INVALID_ISO_DATE_ARGUMENT:$n(),NOT_SUPPORT_NON_STRING_MESSAGE:$n(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:$n(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:$n(),NOT_SUPPORT_LOCALE_TYPE:$n(),__EXTEND_POINT__:$n()};function zt(e){return $r(e,null,void 0)}function qu(e,t){return t.locale!=null?rf(t.locale):rf(e.locale)}let ea;function rf(e){if(ae(e))return e;if(Me(e)){if(e.resolvedOnce&&ea!=null)return ea;if(e.constructor.name==="Function"){const t=e();if(BC(t))throw zt(Dt.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return ea=t}else throw zt(Dt.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw zt(Dt.NOT_SUPPORT_LOCALE_TYPE)}function LA(e,t,n){return[...new Set([n,...ze(t)?t:Se(t)?Object.keys(t):ae(t)?[t]:[n]])]}function Ym(e,t,n){const r=ae(n)?n:Ko,s=e;s.__localeChainCache||(s.__localeChainCache=new Map);let o=s.__localeChainCache.get(r);if(!o){o=[];let i=[n];for(;ze(i);)i=sf(o,i,t);const a=ze(t)||!be(t)?t:t.default?t.default:null;i=ae(a)?[a]:a,ze(i)&&sf(o,i,!1),s.__localeChainCache.set(r,o)}return o}function sf(e,t,n){let r=!0;for(let s=0;s<t.length&&Ne(r);s++){const o=t[s];ae(o)&&(r=IA(e,t[s],n))}return r}function IA(e,t,n){let r;const s=t.split("-");do{const o=s.join("-");r=BA(e,o,n),s.splice(-1,1)}while(s.length&&r===!0);return r}function BA(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r=t[t.length-1]!=="!";const s=t.replace(/!/g,"");e.push(s),(ze(n)||be(n))&&n[s]&&(r=n[s])}return r}const MA="9.14.0",Ci=-1,Ko="en-US",of="",af=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function $A(){return{upper:(e,t)=>t==="text"&&ae(e)?e.toUpperCase():t==="vnode"&&Se(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&ae(e)?e.toLowerCase():t==="vnode"&&Se(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&ae(e)?af(e):t==="vnode"&&Se(e)&&"__v_isVNode"in e?af(e.children):e}}let Qm;function uf(e){Qm=e}let Jm;function UA(e){Jm=e}let Zm;function VA(e){Zm=e}let ep=null;const lf=e=>{ep=e},qA=()=>ep;let cf=0;function HA(e={}){const t=Me(e.onWarn)?e.onWarn:UC,n=ae(e.version)?e.version:MA,r=ae(e.locale)||Me(e.locale)?e.locale:Ko,s=Me(r)?Ko:r,o=ze(e.fallbackLocale)||be(e.fallbackLocale)||ae(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:s,i=be(e.messages)?e.messages:{[s]:{}},a=be(e.datetimeFormats)?e.datetimeFormats:{[s]:{}},u=be(e.numberFormats)?e.numberFormats:{[s]:{}},l=dt({},e.modifiers||{},$A()),c=e.pluralRules||{},f=Me(e.missing)?e.missing:null,d=Ne(e.missingWarn)||qo(e.missingWarn)?e.missingWarn:!0,m=Ne(e.fallbackWarn)||qo(e.fallbackWarn)?e.fallbackWarn:!0,g=!!e.fallbackFormat,C=!!e.unresolving,w=Me(e.postTranslation)?e.postTranslation:null,R=be(e.processor)?e.processor:null,E=Ne(e.warnHtmlMessage)?e.warnHtmlMessage:!0,h=!!e.escapeParameter,p=Me(e.messageCompiler)?e.messageCompiler:Qm,b=Me(e.messageResolver)?e.messageResolver:Jm||SA,S=Me(e.localeFallbacker)?e.localeFallbacker:Zm||LA,x=Se(e.fallbackContext)?e.fallbackContext:void 0,F=e,v=Se(F.__datetimeFormatters)?F.__datetimeFormatters:new Map,O=Se(F.__numberFormatters)?F.__numberFormatters:new Map,I=Se(F.__meta)?F.__meta:{};cf++;const T={version:n,cid:cf,locale:r,fallbackLocale:o,messages:i,modifiers:l,pluralRules:c,missing:f,missingWarn:d,fallbackWarn:m,fallbackFormat:g,unresolving:C,postTranslation:w,processor:R,warnHtmlMessage:E,escapeParameter:h,messageCompiler:p,messageResolver:b,localeFallbacker:S,fallbackContext:x,onWarn:t,__meta:I};return T.datetimeFormats=a,T.numberFormats=u,T.__datetimeFormatters=v,T.__numberFormatters=O,T}function Hu(e,t,n,r,s){const{missing:o,onWarn:i}=e;if(o!==null){const a=o(e,n,t,s);return ae(a)?a:t}else return t}function zr(e,t,n){const r=e;r.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function jA(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function WA(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let r=n+1;r<t.length;r++)if(jA(e,t[r]))return!0;return!1}function ta(e){return n=>KA(n,e)}function KA(e,t){const n=t.b||t.body;if((n.t||n.type)===1){const r=n,s=r.c||r.cases;return e.plural(s.reduce((o,i)=>[...o,ff(e,i)],[]))}else return ff(e,n)}function ff(e,t){const n=t.s||t.static;if(n)return e.type==="text"?n:e.normalize([n]);{const r=(t.i||t.items).reduce((s,o)=>[...s,Ka(e,o)],[]);return e.normalize(r)}}function Ka(e,t){const n=t.t||t.type;switch(n){case 3:{const r=t;return r.v||r.value}case 9:{const r=t;return r.v||r.value}case 4:{const r=t;return e.interpolate(e.named(r.k||r.key))}case 5:{const r=t;return e.interpolate(e.list(r.i!=null?r.i:r.index))}case 6:{const r=t,s=r.m||r.modifier;return e.linked(Ka(e,r.k||r.key),s?Ka(e,s):void 0,e.type)}case 7:{const r=t;return r.v||r.value}case 8:{const r=t;return r.v||r.value}default:throw new Error(`unhandled node type on format message part: ${n}`)}}const tp=e=>e;let _r=Object.create(null);const Dr=e=>Se(e)&&(e.t===0||e.type===0)&&("b"in e||"body"in e);function np(e,t={}){let n=!1;const r=t.onError||XC;return t.onError=s=>{n=!0,r(s)},{...gA(e,t),detectError:n}}const XA=(e,t)=>{if(!ae(e))throw zt(Dt.NOT_SUPPORT_NON_STRING_MESSAGE);{Ne(t.warnHtmlMessage)&&t.warnHtmlMessage;const r=(t.onCacheKey||tp)(e),s=_r[r];if(s)return s;const{code:o,detectError:i}=np(e,t),a=new Function(`return ${o}`)();return i?a:_r[r]=a}};function zA(e,t){if(__INTLIFY_JIT_COMPILATION__&&!__INTLIFY_DROP_MESSAGE_COMPILER__&&ae(e)){Ne(t.warnHtmlMessage)&&t.warnHtmlMessage;const r=(t.onCacheKey||tp)(e),s=_r[r];if(s)return s;const{ast:o,detectError:i}=np(e,{...t,location:!1,jit:!0}),a=ta(o);return i?a:_r[r]=a}else{const n=e.cacheKey;if(n){const r=_r[n];return r||(_r[n]=ta(e))}else return ta(e)}}const df=()=>"",on=e=>Me(e);function hf(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:s,messageCompiler:o,fallbackLocale:i,messages:a}=e,[u,l]=Xa(...t),c=Ne(l.missingWarn)?l.missingWarn:e.missingWarn,f=Ne(l.fallbackWarn)?l.fallbackWarn:e.fallbackWarn,d=Ne(l.escapeParameter)?l.escapeParameter:e.escapeParameter,m=!!l.resolvedMessage,g=ae(l.default)||Ne(l.default)?Ne(l.default)?o?u:()=>u:l.default:n?o?u:()=>u:"",C=n||g!=="",w=qu(e,l);d&&GA(l);let[R,E,h]=m?[u,w,a[w]||{}]:rp(e,u,w,i,f,c),p=R,b=u;if(!m&&!(ae(p)||Dr(p)||on(p))&&C&&(p=g,b=p),!m&&(!(ae(p)||Dr(p)||on(p))||!ae(E)))return s?Ci:u;let S=!1;const x=()=>{S=!0},F=on(p)?p:sp(e,u,E,p,b,x);if(S)return p;const v=JA(e,E,h,l),O=DA(v),I=YA(e,F,O);return r?r(I,u):I}function GA(e){ze(e.list)?e.list=e.list.map(t=>ae(t)?Yc(t):t):Se(e.named)&&Object.keys(e.named).forEach(t=>{ae(e.named[t])&&(e.named[t]=Yc(e.named[t]))})}function rp(e,t,n,r,s,o){const{messages:i,onWarn:a,messageResolver:u,localeFallbacker:l}=e,c=l(e,r,n);let f={},d,m=null;const g="translate";for(let C=0;C<c.length&&(d=c[C],f=i[d]||{},(m=u(f,t))===null&&(m=f[t]),!(ae(m)||Dr(m)||on(m)));C++)if(!WA(d,c)){const w=Hu(e,t,d,o,g);w!==t&&(m=w)}return[m,d,f]}function sp(e,t,n,r,s,o){const{messageCompiler:i,warnHtmlMessage:a}=e;if(on(r)){const l=r;return l.locale=l.locale||n,l.key=l.key||t,l}if(i==null){const l=()=>r;return l.locale=n,l.key=t,l}const u=i(r,QA(e,n,s,r,a,o));return u.locale=n,u.key=t,u.source=r,u}function YA(e,t,n){return t(n)}function Xa(...e){const[t,n,r]=e,s={};if(!ae(t)&&!Qe(t)&&!on(t)&&!Dr(t))throw zt(Dt.INVALID_ARGUMENT);const o=Qe(t)?String(t):(on(t),t);return Qe(n)?s.plural=n:ae(n)?s.default=n:be(n)&&!bi(n)?s.named=n:ze(n)&&(s.list=n),Qe(r)?s.plural=r:ae(r)?s.default=r:be(r)&&dt(s,r),[o,s]}function QA(e,t,n,r,s,o){return{locale:t,key:n,warnHtmlMessage:s,onError:i=>{throw o&&o(i),i},onCacheKey:i=>DC(t,n,i)}}function JA(e,t,n,r){const{modifiers:s,pluralRules:o,messageResolver:i,fallbackLocale:a,fallbackWarn:u,missingWarn:l,fallbackContext:c}=e,d={locale:t,modifiers:s,pluralRules:o,messages:m=>{let g=i(n,m);if(g==null&&c){const[,,C]=rp(c,m,t,a,u,l);g=i(C,m)}if(ae(g)||Dr(g)){let C=!1;const R=sp(e,m,t,g,m,()=>{C=!0});return C?df:R}else return on(g)?g:df}};return e.processor&&(d.processor=e.processor),r.list&&(d.list=r.list),r.named&&(d.named=r.named),Qe(r.plural)&&(d.pluralIndex=r.plural),d}function mf(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:s,onWarn:o,localeFallbacker:i}=e,{__datetimeFormatters:a}=e,[u,l,c,f]=za(...t),d=Ne(c.missingWarn)?c.missingWarn:e.missingWarn;Ne(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn;const m=!!c.part,g=qu(e,c),C=i(e,s,g);if(!ae(u)||u==="")return new Intl.DateTimeFormat(g,f).format(l);let w={},R,E=null;const h="datetime format";for(let S=0;S<C.length&&(R=C[S],w=n[R]||{},E=w[u],!be(E));S++)Hu(e,u,R,d,h);if(!be(E)||!ae(R))return r?Ci:u;let p=`${R}__${u}`;bi(f)||(p=`${p}__${JSON.stringify(f)}`);let b=a.get(p);return b||(b=new Intl.DateTimeFormat(R,dt({},E,f)),a.set(p,b)),m?b.formatToParts(l):b.format(l)}const op=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function za(...e){const[t,n,r,s]=e,o={};let i={},a;if(ae(t)){const u=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!u)throw zt(Dt.INVALID_ISO_DATE_ARGUMENT);const l=u[3]?u[3].trim().startsWith("T")?`${u[1].trim()}${u[3].trim()}`:`${u[1].trim()}T${u[3].trim()}`:u[1].trim();a=new Date(l);try{a.toISOString()}catch{throw zt(Dt.INVALID_ISO_DATE_ARGUMENT)}}else if(LC(t)){if(isNaN(t.getTime()))throw zt(Dt.INVALID_DATE_ARGUMENT);a=t}else if(Qe(t))a=t;else throw zt(Dt.INVALID_ARGUMENT);return ae(n)?o.key=n:be(n)&&Object.keys(n).forEach(u=>{op.includes(u)?i[u]=n[u]:o[u]=n[u]}),ae(r)?o.locale=r:be(r)&&(i=r),be(s)&&(i=s),[o.key||"",a,o,i]}function pf(e,t,n){const r=e;for(const s in n){const o=`${t}__${s}`;!r.__datetimeFormatters.has(o)||r.__datetimeFormatters.delete(o)}}function gf(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:s,onWarn:o,localeFallbacker:i}=e,{__numberFormatters:a}=e,[u,l,c,f]=Ga(...t),d=Ne(c.missingWarn)?c.missingWarn:e.missingWarn;Ne(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn;const m=!!c.part,g=qu(e,c),C=i(e,s,g);if(!ae(u)||u==="")return new Intl.NumberFormat(g,f).format(l);let w={},R,E=null;const h="number format";for(let S=0;S<C.length&&(R=C[S],w=n[R]||{},E=w[u],!be(E));S++)Hu(e,u,R,d,h);if(!be(E)||!ae(R))return r?Ci:u;let p=`${R}__${u}`;bi(f)||(p=`${p}__${JSON.stringify(f)}`);let b=a.get(p);return b||(b=new Intl.NumberFormat(R,dt({},E,f)),a.set(p,b)),m?b.formatToParts(l):b.format(l)}const ip=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Ga(...e){const[t,n,r,s]=e,o={};let i={};if(!Qe(t))throw zt(Dt.INVALID_ARGUMENT);const a=t;return ae(n)?o.key=n:be(n)&&Object.keys(n).forEach(u=>{ip.includes(u)?i[u]=n[u]:o[u]=n[u]}),ae(r)?o.locale=r:be(r)&&(i=r),be(s)&&(i=s),[o.key||"",a,o,i]}function _f(e,t,n){const r=e;for(const s in n){const o=`${t}__${s}`;!r.__numberFormatters.has(o)||r.__numberFormatters.delete(o)}}_A();/*!
  * vue-i18n v9.14.0
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */const ZA="9.14.0";function eS(){typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(Ho().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Ho().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}const ap=NA.__EXTEND_POINT__,Jt=Ei(ap);Jt(),Jt(),Jt(),Jt(),Jt(),Jt(),Jt(),Jt(),Jt();const up=Dt.__EXTEND_POINT__,ht=Ei(up),kt={UNEXPECTED_RETURN_TYPE:up,INVALID_ARGUMENT:ht(),MUST_BE_CALL_SETUP_TOP:ht(),NOT_INSTALLED:ht(),NOT_AVAILABLE_IN_LEGACY_MODE:ht(),REQUIRED_VALUE:ht(),INVALID_VALUE:ht(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:ht(),NOT_INSTALLED_WITH_PROVIDE:ht(),UNEXPECTED_ERROR:ht(),NOT_COMPATIBLE_LEGACY_VUE_I18N:ht(),BRIDGE_SUPPORT_VUE_2_ONLY:ht(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:ht(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:ht(),__EXTEND_POINT__:ht()};function Ut(e,...t){return $r(e,null,void 0)}const Ya=fn("__translateVNode"),Qa=fn("__datetimeParts"),Ja=fn("__numberParts"),tS=fn("__setPluralRules");fn("__intlifyMeta");const nS=fn("__injectWithOption"),Za=fn("__dispose");function Ss(e){if(!Se(e))return e;for(const t in e)if(!!jo(e,t))if(!t.includes("."))Se(e[t])&&Ss(e[t]);else{const n=t.split("."),r=n.length-1;let s=e,o=!1;for(let i=0;i<r;i++){if(n[i]in s||(s[n[i]]={}),!Se(s[n[i]])){o=!0;break}s=s[n[i]]}o||(s[n[r]]=e[t],delete e[t]),Se(s[n[r]])&&Ss(s[n[r]])}return e}function lp(e,t){const{messages:n,__i18n:r,messageResolver:s,flatJson:o}=t,i=be(n)?n:ze(r)?{}:{[e]:{}};if(ze(r)&&r.forEach(a=>{if("locale"in a&&"resource"in a){const{locale:u,resource:l}=a;u?(i[u]=i[u]||{},ho(l,i[u])):ho(l,i)}else ae(a)&&ho(JSON.parse(a),i)}),s==null&&o)for(const a in i)jo(i,a)&&Ss(i[a]);return i}function rS(e){return e.type}function sS(e,t,n){let r=Se(t.messages)?t.messages:{};"__i18nGlobal"in n&&(r=lp(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const s=Object.keys(r);s.length&&s.forEach(o=>{e.mergeLocaleMessage(o,r[o])});{if(Se(t.datetimeFormats)){const o=Object.keys(t.datetimeFormats);o.length&&o.forEach(i=>{e.mergeDateTimeFormat(i,t.datetimeFormats[i])})}if(Se(t.numberFormats)){const o=Object.keys(t.numberFormats);o.length&&o.forEach(i=>{e.mergeNumberFormat(i,t.numberFormats[i])})}}}function vf(e){return De(Tn,null,e,0)}const yf=()=>[],oS=()=>!1;let bf=0;function Ef(e){return(t,n,r,s)=>e(n,r,Ee()||void 0,s)}function cp(e={},t){const{__root:n,__injectWithOption:r}=e,s=n===void 0,o=e.flatJson,i=Vo?ge:fu,a=!!e.translateExistCompatible;let u=Ne(e.inheritLocale)?e.inheritLocale:!0;const l=i(n&&u?n.locale.value:ae(e.locale)?e.locale:Ko),c=i(n&&u?n.fallbackLocale.value:ae(e.fallbackLocale)||ze(e.fallbackLocale)||be(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:l.value),f=i(lp(l.value,e)),d=i(be(e.datetimeFormats)?e.datetimeFormats:{[l.value]:{}}),m=i(be(e.numberFormats)?e.numberFormats:{[l.value]:{}});let g=n?n.missingWarn:Ne(e.missingWarn)||qo(e.missingWarn)?e.missingWarn:!0,C=n?n.fallbackWarn:Ne(e.fallbackWarn)||qo(e.fallbackWarn)?e.fallbackWarn:!0,w=n?n.fallbackRoot:Ne(e.fallbackRoot)?e.fallbackRoot:!0,R=!!e.fallbackFormat,E=Me(e.missing)?e.missing:null,h=Me(e.missing)?Ef(e.missing):null,p=Me(e.postTranslation)?e.postTranslation:null,b=n?n.warnHtmlMessage:Ne(e.warnHtmlMessage)?e.warnHtmlMessage:!0,S=!!e.escapeParameter;const x=n?n.modifiers:be(e.modifiers)?e.modifiers:{};let F=e.pluralRules||n&&n.pluralRules,v;v=(()=>{s&&lf(null);const D={version:ZA,locale:l.value,fallbackLocale:c.value,messages:f.value,modifiers:x,pluralRules:F,missing:h===null?void 0:h,missingWarn:g,fallbackWarn:C,fallbackFormat:R,unresolving:!0,postTranslation:p===null?void 0:p,warnHtmlMessage:b,escapeParameter:S,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};D.datetimeFormats=d.value,D.numberFormats=m.value,D.__datetimeFormatters=be(v)?v.__datetimeFormatters:void 0,D.__numberFormatters=be(v)?v.__numberFormatters:void 0;const U=HA(D);return s&&lf(U),U})(),zr(v,l.value,c.value);function I(){return[l.value,c.value,f.value,d.value,m.value]}const T=$({get:()=>l.value,set:D=>{l.value=D,v.locale=l.value}}),H=$({get:()=>c.value,set:D=>{c.value=D,v.fallbackLocale=c.value,zr(v,l.value,D)}}),L=$(()=>f.value),Z=$(()=>d.value),W=$(()=>m.value);function Q(){return Me(p)?p:null}function V(D){p=D,v.postTranslation=D}function se(){return E}function he(D){D!==null&&(h=Ef(D)),E=D,v.missing=h}const de=(D,U,ue,le,Be,$e)=>{I();let Ge;try{s||(v.fallbackContext=n?qA():void 0),Ge=D(v)}finally{s||(v.fallbackContext=void 0)}if(ue!=="translate exists"&&Qe(Ge)&&Ge===Ci||ue==="translate exists"&&!Ge){const[it,rr]=U();return n&&w?le(n):Be(it)}else{if($e(Ge))return Ge;throw Ut(kt.UNEXPECTED_RETURN_TYPE)}};function z(...D){return de(U=>Reflect.apply(hf,null,[U,...D]),()=>Xa(...D),"translate",U=>Reflect.apply(U.t,U,[...D]),U=>U,U=>ae(U))}function _e(...D){const[U,ue,le]=D;if(le&&!Se(le))throw Ut(kt.INVALID_ARGUMENT);return z(U,ue,dt({resolvedMessage:!0},le||{}))}function Pe(...D){return de(U=>Reflect.apply(mf,null,[U,...D]),()=>za(...D),"datetime format",U=>Reflect.apply(U.d,U,[...D]),()=>of,U=>ae(U))}function Ie(...D){return de(U=>Reflect.apply(gf,null,[U,...D]),()=>Ga(...D),"number format",U=>Reflect.apply(U.n,U,[...D]),()=>of,U=>ae(U))}function oe(D){return D.map(U=>ae(U)||Qe(U)||Ne(U)?vf(String(U)):U)}const te={normalize:oe,interpolate:D=>D,type:"vnode"};function ee(...D){return de(U=>{let ue;const le=U;try{le.processor=te,ue=Reflect.apply(hf,null,[le,...D])}finally{le.processor=null}return ue},()=>Xa(...D),"translate",U=>U[Ya](...D),U=>[vf(U)],U=>ze(U))}function re(...D){return de(U=>Reflect.apply(gf,null,[U,...D]),()=>Ga(...D),"number format",U=>U[Ja](...D),yf,U=>ae(U)||ze(U))}function ye(...D){return de(U=>Reflect.apply(mf,null,[U,...D]),()=>za(...D),"datetime format",U=>U[Qa](...D),yf,U=>ae(U)||ze(U))}function Te(D){F=D,v.pluralRules=F}function A(D,U){return de(()=>{if(!D)return!1;const ue=ae(U)?U:l.value,le=K(ue),Be=v.messageResolver(le,D);return a?Be!=null:Dr(Be)||on(Be)||ae(Be)},()=>[D],"translate exists",ue=>Reflect.apply(ue.te,ue,[D,U]),oS,ue=>Ne(ue))}function k(D){let U=null;const ue=Ym(v,c.value,l.value);for(let le=0;le<ue.length;le++){const Be=f.value[ue[le]]||{},$e=v.messageResolver(Be,D);if($e!=null){U=$e;break}}return U}function N(D){const U=k(D);return U!=null?U:n?n.tm(D)||{}:{}}function K(D){return f.value[D]||{}}function j(D,U){if(o){const ue={[D]:U};for(const le in ue)jo(ue,le)&&Ss(ue[le]);U=ue[D]}f.value[D]=U,v.messages=f.value}function G(D,U){f.value[D]=f.value[D]||{};const ue={[D]:U};if(o)for(const le in ue)jo(ue,le)&&Ss(ue[le]);U=ue[D],ho(U,f.value[D]),v.messages=f.value}function ne(D){return d.value[D]||{}}function _(D,U){d.value[D]=U,v.datetimeFormats=d.value,pf(v,D,U)}function y(D,U){d.value[D]=dt(d.value[D]||{},U),v.datetimeFormats=d.value,pf(v,D,U)}function P(D){return m.value[D]||{}}function X(D,U){m.value[D]=U,v.numberFormats=m.value,_f(v,D,U)}function J(D,U){m.value[D]=dt(m.value[D]||{},U),v.numberFormats=m.value,_f(v,D,U)}bf++,n&&Vo&&(Ae(n.locale,D=>{u&&(l.value=D,v.locale=D,zr(v,l.value,c.value))}),Ae(n.fallbackLocale,D=>{u&&(c.value=D,v.fallbackLocale=D,zr(v,l.value,c.value))}));const Y={id:bf,locale:T,fallbackLocale:H,get inheritLocale(){return u},set inheritLocale(D){u=D,D&&n&&(l.value=n.locale.value,c.value=n.fallbackLocale.value,zr(v,l.value,c.value))},get availableLocales(){return Object.keys(f.value).sort()},messages:L,get modifiers(){return x},get pluralRules(){return F||{}},get isGlobal(){return s},get missingWarn(){return g},set missingWarn(D){g=D,v.missingWarn=g},get fallbackWarn(){return C},set fallbackWarn(D){C=D,v.fallbackWarn=C},get fallbackRoot(){return w},set fallbackRoot(D){w=D},get fallbackFormat(){return R},set fallbackFormat(D){R=D,v.fallbackFormat=R},get warnHtmlMessage(){return b},set warnHtmlMessage(D){b=D,v.warnHtmlMessage=D},get escapeParameter(){return S},set escapeParameter(D){S=D,v.escapeParameter=D},t:z,getLocaleMessage:K,setLocaleMessage:j,mergeLocaleMessage:G,getPostTranslationHandler:Q,setPostTranslationHandler:V,getMissingHandler:se,setMissingHandler:he,[tS]:Te};return Y.datetimeFormats=Z,Y.numberFormats=W,Y.rt=_e,Y.te=A,Y.tm=N,Y.d=Pe,Y.n=Ie,Y.getDateTimeFormat=ne,Y.setDateTimeFormat=_,Y.mergeDateTimeFormat=y,Y.getNumberFormat=P,Y.setNumberFormat=X,Y.mergeNumberFormat=J,Y[nS]=r,Y[Ya]=ee,Y[Qa]=ye,Y[Ja]=re,Y}const ju={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function iS({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((r,s)=>[...r,...s.type===He?s.children:[s]],[]):t.reduce((n,r)=>{const s=e[r];return s&&(n[r]=s()),n},{})}function fp(e){return He}const aS=ln({name:"i18n-t",props:dt({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>Qe(e)||!isNaN(e)}},ju),setup(e,t){const{slots:n,attrs:r}=t,s=e.i18n||Wu({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(n).filter(f=>f!=="_"),i={};e.locale&&(i.locale=e.locale),e.plural!==void 0&&(i.plural=ae(e.plural)?+e.plural:e.plural);const a=iS(t,o),u=s[Ya](e.keypath,a,i),l=dt({},r),c=ae(e.tag)||Se(e.tag)?e.tag:fp();return q(c,l,u)}}}),Cf=aS;function uS(e){return ze(e)&&!ae(e[0])}function dp(e,t,n,r){const{slots:s,attrs:o}=t;return()=>{const i={part:!0};let a={};e.locale&&(i.locale=e.locale),ae(e.format)?i.key=e.format:Se(e.format)&&(ae(e.format.key)&&(i.key=e.format.key),a=Object.keys(e.format).reduce((d,m)=>n.includes(m)?dt({},d,{[m]:e.format[m]}):d,{}));const u=r(e.value,i,a);let l=[i.key];ze(u)?l=u.map((d,m)=>{const g=s[d.type],C=g?g({[d.type]:d.value,index:m,parts:u}):[d.value];return uS(C)&&(C[0].key=`${d.type}-${m}`),C}):ae(u)&&(l=[u]);const c=dt({},o),f=ae(e.tag)||Se(e.tag)?e.tag:fp();return q(f,c,l)}}const lS=ln({name:"i18n-n",props:dt({value:{type:Number,required:!0},format:{type:[String,Object]}},ju),setup(e,t){const n=e.i18n||Wu({useScope:e.scope,__useComponent:!0});return dp(e,t,ip,(...r)=>n[Ja](...r))}}),Af=lS,cS=ln({name:"i18n-d",props:dt({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},ju),setup(e,t){const n=e.i18n||Wu({useScope:e.scope,__useComponent:!0});return dp(e,t,op,(...r)=>n[Qa](...r))}}),Sf=cS;function fS(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return r!=null?r.__composer:e.global.__composer}}function dS(e){const t=i=>{const{instance:a,modifiers:u,value:l}=i;if(!a||!a.$)throw Ut(kt.UNEXPECTED_ERROR);const c=fS(e,a.$),f=wf(l);return[Reflect.apply(c.t,c,[...Tf(f)]),c]};return{created:(i,a)=>{const[u,l]=t(a);Vo&&e.global===l&&(i.__i18nWatcher=Ae(l.locale,()=>{a.instance&&a.instance.$forceUpdate()})),i.__composer=l,i.textContent=u},unmounted:i=>{Vo&&i.__i18nWatcher&&(i.__i18nWatcher(),i.__i18nWatcher=void 0,delete i.__i18nWatcher),i.__composer&&(i.__composer=void 0,delete i.__composer)},beforeUpdate:(i,{value:a})=>{if(i.__composer){const u=i.__composer,l=wf(a);i.textContent=Reflect.apply(u.t,u,[...Tf(l)])}},getSSRProps:i=>{const[a]=t(i);return{textContent:a}}}}function wf(e){if(ae(e))return{path:e};if(be(e)){if(!("path"in e))throw Ut(kt.REQUIRED_VALUE,"path");return e}else throw Ut(kt.INVALID_VALUE)}function Tf(e){const{path:t,locale:n,args:r,choice:s,plural:o}=e,i={},a=r||{};return ae(n)&&(i.locale=n),Qe(s)&&(i.plural=s),Qe(o)&&(i.plural=o),[t,a,i]}function hS(e,t,...n){const r=be(n[0])?n[0]:{},s=!!r.useI18nComponentName;(Ne(r.globalInstall)?r.globalInstall:!0)&&([s?"i18n":Cf.name,"I18nT"].forEach(i=>e.component(i,Cf)),[Af.name,"I18nN"].forEach(i=>e.component(i,Af)),[Sf.name,"I18nD"].forEach(i=>e.component(i,Sf))),e.directive("t",dS(t))}const mS=fn("global-vue-i18n");function pS(e={},t){const n=Ne(e.globalInjection)?e.globalInjection:!0,r=!0,s=new Map,[o,i]=gS(e),a=fn("");function u(f){return s.get(f)||null}function l(f,d){s.set(f,d)}function c(f){s.delete(f)}{const f={get mode(){return"composition"},get allowComposition(){return r},async install(d,...m){if(d.__VUE_I18N_SYMBOL__=a,d.provide(d.__VUE_I18N_SYMBOL__,f),be(m[0])){const w=m[0];f.__composerExtend=w.__composerExtend,f.__vueI18nExtend=w.__vueI18nExtend}let g=null;n&&(g=SS(d,f.global)),hS(d,f,...m);const C=d.unmount;d.unmount=()=>{g&&g(),f.dispose(),C()}},get global(){return i},dispose(){o.stop()},__instances:s,__getInstance:u,__setInstance:l,__deleteInstance:c};return f}}function Wu(e={}){const t=Ee();if(t==null)throw Ut(kt.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Ut(kt.NOT_INSTALLED);const n=_S(t),r=yS(n),s=rS(t),o=vS(e,s);if(o==="global")return sS(r,e,s),r;if(o==="parent"){let u=bS(n,t,e.__useComponent);return u==null&&(u=r),u}const i=n;let a=i.__getInstance(t);if(a==null){const u=dt({},e);"__i18n"in s&&(u.__i18n=s.__i18n),r&&(u.__root=r),a=cp(u),i.__composerExtend&&(a[Za]=i.__composerExtend(a)),CS(i,t,a),i.__setInstance(t,a)}return a}function gS(e,t,n){const r=iu();{const s=r.run(()=>cp(e));if(s==null)throw Ut(kt.UNEXPECTED_ERROR);return[r,s]}}function _S(e){{const t=vt(e.isCE?mS:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Ut(e.isCE?kt.NOT_INSTALLED_WITH_PROVIDE:kt.UNEXPECTED_ERROR);return t}}function vS(e,t){return bi(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function yS(e){return e.mode==="composition"?e.global:e.global.__composer}function bS(e,t,n=!1){let r=null;const s=t.root;let o=ES(t,n);for(;o!=null;){const i=e;if(e.mode==="composition"&&(r=i.__getInstance(o)),r!=null||s===o)break;o=o.parent}return r}function ES(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function CS(e,t,n){Vt(()=>{},t),Ir(()=>{const r=n;e.__deleteInstance(t);const s=r[Za];s&&(s(),delete r[Za])},t)}const AS=["locale","fallbackLocale","availableLocales"],kf=["t","rt","d","n","tm","te"];function SS(e,t){const n=Object.create(null);return AS.forEach(s=>{const o=Object.getOwnPropertyDescriptor(t,s);if(!o)throw Ut(kt.UNEXPECTED_ERROR);const i=Le(o.value)?{get(){return o.value.value},set(a){o.value.value=a}}:{get(){return o.get&&o.get()}};Object.defineProperty(n,s,i)}),e.config.globalProperties.$i18n=n,kf.forEach(s=>{const o=Object.getOwnPropertyDescriptor(t,s);if(!o||!o.value)throw Ut(kt.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${s}`,o)}),()=>{delete e.config.globalProperties.$i18n,kf.forEach(s=>{delete e.config.globalProperties[`$${s}`]})}}eS();__INTLIFY_JIT_COMPILATION__?uf(zA):uf(XA);UA(wA);VA(Ym);function cr(e){var n;let t="\u767C\u751F\u932F\u8AA4";e instanceof CE?t=((n=e.response)==null?void 0:n.data.message)||e.message:e instanceof Error?t=e.message:typeof e=="string"&&(t=e),Io.create({type:"negative",message:t,position:"top"})}var wS={app:{english:"English",chinese:"\u4E2D\u6587"},changeLanguage:"Change Language",success:"Success",failed:"Failed",all:"All",hint:"Hint",optional:"Optional",open:"Open",close:"Close",confirm:"Confirm",logoutConfirm:"Are you sure you want to logout?",cancel:"Cancel",add:"Add",disabled:"Disabled",confirmDelete:"Are you sure you want to delete this item?",error:{required:"The field id required.",passwordNotMatch:"Password not match.",max:"The field may not be greater than {max} characters.",min:"The field must be at least {min} characters.",maxNumber:"The field may not be greater than {max}.",minNumber:"The field must be at least {min}.",betweenNumber:"The field must be between {min} and {max}.",email:"Invalid email format.",noData:"No data available.",noImage:"No image available.",invalidDate:"Invalid date format."},pending:"Pending",processing:"Processing",completed:"Completed",cancelled:"Cancelled","wc-pending":"Pending","wc-processing":"Processing","wc-on-hold":"On Hold","wc-completed":"Completed","wc-cancelled":"Cancelled","wc-refunded":"Refunded","wc-failed":"Failed",onsite:"Onsite",online:"Online",orderDetail:"Order Detail",attendance:{label:"Attendance",history:"Attendance History"},payroll:{label:"Payroll",period:"Payroll Period",payDate:"Pay Date",basicSalary:"Basic Salary",hourlyRate:"Hourly Rate",workDays:"Work Days",workHours:"Work Hours",income:"Income",bonus:"Bonus",deduction:"Deduction",grossSalary:"Gross Salary",totalDeductions:"Total Deductions",netSalary:"Net Salary",payStatus:"Pay Status",paid:"Paid",unpaid:"Unpaid",status:{draft:"Draft",unpaid:"Unpaid",paid:"Paid",void:"Void"},overtime_pay:"Overtime Pay",meal_allowance:"Meal Allowance",transportation_allowance:"Transportation Allowance",position_allowance:"Position Allowance",performance_bonus:"Performance Bonus",perfect_attendance_bonus:"Perfect Attendance Bonus",year_end_bonus:"Year-end Bonus",labor_insurance:"Labor Insurance",health_insurance:"Health Insurance",income_tax:"Income Tax",labor_pension:"Labor Pension",health_pension:"Health Pension",late_deduction:"Late Deduction",absence_deduction:"Absence Deduction"},payrolls:"Payrolls",salary:"Salary",salaryType:{label:"Salary Type",salary:"Salary",monthly:"Monthly Salary",hourly:"Hourly Wage"},order:{label:"Order",list:"Order List",history:"Order History",salesStatistics:"Sales Statistics",void:"Voided Order",qty:"Order Qty",count:"{count} order | {count} orders",total:"Total Orders",noRecord:"No order record."},orders:"Orders",user:{menu:"Users",label:"User",all:"All Users",list:"User List"},username:"Username",account:"Account ID",password:"Password",confirmPassword:"Confirm Password",datePicker:{from:"From Date",to:"To Date"},permissions:"Permissions",permission:{view_page_order:"View Order Page",create_order:"Create Order",update_order:"Update Order",delete_order:"Delete Order",void_order:"Void Order",checkout_order:"Checkout Order"},name:"Name",type:"Type",email:{label:"Email",send:"Send Email",sendAll:"Send All",notSet:"No Email Set"},role:"Role",phone:"Phone",licensePlate:"License Plate",country:"Country",state:"State",city:"City",zipcode:"Zipcode",address:"Address",receiver:"Receiver",date:"Date",time:"Time",item:"Item",quantity:"Qty",status:"Status",allStatus:"All Status",note:{label:"Note",edit:"Edit Note"},group:"Group",catalog:{label:"Catalog"},product:{label:"Product",name:"Product Name",data:"Product Data",all:"All Products"},products:"Products",addProduct:"Add Product",category:"Category",barcode:"Barcode",description:"Description",shortDescription:"Short Description",unit:"Unit",price:"Price",salePrice:"Sale Price",flexiblePrice:"Flexible Price",productType:"Product Type",fixedPriceProduct:"Fixed Price Product",flexiblePriceProduct:"Flexible Price Product",setServiceAmount:"Set Service Amount",enterAmountLabel:"Please enter amount (AU$)",cost:"Cost",stock:"Stock",stockQuantity:"Stock Qty",minStockQuantity:"Min Stock Qty",image:"Image",images:"Images",serviceFee:"Service Fee",tax:"Tax",taxID:"ABN",shippingFee:"Shipping Fee",shippingMethod:"Shipping Method",supplier:"Supplier",clockIn:"Clock In",clock_in:"Clock In",clockOut:"Clock Out",clock_out:"Clock Out",clockType:"Clock Type",clockTime:"Clock Time",createdAt:"Created At",updatedAt:"Updated At",createUser:"Create User",createGroup:"Create Group",createCategory:"Create Category",createProduct:"Create Product",editProduct:"Edit Product",createCustomer:"Create Customer",editCustomer:"Edit Customer",save:"Save",reset:"Reset",clear:"Clear",create:"Create",update:"Update",submit:"Submit",delete:"Delete",rememberMe:"Remember Me",login:"Login",logout:"Logout",itemNum:"#Item",rebate:"Rebate",discount:"Discount",percentageOff:"Discount ({discount}% off)",checkout:"Checkout",payType:"Pay Type",payment:{label:"Payment",stats:"Payment Statistic",cash:"Cash",creditCard:"Credit Card",paypal:"Paypal"},cash:"Cash",creditCard:"Credit Card",subtotal:"Subtotal",total:"Total",cashReceived:"Cash Received",guest:"Guest",customerData:"Customer Data",customer:{label:"Customer",all:"All Customers",notSupplier:"The customer is not a supplier."},customers:"Customers",checkoutSuccess:"Checkout Success",checkoutSuccessMessage:"The order has been created successfully. Do you want to print the invoice?",checkoutError:{noItems:"Please add at least one item to the cart."},printInvoice:"Print Xero Invoice",printInvoiceSuccess:"Successfully printed Xero invoice",printInvoiceError:"Failed to print Xero invoice",sendEmail:"Send Email",sendEmailSuccess:"Successfully sent email",sendEmailError:"Failed to send email",sendEmailRateLimitError:"Xero daily email sending limit reached, please try again tomorrow or send manually from Xero",sendEmailRateLimitTitle:"Email Sending Limit",sendEmailRateLimitMessage:`Xero daily email sending limit reached. The invoice has been marked as sent in Xero, but the actual email was not sent.

Solutions:
1. Try again tomorrow
2. Log into Xero system to send manually
3. Print the invoice and deliver it to the customer personally`,sendEmailInvalidEmailError:"Invalid email address, please check the customer's email address",sendEmailManualError:"Please send this invoice manually from the Xero system",understood:"I understand",sendEmailNoCustomerOrEmail:"This order does not have a customer or the customer does not have an email.",sendEmailNotSynced:"This order has not been synced to Xero, cannot send email",sendEmailSyncFailed:"This order failed to sync to Xero, cannot send email",orderNotLoaded:"Order data not yet loaded",cannotEmailVoidedOrder:"Cannot send email for voided orders",orderNumber:"Order Number",emailInput:{confirmEmailAddress:"Please confirm or modify the email address",emailAddress:"Email Address",emailRequired:"Email address is required",invalidEmail:"Invalid email format",usingCustomerEmail:"Using customer's email address",usingDefaultEmail:"Using default email address",noDefaultEmail:"Please enter an email address"},unknown:{customer:"No Customer Data"},search:{label:"Search",customer:"Enter customer name, phone, ABN",product:"Enter product name or barcode",order:"Enter order No."},punch:"Punch",history:"History",latestPunch:"Latest Punch",changeUser:"Change User",orderNo:"Order No",dateAt:"Date At",orderDate:"Order Date",void:"Void",reason:"Reason",voidReason:"Void Reason",orderVoided:{title:"Order Voided",reasons:{outOfStock:"Out of Stock",duplicateOrder:"Duplicate Order",incorrectInfo:"Incorrect Info",customerChangedMind:"Customer Changed Mind",customerReject:"Customer Reject",other:"Other"},confirm:"Are you sure you want to void this order?",reasonRequired:"Please select a reason.",otherReasonRequired:"Please enter a reason."},orderStatus:{label:"Order Status",pending:"Pending",processing:"Processing","on-hold":"On Hold",packing:"Packing",shipping:"Shipping",completed:"Completed",cancelled:"Cancelled",refunded:"Refunded",void:"Void"},inventory:{label:"Inventory",stockHistory:"Stock History",stock_in:{label:"Stock In",caption:"Add stock to the product"},return:{label:"Return",caption:"Return stock from customer or to supplier"},scrap:{label:"Scrap",caption:"Remove stock from inventory"},stocktaking:{label:"Stocktaking",caption:"Count stock in the store"},transaction:{sale:"Sale",purchase:"Purchase",stock_in:"Stock In",stock_out:"Stock Out",stocktaking:"Stocktaking",scrap:"Scrap",return_in:"Return In",return_out:"Return Out"},returnType:{label:"Return Type",customer_return:"Return from Customer",supplier_return:"Return to Supplier"},beforeQuantity:"Before Qty",afterQuantity:"After Qty",diffQuantity:"Diff Qty"},actions:"Actions",wpOrder:{actions:{complete:"Complete",cancel:"Cancel",refund:"Refund",processing:"Change to Processing"},title:{complete:"Complete Order",cancel:"Cancel Order",refund:"Refund Order",processing:"Processing Order"}},wcOrder:{statusUpdate:{title:"Update Order Status",confirm:"Are you sure you want to update the order status?",success:"Order status updated successfully"},cancel:{title:"Cancel Order",reasonLabel:"Cancellation Reason",reasonPlaceholder:"Please enter cancellation reason...",confirm:"Are you sure you want to cancel this order?",reasonRequired:"Please enter a cancellation reason",success:"Order has been cancelled",reasons:{outOfStock:"Out of Stock",customerRequest:"Customer Request",paymentIssue:"Payment Issue",duplicateOrder:"Duplicate Order",incorrectInfo:"Incorrect Information",other:"Other"}}},orderItem:{is_free:"Free"},vip:{label:"VIP",expiryDate:"VIP Expiry Date"},year:{label:"Year"},month:{label:"Month"},barcodeScanner:{ready:"Barcode Scanner Ready",scanning:"Scanning...",notFound:"Product Not Found",notFoundMessage:"No product found with barcode {barcode}",scanCompleted:"Scan Completed"},added:"Added",system:"System",announcement:{settings:"Announcement Settings",title:"Website Announcement Settings",content:"Announcement Content",placeholder:"Please enter announcement content...",save:"Save Announcement",success:"Announcement saved successfully"},updateService:{updateNotification:{title:"System Update Available",newVersion:"New version available",currentVersion:"Current version",buildTime:"Build time",recommendUpdate:"We recommend updating immediately for the best experience",doNotClose:"Please do not close the browser window during the update",updateLater:"Remind Later",updateNow:"Update Now",laterReminder:"We will remind you to update later"},updateProcess:{updating:"Updating system, please wait...",success:"System update completed, reloading soon",failed:"Update failed, attempting force refresh",forceRefresh:"Force refreshing..."}},xero:{menu:{setup:"Setup",invoices:"Invoices"},setup:{title:"Xero API Setup",connectionStatus:{connected:"Connected to Xero account: {tenantName}",tokenExpiry:"Token expires at: {expiryDate}",notConnected:"Not connected to Xero account",disconnect:"Disconnect",refreshToken:"Refresh Token"},form:{clientId:"Client ID",clientSecret:"Client Secret",redirectUri:"Redirect URI",scopes:"Scopes",scopesHint:"Default: accounting.transactions accounting.contacts",scopesPlaceholder:"accounting.transactions accounting.contacts",defaultEmail:"Default Email Address",defaultEmailHint:"Use this email address when customer has no email",saveConfig:"Save Configuration",connectXero:"Connect to Xero"},validation:{clientIdRequired:"Client ID is required",clientSecretRequired:"Client Secret is required",redirectUriRequired:"Redirect URI is required",invalidEmail:"Invalid email format"},dialog:{disconnectTitle:"Confirm Disconnect",disconnectMessage:"Are you sure you want to disconnect from Xero?"},error:{stateStorageFailed:"Failed to save authentication state. Please try again."}},redirect:{processing:"Processing Xero authentication...",success:{title:"Xero Connection Successful!",connectedTo:"Successfully connected to: {tenantName}",backToSettings:"Back to Settings",notification:"Xero connection successful!"},error:{title:"Connection Failed",retry:"Try Again",clearState:"Clear State & Retry",messages:{authError:"Authentication error: {error}",missingParams:"Missing required authentication parameters",invalidState:"Invalid state parameter, possible security risk",noSavedState:"No saved state found, please try connecting again",unknownOrg:"Unknown Organization"}},debug:{title:"Debug Information",stateCleared:"Authentication state cleared successfully"}},invoices:{title:"Xero Invoices",notConnected:"Not connected to Xero. Please configure the connection first.",goToSetup:"Go to Setup",dateFrom:"Date From",dateTo:"Date To",invoiceNumber:"Invoice Number",contact:"Contact",date:"Date",dueDate:"Due Date",subTotal:"Sub Total",totalTax:"Total Tax",total:"Total",invoiceDetails:"Invoice Details",basicInfo:"Basic Information",lineItems:"Line Items",unitAmount:"Unit Amount",lineAmount:"Line Amount",enterEmailAddress:"Please enter email address",invoiceNotFound:"Invoice not found",type:{label:"Type",accrec:"Sales Invoice",accpay:"Purchase Invoice"},status:{draft:"Draft",submitted:"Submitted",authorised:"Authorised",paid:"Paid",voided:"Voided",deleted:"Deleted"}},syncStatus:"Xero Sync Status",notSynced:"Not Synced",syncPending:"Sync Pending",syncing:"Syncing",syncSuccess:"Sync Success",syncFailed:"Sync Failed",voidedInvoice:"Voided Invoice",syncToXero:"Sync to Xero",syncToXeroSuccess:"Successfully synced to Xero",syncToXeroError:"Failed to sync to Xero",alreadySynced:"Already Synced"}},TS={app:{english:"English",chinese:"\u4E2D\u6587"},changeLanguage:"\u5207\u63DB\u8A9E\u8A00",success:"\u6210\u529F",failed:"\u5931\u6557",all:"\u5168\u90E8",hint:"\u63D0\u793A",optional:"\u9078\u586B",open:"\u958B\u555F",close:"\u95DC\u9589",confirm:"\u78BA\u8A8D",logoutConfirm:"\u78BA\u5B9A\u8981\u767B\u51FA\u55CE\uFF1F",cancel:"\u53D6\u6D88",add:"\u65B0\u589E",disabled:"\u672A\u555F\u7528",confirmDelete:"\u78BA\u5B9A\u8981\u522A\u9664\u6B64\u9805\u76EE\u55CE\uFF1F",error:{required:"\u6B64\u6B04\u4F4D\u70BA\u5FC5\u586B\u3002",passwordNotMatch:"\u5BC6\u78BC\u4E0D\u7B26\u3002",max:"\u6B64\u6B04\u4F4D\u4E0D\u5F97\u8D85\u904E {max} \u500B\u5B57\u5143\u3002",min:"\u6B64\u6B04\u4F4D\u81F3\u5C11\u9700\u8981 {min} \u500B\u5B57\u5143\u3002",maxNumber:"\u6B64\u6B04\u4F4D\u6578\u503C\u4E0D\u5F97\u5927\u65BC {max}\u3002",minNumber:"\u6B64\u6B04\u4F4D\u6578\u503C\u4E0D\u5F97\u5C0F\u65BC {min}\u3002",betweenNumber:"\u6B64\u6B04\u4F4D\u6578\u503C\u5FC5\u9808\u5728 {min} \u548C {max} \u4E4B\u9593\u3002",email:"\u7121\u6548\u7684\u96FB\u5B50\u90F5\u4EF6\u683C\u5F0F\u3002",noData:"\u7121\u53EF\u7528\u8CC7\u6599\u3002",noImage:"\u7121\u53EF\u7528\u5716\u7247\u3002",invalidDate:"\u7121\u6548\u7684\u65E5\u671F\u683C\u5F0F\u3002"},pending:"\u5F85\u8655\u7406",processing:"\u8655\u7406\u4E2D",completed:"\u5DF2\u5B8C\u6210",cancelled:"\u5DF2\u53D6\u6D88","wc-pending":"\u5F85\u8655\u7406","wc-processing":"\u8655\u7406\u4E2D","wc-on-hold":"\u66AB\u505C","wc-completed":"\u5DF2\u5B8C\u6210","wc-cancelled":"\u5DF2\u53D6\u6D88","wc-refunded":"\u5DF2\u9000\u6B3E","wc-failed":"\u5931\u6557",onsite:"\u73FE\u5834",online:"\u7DDA\u4E0A",orderDetail:"\u8A02\u55AE\u8A73\u60C5",attendance:{label:"\u51FA\u52E4",history:"\u51FA\u52E4\u7D00\u9304"},payroll:{label:"\u85AA\u8CC7\u55AE",period:"\u85AA\u8CC7\u671F\u9593",payDate:"\u767C\u85AA\u65E5\u671F",basicSalary:"\u57FA\u672C\u85AA\u8CC7",hourlyRate:"\u6642\u85AA",workDays:"\u5DE5\u4F5C\u5929\u6578",workHours:"\u5DE5\u4F5C\u6642\u6578",income:"\u6536\u5165",bonus:"\u734E\u91D1",deduction:"\u6263\u6B3E",grossSalary:"\u7E3D\u6536\u5165",totalDeductions:"\u7E3D\u6263\u6B3E",netSalary:"\u6DE8\u6536\u5165",payStatus:"\u767C\u85AA\u72C0\u614B",paid:"\u5DF2\u767C\u85AA",unpaid:"\u672A\u767C\u85AA",status:{draft:"\u8349\u7A3F",unpaid:"\u672A\u767C\u85AA",paid:"\u5DF2\u767C\u85AA",void:"\u5DF2\u4F5C\u5EE2"},overtime_pay:"\u52A0\u73ED\u8CBB",meal_allowance:"\u9910\u8CBB\u6D25\u8CBC",transportation_allowance:"\u4EA4\u901A\u6D25\u8CBC",position_allowance:"\u8077\u52D9\u6D25\u8CBC",performance_bonus:"\u7E3E\u6548\u734E\u91D1",perfect_attendance_bonus:"\u5168\u52E4\u734E\u91D1",year_end_bonus:"\u5E74\u7D42\u734E\u91D1",labor_insurance:"\u52DE\u5DE5\u4FDD\u96AA",health_insurance:"\u5065\u4FDD",income_tax:"\u6240\u5F97\u7A05",labor_pension:"\u52DE\u5DE5\u9000\u4F11\u91D1",health_pension:"\u5065\u4FDD\u9000\u4F11\u91D1",late_deduction:"\u9072\u5230\u6263\u6B3E",absence_deduction:"\u7F3A\u52E4\u6263\u6B3E"},payrolls:"\u85AA\u8CC7\u8A18\u9304",salary:"\u85AA\u8CC7",salaryType:{label:"\u85AA\u8CC7\u985E\u578B",salary:"\u56FA\u5B9A\u85AA\u8CC7",monthly:"\u6708\u85AA",hourly:"\u6642\u85AA"},order:{label:"\u8A02\u55AE",list:"\u8A02\u55AE\u5217\u8868",history:"\u8A02\u55AE\u7D00\u9304",salesStatistics:"\u92B7\u552E\u7D71\u8A08",void:"\u4F5C\u5EE2\u8A02\u55AE",qty:"\u8A02\u55AE\u6578\u91CF",count:"{count} \u7B46\u8A02\u55AE",total:"\u8A02\u55AE\u7E3D\u6578",noRecord:"\u7121\u8A02\u55AE\u7D00\u9304"},orders:"\u8A02\u55AE\u5217\u8868",user:{menu:"\u7528\u6236\u7BA1\u7406",label:"\u7528\u6236",all:"\u6240\u6709\u7528\u6236",list:"\u7528\u6236\u5217\u8868"},username:"\u7528\u6236\u540D",account:"\u5E33\u865F ID",password:"\u5BC6\u78BC",confirmPassword:"\u78BA\u8A8D\u5BC6\u78BC",datePicker:{from:"\u958B\u59CB\u65E5\u671F",to:"\u7D50\u675F\u65E5\u671F"},permissions:"\u6B0A\u9650",permission:{view_page_order:"\u6AA2\u8996\u8A02\u55AE\u9801\u9762",create_order:"\u5EFA\u7ACB\u8A02\u55AE",update_order:"\u66F4\u65B0\u8A02\u55AE",delete_order:"\u522A\u9664\u8A02\u55AE",void_order:"\u4F5C\u5EE2\u8A02\u55AE",checkout_order:"\u7D50\u5E33"},name:"\u540D\u7A31",type:"\u985E\u578B",email:{label:"\u96FB\u5B50\u90F5\u4EF6",send:"\u767C\u9001\u90F5\u4EF6",sendAll:"\u767C\u9001\u5168\u90E8",notSet:"\u672A\u8A2D\u7F6EEmail"},role:"\u89D2\u8272",phone:"\u96FB\u8A71",licensePlate:"\u8ECA\u724C\u865F\u78BC",country:"\u570B\u5BB6",state:"\u5DDE/\u7701",city:"\u57CE\u5E02",zipcode:"\u90F5\u905E\u5340\u865F",address:"\u5730\u5740",receiver:"\u6536\u8CA8\u4EBA",date:"\u65E5\u671F",time:"\u6642\u9593",item:"\u9805\u76EE",quantity:"\u6578\u91CF",status:"\u72C0\u614B",allStatus:"\u6240\u6709\u72C0\u614B",note:{label:"\u5099\u8A3B",edit:"\u7DE8\u8F2F\u5099\u8A3B"},group:"\u7FA4\u7D44",catalog:{label:"\u578B\u9304"},product:{label:"\u7522\u54C1",name:"\u7522\u54C1\u540D\u7A31",data:"\u7522\u54C1\u8CC7\u6599",all:"\u6240\u6709\u7522\u54C1"},products:"\u7522\u54C1\u5217\u8868",addProduct:"\u65B0\u589E\u7522\u54C1",category:"\u985E\u5225",barcode:"\u689D\u78BC",description:"\u63CF\u8FF0",shortDescription:"\u7C21\u77ED\u63CF\u8FF0",unit:"\u55AE\u4F4D",price:"\u50F9\u683C",salePrice:"\u7279\u50F9",flexiblePrice:"\u5F48\u6027\u91D1\u984D",productType:"\u7522\u54C1\u985E\u578B",fixedPriceProduct:"\u56FA\u5B9A\u91D1\u984D\u7522\u54C1",flexiblePriceProduct:"\u5F48\u6027\u91D1\u984D\u7522\u54C1",setServiceAmount:"\u8A2D\u5B9A\u670D\u52D9\u91D1\u984D",enterAmountLabel:"\u8ACB\u8F38\u5165\u91D1\u984D (AU$)",cost:"\u6210\u672C",stock:"\u5EAB\u5B58",stockQuantity:"\u5EAB\u5B58\u6578\u91CF",minStockQuantity:"\u6700\u4F4E\u5EAB\u5B58\u6578\u91CF",image:"\u5716\u7247",images:"\u5716\u7247",serviceFee:"\u670D\u52D9\u8CBB",tax:"\u7A05\u91D1",taxID:"ABN",shippingFee:"\u904B\u8CBB",shippingMethod:"\u904B\u9001\u65B9\u5F0F",supplier:"\u4F9B\u61C9\u5546",clockIn:"\u6253\u5361\u4E0A\u73ED",clock_in:"\u6253\u5361\u4E0A\u73ED",clockOut:"\u6253\u5361\u4E0B\u73ED",clock_out:"\u6253\u5361\u4E0B\u73ED",clockType:"\u6253\u5361\u985E\u578B",clockTime:"\u6253\u5361\u6642\u9593",createdAt:"\u5EFA\u7ACB\u6642\u9593",updatedAt:"\u66F4\u65B0\u6642\u9593",createUser:"\u5EFA\u7ACB\u7528\u6236",createGroup:"\u5EFA\u7ACB\u7FA4\u7D44",createCategory:"\u5EFA\u7ACB\u985E\u5225",createProduct:"\u5EFA\u7ACB\u7522\u54C1",editProduct:"\u7DE8\u8F2F\u7522\u54C1",createCustomer:"\u5EFA\u7ACB\u5BA2\u6236",editCustomer:"\u7DE8\u8F2F\u5BA2\u6236",save:"\u5132\u5B58",reset:"\u91CD\u8A2D",clear:"\u6E05\u9664",create:"\u5EFA\u7ACB",update:"\u66F4\u65B0",submit:"\u63D0\u4EA4",delete:"\u522A\u9664",rememberMe:"\u8A18\u4F4F\u6211",login:"\u767B\u5165",logout:"\u767B\u51FA",itemNum:"\u9805\u76EE\u6578",rebate:"\u6298\u8B93",discount:"\u6298\u6263",percentageOff:"\u6298\u6263 (\u6E1B\u514D {discount}%)",checkout:"\u7D50\u5E33",payType:"\u4ED8\u6B3E\u65B9\u5F0F",payment:{label:"\u4ED8\u6B3E\u65B9\u5F0F",stats:"\u4ED8\u6B3E\u65B9\u5F0F\u7D71\u8A08",cash:"\u73FE\u91D1",creditCard:"\u4FE1\u7528\u5361",paypal:"Paypal"},cash:"\u73FE\u91D1",creditCard:"\u4FE1\u7528\u5361",subtotal:"\u5C0F\u8A08",total:"\u7E3D\u8A08",cashReceived:"\u6536\u5230\u73FE\u91D1",guest:"\u8A2A\u5BA2",customerData:"\u5BA2\u6236\u8CC7\u6599",customer:{label:"\u5BA2\u6236",all:"\u6240\u6709\u5BA2\u6236",notSupplier:"\u6B64\u5BA2\u6236\u975E\u4F9B\u61C9\u5546"},customers:"\u5BA2\u6236\u5217\u8868",checkoutSuccess:"\u7D50\u5E33\u6210\u529F",checkoutSuccessMessage:"\u8A02\u55AE\u5DF2\u6210\u529F\u5EFA\u7ACB\uFF0C\u662F\u5426\u8981\u5217\u5370\u767C\u7968\uFF1F",checkoutError:{noItems:"\u8ACB\u81F3\u5C11\u9078\u64C7\u4E00\u9805\u5546\u54C1"},printInvoice:"\u5217\u5370 Xero \u767C\u7968",printInvoiceSuccess:"Xero \u767C\u7968\u5217\u5370\u6210\u529F",printInvoiceError:"Xero \u767C\u7968\u5217\u5370\u5931\u6557",sendEmail:"\u767C\u9001 Email",sendEmailSuccess:"Email \u767C\u9001\u6210\u529F",sendEmailError:"Email \u767C\u9001\u5931\u6557",sendEmailRateLimitError:"Xero \u6BCF\u65E5 Email \u767C\u9001\u9650\u5236\u5DF2\u9054\u4E0A\u9650\uFF0C\u8ACB\u660E\u5929\u518D\u8A66\u6216\u624B\u52D5\u5F9E Xero \u767C\u9001",sendEmailRateLimitTitle:"Email \u767C\u9001\u9650\u5236",sendEmailRateLimitMessage:`Xero \u6BCF\u65E5 Email \u767C\u9001\u9650\u5236\u5DF2\u9054\u4E0A\u9650\u3002\u767C\u7968\u5DF2\u5728 Xero \u4E2D\u6A19\u8A18\u70BA\u5DF2\u767C\u9001\uFF0C\u4F46\u5BE6\u969B Email \u672A\u767C\u9001\u3002

\u89E3\u6C7A\u65B9\u6848\uFF1A
1. \u660E\u5929\u518D\u5617\u8A66\u767C\u9001
2. \u767B\u5165 Xero \u7CFB\u7D71\u624B\u52D5\u767C\u9001
3. \u5217\u5370\u767C\u7968\u4E26\u89AA\u81EA\u4EA4\u7D66\u5BA2\u6236`,sendEmailInvalidEmailError:"Email \u5730\u5740\u7121\u6548\uFF0C\u8ACB\u6AA2\u67E5\u5BA2\u6236\u7684 Email \u5730\u5740",sendEmailManualError:"\u8ACB\u624B\u52D5\u5F9E Xero \u7CFB\u7D71\u767C\u9001\u6B64\u767C\u7968",understood:"\u6211\u4E86\u89E3",sendEmailNoCustomerOrEmail:"\u6B64\u8A02\u55AE\u6C92\u6709\u5BA2\u6236\u6216\u5BA2\u6236\u6C92\u6709 Email",sendEmailNotSynced:"\u6B64\u8A02\u55AE\u5C1A\u672A\u540C\u6B65\u5230 Xero\uFF0C\u7121\u6CD5\u767C\u9001 Email",sendEmailSyncFailed:"\u6B64\u8A02\u55AE\u540C\u6B65\u5230 Xero \u5931\u6557\uFF0C\u7121\u6CD5\u767C\u9001 Email",orderNotLoaded:"\u8A02\u55AE\u8CC7\u6599\u5C1A\u672A\u8F09\u5165",emailInput:{confirmEmailAddress:"\u8ACB\u78BA\u8A8D\u6216\u4FEE\u6539 Email \u5730\u5740",emailAddress:"Email \u5730\u5740",emailRequired:"Email \u5730\u5740\u70BA\u5FC5\u586B",invalidEmail:"\u7121\u6548\u7684 Email \u683C\u5F0F",usingCustomerEmail:"\u4F7F\u7528\u5BA2\u6236\u7684 Email \u5730\u5740",usingDefaultEmail:"\u4F7F\u7528\u9810\u8A2D Email \u5730\u5740",noDefaultEmail:"\u8ACB\u8F38\u5165 Email \u5730\u5740"},cannotEmailVoidedOrder:"\u5DF2\u4F5C\u5EE2\u7684\u8A02\u55AE\u7121\u6CD5\u767C\u9001 Email",orderNumber:"\u8A02\u55AE\u865F\u78BC",unknown:{customer:"\u7121\u5BA2\u6236\u8CC7\u6599"},search:{label:"\u641C\u5C0B",customer:"\u8F38\u5165\u5BA2\u6236\u540D\u7A31\u3001\u96FB\u8A71\u3001ABN",product:"\u8F38\u5165\u7522\u54C1\u540D\u7A31\u6216\u689D\u78BC",order:"\u8F38\u5165\u8A02\u55AE\u7DE8\u865F"},punch:"\u6253\u5361",history:"\u6B77\u53F2\u8A18\u9304",latestPunch:"\u6700\u8FD1\u6253\u5361",changeUser:"\u66F4\u63DB\u4F7F\u7528\u8005",orderNo:"\u8A02\u55AE\u7DE8\u865F",dateAt:"\u65E5\u671F",orderDate:"\u8A02\u55AE\u65E5\u671F",void:"\u4F5C\u5EE2",reason:"\u539F\u56E0",voidReason:"\u4F5C\u5EE2\u539F\u56E0",orderVoided:{title:"\u8A02\u55AE\u5DF2\u4F5C\u5EE2",reasons:{outOfStock:"\u5EAB\u5B58\u4E0D\u8DB3",duplicateOrder:"\u91CD\u8907\u8A02\u55AE",incorrectInfo:"\u8CC7\u8A0A\u4E0D\u6B63\u78BA",customerChangedMind:"\u5BA2\u6236\u6539\u8B8A\u4E3B\u610F",customerReject:"\u5BA2\u6236\u53D6\u6D88",other:"\u5176\u4ED6"},confirm:"\u78BA\u5B9A\u8981\u4F5C\u5EE2\u6B64\u8A02\u55AE\u55CE\uFF1F",reasonRequired:"\u8ACB\u9078\u64C7\u4E00\u500B\u539F\u56E0\u3002",otherReasonRequired:"\u8ACB\u8F38\u5165\u539F\u56E0\u3002"},orderStatus:{label:"\u8A02\u55AE\u72C0\u614B",pending:"\u5F85\u8655\u7406",processing:"\u8655\u7406\u4E2D","on-hold":"\u7B49\u5F85\u4ED8\u6B3E\u4E2D",packing:"\u51FA\u8CA8\u4E2D",shipping:"\u5DF2\u5BC4\u8CA8",completed:"\u5DF2\u5B8C\u6210",cancelled:"\u5DF2\u53D6\u6D88",refunded:"\u5DF2\u9000\u6B3E",void:"\u5DF2\u4F5C\u5EE2"},inventory:{label:"\u5EAB\u5B58",stockHistory:"\u5EAB\u5B58\u7D00\u9304",stock_in:{label:"\u5165\u5EAB",caption:"\u589E\u52A0\u7522\u54C1\u5EAB\u5B58"},return:{label:"\u9000\u8CA8",caption:"\u5BA2\u6236\u9000\u8CA8\u6216\u9000\u56DE\u7D66\u4F9B\u61C9\u5546"},scrap:{label:"\u5831\u5EE2",caption:"\u5F9E\u5EAB\u5B58\u4E2D\u79FB\u9664"},stocktaking:{label:"\u76E4\u9EDE",caption:"\u6E05\u9EDE\u5E97\u5167\u5EAB\u5B58"},transaction:{sale:"\u92B7\u552E",purchase:"\u63A1\u8CFC",stock_in:"\u5165\u5EAB",stock_out:"\u51FA\u5EAB",stocktaking:"\u76E4\u9EDE",scrap:"\u5831\u5EE2",return_in:"\u9000\u8CA8\u5165\u5EAB",return_out:"\u9000\u8CA8\u51FA\u5EAB"},returnType:{label:"\u9000\u8CA8\u985E\u578B",customer_return:"\u5BA2\u6236\u9000\u8CA8",supplier_return:"\u9000\u56DE\u4F9B\u61C9\u5546"},beforeQuantity:"\u8B8A\u66F4\u524D\u5EAB\u5B58",afterQuantity:"\u8B8A\u66F4\u5F8C\u5EAB\u5B58",diffQuantity:"\u5DEE\u7570\u91CF"},actions:"\u64CD\u4F5C",wpOrder:{actions:{complete:"\u5B8C\u6210\u8A02\u55AE",cancel:"\u53D6\u6D88\u8A02\u55AE",refund:"\u9000\u6B3E",processing:"\u66F4\u6539\u70BA\u8655\u7406\u4E2D"},title:{complete:"\u5B8C\u6210\u8A02\u55AE",cancel:"\u53D6\u6D88\u8A02\u55AE",refund:"\u9000\u6B3E",processing:"\u8655\u7406\u4E2D"}},wcOrder:{statusUpdate:{title:"\u66F4\u65B0\u8A02\u55AE\u72C0\u614B",confirm:"\u78BA\u5B9A\u8981\u66F4\u65B0\u8A02\u55AE\u72C0\u614B\u55CE\uFF1F",success:"\u8A02\u55AE\u72C0\u614B\u5DF2\u66F4\u65B0"},cancel:{title:"\u53D6\u6D88\u8A02\u55AE",reasonLabel:"\u53D6\u6D88\u539F\u56E0",reasonPlaceholder:"\u8ACB\u8F38\u5165\u53D6\u6D88\u539F\u56E0...",confirm:"\u78BA\u5B9A\u8981\u53D6\u6D88\u6B64\u8A02\u55AE\u55CE\uFF1F",reasonRequired:"\u8ACB\u8F38\u5165\u53D6\u6D88\u539F\u56E0",success:"\u8A02\u55AE\u5DF2\u53D6\u6D88",reasons:{outOfStock:"\u5EAB\u5B58\u4E0D\u8DB3",customerRequest:"\u5BA2\u6236\u8981\u6C42",paymentIssue:"\u4ED8\u6B3E\u554F\u984C",duplicateOrder:"\u91CD\u8907\u8A02\u55AE",incorrectInfo:"\u8CC7\u8A0A\u932F\u8AA4",other:"\u5176\u4ED6"}}},orderItem:{is_free:"\u8D08\u54C1"},vip:{label:"VIP",expiryDate:"VIP \u5230\u671F\u65E5"},year:{label:"\u5E74"},month:{label:"\u6708"},barcodeScanner:{ready:"\u689D\u78BC\u6383\u63CF\u5668\u5C31\u7DD2",scanning:"\u6383\u63CF\u4E2D...",notFound:"\u627E\u4E0D\u5230\u5546\u54C1",notFoundMessage:"\u627E\u4E0D\u5230\u689D\u78BC\u70BA {barcode} \u7684\u5546\u54C1",scanCompleted:"\u6383\u63CF\u5B8C\u6210"},added:"\u5DF2\u52A0\u5165",system:"\u7CFB\u7D71\u8A2D\u5B9A",announcement:{settings:"\u516C\u544A\u8A2D\u5B9A",title:"\u7DB2\u7AD9\u516C\u544A\u8A2D\u5B9A",content:"\u516C\u544A\u5167\u5BB9",placeholder:"\u8ACB\u8F38\u5165\u516C\u544A\u5167\u5BB9...",save:"\u5132\u5B58\u516C\u544A",success:"\u516C\u544A\u5132\u5B58\u6210\u529F"},updateService:{updateNotification:{title:"\u7CFB\u7D71\u66F4\u65B0\u901A\u77E5",newVersion:"\u767C\u73FE\u65B0\u7248\u672C",currentVersion:"\u76EE\u524D\u7248\u672C",buildTime:"\u5EFA\u7F6E\u6642\u9593",recommendUpdate:"\u5EFA\u8B70\u7ACB\u5373\u66F4\u65B0\u4EE5\u7372\u5F97\u6700\u4F73\u9AD4\u9A57",doNotClose:"\u66F4\u65B0\u671F\u9593\u8ACB\u52FF\u95DC\u9589\u700F\u89BD\u5668\u8996\u7A97",updateLater:"\u7A0D\u5F8C\u63D0\u9192",updateNow:"\u7ACB\u5373\u66F4\u65B0",laterReminder:"\u{1F4A1} \u7A0D\u5F8C\u5C07\u518D\u6B21\u63D0\u9192\u60A8\u66F4\u65B0"},updateProcess:{updating:"\u6B63\u5728\u66F4\u65B0\u7CFB\u7D71\uFF0C\u8ACB\u7A0D\u5019...",success:"\u7CFB\u7D71\u66F4\u65B0\u5B8C\u6210\uFF0C\u5373\u5C07\u91CD\u65B0\u8F09\u5165",failed:"\u66F4\u65B0\u5931\u6557\uFF0C\u5C07\u5617\u8A66\u5F37\u5236\u91CD\u65B0\u6574\u7406",forceRefresh:"\u5F37\u5236\u91CD\u65B0\u6574\u7406\u4E2D..."}},xero:{menu:{setup:"\u8A2D\u5B9A",invoices:"\u767C\u7968"},setup:{title:"Xero API \u8A2D\u5B9A",connectionStatus:{connected:"\u5DF2\u9023\u63A5\u5230 Xero \u5E33\u865F: {tenantName}",tokenExpiry:"Token \u5230\u671F\u6642\u9593: {expiryDate}",notConnected:"\u5C1A\u672A\u9023\u63A5\u5230 Xero \u5E33\u865F",disconnect:"\u4E2D\u65B7\u9023\u63A5",refreshToken:"\u5237\u65B0Token"},form:{clientId:"Client ID",clientSecret:"Client Secret",redirectUri:"Redirect URI",scopes:"\u6B0A\u9650\u7BC4\u570D",scopesHint:"\u9810\u8A2D: accounting.transactions accounting.contacts",scopesPlaceholder:"accounting.transactions accounting.contacts",defaultEmail:"\u9810\u8A2D Email \u5730\u5740",defaultEmailHint:"\u7576\u5BA2\u6236\u6C92\u6709 Email \u6642\u4F7F\u7528\u6B64\u5730\u5740\u767C\u9001\u767C\u7968",saveConfig:"\u5132\u5B58\u8A2D\u5B9A",connectXero:"\u9023\u63A5 Xero"},validation:{clientIdRequired:"Client ID\u70BA\u5FC5\u586B",clientSecretRequired:"Client Secret\u70BA\u5FC5\u586B",redirectUriRequired:"Redirect URI\u70BA\u5FC5\u586B",invalidEmail:"\u7121\u6548\u7684 Email \u683C\u5F0F"},dialog:{disconnectTitle:"\u78BA\u8A8D\u4E2D\u65B7\u9023\u63A5",disconnectMessage:"\u78BA\u5B9A\u8981\u4E2D\u65B7\u8207Xero\u7684\u9023\u63A5\u55CE\uFF1F"},error:{stateStorageFailed:"\u7121\u6CD5\u4FDD\u5B58\u8A8D\u8B49\u72C0\u614B\uFF0C\u8ACB\u91CD\u65B0\u5617\u8A66\u3002"}},redirect:{processing:"\u6B63\u5728\u8655\u7406 Xero \u8A8D\u8B49...",success:{title:"Xero \u9023\u63A5\u6210\u529F\uFF01",connectedTo:"\u5DF2\u6210\u529F\u9023\u63A5\u5230: {tenantName}",backToSettings:"\u8FD4\u56DE\u8A2D\u5B9A\u9801\u9762",notification:"Xero\u9023\u63A5\u6210\u529F\uFF01"},error:{title:"\u9023\u63A5\u5931\u6557",retry:"\u91CD\u65B0\u5617\u8A66",clearState:"\u6E05\u9664\u72C0\u614B\u4E26\u91CD\u8A66",messages:{authError:"\u8A8D\u8B49\u932F\u8AA4: {error}",missingParams:"\u7F3A\u5C11\u5FC5\u8981\u7684\u8A8D\u8B49\u53C3\u6578",invalidState:"\u7121\u6548\u7684state\u53C3\u6578\uFF0C\u53EF\u80FD\u5B58\u5728\u5B89\u5168\u98A8\u96AA",noSavedState:"\u627E\u4E0D\u5230\u5DF2\u4FDD\u5B58\u7684state\uFF0C\u8ACB\u91CD\u65B0\u5617\u8A66\u9023\u63A5",unknownOrg:"\u672A\u77E5\u7D44\u7E54"}},debug:{title:"\u8ABF\u8A66\u4FE1\u606F",stateCleared:"\u8A8D\u8B49\u72C0\u614B\u5DF2\u6210\u529F\u6E05\u9664"}},invoices:{title:"Xero \u767C\u7968",notConnected:"\u5C1A\u672A\u9023\u63A5\u5230 Xero\uFF0C\u8ACB\u5148\u914D\u7F6E\u9023\u63A5\u3002",goToSetup:"\u524D\u5F80\u8A2D\u5B9A",dateFrom:"\u958B\u59CB\u65E5\u671F",dateTo:"\u7D50\u675F\u65E5\u671F",invoiceNumber:"\u767C\u7968\u865F\u78BC",contact:"\u806F\u7D61\u4EBA",date:"\u65E5\u671F",dueDate:"\u5230\u671F\u65E5",subTotal:"\u5C0F\u8A08",totalTax:"\u7E3D\u7A05\u984D",total:"\u7E3D\u8A08",invoiceDetails:"\u767C\u7968\u8A73\u60C5",basicInfo:"\u57FA\u672C\u8CC7\u6599",lineItems:"\u9805\u76EE\u660E\u7D30",unitAmount:"\u55AE\u50F9",lineAmount:"\u91D1\u984D",enterEmailAddress:"\u8ACB\u8F38\u5165\u90F5\u4EF6\u5730\u5740",invoiceNotFound:"\u627E\u4E0D\u5230\u767C\u7968",type:{label:"\u985E\u578B",accrec:"\u92B7\u552E\u767C\u7968",accpay:"\u63A1\u8CFC\u767C\u7968"},status:{draft:"\u8349\u7A3F",submitted:"\u5DF2\u63D0\u4EA4",authorised:"\u5DF2\u6388\u6B0A",paid:"\u5DF2\u4ED8\u6B3E",voided:"\u5DF2\u4F5C\u5EE2",deleted:"\u5DF2\u522A\u9664"}},syncStatus:"Xero \u540C\u6B65\u72C0\u614B",notSynced:"\u672A\u540C\u6B65",syncPending:"\u5F85\u540C\u6B65",syncing:"\u540C\u6B65\u4E2D",syncSuccess:"\u540C\u6B65\u6210\u529F",syncFailed:"\u540C\u6B65\u5931\u6557",voidedInvoice:"\u5DF2\u4F5C\u5EE2\u767C\u7968",syncToXero:"\u540C\u6B65\u5230 Xero",syncToXeroSuccess:"\u6210\u529F\u540C\u6B65\u5230 Xero",syncToXeroError:"\u540C\u6B65\u5230 Xero \u5931\u6557",alreadySynced:"\u5DF2\u540C\u6B65"}},kS={"en-US":wS,"zh-TW":TS};const hp=pS({locale:"en-US",legacy:!1,messages:kS,modifiers:{"@":()=>"@"}});var xS=({app:e})=>{e.use(hp)},OS=Object.freeze(Object.defineProperty({__proto__:null,i18n:hp,default:xS},Symbol.toStringTag,{value:"Module"}));const Ye=Bu.create({baseURL:"/au-pos/api",timeout:1e4,headers:{"Content-Type":"application/json"}});var RS=({app:e,router:t})=>{e.config.globalProperties.$axios=Bu,e.config.globalProperties.$api=Ye;let n=!1,r=[];const s=(i,a=null)=>{r.forEach(u=>{i?u.reject(i):u.resolve(a)}),r=[]};Ye.interceptors.request.use(i=>{var a;if(!((a=i.headers)!=null&&a.Authorization)){const u=eu();u.accessToken&&(i.headers.Authorization=`Bearer ${u.accessToken}`,i.headers["X-Refresh-Token"]=u.refreshToken)}return i},i=>Promise.reject(i)),Ye.interceptors.response.use(i=>i,async i=>{var u;const a=i.config;switch((u=i.response)==null?void 0:u.status){case 401:return o(a);case 403:t.push("/index");break}return Promise.reject(i)});const o=async i=>{const a=["v1/auth/logout","v1/auth/refresh-token"];if(!i._retry&&!a.some(u=>{var l;return(l=i.url)==null?void 0:l.includes(u)})){i._retry=!0;const u=eu();if(n)return new Promise((l,c)=>{r.push({resolve:l,reject:c})}).then(l=>(i.headers||(i.headers={}),i.headers.Authorization=`Bearer ${l}`,i.headers["X-Refresh-Token"]=u.refreshToken,Ye(i))).catch(l=>Promise.reject(l));n=!0;try{if(!localStorage.getItem("refreshToken"))throw new Error("No refresh token");const f=(await Tm.refreshToken()).result;return u.updateToken(f),n=!1,s(null,f.access_token),i.headers||(i.headers={}),i.headers.Authorization=`Bearer ${f.access_token}`,i.headers["X-Refresh-Token"]=f.refresh_token,Ye(i)}catch(l){return n=!1,s(l,null),u.logout(),t.push("/login"),Promise.reject(l)}}}};const Jr={async get(e,t){try{return(await Ye.get(e,t)).data}catch(n){return cr(n),Promise.reject(n)}},async post(e,t,n){try{const r=await Ye.post(e,t,n);return r==null?void 0:r.data}catch(r){return cr(r),Promise.reject(r)}},async put(e,t,n){try{return(await Ye.put(e,t,n)).data}catch(r){return cr(r),Promise.reject(r)}},async patch(e,t,n){try{return(await Ye.patch(e,t,n)).data}catch(r){return cr(r),Promise.reject(r)}},async delete(e,t){try{return(await Ye.delete(e,t)).data}catch(n){return cr(n),Promise.reject(n)}},async uploadFile(e,t,n,r){try{const s=new FormData;s.append("file",t),n&&Object.entries(n).forEach(([a,u])=>{s.append(a,u)});const o={...r,headers:{...(r==null?void 0:r.headers)||{},"Content-Type":"multipart/form-data"}};return(await Ye.post(e,s,o)).data}catch(s){return cr(s),Promise.reject(s)}}};var PS=Object.freeze(Object.defineProperty({__proto__:null,default:RS,apiWrapper:Jr,api:Ye},Symbol.toStringTag,{value:"Module"}));const eu=Nb("auth",{state:()=>({userUUID:"",userName:"",userInfo:{},accessToken:"",tokenExpiry:null,refreshToken:"",refreshTokenExpiry:null}),persist:!0,getters:{getUserInfo:e=>e.userInfo,getUserUUID:e=>e.userUUID,getUserName:e=>e.userName,isAuthenticated:e=>!!e.accessToken,isTokenExpired:e=>e.tokenExpiry?new Date>=e.tokenExpiry:!0,isRefreshTokenExpired:e=>e.refreshTokenExpiry?new Date>=e.refreshTokenExpiry:!0},actions:{login(e){this.setUser(e.user),this.updateToken(e)},setUser(e){this.userInfo=e,this.userUUID=e.uuid,this.userName=e.name},getTokenClaims(){const e=this.accessToken;if(!e)return null;try{const n=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),r=JSON.parse(atob(n));return{userUUID:r.user_uuid,isAdmin:r.is_admin}}catch{return null}},isAdmin(){const e=this.getTokenClaims();return(e==null?void 0:e.isAdmin)||!1},updateToken(e){this.accessToken=e.access_token,this.tokenExpiry=e.expires_at,this.refreshToken=e.refresh_token,this.refreshTokenExpiry=e.refresh_expires_at,localStorage.setItem("accessToken",e.access_token),localStorage.setItem("tokenExpiry",e.expires_at.toString()),localStorage.setItem("refreshToken",e.refresh_token),localStorage.setItem("refreshTokenExpiry",e.refresh_expires_at.toString()),Ye.defaults.headers.common.Authorization=`Bearer ${e.access_token}`,Ye.defaults.headers.common["X-Refresh-Token"]=e.refresh_token},logout(){this.accessToken="",this.tokenExpiry=null,this.refreshToken="",this.refreshTokenExpiry=null,this.userUUID="",this.userName="",localStorage.removeItem("accessToken"),localStorage.removeItem("tokenExpiry"),localStorage.removeItem("refreshToken"),localStorage.removeItem("refreshTokenExpiry"),delete Ye.defaults.headers.common.Authorization,delete Ye.defaults.headers.common["X-Refresh-Token"]},initializeFromStorage(){const e=localStorage.getItem("accessToken"),t=localStorage.getItem("refreshToken"),n=localStorage.getItem("tokenExpiry"),r=localStorage.getItem("refreshTokenExpiry");e&&t&&n&&r&&(this.accessToken=e,this.tokenExpiry=new Date(n),this.refreshToken=t,this.refreshTokenExpiry=new Date(r),Ye.defaults.headers.common.Authorization=`Bearer ${e}`,Ye.defaults.headers.common["X-Refresh-Token"]=t)}}}),FS=[{path:"/login",component:()=>Fe(()=>import("./LoginLayout.c902d763.js"),["assets/LoginLayout.c902d763.js","assets/LoginLayout.657f1968.css","assets/QImg.82abcee1.js","assets/QToolbar.0e8beb30.js","assets/QHeader.4b0fd432.js","assets/QScrollObserver.b02e8e20.js","assets/QLayout.1c8c7694.js","assets/plugin-vue_export-helper.21dcd24c.js"]),children:[{path:"",component:()=>Fe(()=>import("./LoginPage.b552fb64.js"),["assets/LoginPage.b552fb64.js","assets/LoginPage.45422771.css","assets/QForm.534886fc.js","assets/QPage.8bf63692.js"])}]},{path:"/",redirect:"/login",component:()=>Fe(()=>import("./MainLayout.8fd89484.js"),["assets/MainLayout.8fd89484.js","assets/QToolbar.0e8beb30.js","assets/QHeader.4b0fd432.js","assets/QScrollObserver.b02e8e20.js","assets/QDrawer.9a90173f.js","assets/TouchPan.ddd1c5b8.js","assets/selection.787abe58.js","assets/format.054b8074.js","assets/QLayout.1c8c7694.js","assets/pageInfo.0fe0917e.js","assets/dialog.c8d4f0ed.js"]),children:[{path:"attendance",component:()=>Fe(()=>import("./AttendancePage.b5228e57.js"),["assets/AttendancePage.b5228e57.js","assets/AttendancePage.d72f7a92.css","assets/QDate.10e993fc.js","assets/format.054b8074.js","assets/QPopupProxy.1fd4617d.js","assets/QMenu.abf49c1b.js","assets/selection.787abe58.js","assets/QPage.8bf63692.js","assets/ClosePopup.f68b6158.js","assets/pageInfo.0fe0917e.js","assets/plugin-vue_export-helper.21dcd24c.js"])}]},{path:"/order",redirect:"/order",component:()=>Fe(()=>import("./OrderLayout.92693d00.js"),["assets/OrderLayout.92693d00.js","assets/OrderLayout.4956267f.css","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/QList.f0282e16.js","assets/QMenu.abf49c1b.js","assets/selection.787abe58.js","assets/QDrawer.9a90173f.js","assets/TouchPan.ddd1c5b8.js","assets/format.054b8074.js","assets/QLayout.1c8c7694.js","assets/QScrollObserver.b02e8e20.js","assets/ClosePopup.f68b6158.js","assets/QTabPanels.04761242.js","assets/QSelect.c4b20219.js","assets/QDate.10e993fc.js","assets/QSpace.bd91c020.js","assets/QScrollArea.44613085.js","assets/attendance.e62c1b09.js","assets/date.6d29930c.js","assets/plugin-vue_export-helper.21dcd24c.js","assets/QTd.6cf74813.js","assets/QTable.e4650bec.js","assets/use-fullscreen.e0d6e2b2.js","assets/QPopupProxy.1fd4617d.js","assets/QTooltip.7e86cfcb.js","assets/QTr.5f91a8aa.js","assets/customer.c1e7442d.js","assets/product.84ec250a.js","assets/BarcodeScannerWrapper.e4276634.js","assets/BarcodeScannerWrapper.43e7de38.css","assets/use-quasar.66282a44.js","assets/inventory.fb6b0e82.js","assets/dialog.c8d4f0ed.js"]),children:[{path:"",component:()=>Fe(()=>import("./OrderIndexPage.9dd88ea7.js"),["assets/OrderIndexPage.9dd88ea7.js","assets/OrderIndexPage.a614bf7b.css","assets/QTabPanels.04761242.js","assets/QScrollObserver.b02e8e20.js","assets/QSelect.c4b20219.js","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/QMenu.abf49c1b.js","assets/selection.787abe58.js","assets/format.054b8074.js","assets/TouchPan.ddd1c5b8.js","assets/QDate.10e993fc.js","assets/QTd.6cf74813.js","assets/QTr.5f91a8aa.js","assets/QTable.e4650bec.js","assets/QList.f0282e16.js","assets/use-fullscreen.e0d6e2b2.js","assets/QPage.8bf63692.js","assets/QSpace.bd91c020.js","assets/QScrollArea.44613085.js","assets/usePrintInvoice.e1a96b8f.js","assets/usePrintInvoice.cdd7b13b.css","assets/plugin-vue_export-helper.21dcd24c.js","assets/xero.72a68f5d.js","assets/order.31b2d3ca.js","assets/date.6d29930c.js","assets/OrderDetailDialog.c8ef92b5.js","assets/use-quasar.66282a44.js","assets/useOrder.c3e6878c.js","assets/DateRangePicker.7842c5b2.js","assets/QPopupProxy.1fd4617d.js","assets/ClosePopup.f68b6158.js","assets/WCOrderDetailDialog.c0a24920.js","assets/dialog.c8d4f0ed.js"])},{path:":orderID",component:()=>Fe(()=>import("./OrderCheckoutPage.a04aeb7b.js"),["assets/OrderCheckoutPage.a04aeb7b.js","assets/OrderCheckoutPage.4c97793b.css","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/QDate.10e993fc.js","assets/format.054b8074.js","assets/TouchPan.ddd1c5b8.js","assets/selection.787abe58.js","assets/QList.f0282e16.js","assets/QScrollArea.44613085.js","assets/QScrollObserver.b02e8e20.js","assets/QImg.82abcee1.js","assets/QPage.8bf63692.js","assets/use-quasar.66282a44.js","assets/productCategory.ec463d5f.js","assets/product.84ec250a.js","assets/usePrintInvoice.e1a96b8f.js","assets/usePrintInvoice.cdd7b13b.css","assets/plugin-vue_export-helper.21dcd24c.js","assets/xero.72a68f5d.js","assets/useOrder.c3e6878c.js","assets/date.6d29930c.js","assets/QSpace.bd91c020.js","assets/QTd.6cf74813.js","assets/QTr.5f91a8aa.js","assets/QTable.e4650bec.js","assets/QSelect.c4b20219.js","assets/QMenu.abf49c1b.js","assets/use-fullscreen.e0d6e2b2.js","assets/customer.c1e7442d.js","assets/useCustomer.1cae112a.js","assets/BarcodeScannerWrapper.e4276634.js","assets/BarcodeScannerWrapper.43e7de38.css","assets/dialog.c8d4f0ed.js"])}],meta:{requiresAuth:!0}},{path:"/admin/dashboard",redirect:"/admin/dashboard/user",component:()=>Fe(()=>import("./AdminLayout.4558d588.js"),["assets/AdminLayout.4558d588.js","assets/AdminLayout.6bc9ac01.css","assets/QToolbar.0e8beb30.js","assets/QHeader.4b0fd432.js","assets/QScrollObserver.b02e8e20.js","assets/QDrawer.9a90173f.js","assets/TouchPan.ddd1c5b8.js","assets/selection.787abe58.js","assets/format.054b8074.js","assets/QLayout.1c8c7694.js","assets/QExpansionItem.e9c9f9d6.js","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/QList.f0282e16.js","assets/plugin-vue_export-helper.21dcd24c.js","assets/pageInfo.0fe0917e.js","assets/dialog.c8d4f0ed.js"]),children:[{path:"user",component:()=>Fe(()=>import("./UserPage.16cc2906.js"),["assets/UserPage.16cc2906.js","assets/UserPage.f8f87c1d.css","assets/QItemLabel.3b58be08.js","assets/QItemSection.7dc1f54f.js","assets/QScrollArea.44613085.js","assets/QScrollObserver.b02e8e20.js","assets/TouchPan.ddd1c5b8.js","assets/selection.787abe58.js","assets/format.054b8074.js","assets/QPage.8bf63692.js","assets/use-quasar.66282a44.js","assets/user.dacaea99.js","assets/QSelect.c4b20219.js","assets/QMenu.abf49c1b.js","assets/QForm.534886fc.js","assets/dialog.c8d4f0ed.js","assets/plugin-vue_export-helper.21dcd24c.js"])},{path:"attendance",component:()=>Fe(()=>import("./AttendancePage.8c4dfa45.js"),["assets/AttendancePage.8c4dfa45.js","assets/QToolbar.0e8beb30.js","assets/QDate.10e993fc.js","assets/format.054b8074.js","assets/QPopupProxy.1fd4617d.js","assets/QMenu.abf49c1b.js","assets/selection.787abe58.js","assets/QSelect.c4b20219.js","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/QTable.e4650bec.js","assets/QList.f0282e16.js","assets/use-fullscreen.e0d6e2b2.js","assets/QPage.8bf63692.js","assets/ClosePopup.f68b6158.js","assets/attendance.e62c1b09.js","assets/date.6d29930c.js","assets/user.dacaea99.js"])},{path:"payroll",component:()=>Fe(()=>import("./PayrollPage.6eb2de28.js"),["assets/PayrollPage.6eb2de28.js","assets/QToolbar.0e8beb30.js","assets/QTd.6cf74813.js","assets/QTr.5f91a8aa.js","assets/QTable.e4650bec.js","assets/QList.f0282e16.js","assets/QSelect.c4b20219.js","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/QMenu.abf49c1b.js","assets/selection.787abe58.js","assets/format.054b8074.js","assets/use-fullscreen.e0d6e2b2.js","assets/QSpace.bd91c020.js","assets/QScrollArea.44613085.js","assets/QScrollObserver.b02e8e20.js","assets/TouchPan.ddd1c5b8.js","assets/QDate.10e993fc.js","assets/QPopupProxy.1fd4617d.js","assets/QPage.8bf63692.js","assets/ClosePopup.f68b6158.js","assets/user.dacaea99.js","assets/attendance.e62c1b09.js","assets/order.31b2d3ca.js","assets/date.6d29930c.js","assets/dialog.c8d4f0ed.js","assets/DateRangePicker.7842c5b2.js"])},{path:"customer",component:()=>Fe(()=>import("./CustomerPage.603b18b5.js"),["assets/CustomerPage.603b18b5.js","assets/CustomerPage.68b87f77.css","assets/QToolbar.0e8beb30.js","assets/QTd.6cf74813.js","assets/QTr.5f91a8aa.js","assets/QTable.e4650bec.js","assets/QList.f0282e16.js","assets/QSelect.c4b20219.js","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/QMenu.abf49c1b.js","assets/selection.787abe58.js","assets/format.054b8074.js","assets/use-fullscreen.e0d6e2b2.js","assets/QPage.8bf63692.js","assets/customer.c1e7442d.js","assets/useCustomer.1cae112a.js","assets/date.6d29930c.js","assets/QSpace.bd91c020.js","assets/QDate.10e993fc.js","assets/QPopupProxy.1fd4617d.js","assets/QScrollArea.44613085.js","assets/QScrollObserver.b02e8e20.js","assets/TouchPan.ddd1c5b8.js","assets/ClosePopup.f68b6158.js","assets/QForm.534886fc.js","assets/product.84ec250a.js","assets/usePrintInvoice.e1a96b8f.js","assets/usePrintInvoice.cdd7b13b.css","assets/plugin-vue_export-helper.21dcd24c.js","assets/xero.72a68f5d.js","assets/dialog.c8d4f0ed.js","assets/order.31b2d3ca.js","assets/TablePagination.215cbc35.js","assets/OrderDetailDialog.c8ef92b5.js","assets/use-quasar.66282a44.js","assets/useOrder.c3e6878c.js","assets/DateRangePicker.7842c5b2.js"])},{path:"catalog",redirect:"/admin/dashboard/catalog/product",children:[{path:"product",component:()=>Fe(()=>import("./ProductPage.a5b9e30b.js"),["assets/ProductPage.a5b9e30b.js","assets/ProductPage.24956ece.css","assets/QItemLabel.3b58be08.js","assets/QItemSection.7dc1f54f.js","assets/QScrollArea.44613085.js","assets/QScrollObserver.b02e8e20.js","assets/TouchPan.ddd1c5b8.js","assets/selection.787abe58.js","assets/format.054b8074.js","assets/QPage.8bf63692.js","assets/use-quasar.66282a44.js","assets/productCategory.ec463d5f.js","assets/QImg.82abcee1.js","assets/ClosePopup.f68b6158.js","assets/QForm.534886fc.js","assets/QTd.6cf74813.js","assets/QTr.5f91a8aa.js","assets/QTable.e4650bec.js","assets/QList.f0282e16.js","assets/QSelect.c4b20219.js","assets/QMenu.abf49c1b.js","assets/use-fullscreen.e0d6e2b2.js","assets/product.84ec250a.js","assets/plugin-vue_export-helper.21dcd24c.js","assets/QSpace.bd91c020.js","assets/QEditor.94539128.js","assets/QTooltip.7e86cfcb.js","assets/_commonjsHelpers.8402d862.js","assets/customer.c1e7442d.js","assets/dialog.c8d4f0ed.js"])},{path:"stock-history",component:()=>Fe(()=>import("./StockHistoryPage.ea546cee.js"),["assets/StockHistoryPage.ea546cee.js","assets/QTd.6cf74813.js","assets/QTable.e4650bec.js","assets/QList.f0282e16.js","assets/QSelect.c4b20219.js","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/QMenu.abf49c1b.js","assets/selection.787abe58.js","assets/format.054b8074.js","assets/use-fullscreen.e0d6e2b2.js","assets/QPage.8bf63692.js","assets/inventory.fb6b0e82.js"])}]},{path:"order",redirect:"/admin/dashboard/order/onsite",children:[{path:"onsite",component:()=>Fe(()=>import("./OnsiteOrderPage.a674c873.js"),["assets/OnsiteOrderPage.a674c873.js","assets/QToolbar.0e8beb30.js","assets/QSelect.c4b20219.js","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/QMenu.abf49c1b.js","assets/selection.787abe58.js","assets/format.054b8074.js","assets/QTd.6cf74813.js","assets/QTr.5f91a8aa.js","assets/QTable.e4650bec.js","assets/QList.f0282e16.js","assets/use-fullscreen.e0d6e2b2.js","assets/QPage.8bf63692.js","assets/customer.c1e7442d.js","assets/usePrintInvoice.e1a96b8f.js","assets/usePrintInvoice.cdd7b13b.css","assets/plugin-vue_export-helper.21dcd24c.js","assets/xero.72a68f5d.js","assets/order.31b2d3ca.js","assets/date.6d29930c.js","assets/DateRangePicker.7842c5b2.js","assets/QDate.10e993fc.js","assets/QPopupProxy.1fd4617d.js","assets/ClosePopup.f68b6158.js","assets/OrderDetailDialog.c8ef92b5.js","assets/QSpace.bd91c020.js","assets/QScrollArea.44613085.js","assets/QScrollObserver.b02e8e20.js","assets/TouchPan.ddd1c5b8.js","assets/use-quasar.66282a44.js","assets/useOrder.c3e6878c.js","assets/TablePagination.215cbc35.js"])},{path:"online",component:()=>Fe(()=>import("./OnlineOrderPage.c8745d91.js"),["assets/OnlineOrderPage.c8745d91.js","assets/QToolbar.0e8beb30.js","assets/QSelect.c4b20219.js","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/QMenu.abf49c1b.js","assets/selection.787abe58.js","assets/format.054b8074.js","assets/QTd.6cf74813.js","assets/QTr.5f91a8aa.js","assets/QTable.e4650bec.js","assets/QList.f0282e16.js","assets/use-fullscreen.e0d6e2b2.js","assets/QPage.8bf63692.js","assets/WCOrderDetailDialog.c0a24920.js","assets/QSpace.bd91c020.js","assets/QScrollArea.44613085.js","assets/QScrollObserver.b02e8e20.js","assets/TouchPan.ddd1c5b8.js","assets/use-quasar.66282a44.js","assets/usePrintInvoice.e1a96b8f.js","assets/usePrintInvoice.cdd7b13b.css","assets/plugin-vue_export-helper.21dcd24c.js","assets/xero.72a68f5d.js","assets/date.6d29930c.js","assets/dialog.c8d4f0ed.js","assets/TablePagination.215cbc35.js","assets/customer.c1e7442d.js","assets/order.31b2d3ca.js"])}]},{path:"system",component:()=>Fe(()=>import("./SystemPage.78490a95.js"),["assets/SystemPage.78490a95.js","assets/QForm.534886fc.js","assets/QPage.8bf63692.js"])},{path:"announcement",component:()=>Fe(()=>import("./AnnouncementPage.5fb8444f.js"),["assets/AnnouncementPage.5fb8444f.js","assets/QEditor.94539128.js","assets/QMenu.abf49c1b.js","assets/selection.787abe58.js","assets/QTooltip.7e86cfcb.js","assets/QItemSection.7dc1f54f.js","assets/use-fullscreen.e0d6e2b2.js","assets/QForm.534886fc.js","assets/QPage.8bf63692.js","assets/use-quasar.66282a44.js"])},{path:"xero",redirect:"/admin/dashboard/xero/setup",children:[{path:"setup",component:()=>Fe(()=>import("./XeroSetupPage.68440129.js"),["assets/XeroSetupPage.68440129.js","assets/QBanner.7a7a9f89.js","assets/QForm.534886fc.js","assets/QPage.8bf63692.js","assets/use-quasar.66282a44.js","assets/xero.72a68f5d.js","assets/date.6d29930c.js"])},{path:"invoices",component:()=>Fe(()=>import("./XeroInvoicesPage.e1c58def.js"),["assets/XeroInvoicesPage.e1c58def.js","assets/QBanner.7a7a9f89.js","assets/QSelect.c4b20219.js","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/QMenu.abf49c1b.js","assets/selection.787abe58.js","assets/format.054b8074.js","assets/QTd.6cf74813.js","assets/QTooltip.7e86cfcb.js","assets/QTable.e4650bec.js","assets/QList.f0282e16.js","assets/use-fullscreen.e0d6e2b2.js","assets/QSpace.bd91c020.js","assets/QScrollArea.44613085.js","assets/QScrollObserver.b02e8e20.js","assets/TouchPan.ddd1c5b8.js","assets/QPage.8bf63692.js","assets/use-quasar.66282a44.js","assets/xero.72a68f5d.js","assets/date.6d29930c.js","assets/DateRangePicker.7842c5b2.js","assets/QDate.10e993fc.js","assets/QPopupProxy.1fd4617d.js","assets/ClosePopup.f68b6158.js","assets/TablePagination.215cbc35.js"])}]}],meta:{requiresAuth:!0,requiresAdmin:!0}},{path:"/xero/redirect",component:()=>Fe(()=>import("./LoginLayout.c902d763.js"),["assets/LoginLayout.c902d763.js","assets/LoginLayout.657f1968.css","assets/QImg.82abcee1.js","assets/QToolbar.0e8beb30.js","assets/QHeader.4b0fd432.js","assets/QScrollObserver.b02e8e20.js","assets/QLayout.1c8c7694.js","assets/plugin-vue_export-helper.21dcd24c.js"]),children:[{path:"",component:()=>Fe(()=>import("./XeroRedirectPage.032a126d.js"),["assets/XeroRedirectPage.032a126d.js","assets/QExpansionItem.e9c9f9d6.js","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/QPage.8bf63692.js","assets/use-quasar.66282a44.js","assets/xero.72a68f5d.js"])}]},{path:"/:catchAll(.*)*",redirect:"/login"}];var na=function(){const t=qy({scrollBehavior:()=>({left:0,top:0}),routes:FS,history:gy("/au-pos/")}),n=u=>u.matched.some(l=>l.meta.requiresAuth),r=u=>u.matched.some(l=>l.meta.requiresAdmin),s=(u,l)=>{l({path:"/login",query:{redirect:u.fullPath}})},o=u=>{u.accessToken||u.initializeFromStorage()},i=async(u,l,c)=>{try{const f=await Tm.refreshToken();return u.updateToken(f.result),c(),!0}catch{return u.logout(),s(l,c),!1}},a=(u,l)=>!u.isAuthenticated||!u.isAdmin()?(l({path:"/order"}),!1):!0;return t.beforeEach(async(u,l,c)=>{const f=eu(),d=n(u),m=r(u);if(o(f),d){if(!f.isAuthenticated){s(u,c);return}if(f.isTokenExpired&&!f.isRefreshTokenExpired&&!await i(f,u,c))return;if(f.isRefreshTokenExpired){f.logout(),s(u,c);return}}m&&!a(f,c)||c()}),t};async function DS(e,t){const n=e(Tb);n.use($v,t);const r=typeof Hi=="function"?await Hi({}):Hi;n.use(r);const s=Zn(typeof na=="function"?await na({store:r}):na);return r.use(({store:o})=>{o.router=s}),{app:n,store:r,router:s}}var NS={config:{},lang:Ta,plugins:{Dialog:FC,Notify:Io}},LS=function(){return Boolean(window.location.hostname==="localhost"||window.location.hostname==="[::1]"||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/))},tu;typeof window!="undefined"&&(typeof Promise!="undefined"?tu=new Promise(function(e){return window.addEventListener("load",e)}):tu={then:function(e){return window.addEventListener("load",e)}});function IS(e,t){t===void 0&&(t={});var n=t.registrationOptions;n===void 0&&(n={}),delete t.registrationOptions;var r=function(s){for(var o=[],i=arguments.length-1;i-- >0;)o[i]=arguments[i+1];t&&t[s]&&t[s].apply(t,o)};"serviceWorker"in navigator&&tu.then(function(){LS()?(BS(e,r,n),navigator.serviceWorker.ready.then(function(s){r("ready",s)}).catch(function(s){return ws(r,s)})):(mp(e,r,n),navigator.serviceWorker.ready.then(function(s){r("ready",s)}).catch(function(s){return ws(r,s)}))})}function ws(e,t){navigator.onLine||e("offline"),e("error",t)}function mp(e,t,n){navigator.serviceWorker.register(e,n).then(function(r){if(t("registered",r),r.waiting){t("updated",r);return}r.onupdatefound=function(){t("updatefound",r);var s=r.installing;s.onstatechange=function(){s.state==="installed"&&(navigator.serviceWorker.controller?t("updated",r):t("cached",r))}}}).catch(function(r){return ws(t,r)})}function BS(e,t,n){fetch(e).then(function(r){r.status===404?(t("error",new Error("Service worker not found at "+e)),xf()):r.headers.get("content-type").indexOf("javascript")===-1?(t("error",new Error("Expected "+e+" to have javascript content-type, but received "+r.headers.get("content-type"))),xf()):mp(e,t,n)}).catch(function(r){return ws(t,r)})}function xf(){"serviceWorker"in navigator&&navigator.serviceWorker.ready.then(function(e){e.unregister()}).catch(function(e){return ws(emit,e)})}IS("/au-pos/sw.js",{ready(){},registered(){},cached(){},updatefound(){},updated(){},offline(){},error(){}});const MS="/au-pos/";async function $S({app:e,router:t,store:n},r){let s=!1;const o=u=>{try{return t.resolve(u).href}catch{}return Object(u)===u?null:u},i=u=>{if(s=!0,typeof u=="string"&&/^https?:\/\//.test(u)){window.location.href=u;return}const l=o(u);l!==null&&(window.location.href=l)},a=window.location.href.replace(window.location.origin,"");for(let u=0;s===!1&&u<r.length;u++)try{await r[u]({app:e,router:t,store:n,ssrContext:null,redirect:i,urlPath:a,publicPath:MS})}catch(l){if(l&&l.url){i(l.url);return}console.error("[Quasar] boot error:",l);return}s!==!0&&(e.use(t),e.mount("#q-app"))}DS(Fo,NS).then(e=>{const[t,n]=Promise.allSettled!==void 0?["allSettled",r=>r.map(s=>{if(s.status==="rejected"){console.error("[Quasar] boot error:",s.reason);return}return s.value.default})]:["all",r=>r.map(s=>s.default)];return Promise[t]([Fe(()=>Promise.resolve().then(function(){return OS}),void 0),Fe(()=>Promise.resolve().then(function(){return PS}),void 0),Fe(()=>import("./global-components.da36bbd5.js"),["assets/global-components.da36bbd5.js","assets/global-components.9917226e.css","assets/plugin-vue_export-helper.21dcd24c.js","assets/QItemSection.7dc1f54f.js","assets/QItemLabel.3b58be08.js","assets/TablePagination.215cbc35.js","assets/format.054b8074.js","assets/QSpace.bd91c020.js","assets/_commonjsHelpers.8402d862.js","assets/QForm.534886fc.js","assets/QList.f0282e16.js","assets/QMenu.abf49c1b.js","assets/selection.787abe58.js","assets/QTooltip.7e86cfcb.js","assets/ClosePopup.f68b6158.js"])]).then(r=>{const s=n(r).filter(o=>typeof o=="function");$S(e,s)})});export{Sn as $,Tm as A,Zo as B,ud as C,y1 as D,Ze as E,He as F,tr as G,mb as H,nr as I,q as J,Ee as K,fc as L,jh as M,vs as N,_1 as O,cb as P,CC as Q,St as R,m1 as S,Ft as T,ii as U,vd as V,qh as W,rt as X,Mu as Y,eo as Z,Lv as _,Dw as a,Io as a$,Ev as a0,xt as a1,Cv as a2,pt as a3,fo as a4,Xe as a5,V1 as a6,TE as a7,OE as a8,B1 as a9,Jn as aA,Ir as aB,_s as aC,xm as aD,Om as aE,jy as aF,eu as aG,QE as aH,kC as aI,AE as aJ,p1 as aK,qE as aL,Nb as aM,FC as aN,s1 as aO,o1 as aP,Do as aQ,i1 as aR,Uc as aS,Cn as aT,f1 as aU,Ta as aV,$u as aW,q1 as aX,Xt as aY,Lm as aZ,er as a_,kE as aa,wE as ab,SE as ac,RE as ad,xE as ae,IE as af,XE as ag,yv as ah,Mc as ai,Bc as aj,M1 as ak,xo as al,KE as am,DE as an,rb as ao,vv as ap,L1 as aq,I1 as ar,Sa as as,vt as at,c1 as au,a1 as av,Pn as aw,Un as ax,Es as ay,u1 as az,De as b,jS as b$,h1 as b0,ob as b1,l1 as b2,vh as b3,Ii as b4,ja as b5,g1 as b6,lw as b7,gh as b8,U1 as b9,Bu as bA,y_ as bB,Qf as bC,tC as bD,L_ as bE,fu as bF,Zn as bG,cw as bH,Zy as bI,Nf as bJ,go as bK,zS as bL,GS as bM,sg as bN,qS as bO,iu as bP,Lf as bQ,YS as bR,cu as bS,rn as bT,Xn as bU,Le as bV,wt as bW,Lp as bX,cg as bY,ed as bZ,Jf as b_,$1 as ba,Pg as bb,Ps as bc,Fs as bd,d1 as be,r1 as bf,TC as bg,Jr as bh,uC as bi,lC as bj,fC as bk,cC as bl,Wy as bm,pC as bn,Wa as bo,pu as bp,gu as bq,EC as br,Vh as bs,wa as bt,eb as bu,tb as bv,e1 as bw,Nv as bx,hp as by,OC as bz,$ as c,ds as c$,HS as c0,me as c1,XS as c2,og as c3,KS as c4,WS as c5,ot as c6,Yo as c7,VS as c8,Jo as c9,bw as cA,Hw as cB,hu as cC,v_ as cD,Lr as cE,Kg as cF,sw as cG,aw as cH,iw as cI,ow as cJ,$w as cK,w_ as cL,Mw as cM,On as cN,ww as cO,Tw as cP,Lg as cQ,Ng as cR,Dg as cS,Fg as cT,ew as cU,ZS as cV,bo as cW,Bw as cX,mw as cY,fw as cZ,Kw as c_,Js as ca,bg as cb,md as cc,je as cd,zw as ce,JS as cf,qw as cg,wr as ch,Fw as ci,vg as cj,Tn as ck,QS as cl,Mt as cm,ks as cn,an as co,Xw as cp,e_ as cq,kw as cr,Zg as cs,Lw as ct,uw as cu,_w as cv,vw as cw,Ew as cx,yw as cy,gw as cz,ln as d,hl as d0,jw as d1,xn as d2,n_ as d3,Ww as d4,pw as d5,Nw as d6,Sw as d7,nw as d8,Pw as d9,Qw as dA,ih as dB,av as dC,ah as dD,iv as dE,Ca as dF,pb as dG,v1 as dH,nC as dI,r_ as da,Aw as db,rw as dc,hd as dd,T_ as de,Vw as df,Ow as dg,Rw as dh,s_ as di,xw as dj,Cw as dk,Uw as dl,tw as dm,ev as dn,Su as dp,Fo as dq,hv as dr,G_ as ds,Yw as dt,t1 as du,n1 as dv,dv as dw,Jw as dx,Gw as dy,Q_ as dz,hw as e,ad as f,Zw as g,un as h,jd as i,dw as j,wo as k,Iw as l,Fa as m,Bg as n,gs as o,Wd as p,sn as q,ge as r,Vt as s,Np as t,Wu as u,cr as v,Ae as w,GE as x,Xr as y,YE as z};
