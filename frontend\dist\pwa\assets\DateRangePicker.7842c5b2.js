import{d as j,u as I,r as h,c as m,w as N,o as s,a as w,k as f,f as n,b as c,i as C,m as y,q as D,l as i,C as B,h as g,B as k,Q as V,t as A}from"./index.9477d5a3.js";import{Q as T}from"./QDate.10e993fc.js";import{Q as R}from"./QPopupProxy.1fd4617d.js";import{C as F}from"./ClosePopup.f68b6158.js";const $={class:"date-range-picker row items-center"},z={class:"row items-center justify-end q-gutter-sm"},G={class:"row items-center justify-end q-gutter-sm"},X=j({__name:"DateRangePicker",props:{modelValue:{type:Object,default:()=>({from:"",to:""})},dateMask:{type:String,default:"YYYY/MM/DD"},fromLabel:{type:String,default:""},toLabel:{type:String,default:""},fromRules:{type:Array,default:()=>["date"]},toRules:{type:Array,default:()=>["date"]},autoClose:{type:Boolean,default:!0},showTodayBtn:{type:Boolean,default:!0},showClearButton:{type:Boolean,default:!0},showCloseButton:{type:Boolean,default:!0},showFromDate:{type:Boolean,default:!0},showToDate:{type:Boolean,default:!0},showSeparator:{type:Boolean,default:!0},separatorText:{type:String,default:"\uFF5E"},separatorClass:{type:String,default:"q-mx-md"},inputClass:{type:String,default:""},disable:{type:Boolean,default:!1},customFromDateOptions:{type:Function,default:null},customToDateOptions:{type:Function,default:null},enableDateRangeValidation:{type:Boolean,default:!0},allowEmptyDate:{type:Boolean,default:!0}},emits:["update:modelValue","from-date-change","to-date-change","date-clear"],setup(a,{emit:P}){const{t:u}=I(),l=a,d=P,t=h({from:l.modelValue.from||"",to:l.modelValue.to||""}),v=h(),b=h(),O=m(()=>l.fromLabel||u("datePicker.from")),S=m(()=>l.toLabel||u("datePicker.to")),Q=m(()=>e=>{if(l.customFromDateOptions)return l.customFromDateOptions(e,t.value);if(!l.enableDateRangeValidation||!t.value.to)return!0;const o=new Date(e),r=new Date(t.value.to);return o<=r}),x=m(()=>e=>{if(l.customToDateOptions)return l.customToDateOptions(e,t.value);if(!l.enableDateRangeValidation||!t.value.from)return!0;const o=new Date(e),r=new Date(t.value.from);return o>=r}),L=m(()=>l.allowEmptyDate?l.fromRules.map(e=>e==="date"?o=>!o||o===""||/^\d{4}\/\d{1,2}\/\d{1,2}$/.test(o)||u("error.invalidDate"):e):l.fromRules),M=m(()=>l.allowEmptyDate?l.toRules.map(e=>e==="date"?o=>!o||o===""||/^\d{4}\/\d{1,2}\/\d{1,2}$/.test(o)||u("error.invalidDate"):e):l.toRules),U=()=>{d("from-date-change",t.value.from),p(),l.autoClose&&setTimeout(()=>{var e;(e=v.value)==null||e.hide()},50)},E=()=>{d("to-date-change",t.value.to),p(),l.autoClose&&setTimeout(()=>{var e;(e=b.value)==null||e.hide()},50)},q=()=>{var e;t.value.from="",d("from-date-change",""),d("date-clear","from"),p(),(e=v.value)==null||e.hide()},Y=()=>{var e;t.value.to="",d("to-date-change",""),d("date-clear","to"),p(),(e=b.value)==null||e.hide()},p=()=>{d("update:modelValue",{from:t.value.from,to:t.value.to})};return N(()=>l.modelValue,e=>{(e.from!==t.value.from||e.to!==t.value.to)&&(t.value={from:e.from||"",to:e.to||""})},{deep:!0}),(e,o)=>(s(),w("div",$,[a.showFromDate?(s(),f(V,{key:0,modelValue:t.value.from,"onUpdate:modelValue":o[1]||(o[1]=r=>t.value.from=r),label:O.value,mask:"date",rules:L.value,dense:"",readonly:"",disable:a.disable,class:k(a.inputClass)},{prepend:n(()=>[c(g,{name:"event",class:"cursor-pointer"},{default:n(()=>[c(R,{ref_key:"fromDatePopup",ref:v,cover:"","transition-show":"scale","transition-hide":"scale"},{default:n(()=>[c(T,{modelValue:t.value.from,"onUpdate:modelValue":[o[0]||(o[0]=r=>t.value.from=r),U],options:Q.value,mask:a.dateMask,"today-btn":a.showTodayBtn,disable:a.disable},{default:n(()=>[C("div",z,[a.showClearButton?(s(),f(y,{key:0,label:D(u)("clear"),color:"negative",flat:"",onClick:q},null,8,["label"])):i("",!0),a.showCloseButton?B((s(),f(y,{key:1,label:D(u)("close"),color:"primary",flat:""},null,8,["label"])),[[F]]):i("",!0)])]),_:1},8,["modelValue","options","mask","today-btn","disable"])]),_:1},512)]),_:1})]),_:1},8,["modelValue","label","rules","disable","class"])):i("",!0),a.showSeparator?(s(),w("span",{key:1,class:k(a.separatorClass)},A(a.separatorText),3)):i("",!0),a.showToDate?(s(),f(V,{key:2,modelValue:t.value.to,"onUpdate:modelValue":o[3]||(o[3]=r=>t.value.to=r),label:S.value,mask:"date",rules:M.value,dense:"",readonly:"",disable:a.disable,class:k(a.inputClass)},{prepend:n(()=>[c(g,{name:"event",class:"cursor-pointer"},{default:n(()=>[c(R,{ref_key:"toDatePopup",ref:b,cover:"","transition-show":"scale","transition-hide":"scale"},{default:n(()=>[c(T,{modelValue:t.value.to,"onUpdate:modelValue":[o[2]||(o[2]=r=>t.value.to=r),E],options:x.value,mask:a.dateMask,"today-btn":a.showTodayBtn,disable:a.disable},{default:n(()=>[C("div",G,[a.showClearButton?(s(),f(y,{key:0,label:D(u)("clear"),color:"negative",flat:"",onClick:Y},null,8,["label"])):i("",!0),a.showCloseButton?B((s(),f(y,{key:1,label:D(u)("close"),color:"primary",flat:""},null,8,["label"])),[[F]]):i("",!0)])]),_:1},8,["modelValue","options","mask","today-btn","disable"])]),_:1},512)]),_:1})]),_:1},8,["modelValue","label","rules","disable","class"])):i("",!0)]))}});export{X as _};
