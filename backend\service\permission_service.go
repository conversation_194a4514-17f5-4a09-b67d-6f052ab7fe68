package service

import (
	"context"
	"cx/domain"
	"cx/repository"

	"gorm.io/gorm"
)

type PermissionService interface {
	// Only fetch active permissions
	FetchPermissions(ctx context.Context, filter *domain.PermissionFilter) []domain.Permission

	UpdateUserGroupPermissions(ctx context.Context, payload *domain.GroupPermissionUpdatePayload) error
}

type permissionService struct {
	db             *gorm.DB
	permissionRepo repository.PermissionRepository
}

func NewPermissionService(db *gorm.DB, permissionRepo repository.PermissionRepository) PermissionService {
	return &permissionService{db, permissionRepo}
}

func (s *permissionService) FetchPermissions(ctx context.Context, filter *domain.PermissionFilter) []domain.Permission {
	return s.permissionRepo.Fetch(ctx, filter)
}

func (s *permissionService) UpdateUserGroupPermissions(ctx context.Context, payload *domain.GroupPermissionUpdatePayload) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		permissionRepo := repository.NewPermissionRepository(tx)

		// Delete all permissions of the group
		if err := permissionRepo.DeleteGroupPermission(ctx, payload.GroupID); err != nil {
			return err
		}

		userRepo := repository.NewUserRepository(tx)
		user, err := userRepo.GetByUUID(ctx, payload.UpdatedBy)
		if user.ID == 0 {
			return err
		}

		// Create new permissions
		var permissions []domain.GroupPermission
		for _, code := range payload.PermissionCodes {
			permission := domain.GroupPermission{
				GroupID:      payload.GroupID,
				PermissionID: permissionRepo.GetByCode(ctx, code).ID,
				CreatedBy:    user.ID,
			}

			if permission.PermissionID == 0 {
				continue
			}
			permissions = append(permissions, permission)
		}

		return permissionRepo.BatchCreateGroupPermission(ctx, permissions)
	})
}
