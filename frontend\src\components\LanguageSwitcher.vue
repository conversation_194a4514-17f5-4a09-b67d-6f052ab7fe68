<template>
  <q-btn 
    flat 
    :icon="icon" 
    :size="size" 
    :class="btnClass"
    :label="showLabel ? currentLanguageLabel : undefined"
  >
    <q-menu :anchor="menuAnchor">
      <q-list style="min-width: 120px">
        <q-item 
          v-for="lang in languages" 
          :key="lang.code"
          clickable 
          v-close-popup 
          @click="changeLanguage(lang.code)"
        >
          <q-item-section>
            <q-item-label>{{ lang.label }}</q-item-label>
          </q-item-section>
          <q-item-section avatar v-if="locale === lang.code">
            <q-icon name="check" color="primary" />
          </q-item-section>
        </q-item>
      </q-list>
    </q-menu>
    
    <q-tooltip v-if="showTooltip">
      {{ $t('changeLanguage') }}
    </q-tooltip>
  </q-btn>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';

interface Language {
  code: string;
  label: string;
  flag?: string;
}

interface Props {
  icon?: string;
  size?: string;
  btnClass?: string;
  menuAnchor?: string;
  showLabel?: boolean;
  showTooltip?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  icon: 'language',
  size: 'sm',
  btnClass: 'q-pa-md',
  menuAnchor: 'bottom left',
  showLabel: false,
  showTooltip: true,
});

const { t, locale } = useI18n();

const languages: Language[] = [
  { code: 'en-US', label: 'English' },
  { code: 'zh-TW', label: '中文' },
];

const currentLanguageLabel = computed(() => {
  const current = languages.find(lang => lang.code === locale.value);
  return current?.label || 'Language';
});

const changeLanguage = (langCode: string) => {
  locale.value = langCode;
  localStorage.setItem('user-locale', langCode);
  
  // 可選：發送事件通知父組件
  emit('language-changed', langCode);
};

const emit = defineEmits<{
  'language-changed': [langCode: string];
}>();

// 在組件掛載時恢復用戶選擇的語言
onMounted(() => {
  const userLocale = localStorage.getItem('user-locale');
  if (userLocale && languages.some(lang => lang.code === userLocale)) {
    locale.value = userLocale;
  }
});
</script>

<style scoped>
/* 可以添加自定義樣式 */
</style>
