import{Q as c,a as N}from"./QToolbar.3910277c.js";import{d as O,u as S,r as u,c as _,w,s as j,n as H,o as R,k as z,f as l,b as o,y as E,p as G,t as L,q as m,Q,h as P,i as v,C as V,m as k,z as J}from"./index.b4716878.js";import{Q as M}from"./QDate.39a8a27c.js";import{Q as T}from"./QPopupProxy.7c048fcd.js";import{Q as K}from"./QSelect.c42fe000.js";import{Q as W}from"./QTable.21f73a64.js";import{Q as X}from"./QPage.cc993543.js";import{C as U}from"./ClosePopup.050f93d1.js";import{A as Z}from"./attendance.a0ddcb07.js";import{f as D}from"./date.6d29930c.js";import{U as $}from"./user.8e7275ff.js";import"./format.054b8074.js";import"./QMenu.45353d3c.js";import"./selection.7371c306.js";import"./QItemSection.a2ef2d56.js";import"./QItemLabel.88fb9012.js";import"./QList.67451ef5.js";import"./use-fullscreen.12714a56.js";const ee={class:"row items-center justify-end q-gutter-sm"},te={class:"row items-center justify-end q-gutter-sm"},_e=O({__name:"AttendancePage",setup(ae){const{t:s}=S(),g=u(),b=u(),C=()=>{setTimeout(()=>{g.value.hide()},50)},x=()=>{setTimeout(()=>{b.value.hide()},50)},A=_(()=>[{name:"name",label:s("name"),field:t=>{var e;return(e=t.user)==null?void 0:e.name},align:"center"},{name:"type",label:s("clockType"),field:t=>s(t.type),align:"center"},{name:"clock_time",label:s("clockTime"),field:t=>D(t.clock_time,"YYYY/MM/DD HH:mm"),align:"center"}]),a=u({from:"",to:""}),p=u(""),q=_(()=>[{label:s("user.all"),value:""},...Y.value]),B=t=>{if(!a.value.to)return!0;const e=new Date(t),r=new Date(a.value.to);return e<=r},F=t=>{if(!a.value.from)return!0;const e=new Date(t),r=new Date(a.value.from);return e>=r};w(()=>a.value.from,t=>{if(t&&a.value.to){const e=new Date(t),r=new Date(a.value.to);e>r&&(a.value.to=t)}}),w(()=>a.value.to,t=>{if(t&&a.value.from){const e=new Date(a.value.from);new Date(t)<e&&(a.value.from=t)}});const h=()=>{const t=new Date,e=new Date(t.getFullYear(),t.getMonth(),1),r=new Date(t.getFullYear(),t.getMonth()+1,0);a.value={from:D(e,"YYYY/MM/DD"),to:D(r,"YYYY/MM/DD")}},y=u([]),i=u({sortBy:"clock_time",descending:!0,page:1,rowsPerPage:20,rowsNumber:0}),d=u(!1),f=async()=>{try{d.value=!0;const t=await Z.fetch({filter:{user_uuid:p.value,start_date:a.value.from,end_date:a.value.to},pagination:i.value});y.value=t.result.data,i.value=t.result.pagination}finally{d.value=!1}},Y=u([]),I=async()=>{try{d.value=!0;const t=await $.fetch();Y.value=t.result.map(e=>({label:e.name,value:e.uuid}))}finally{d.value=!1}};return j(()=>{h(),f(),I()}),(t,e)=>{const r=H("TablePagination");return R(),z(X,null,{default:l(()=>[o(J,{flat:"",square:"",class:"bg-cream"},{default:l(()=>[o(E,null,{default:l(()=>[o(W,{"virtual-scroll":"",rows:y.value,columns:A.value,pagination:i.value,"onUpdate:pagination":e[5]||(e[5]=n=>i.value=n),"hide-pagination":"","binary-state-sort":"","table-header-class":"bg-grey-3",loading:d.value},{top:l(()=>[o(c,null,{default:l(()=>[o(N,null,{default:l(()=>[G(L(m(s)("attendance.history")),1)]),_:1})]),_:1}),o(c,null,{default:l(()=>[o(Q,{modelValue:a.value.from,"onUpdate:modelValue":e[1]||(e[1]=n=>a.value.from=n),label:m(s)("datePicker.from"),mask:"date",rules:["date"],readonly:""},{prepend:l(()=>[o(P,{name:"event",class:"cursor-pointer"},{default:l(()=>[o(T,{ref_key:"fromDatePopup",ref:g,cover:"","transition-show":"scale","transition-hide":"scale"},{default:l(()=>[o(M,{modelValue:a.value.from,"onUpdate:modelValue":[e[0]||(e[0]=n=>a.value.from=n),C],options:B,mask:"YYYY/MM/DD","today-btn":""},{default:l(()=>[v("div",ee,[V(o(k,{label:m(s)("close"),color:"primary",flat:""},null,8,["label"]),[[U]])])]),_:1},8,["modelValue"])]),_:1},512)]),_:1})]),_:1},8,["modelValue","label"]),e[7]||(e[7]=v("span",{class:"q-mx-md"},"\uFF5E",-1)),o(Q,{modelValue:a.value.to,"onUpdate:modelValue":e[3]||(e[3]=n=>a.value.to=n),label:m(s)("datePicker.to"),mask:"date",rules:["date"],readonly:""},{prepend:l(()=>[o(P,{name:"event",class:"cursor-pointer"},{default:l(()=>[o(T,{ref_key:"toDatePopup",ref:b,cover:"","transition-show":"scale","transition-hide":"scale"},{default:l(()=>[o(M,{modelValue:a.value.to,"onUpdate:modelValue":[e[2]||(e[2]=n=>a.value.to=n),x],options:F,mask:"YYYY/MM/DD","today-btn":""},{default:l(()=>[v("div",te,[V(o(k,{label:m(s)("close"),color:"primary",flat:""},null,8,["label"]),[[U]])])]),_:1},8,["modelValue"])]),_:1},512)]),_:1})]),_:1},8,["modelValue","label"])]),_:1}),o(c,null,{default:l(()=>[o(K,{modelValue:p.value,"onUpdate:modelValue":[e[4]||(e[4]=n=>p.value=n),f],options:q.value,"emit-value":"","map-options":"",label:m(s)("user.label")},null,8,["modelValue","options","label"])]),_:1})]),_:1},8,["rows","columns","pagination","loading"]),o(r,{modelValue:i.value,"onUpdate:modelValue":e[6]||(e[6]=n=>i.value=n),onGetData:f},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})}}});export{_e as default};
