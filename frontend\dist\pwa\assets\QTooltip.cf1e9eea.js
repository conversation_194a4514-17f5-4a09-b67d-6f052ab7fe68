import{E as te,a7 as ae,a8 as T,a9 as oe,aa as ne,K as ie,r as E,c as f,ab as le,ac as se,ad as re,ae as ue,af as ce,w as k,a1 as C,a2 as x,a0 as H,ak as de,J as A,al as fe,T as he,R as ve}from"./index.b4716878.js";import{u as me,v as q,a as ge,b as Te,c as ye,r as D,s as pe,p as M,d as be}from"./QMenu.45353d3c.js";import{c as j}from"./selection.7371c306.js";var Oe=te({name:"QTooltip",inheritAttrs:!1,props:{...me,...ae,...T,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null},transitionShow:{...T.transitionShow,default:"jump-down"},transitionHide:{...T.transitionHide,default:"jump-up"},anchor:{type:String,default:"bottom middle",validator:q},self:{type:String,default:"top middle",validator:q},offset:{type:Array,default:()=>[14,14],validator:ge},scrollTarget:oe,delay:{type:Number,default:0},hideDelay:{type:Number,default:0},persistent:Boolean},emits:[...ne],setup(e,{slots:L,emit:y,attrs:h}){let i,l;const v=ie(),{proxy:{$q:a}}=v,s=E(null),c=E(!1),Q=f(()=>M(e.anchor,a.lang.rtl)),R=f(()=>M(e.self,a.lang.rtl)),W=f(()=>e.persistent!==!0),{registerTick:B,removeTick:N}=le(),{registerTimeout:d}=se(),{transitionProps:_,transitionStyle:I}=re(e),{localScrollTarget:p,changeScrollEvent:J,unconfigureScrollTarget:K}=Te(e,w),{anchorEl:o,canShow:U,anchorEvents:r}=ye({showing:c,configureAnchorEl:Y}),{show:V,hide:m}=ue({showing:c,canShow:U,handleShow:z,handleHide:F,hideOnRouteChange:W,processOnMount:!0});Object.assign(r,{delayShow:G,delayHide:X});const{showPortal:b,hidePortal:S,renderPortal:$}=ce(v,s,ee,"tooltip");if(a.platform.is.mobile===!0){const t={anchorEl:o,innerRef:s,onClickOutside(n){return m(n),n.target.classList.contains("q-dialog__backdrop")&&ve(n),!0}},g=f(()=>e.modelValue===null&&e.persistent!==!0&&c.value===!0);k(g,n=>{(n===!0?be:D)(t)}),C(()=>{D(t)})}function z(t){b(),B(()=>{l=new MutationObserver(()=>u()),l.observe(s.value,{attributes:!1,childList:!0,characterData:!0,subtree:!0}),u(),w()}),i===void 0&&(i=k(()=>a.screen.width+"|"+a.screen.height+"|"+e.self+"|"+e.anchor+"|"+a.lang.rtl,u)),d(()=>{b(!0),y("show",t)},e.transitionDuration)}function F(t){N(),S(),P(),d(()=>{S(!0),y("hide",t)},e.transitionDuration)}function P(){l!==void 0&&(l.disconnect(),l=void 0),i!==void 0&&(i(),i=void 0),K(),x(r,"tooltipTemp")}function u(){pe({targetEl:s.value,offset:e.offset,anchorEl:o.value,anchorOrigin:Q.value,selfOrigin:R.value,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function G(t){if(a.platform.is.mobile===!0){j(),document.body.classList.add("non-selectable");const g=o.value,n=["touchmove","touchcancel","touchend","click"].map(O=>[g,O,"delayHide","passiveCapture"]);H(r,"tooltipTemp",n)}d(()=>{V(t)},e.delay)}function X(t){a.platform.is.mobile===!0&&(x(r,"tooltipTemp"),j(),setTimeout(()=>{document.body.classList.remove("non-selectable")},10)),d(()=>{m(t)},e.hideDelay)}function Y(){if(e.noParentEvent===!0||o.value===null)return;const t=a.platform.is.mobile===!0?[[o.value,"touchstart","delayShow","passive"]]:[[o.value,"mouseenter","delayShow","passive"],[o.value,"mouseleave","delayHide","passive"]];H(r,"anchor",t)}function w(){if(o.value!==null||e.scrollTarget!==void 0){p.value=de(o.value,e.scrollTarget);const t=e.noParentEvent===!0?u:m;J(p.value,t)}}function Z(){return c.value===!0?A("div",{...h,ref:s,class:["q-tooltip q-tooltip--style q-position-engine no-pointer-events",h.class],style:[h.style,I.value],role:"tooltip"},he(L.default)):null}function ee(){return A(fe,_.value,Z)}return C(P),Object.assign(v.proxy,{updatePosition:u}),$}});export{Oe as Q};
