package seeder

import "gorm.io/gorm"

type Seeder interface {
	Seed(*gorm.DB) error
}

type Manager struct {
	db      *gorm.DB
	seeders []Seeder
}

func NewManager(db *gorm.DB) *Manager {
	return &Manager{
		db:      db,
		seeders: make([]Seeder, 0),
	}
}

func (m *Manager) Register(seeders ...Seeder) {
	m.seeders = append(m.seeders, seeders...)
}

func (m *Manager) Execute() error {
	for _, seeder := range m.seeders {
		if err := seeder.Seed(m.db); err != nil {
			return err
		}
	}
	return nil
}
