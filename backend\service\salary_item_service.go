package service

import (
	"context"
	"cx/domain"
	"cx/repository"

	"gorm.io/gorm"
)

type SalaryItemService interface {
	ListSalaryItems(ctx context.Context) ([]domain.SalaryItem, error)
	CreateSalaryItem(ctx context.Context, payload *domain.SalaryItemCreatePayload) error
}

type salaryItemService struct {
	db             *gorm.DB
	salaryItemRepo repository.SalaryItemRepository
}

func NewSalaryItemService(db *gorm.DB, salaryItemRepo repository.SalaryItemRepository) SalaryItemService {
	return &salaryItemService{db, salaryItemRepo}
}

func (s *salaryItemService) ListSalaryItems(ctx context.Context) ([]domain.SalaryItem, error) {
	return s.salaryItemRepo.List(ctx)
}

func (s *salaryItemService) CreateSalaryItem(ctx context.Context, payload *domain.SalaryItemCreatePayload) error {
	return s.salaryItemRepo.Create(ctx, payload)
}
