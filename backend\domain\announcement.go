package domain

import "time"

type Announcement struct {
	ID        int64     `json:"-"`
	Content   string    `form:"content" json:"content"`
	CreatedAt time.Time `gorm:"<-:create" json:"-"`
	UpdatedAt time.Time `json:"-"`
}

type AnnouncementUpdatePayload struct {
	ID      int64
	Content string `form:"content" json:"content"`
}

func (AnnouncementUpdatePayload) TableName() string {
	return "announcements"
}
