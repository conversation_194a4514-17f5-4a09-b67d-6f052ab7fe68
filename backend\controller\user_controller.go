package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type UserController struct {
	userService service.UserService
}

func NewUserController(r *gin.RouterGroup, userService service.UserService) {
	userController := UserController{userService}

	v1 := r.Group("/v1/users")
	{
		v1.GET("", userController.FetchHandler)
		v1.GET("/:uuid", userController.GetByUUIDHandler)
		v1.POST("", userController.CreateHandler)
		v1.PUT("/:uuid", userController.UpdateHandler)
		v1.DELETE("/:uuid", userController.DeleteHandler)
	}
}

func (ctr *UserController) FetchHandler(c *gin.Context) {
	users, err := ctr.userService.Fetch(c)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to fetch users")
		return
	}

	utils.HandleSuccess(c, users)
}

func (ctr *UserController) GetByUUIDHandler(c *gin.Context) {
	uuid := c.Param("uuid")

	user, err := ctr.userService.GetByUUID(c, uuid)
	if err != nil {
		utils.HandleError(c, http.StatusNotFound, err, "User not found")
		return
	}

	utils.HandleSuccess(c, user)
}

func (ctr *UserController) CreateHandler(c *gin.Context) {
	var user domain.UserCreatePayload
	if err := c.ShouldBind(&user); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	if user.Username == "" || user.Password == "" {
		utils.HandleError(c, http.StatusBadRequest, nil, "Username and password are required")
		return
	}

	err := ctr.userService.Create(c, &user)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, err.Error())
		return
	}

	utils.HandleSuccess(c, gin.H{
		"uuid": user.UUID,
	})
}

func (ctr *UserController) UpdateHandler(c *gin.Context) {
	uuid := c.Param("uuid")

	var user domain.UserUpdatePayload
	if err := c.ShouldBind(&user); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	err := ctr.userService.Update(c, uuid, &user)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update user")
		return
	}

	utils.HandleSuccess(c, user)
}

func (ctr *UserController) DeleteHandler(c *gin.Context) {
	uuid := c.Param("uuid")

	err := ctr.userService.Delete(c, uuid)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to delete user")
		return
	}

	utils.HandleSuccess(c, nil)
}
