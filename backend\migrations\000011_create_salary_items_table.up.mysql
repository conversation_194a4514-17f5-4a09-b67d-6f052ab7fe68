CREATE TABLE IF NOT EXISTS `salary_items` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL,
    `type` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '1: Income, 2: Deduction, 3: Bonus, 4: Excluded',
    `is_default` BOOLEAN NOT NULL DEFAULT FALSE,
    `is_required` BOOLEAN NOT NULL DEFAULT FALSE,
    `default_value` DECIMAL(10, 2) NOT NULL DEFAULT 0,
    `apply_to_salary_type` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '0: Both, 1: Monthly, 2: Hourly',
    `sort_order` INT NOT NULL DEFAULT 0,
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` D<PERSON>ETIME NULL DEFAULT NULL,
    <PERSON>IQ<PERSON> KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='薪資項目定義表';

-- sort_order insert trigger
CREATE TRIGGER IF NOT EXISTS `set_sort_order_before_insert`
BEFORE INSERT ON `salary_items`
FOR EACH ROW
BEGIN
    DECLARE `max_sort_order` INT;

    -- 查詢該 type 的最大 sort_order，若無記錄則返回 0
    SELECT COALESCE(MAX(`sort_order`), 0) INTO `max_sort_order`
    FROM `salary_items`
    WHERE `type` = NEW.`type`;

    -- 設置新記錄的 sort_order 為最大值加 1
    SET NEW.`sort_order` = `max_sort_order` + 1;
END;

-- 薪資項目初始資料
INSERT INTO `salary_items` 
(`name`, `type`, `is_default`, `is_required`, `default_value`, `apply_to_salary_type`) VALUES 
-- 收入
('base_salary', 1, TRUE, TRUE, 0, 1), -- 基本薪資
('hourly_wage', 1, TRUE, TRUE, 0, 2), -- 時薪
('overtime_pay', 1, TRUE, FALSE, 0, 0), -- 加班費
-- Bonus
('meal_allowance', 3, TRUE, FALSE, 0, 0), -- 伙食津貼
('transportation_allowance', 3, TRUE, FALSE, 0, 0), -- 交通津貼
('position_allowance', 3, TRUE, FALSE, 0, 0), -- 職務津貼
('performance_bonus', 3, TRUE, FALSE, 0, 0), -- 績效獎金
('perfect_attendance_bonus', 3, TRUE, FALSE, 0, 0), -- 全勤獎金
('year_end_bonus', 3, TRUE, FALSE, 0, 0), -- 年終獎金
-- 扣除項
('labor_insurance', 2, TRUE, TRUE, 0, 0), -- 勞保
('health_insurance', 2, TRUE, TRUE, 0, 0), -- 健保
('income_tax', 2, TRUE, TRUE, 0, 0), -- 所得稅
('labor_pension', 2, TRUE, TRUE, 0, 0), -- 勞退
('health_pension', 2, TRUE, TRUE, 0, 0), -- 健保
('late_deduction', 2, TRUE, FALSE, 0, 0), -- 遲到扣款
('absence_deduction', 2, TRUE, FALSE, 0, 0); -- 缺勤扣款