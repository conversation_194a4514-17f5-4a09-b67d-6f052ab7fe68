package service

import (
	"context"
	"cx/domain"
	"cx/repository"
	"cx/utils"
	"errors"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type OrderService interface {
	Create(ctx context.Context, order *domain.Order) error
	Update(ctx context.Context, uuid string, order *domain.Order) error
	UpdateNotes(ctx context.Context, uuid string, notes string, updatedByID int64) error
	Fetch(ctx context.Context, filter *domain.OrderFilter, pagination *domain.Pagination) ([]domain.Order, error)
	GetByID(ctx context.Context, id int64) (*domain.Order, error)
	GetByUUID(ctx context.Context, uuid string) (*domain.Order, error)
	VoidOrder(ctx context.Context, uuid string, payload *domain.VoidOrderPayload) error
	DeleteOrder(ctx context.Context, uuid string) error

	Checkout(ctx context.Context, uuid string, payload *domain.CheckoutPayload) error
}

type orderService struct {
	db            *gorm.DB
	wpDB          *gorm.DB
	orderRepo     repository.OrderRepository
	orderItemRepo repository.OrderItemRepository
	productRepo   repository.ProductRepository
	inventoryRepo repository.InventoryRepository
	customerRepo  repository.CustomerRepository
	xeroService   XeroService
}

func NewOrderService(
	db *gorm.DB,
	wpDB *gorm.DB,
	orderRepo repository.OrderRepository,
	orderItemRepo repository.OrderItemRepository,
	productRepo repository.ProductRepository,
	inventoryRepo repository.InventoryRepository,
	customerRepo repository.CustomerRepository,
	xeroService XeroService,
) OrderService {
	return &orderService{
		db,
		wpDB,
		orderRepo,
		orderItemRepo,
		productRepo,
		inventoryRepo,
		customerRepo,
		xeroService,
	}
}

func (s *orderService) Create(ctx context.Context, order *domain.Order) error {
	userRepo := repository.NewUserRepository(s.db)
	user, err := userRepo.GetByID(ctx, order.CreatedByID.Int64)
	if err != nil {
		return err
	}

	permissionRepo := repository.NewPermissionRepository(s.db)
	hasPermission, err := permissionRepo.HasPermission(ctx, domain.PermissionCreateOrder, user.GroupID.Int64)
	if err != nil {
		return err
	}
	if !hasPermission {
		return errors.New("user does not have permission to void order")
	}

	if order.OrderAt.IsZero() {
		order.OrderAt = time.Now()
	}

	orderNo, err := s.orderRepo.GenerateOrderNumber(ctx, order.OrderAt)
	if err != nil {
		return err
	}
	order.OrderNo = orderNo
	order.Status = domain.OrderStatusPending
	order.Source = domain.OrderSourcePOS

	return s.orderRepo.Create(ctx, order)
}

func (s *orderService) Update(ctx context.Context, uuid string, order *domain.Order) error {
	return s.orderRepo.Update(ctx, uuid, order)
}

func (s *orderService) UpdateNotes(ctx context.Context, uuid string, notes string, updatedByID int64) error {
	return s.orderRepo.UpdateNotes(ctx, uuid, notes, updatedByID)
}

func (s *orderService) Fetch(ctx context.Context, filter *domain.OrderFilter, pagination *domain.Pagination) ([]domain.Order, error) {
	return s.orderRepo.Fetch(ctx, filter, pagination)
}

func (s *orderService) GetByID(ctx context.Context, id int64) (*domain.Order, error) {
	return s.orderRepo.GetByID(ctx, id)
}

func (s *orderService) GetByUUID(ctx context.Context, uuid string) (*domain.Order, error) {
	return s.orderRepo.GetByUUID(ctx, uuid)
}

func (s *orderService) Checkout(ctx context.Context, uuid string, payload *domain.CheckoutPayload) error {
	userRepo := repository.NewUserRepository(s.db)
	user, err := userRepo.GetByID(ctx, payload.UpdatedByID.Int64)
	if err != nil {
		return err
	}

	permissionRepo := repository.NewPermissionRepository(s.db)
	hasPermission, err := permissionRepo.HasPermission(ctx, domain.PermissionCheckoutOrder, user.GroupID.Int64)
	if err != nil {
		return err
	}
	if !hasPermission {
		return errors.New("user does not have permission to void order")
	}

	customerID := null.Int{}

	// 檢查客戶UUID是否存在
	if payload.CustomerUUID != "" {
		if customer, _ := s.customerRepo.GetByUUID(ctx, payload.CustomerUUID); customer.ID > 0 {
			customerID = null.IntFrom(customer.ID)
		} else {
			return errors.New("customer not found")
		}
	}
	payload.CustomerID = customerID

	// 取得訂單資訊
	order, err := s.orderRepo.GetByUUID(ctx, uuid)
	if err != nil {
		return err
	}

	// 事務
	wpDB := s.wpDB.Begin()
	err = s.db.Transaction(func(tx *gorm.DB) error {
		orderRepo := repository.NewOrderRepository(tx)
		orderItemRepo := repository.NewOrderItemRepository(tx)
		productRepo := repository.NewProductRepository(tx)
		inventoryRepo := repository.NewInventoryRepository(tx, wpDB)

		orderID := null.Int{}
		if order.ID != 0 {
			orderID = null.IntFrom(order.ID)
		}

		// 更新訂單狀態
		if err := orderRepo.Checkout(ctx, uuid, payload); err != nil {
			return err
		}

		// 處理訂單項目
		for _, orderItem := range payload.OrderItems {
			// 取得商品資訊
			product, err := productRepo.GetByUUID(ctx, orderItem.Product.UUID)
			if err != nil {
				return err
			}

			// 彈性金額產品不更新庫存，因為它們通常是服務類產品
			if !product.IsFlexiblePrice {
				// 更新庫存
				err = inventoryRepo.UpdateProductStock(ctx, product.ID, -orderItem.Quantity)
				if err != nil {
					return err
				}

				// 創建庫存交易記錄
				inventoryTransaction := &domain.InventoryTransaction{
					UUID:            utils.GenerateUUID(),
					OrderID:         orderID,
					ProductID:       product.ID,
					TransactionType: domain.InventoryTransactionTypeSale,
					Quantity:        -orderItem.Quantity,
					BeforeQuantity:  product.StockQuantity,
					AfterQuantity:   product.StockQuantity - orderItem.Quantity,
					UnitPrice:       orderItem.Price,
					TotalAmount:     utils.RoundToDecimal(orderItem.Price*float64(orderItem.Quantity), 2),
					Notes:           "Order Checkout",
					CreatedByID:     payload.UpdatedByID.Int64,
					UpdatedByID:     payload.UpdatedByID.Int64,
				}

				err = inventoryRepo.CreateInventoryTransaction(ctx, inventoryTransaction)
				if err != nil {
					return err
				}
			}

			// 創建訂單項目
			orderItem.OrderUUID = uuid
			orderItem.ProductID = product.ID
			if err := orderItemRepo.Create(ctx, &orderItem); err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		wpDB.Rollback()
		return err
	}

	wpDB.Commit()

	// 同步到 Xero（非阻塞，錯誤不影響主流程）
	go func() {
		if s.xeroService != nil {
			_, err := s.xeroService.SyncOrderToXero(context.Background(), uuid)
			if err != nil {
				// 記錄錯誤但不影響主流程
				// 可以考慮添加日誌記錄
				// log.Printf("Failed to sync order %s to Xero: %v", uuid, err)
			}
		}
	}()

	return nil
}

func (s *orderService) VoidOrder(ctx context.Context, uuid string, payload *domain.VoidOrderPayload) error {
	userRepo := repository.NewUserRepository(s.db)
	user, err := userRepo.GetByID(ctx, payload.UpdatedByID)
	if err != nil {
		return err
	}

	permissionRepo := repository.NewPermissionRepository(s.db)
	hasPermission, err := permissionRepo.HasPermission(ctx, domain.PermissionVoidOrder, user.GroupID.Int64)
	if err != nil {
		return err
	}
	if !hasPermission {
		return errors.New("user does not have permission to void order")
	}

	if payload.Reason == "" || (payload.Reason == "other" && payload.OtherReason == "") {
		return errors.New("reason is required")
	}

	if payload.Reason == "other" {
		payload.Reason = payload.OtherReason
	}

	// 取得訂單
	order, err := s.orderRepo.GetByUUID(ctx, uuid)
	if err != nil {
		return err
	}

	wpDB := s.wpDB.Begin()
	err = s.db.Transaction(func(tx *gorm.DB) error {
		orderRepo := repository.NewOrderRepository(tx)
		productRepo := repository.NewProductRepository(tx)
		inventoryRepo := repository.NewInventoryRepository(tx, wpDB)

		// 作廢訂單
		err := orderRepo.VoidOrder(ctx, uuid, payload.Reason)
		if err != nil {
			tx.Rollback()
			return err
		}

		// 恢復庫存
		if payload.RestoreStock {
			for _, item := range order.OrderItems {
				// 取得商品資訊
				product, err := productRepo.GetByID(ctx, item.ProductID)
				if err != nil {
					return err
				}

				// 更新庫存
				err = inventoryRepo.UpdateProductStock(ctx, product.ID, item.Quantity)
				if err != nil {
					return err
				}

				orderID := null.Int{}
				if order.ID != 0 {
					orderID = null.IntFrom(order.ID)
				}

				// 創建庫存交易記錄
				transaction := &domain.InventoryTransaction{
					UUID:            utils.GenerateUUID(),
					OrderID:         orderID,
					ProductID:       product.ID,
					TransactionType: domain.InventoryTransactionTypeOrderVoid,
					Quantity:        item.Quantity,
					BeforeQuantity:  product.StockQuantity,
					AfterQuantity:   product.StockQuantity + item.Quantity,
					UnitPrice:       item.Price,
					TotalAmount:     utils.RoundToDecimal(item.Price*float64(item.Quantity), 2),
					Notes:           "Order Void",
					CreatedByID:     order.UpdatedByID.Int64,
					UpdatedByID:     order.UpdatedByID.Int64,
				}

				err = inventoryRepo.CreateInventoryTransaction(ctx, transaction)
				if err != nil {
					return err
				}
			}
		}

		return nil
	})
	if err != nil {
		wpDB.Rollback()
		return err
	}

	wpDB.Commit()

	// 同步作廢到 Xero（非阻塞，錯誤不影響主流程）
	go func() {
		if s.xeroService != nil {
			err := s.xeroService.VoidOrderInXero(context.Background(), uuid)
			if err != nil {
				// 記錄錯誤但不影響主流程
				// 可以考慮添加日誌記錄
				// log.Printf("Failed to void order %s in Xero: %v", uuid, err)
			}
		}
	}()

	return nil
}

// only delete pending order
func (s *orderService) DeleteOrder(ctx context.Context, uuid string) error {
	order, err := s.orderRepo.GetByUUID(ctx, uuid)
	if err != nil {
		return err
	}

	if order.Status != domain.OrderStatusPending {
		return errors.New("order is not pending")
	}

	return s.orderRepo.Delete(ctx, uuid)
}
