package main

import (
	"context"
	"cx/config"
	"cx/domain"
	"cx/routes"
	"cx/utils"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"gorm.io/gorm"
)

var DB *gorm.DB

func main() {
	conf, err := config.LoadConfig()
	if err != nil {
		panic(err)
	}

	// 初始化資料庫連線
	DB, err := config.ConnectDB(conf)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := config.CloseDB(DB); err != nil {
			utils.ErrorLog(domain.HttpResponse{
				Status:  http.StatusInternalServerError,
				Message: "failed to close database",
				Error:   err.Error(),
			})
		}
	}()

	// 初始化WP資料庫連線
	wpDB, err := config.ConnectWpDB(conf)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := config.CloseDB(wpDB); err != nil {
			utils.ErrorLog(domain.HttpResponse{
				Status:  http.StatusInternalServerError,
				Message: "failed to close database",
				Error:   err.Error(),
			})
		}
	}()

	// 初始化 HTTP 服務
	router := routes.SetupRouter(DB, wpDB)

	s := &http.Server{
		Addr:           ":" + conf.Server.ServerPort,
		Handler:        router,
		ReadTimeout:    30 * time.Second,
		WriteTimeout:   30 * time.Second,
		MaxHeaderBytes: 1 << 20, // 20 MB
	}

	go func() {
		fmt.Printf("Server started at http://localhost:%s\n", conf.Server.ServerPort)
		if err := s.ListenAndServe(); err != nil {
			utils.ErrorLog(domain.HttpResponse{
				Status:  http.StatusInternalServerError,
				Message: "failed to start server",
				Error:   err.Error(),
			})
			panic(err)
		}
	}()

	select {}

	// 等待終止訊號
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutdown Server ...")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := s.Shutdown(ctx); err != nil {
		log.Fatalf("Server Shutdown: %v", err)
	}

	log.Println("Server exiting")
}
