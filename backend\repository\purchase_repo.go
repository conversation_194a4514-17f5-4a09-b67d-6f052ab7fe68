// 商品進貨
package repository

import (
	"context"
	"cx/domain"
	"time"

	"gorm.io/gorm"
)

type PurchaseOrderRepository interface {
	CreatePurchaseOrder(ctx context.Context, order *domain.CreatePurchaseOrderRequest) error
	CreateStockInItem(ctx context.Context, item *domain.PurchaseOrderItem) error
	GetPurchaseOrderByID(ctx context.Context, id int64) (*domain.PurchaseOrder, error)
	GetPurchaseOrderByUUID(ctx context.Context, uuid string) (*domain.PurchaseOrder, error)
	UpdatePurchaseOrder(ctx context.Context, order *domain.PurchaseOrder) error
	ListPurchaseOrders(ctx context.Context, pagination *domain.Pagination) ([]domain.PurchaseOrder, error)
	ReceivePurchaseOrder(ctx context.Context, orderID int64, items []domain.PurchaseOrderItem) error
}

type purchaseOrderRepository struct {
	db *gorm.DB
}

func NewPurchaseOrderRepository(db *gorm.DB) PurchaseOrderRepository {
	return &purchaseOrderRepository{
		db: db,
	}
}

func (r *purchaseOrderRepository) CreatePurchaseOrder(ctx context.Context, purchaseOrder *domain.CreatePurchaseOrderRequest) error {
	tx := r.db.WithContext(ctx)

	if err := tx.Create(purchaseOrder).Error; err != nil {
		return err
	}

	return nil
}

func (r *purchaseOrderRepository) CreateStockInItem(ctx context.Context, item *domain.PurchaseOrderItem) error {
	tx := r.db.WithContext(ctx)

	if err := tx.Create(item).Error; err != nil {
		return err
	}

	return nil
}

func (r *purchaseOrderRepository) GetPurchaseOrderByID(ctx context.Context, id int64) (*domain.PurchaseOrder, error) {
	var order domain.PurchaseOrder
	err := r.db.WithContext(ctx).Preload("Items").Preload("Items.Product").
		Preload("Customer").First(&order, id).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

func (r *purchaseOrderRepository) GetPurchaseOrderByUUID(ctx context.Context, uuid string) (*domain.PurchaseOrder, error) {
	var order domain.PurchaseOrder
	err := r.db.WithContext(ctx).Preload("Items").Preload("Items.Product").
		Preload("Customer").Where("uuid = ?", uuid).First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

func (r *purchaseOrderRepository) UpdatePurchaseOrder(ctx context.Context, order *domain.PurchaseOrder) error {
	return r.db.WithContext(ctx).Save(order).Error
}

func (r *purchaseOrderRepository) ListPurchaseOrders(ctx context.Context, pagination *domain.Pagination) ([]domain.PurchaseOrder, error) {
	var orders []domain.PurchaseOrder

	tx := r.db.WithContext(ctx).Model(&domain.PurchaseOrder{})

	if err := tx.Count(&pagination.RowsNumber).Error; err != nil {
		return nil, err
	}

	tx = pagination.SetPaginationToDB(tx, "created_at", true)

	err := tx.Preload("Customer").Find(&orders).Error
	if err != nil {
		return nil, err
	}

	return orders, nil
}

func (r *purchaseOrderRepository) ReceivePurchaseOrder(ctx context.Context, purchaseOrderID int64, items []domain.PurchaseOrderItem) error {
	tx := r.db.WithContext(ctx)

	// 獲取採購單
	var purchaseOrder domain.PurchaseOrder
	if err := tx.First(&purchaseOrder, purchaseOrderID).Error; err != nil {
		return err
	}

	// 更新採購單狀態
	receivedCount := 0
	totalOrdered := 0

	// 更新每個項目的已收數量
	for _, updateItem := range items {
		var item domain.PurchaseOrderItem
		if err := tx.Where("id = ? AND purchase_order_id = ?", updateItem.ID, purchaseOrderID).First(&item).Error; err != nil {
			return err
		}

		// 計算本次收貨數量
		newReceived := updateItem.QuantityReceived - item.QuantityReceived
		if newReceived <= 0 {
			continue
		}

		// 更新已收數量
		if err := tx.Model(&item).Update("quantity_received", updateItem.QuantityReceived).Error; err != nil {
			return err
		}

		receivedCount += item.QuantityReceived
		totalOrdered += item.QuantityOrdered
	}

	// 更新訂單狀態
	var status domain.PurchaseOrderStatus
	if receivedCount == 0 {
		status = domain.PurchaseOrderStatusOrdered
	} else if receivedCount < totalOrdered {
		status = domain.PurchaseOrderStatusPartiallyReceived
	} else {
		status = domain.PurchaseOrderStatusReceived
		purchaseOrder.ArrivalDate.SetValid(time.Now())
	}

	if err := tx.Model(&purchaseOrder).Updates(map[string]interface{}{
		"status":       status,
		"arrival_date": purchaseOrder.ArrivalDate,
		"updated_at":   time.Now(),
	}).Error; err != nil {
		return err
	}

	return nil
}
