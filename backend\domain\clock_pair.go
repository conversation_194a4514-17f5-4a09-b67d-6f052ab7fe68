package domain

import (
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type ClockPairStatus string

const (
	ClockPairStatusPending ClockPairStatus = "pending"
	ClockPairStatusPaired  ClockPairStatus = "paired"
	ClockPairStatusMissing ClockPairStatus = "missing"
)

type ClockPair struct {
	ID         int64           `gorm:"primaryKey" json:"-"`
	UUID       string          `gorm:"<-:create" json:"uuid"`
	UserID     int64           `gorm:"<-:create" json:"-"`
	DateAt     time.Time       `json:"date_at"`
	ClockInID  null.Int        `gorm:"default:null" json:"-"`
	ClockOutID null.Int        `gorm:"default:null" json:"-"`
	Status     ClockPairStatus `gorm:"default:'pending'" json:"status"` // pending, paired, missing
	CreatedAt  time.Time       `json:"-"`
	UpdatedAt  time.Time       `json:"-"`
	DeletedAt  gorm.DeletedAt  `json:"-"`

	// Relations
	ClockIn  Clock `gorm:"<-:false;foreignKey:ClockInID;references:ID" json:"clock_in"`
	ClockOut Clock `gorm:"<-:false;foreignKey:ClockOutID;references:ID" json:"clock_out"`
}

type ClockPairFilter struct {
	StartDate string            `form:"start_date" json:"start_date"`
	EndDate   string            `form:"end_date" json:"end_date"`
	UserUUID  string            `form:"user_uuid" json:"user_uuid"`
	Status    []ClockPairStatus `form:"status[]" json:"status[]"`
}

type ClockPairResponse struct {
	UUID       string          `json:"uuid"`
	DateAt     string          `json:"date_at"`
	UserID     int64           `json:"-"`
	User       UserInfo        `json:"user"`
	ClockInID  int64           `json:"-"`
	ClockIn    *ClockInfo      `gorm:"foreignKey:ID;references:ClockInID" json:"clock_in"`
	ClockOutID int64           `json:"-"`
	ClockOut   *ClockInfo      `gorm:"foreignKey:ID;references:ClockOutID" json:"clock_out"`
	Status     ClockPairStatus `json:"status"`
}

func (ClockPairResponse) TableName() string {
	return "clock_pairs"
}
