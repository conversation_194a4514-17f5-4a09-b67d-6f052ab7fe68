import { defineStore } from 'pinia';
import { LoginResponse, TokenResponse } from '@/api/auth';
import { UserInfo } from '@/api/user';
import { api } from '@/boot/axios';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    userUUID: '',
    userName: '',
    userInfo: <UserInfo>{},
    accessToken: '',
    tokenExpiry: null as Date | null,
    refreshToken: '',
    refreshTokenExpiry: null as Date | null,
  }),
  persist: true,
  getters: {
    getUserInfo: (state) => state.userInfo,
    getUserUUID: (state) => state.userUUID,
    getUserName: (state) => state.userName,
    isAuthenticated: (state) => !!state.accessToken,
    isTokenExpired: (state) => {
      if (!state.tokenExpiry) {
        return true;
      }

      return new Date() >= state.tokenExpiry;
    },
    isRefreshTokenExpired: (state) => {
      if (!state.refreshTokenExpiry) {
        return true;
      }

      return new Date() >= state.refreshTokenExpiry;
    },
  },
  actions: {
    login(data: LoginResponse) {
      this.setUser(data.user);
      this.updateToken(data);
    },
    setUser(data: UserInfo) {
      this.userInfo = data;
      this.userUUID = data.uuid;
      this.userName = data.name;
    },
    getTokenClaims() {
      const accessToken = this.accessToken;
      if (!accessToken) {
        return null;
      }

      try {
        // 解碼 accessToken
        const base64Url = accessToken.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const claims = JSON.parse(atob(base64));

        return {
          userUUID: claims.user_uuid,
          isAdmin: claims.is_admin,
        };
      } catch {
        return null;
      }
    },
    isAdmin() {
      const claims = this.getTokenClaims();
      return claims?.isAdmin || false;
    },
    updateToken(data: TokenResponse) {
      this.accessToken = data.access_token;
      this.tokenExpiry = data.expires_at;
      this.refreshToken = data.refresh_token;
      this.refreshTokenExpiry = data.refresh_expires_at;

      localStorage.setItem('accessToken', data.access_token);
      localStorage.setItem('tokenExpiry', data.expires_at.toString());
      localStorage.setItem('refreshToken', data.refresh_token);
      localStorage.setItem(
        'refreshTokenExpiry',
        data.refresh_expires_at.toString()
      );

      api.defaults.headers.common.Authorization = `Bearer ${data.access_token}`;
      api.defaults.headers.common['X-Refresh-Token'] = data.refresh_token;
    },
    logout() {
      this.accessToken = '';
      this.tokenExpiry = null;
      this.refreshToken = '';
      this.refreshTokenExpiry = null;
      this.userUUID = '';
      this.userName = '';

      localStorage.removeItem('accessToken');
      localStorage.removeItem('tokenExpiry');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('refreshTokenExpiry');

      delete api.defaults.headers.common.Authorization;
      delete api.defaults.headers.common['X-Refresh-Token'];
    },
    initializeFromStorage() {
      const accessToken = localStorage.getItem('accessToken');
      const refreshToken = localStorage.getItem('refreshToken');
      const tokenExpiry = localStorage.getItem('tokenExpiry');
      const refreshTokenExpiry = localStorage.getItem('refreshTokenExpiry');

      if (accessToken && refreshToken && tokenExpiry && refreshTokenExpiry) {
        this.accessToken = accessToken;
        this.tokenExpiry = new Date(tokenExpiry);
        this.refreshToken = refreshToken;
        this.refreshTokenExpiry = new Date(refreshTokenExpiry);
        api.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
        api.defaults.headers.common['X-Refresh-Token'] = refreshToken;
      }
    },
  },
});

export type AuthStore = ReturnType<typeof useAuthStore>;
