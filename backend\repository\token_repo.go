package repository

import (
	"context"
	"cx/domain"
	"time"

	"gorm.io/gorm"
)

type TokenRepository interface {
	WithTx(tx *gorm.DB) TokenRepository

	// AccessToken
	CreateToken(ctx context.Context, token *domain.AccessToken) error
	GetToken(ctx context.Context, id int64) (*domain.AccessToken, error)
	GetTokenByJwtID(ctx context.Context, jwtID string) (*domain.AccessToken, error)
	UpdateLastUsedAt(ctx context.Context, jwtID string) error
	RevokeToken(ctx context.Context, id int64) error
	RevokeTokenByJwtID(ctx context.Context, jwtID string) error
	RevokeUserTokens(ctx context.Context, userID int64) error

	// RefreshToken
	CreateRefreshToken(ctx context.Context, token *domain.RefreshToken) error
	GetRefreshToken(ctx context.Context, refreshToken string) (*domain.RefreshToken, error)
	RevokeRefreshToken(ctx context.Context, id int64) error
}

type tokenRepository struct {
	db *gorm.DB
}

func NewTokenRepository(db *gorm.DB) TokenRepository {
	return &tokenRepository{db}
}

func (r *tokenRepository) WithTx(tx *gorm.DB) TokenRepository {
	return &tokenRepository{tx}
}

// AccessToken
func (r *tokenRepository) CreateToken(ctx context.Context, token *domain.AccessToken) error {
	return r.db.WithContext(ctx).Create(token).Error
}

func (r *tokenRepository) GetToken(ctx context.Context, id int64) (*domain.AccessToken, error) {
	var token domain.AccessToken
	err := r.db.WithContext(ctx).Where("id = ? AND is_revoked = FALSE", id).First(&token).Error
	return &token, err
}

func (r *tokenRepository) GetTokenByJwtID(ctx context.Context, jwtID string) (*domain.AccessToken, error) {
	var token domain.AccessToken
	err := r.db.WithContext(ctx).Where("jwt_id = ? AND is_revoked = FALSE", jwtID).First(&token).Error
	return &token, err
}

func (r *tokenRepository) UpdateLastUsedAt(ctx context.Context, jwtID string) error {
	return r.db.WithContext(ctx).Model(&domain.AccessToken{}).Where("jwt_id = ?", jwtID).Update("last_used_at", time.Now()).Error
}

func (r *tokenRepository) RevokeToken(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Model(&domain.AccessToken{}).Where("id = ?", id).Update("is_revoked", true).Error
}

func (r *tokenRepository) RevokeTokenByJwtID(ctx context.Context, jwtID string) error {
	return r.db.WithContext(ctx).Model(&domain.AccessToken{}).Where("jwt_id = ?", jwtID).Update("is_revoked", true).Error
}

func (r *tokenRepository) RevokeUserTokens(ctx context.Context, userID int64) error {
	return r.db.WithContext(ctx).Model(&domain.AccessToken{}).Where("user_id = ? AND is_revoked = FALSE", userID).Update("is_revoked", true).Error
}

// RefreshToken
func (r *tokenRepository) CreateRefreshToken(ctx context.Context, token *domain.RefreshToken) error {
	return r.db.WithContext(ctx).Create(token).Error
}

func (r *tokenRepository) GetRefreshToken(ctx context.Context, refreshToken string) (*domain.RefreshToken, error) {
	var token domain.RefreshToken
	err := r.db.WithContext(ctx).Where("refresh_token = ? AND is_revoked = FALSE", refreshToken).First(&token).Error
	return &token, err
}

func (r *tokenRepository) RevokeRefreshToken(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Model(&domain.RefreshToken{}).Where("id = ?", id).Update("is_revoked", true).Error
}
