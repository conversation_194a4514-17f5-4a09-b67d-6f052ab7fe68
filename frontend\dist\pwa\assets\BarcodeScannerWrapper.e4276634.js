import{J as t,E as S,bu as N,bv as I,G as Q,a8 as q,I as F,ad as P,c as _,al as k,K as M,ay as $,r as w,d as h,u as B,s as D,aB as E,w as V,o as C,a as x,b as p,f as g,h as z,m as T,q as v,bw as L,Q as A}from"./index.9477d5a3.js";import{u as K}from"./use-quasar.66282a44.js";import{_ as R}from"./plugin-vue_export-helper.21dcd24c.js";const U=[t("circle",{cx:"15",cy:"15",r:"15"},[t("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"60",cy:"15",r:"9","fill-opacity":".3"},[t("animate",{attributeName:"r",from:"9",to:"9",begin:"0s",dur:"0.8s",values:"9;15;9",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"fill-opacity",from:".5",to:".5",begin:"0s",dur:"0.8s",values:".5;1;.5",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"105",cy:"15",r:"15"},[t("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})])];var j=S({name:"QSpinnerDots",props:N,setup(e){const{cSize:r,classes:s}=I(e);return()=>t("svg",{class:s.value,fill:"currentColor",width:r.value,height:r.value,viewBox:"0 0 120 30",xmlns:"http://www.w3.org/2000/svg"},U)}}),G=S({name:"QInnerLoading",props:{...Q,...q,showing:Boolean,color:String,size:{type:[String,Number],default:"42px"},label:String,labelClass:String,labelStyle:[String,Array,Object]},setup(e,{slots:r}){const s=M(),c=F(e,s.proxy.$q),{transitionProps:d,transitionStyle:o}=P(e),i=_(()=>"q-inner-loading q--avoid-card-border absolute-full column flex-center"+(c.value===!0?" q-inner-loading--dark":"")),l=_(()=>"q-inner-loading__label"+(e.labelClass!==void 0?` ${e.labelClass}`:""));function n(){const a=[t($,{size:e.size,color:e.color})];return e.label!==void 0&&a.push(t("div",{class:l.value,style:e.labelStyle},[e.label])),a}function m(){return e.showing===!0?t("div",{class:i.value,style:o.value},r.default!==void 0?r.default():n()):null}return()=>t(k,d.value,m)}});function J(e={}){const{timeout:r=20,barcodeField:s="barcode"}=e,c=w(!0);return{isReady:c,processBarcode:i=>{c.value=!1;const l=new CustomEvent("barcode-scanned",{detail:{barcode:i}});window.dispatchEvent(l),setTimeout(()=>{c.value=!0},r)},findProductByBarcode:(i,l)=>i.find(n=>n[s]===l)}}const O={class:"barcode-scanner",style:{width:"15rem","max-width":"80%"}},W=h({__name:"BarcodeScanner",props:{products:{type:Array,required:!0},barcodeField:{type:String,default:"barcode"}},emits:["update:modelValue","barcode-detected","product-found","product-not-found"],setup(e,{emit:r}){const{t:s}=B(),c=e,d=r,{isReady:o,processBarcode:i,findProductByBarcode:l}=J({timeout:200,barcodeField:c.barcodeField}),n=w(""),m=_(()=>o.value?"":s("barcodeScanner.scanning")),a=()=>{n.value&&o.value&&(i(n.value),n.value="")},y=f=>{const u=f.detail.barcode;d("barcode-detected",u);const b=l(c.products,u);b?d("product-found",b):d("product-not-found",u)};return D(()=>{window.addEventListener("barcode-scanned",y)}),E(()=>{window.removeEventListener("barcode-scanned",y)}),V(()=>n,f=>{d("update:modelValue",f)}),(f,u)=>(C(),x("div",O,[p(A,{modelValue:n.value,"onUpdate:modelValue":u[0]||(u[0]=b=>n.value=b),label:v(s)("barcode"),placeholder:m.value,onKeydown:L(a,["enter"]),dense:"",clearable:"",loading:!v(o)},{prepend:g(()=>[p(z,{name:"sym_o_barcode_scanner"})]),append:g(()=>[p(T,{round:"",flat:"",icon:"search",onClick:a,disable:!n.value||!v(o)},null,8,["disable"])]),_:1},8,["modelValue","label","placeholder","loading"]),p(G,{showing:!v(o)},{default:g(()=>[p(j,{size:"40px",color:"primary"})]),_:1},8,["showing"])]))}});var H=R(W,[["__scopeId","data-v-ccaebd76"]]);const X={class:"barcode-scanner-wrapper"},ae=h({__name:"BarcodeScannerWrapper",props:{products:{type:Array,required:!0},barcodeField:{type:String,default:"barcode"},autoNotify:{type:Boolean,default:!0}},emits:["barcode-scanned","product-found","product-not-found"],setup(e,{emit:r}){const{t:s}=B(),c=K(),d=e,o=r,i=w(""),l=a=>{i.value=a,o("barcode-scanned",a)},n=a=>{d.autoNotify&&c.notify({message:`${a.name} ${s("barcodeScanner.scanCompleted")}`,color:"positive",position:"top",timeout:1500}),o("product-found",a)},m=a=>{d.autoNotify&&c.notify({message:s("barcodeScanner.notFound",{barcode:a}),color:"warning",position:"top",timeout:1500}),o("product-not-found",a)};return(a,y)=>(C(),x("div",X,[p(H,{products:e.products,"barcode-field":e.barcodeField,onBarcodeDetected:l,onProductFound:n,onProductNotFound:m},null,8,["products","barcode-field"])]))}});export{ae as _};
