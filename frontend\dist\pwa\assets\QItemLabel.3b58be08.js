import{E as o,c as t,J as r,T as s}from"./index.9477d5a3.js";var c=o({name:"QItemLabel",props:{overline:<PERSON><PERSON><PERSON>,caption:<PERSON><PERSON><PERSON>,header:<PERSON><PERSON><PERSON>,lines:[Number,String]},setup(e,{slots:a}){const l=t(()=>parseInt(e.lines,10)),i=t(()=>"q-item__label"+(e.overline===!0?" q-item__label--overline text-overline":"")+(e.caption===!0?" q-item__label--caption text-caption":"")+(e.header===!0?" q-item__label--header":"")+(l.value===1?" ellipsis":"")),n=t(()=>e.lines!==void 0&&l.value>1?{overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":l.value}:null);return()=>r("div",{style:n.value,class:i.value},s(a.default))}});export{c as Q};
