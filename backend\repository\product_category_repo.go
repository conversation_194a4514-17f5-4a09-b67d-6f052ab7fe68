package repository

import (
	"context"

	"gorm.io/gorm"

	"cx/domain"
)

type ProductCategoryRepository interface {
	WithTx(tx *gorm.DB) ProductCategoryRepository

	Create(ctx context.Context, productCategory *domain.ProductCategory) error
	Update(ctx context.Context, id int64, productCategory *domain.ProductCategory) error
	Fetch(ctx context.Context) ([]domain.ProductCategory, error)
	GetByID(ctx context.Context, id int64) (*domain.ProductCategory, error)

	GetImageByUUID(ctx context.Context, imageUUID string) (*domain.ProductCategoryImage, error)
	CreateImage(ctx context.Context, image *domain.ProductCategoryImage) error
	UpdateImageWcID(ctx context.Context, imageID int64, wcID int64) error
	DeleteImage(ctx context.Context, imageUUID string) error
}

type productCategoryRepository struct {
	db *gorm.DB
}

func NewProductCategoryRepository(db *gorm.DB) ProductCategoryRepository {
	return &productCategoryRepository{db}
}

func (r *productCategoryRepository) WithTx(tx *gorm.DB) ProductCategoryRepository {
	return &productCategoryRepository{tx}
}

func (r *productCategoryRepository) Create(ctx context.Context, productCategory *domain.ProductCategory) error {
	return r.db.WithContext(ctx).Create(productCategory).Error
}

func (r *productCategoryRepository) Update(ctx context.Context, id int64, productCategory *domain.ProductCategory) error {
	return r.db.WithContext(ctx).Model(&domain.ProductCategory{}).Where("id = ?", id).Updates(productCategory).Error
}

func (r *productCategoryRepository) Fetch(ctx context.Context) ([]domain.ProductCategory, error) {
	var productCategories []domain.ProductCategory

	err := r.db.WithContext(ctx).Find(&productCategories).Error

	return productCategories, err
}

func (r *productCategoryRepository) GetByID(ctx context.Context, id int64) (*domain.ProductCategory, error) {
	var productCategory domain.ProductCategory
	err := r.db.WithContext(ctx).Preload("Image").Where("id = ?", id).First(&productCategory).Error
	return &productCategory, err
}

func (r *productCategoryRepository) GetImageByUUID(ctx context.Context, imageUUID string) (*domain.ProductCategoryImage, error) {
	var image domain.ProductCategoryImage

	err := r.db.WithContext(ctx).Where("uuid = ?", imageUUID).First(&image).Error
	return &image, err
}

func (r *productCategoryRepository) CreateImage(ctx context.Context, image *domain.ProductCategoryImage) error {
	return r.db.WithContext(ctx).Create(image).Error
}

func (r *productCategoryRepository) UpdateImageWcID(ctx context.Context, imageID int64, wcID int64) error {
	return r.db.WithContext(ctx).Model(&domain.ProductCategoryImage{}).Where("id = ?", imageID).Update("wc_id", wcID).Error
}

func (r *productCategoryRepository) DeleteImage(ctx context.Context, imageUUID string) error {
	return r.db.WithContext(ctx).Where("uuid = ?", imageUUID).Delete(&domain.ProductCategoryImage{}).Error
}
