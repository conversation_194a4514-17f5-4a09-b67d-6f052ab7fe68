<template>
  <q-page class="q-pt-md">
    <q-card
      flat
      square
      bordered
      class="q-mx-auto"
      style="width: 800px; max-width: 100%"
    >
      <q-form @submit.prevent="onSubmit" greedy>
        <q-card-section>
          <div class="row q-mb-md">
            <div class="col-12 text-h6 text-bold text-black">
              <div class="row items-center">
                Gmail STMP
                <a
                  href="https://myaccount.google.com/apppasswords"
                  target="_blank"
                  class="self-center q-ml-sm"
                >
                  <q-icon name="help" size="sm" color="primary" />
                </a>
              </div>
              <q-separator color="black" size="2px" class="q-mb-sm" />
            </div>
          </div>
          <!-- Gmail -->
          <div class="row q-mb-md">
            <div class="col-12 col-md-2 text-subtitle1 text-md-center">
              Gmail
            </div>
            <div class="col-12 col-md-10">
              <q-input
                type="email"
                v-model="sys.gmail"
                outlined
                dense
                hide-bottom-space
              />
            </div>
          </div>
          <!-- Gmail STMP App Password -->
          <div class="row q-mb-md">
            <div class="col-12 col-md-2 text-subtitle1 text-md-center">
              App Password
            </div>
            <div class="col-12 col-md-10">
              <q-input
                v-model="sys.gmail_app_password"
                outlined
                dense
                hide-bottom-space
                :rules="[
                (val: string) => (!!val || !sys.gmail) || t('error.required'),
              ]"
              />
            </div>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="q-px-lg q-py-md">
          <q-btn
            type="submit"
            :label="t('save')"
            color="positive"
            size="md"
            :loading="isLoading"
          />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { Notify } from 'quasar';
import { SystemApi, SystemConfig } from '@/api/system';

const { t } = useI18n();

const sys = ref<SystemConfig>({
  gmail: '',
  gmail_app_password: '',
});

const isLoading = ref(false);
const getData = async () => {
  try {
    isLoading.value = true;
    const response = await SystemApi.get();

    sys.value = response.result;
  } finally {
    isLoading.value = false;
  }
};

const onSubmit = async () => {
  try {
    isLoading.value = true;
    await SystemApi.update(sys.value);

    Notify.create({
      type: 'positive',
      message: t('success'),
      position: 'top',
    });
  } finally {
    isLoading.value = false;
    getData();
  }
};

onMounted(() => {
  getData();
});
</script>
