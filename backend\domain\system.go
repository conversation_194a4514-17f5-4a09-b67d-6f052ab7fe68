package domain

import "time"

type SystemOption struct {
	ID               int64     `json:"-"`
	Gmail            string    `form:"gmail" json:"gmail"`
	GmailAppPassword string    `form:"gmail_app_password" json:"gmail_app_password"`
	CreatedAt        time.Time `gorm:"<-:create" json:"-"`
	UpdatedAt        time.Time `json:"-"`
}

type SystemOptionUpdatePayload struct {
	ID               int64
	Gmail            string `form:"gmail" json:"gmail"`
	GmailAppPassword string `form:"gmail_app_password" json:"gmail_app_password"`
}

func (SystemOptionUpdatePayload) TableName() string {
	return "system_options"
}
