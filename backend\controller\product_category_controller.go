package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type ProductCategoryController struct {
	productCategoryService service.ProductCategoryService
}

func NewProductCategoryController(r *gin.RouterGroup, productCategoryService service.ProductCategoryService) {
	productCategoryController := ProductCategoryController{productCategoryService}

	v1 := r.Group("/v1/product-categories")
	{
		v1.GET("", productCategoryController.FetchHandler)
		v1.GET("/:id", productCategoryController.GetByIDHandler)
		v1.POST("", productCategoryController.CreateHandler)
		v1.PUT("/:id", productCategoryController.UpdateHandler)

		v1.POST("/:id/images", productCategoryController.UploadImageHandler)
		v1.DELETE("/:id/images/:image_id", productCategoryController.DeleteImageHandler)
	}
}

func (ctr *ProductCategoryController) FetchHandler(c *gin.Context) {
	productCategories, err := ctr.productCategoryService.Fetch(c)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to fetch product categories")
		return
	}

	utils.HandleSuccess(c, productCategories)
}

func (ctr *ProductCategoryController) GetByIDHandler(c *gin.Context) {
	id := c.Param("id")

	productCategory, err := ctr.productCategoryService.GetByID(c, utils.ParseInt64(id))
	if err != nil {
		utils.HandleError(c, http.StatusNotFound, err, "Product category not found")
		return
	}

	utils.HandleSuccess(c, productCategory)
}

func (ctr *ProductCategoryController) CreateHandler(c *gin.Context) {
	var productCategory domain.ProductCategory
	if err := c.ShouldBind(&productCategory); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	_, err := ctr.productCategoryService.Create(c, &productCategory)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to create product category")
		return
	}

	utils.HandleSuccess(c, productCategory)
}

func (ctr *ProductCategoryController) UpdateHandler(c *gin.Context) {
	id := c.Param("id")

	var productCategory domain.ProductCategory
	if err := c.ShouldBind(&productCategory); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	err := ctr.productCategoryService.Update(c, utils.ParseInt64(id), &productCategory)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update product category")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *ProductCategoryController) UploadImageHandler(c *gin.Context) {
	categoryID := c.Param("id")

	image, err := c.FormFile("file")
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	err = ctr.productCategoryService.UploadImage(c, utils.ParseInt64(categoryID), image)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to upload image")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *ProductCategoryController) DeleteImageHandler(c *gin.Context) {
	imageUUID := c.Param("image_id") // uuid

	err := ctr.productCategoryService.DeleteImage(c, imageUUID)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to delete image")
		return
	}

	utils.HandleSuccess(c, nil)
}
