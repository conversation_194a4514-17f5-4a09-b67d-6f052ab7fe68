package controller

import (
	"cx/service"
	"cx/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type SalaryItemController struct {
	salaryItemService service.SalaryItemService
}

func NewSalaryItemController(r *gin.RouterGroup, salaryItemService service.SalaryItemService) {
	salaryItemController := SalaryItemController{salaryItemService}

	salaryItemGroup := r.Group("/v1/salary-items")
	{
		salaryItemGroup.GET("", salaryItemController.ListSalaryItemsHandler)
	}
}

func (ctr *SalaryItemController) ListSalaryItemsHandler(c *gin.Context) {
	salaryItems, err := ctr.salaryItemService.ListSalaryItems(c)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to list salary items")
		return
	}

	utils.HandleSuccess(c, salaryItems)
}
