package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type CustomerController struct {
	customerService service.CustomerService
}

func NewCustomerController(r *gin.RouterGroup, customerService service.CustomerService) {
	customerController := CustomerController{customerService}

	v1 := r.Group("/v1/customers")
	{
		v1.GET("", customerController.FetchHandler)
		v1.GET("/:id", customerController.GetByIDHandler)
		v1.POST("", customerController.CreateHandler)
		v1.PUT("/:id", customerController.UpdateHandler)
		v1.DELETE("/:id", customerController.DeleteHandler)
	}
}

func (ctr *CustomerController) FetchHandler(c *gin.Context) {
	req := struct {
		Filter     domain.CustomerFilter `form:"filter"`
		Pagination domain.Pagination     `form:"pagination"`
	}{}
	c.ShouldBind(&req)

	customers, err := ctr.customerService.Fetch(c, &req.Filter, &req.Pagination)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to fetch customers")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"data":       customers,
		"pagination": req.Pagination,
	})
}

func (ctr *CustomerController) GetByIDHandler(c *gin.Context) {
	id := c.Param("id") // uuid

	customer, err := ctr.customerService.GetByUUID(c, id)
	if err != nil {
		utils.HandleError(c, http.StatusNotFound, err, "Customer not found")
		return
	}

	utils.HandleSuccess(c, customer)
}

func (ctr *CustomerController) CreateHandler(c *gin.Context) {
	var customer domain.Customer
	if err := c.ShouldBind(&customer); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	err := ctr.customerService.Create(c, &customer)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to create customer")
		return
	}

	utils.HandleSuccess(c, customer)
}

func (ctr *CustomerController) UpdateHandler(c *gin.Context) {
	id := c.Param("id")

	var customer domain.CustomerUpdatePayload
	if err := c.ShouldBind(&customer); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	err := ctr.customerService.Update(c, id, &customer)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update customer")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *CustomerController) DeleteHandler(c *gin.Context) {
	id := c.Param("id")

	err := ctr.customerService.Delete(c, id)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to delete customer")
		return
	}

	utils.HandleSuccess(c, nil)
}
