package utils

import "os"

func CreatePathIfNotExists(dirPath string) error {
	// 檢查目錄是否存在
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		// 創建目錄（包括所有必要的父目錄）
		return os.MkdirAll(dirPath, 0755)
	} else if err != nil {
		// 其他錯誤
		return err
	}

	// 目錄已存在
	return nil
}

func DeleteFile(filePath string) error {
	// 檢查檔案是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil
	}

	// 刪除檔案
	return os.Remove(filePath)
}
