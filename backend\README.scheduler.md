# WooCommerce to Xero Invoice Scheduler

這個調度器會定期檢查WooCommerce中已付款但尚未創建Xero發票的訂單，自動為這些訂單創建Xero發票並發送郵件給客戶。

## 功能特點

- 每5分鐘自動檢查一次WooCommerce訂單
- 處理已付款狀態的訂單：`wc-processing`, `wc-packing`, `wc-shipping`, `wc-completed`
- 自動創建Xero發票
- 自動發送發票郵件給客戶
- 支持默認郵箱配置（當客戶沒有郵箱時使用）
- 完整的錯誤處理和日誌記錄

## 安裝和配置

### 1. 數據庫遷移

首先確保數據庫已經運行了最新的遷移，特別是 `000040_create_wc_xero_order_syncs_table.up.mysql`：

```sql
-- 創建 WooCommerce Xero 同步表
CREATE TABLE `wc_xero_order_syncs` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `uuid` VARCHAR(36) NOT NULL UNIQUE,
    `wc_order_id` INT NOT NULL,
    `xero_invoice_id` VARCHAR(255),
    `xero_invoice_no` VARCHAR(255),
    `sync_status` ENUM('pending', 'syncing', 'success', 'failed', 'voided') NOT NULL DEFAULT 'pending',
    `error_message` TEXT,
    `last_sync_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_wc_order_id` (`wc_order_id`),
    INDEX `idx_sync_status` (`sync_status`),
    INDEX `idx_xero_invoice_id` (`xero_invoice_id`),
    
    UNIQUE KEY `unique_wc_order_sync` (`wc_order_id`)
);
```

### 2. Xero 配置

在系統管理界面中配置Xero API設置：

1. 進入 **系統管理 > Xero 設定**
2. 填寫 Xero API 配置信息
3. **重要**: 設置 **默認 Email 地址**，當客戶沒有郵箱時會使用此地址

### 3. 環境配置

複製環境變量配置文件：

```bash
cp .env.scheduler.example .env.scheduler
```

編輯 `.env.scheduler` 文件：

```bash
# 數據庫配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=cx_pos

# 默認郵箱配置（可選，如果Xero配置中沒有設置默認郵箱時使用）
DEFAULT_EMAIL=<EMAIL>
```

## 運行方式

### 方式1: 直接運行 (推薦用於開發)

#### Linux/macOS:
```bash
chmod +x scripts/start-scheduler.sh
./scripts/start-scheduler.sh
```

#### Windows:
```cmd
scripts\start-scheduler.bat
```

### 方式2: 手動構建和運行

```bash
# 構建
go build -o scheduler ./cmd/scheduler

# 設置環境變量
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=your_password
export DB_NAME=cx_pos
export DEFAULT_EMAIL=<EMAIL>

# 運行
./scheduler
```

### 方式3: Docker 運行

```bash
# 使用 Docker Compose
docker-compose -f docker-compose.scheduler.yml up -d

# 查看日誌
docker-compose -f docker-compose.scheduler.yml logs -f wc-xero-scheduler
```

## 工作流程

1. **訂單檢查**: 每5分鐘檢查一次WooCommerce訂單表 `wp_wc_orders`
2. **狀態篩選**: 只處理狀態為 `wc-processing`, `wc-packing`, `wc-shipping`, `wc-completed` 的訂單
3. **同步檢查**: 檢查 `wc_xero_order_syncs` 表，排除已經成功同步的訂單
4. **發票創建**: 為符合條件的訂單創建Xero發票
5. **郵件發送**: 發送發票郵件給客戶
6. **狀態更新**: 更新同步狀態到數據庫

## 郵件地址優先級

調度器會按以下優先級選擇郵件地址：

1. **客戶郵箱**: 訂單中的 `billing_email`
2. **Xero默認郵箱**: Xero配置中的 `default_email`
3. **環境變量郵箱**: `.env.scheduler` 中的 `DEFAULT_EMAIL`

如果以上都沒有配置，該訂單會被跳過並記錄錯誤。

## 監控和日誌

調度器會輸出詳細的日誌信息：

```
2024/01/01 10:00:00 Starting WooCommerce to Xero Invoice Scheduler...
2024/01/01 10:00:00 Scheduler started: checking for paid orders without Xero invoices every 5 minutes
2024/01/01 10:00:00 Starting to process paid orders for Xero invoice creation...
2024/01/01 10:00:01 Found 3 paid orders that need Xero invoice creation
2024/01/01 10:00:01 Processing order 12345 for Xero invoice creation
2024/01/01 10:00:02 Successfully processed order 12345
2024/01/01 10:00:02 Completed processing: 3 successful, 0 errors
```

## 錯誤處理

- **數據庫連接失敗**: 調度器會記錄錯誤並繼續嘗試
- **Xero API 錯誤**: 會記錄到同步表的 `error_message` 字段
- **郵件發送失敗**: 不會影響發票創建，只會記錄警告
- **訂單處理失敗**: 會記錄錯誤並繼續處理下一個訂單

## 停止調度器

按 `Ctrl+C` 停止調度器，它會優雅地關閉並完成當前正在處理的任務。

## 故障排除

### 1. 數據庫連接問題
檢查 `.env.scheduler` 中的數據庫配置是否正確。

### 2. Xero API 錯誤
確保 Xero 配置正確且 token 沒有過期。

### 3. 郵件發送失敗
檢查 Xero 配置中的默認郵箱設置。

### 4. 沒有找到訂單
確認 WooCommerce 訂單狀態是否正確，以及是否已經創建過發票。

## 注意事項

- 調度器會自動跳過已經處理過的訂單
- 每個訂單只會創建一次Xero發票
- 建議在生產環境中使用 Docker 或 systemd 來管理調度器進程
- 定期檢查日誌以確保調度器正常運行
