package repository

import (
	"context"
	"cx/domain"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type WcXeroOrderSyncRepository interface {
	Create(ctx context.Context, sync *domain.WcXeroOrderSync) error
	Update(ctx context.Context, sync *domain.WcXeroOrderSync) error
	GetByWcOrderID(ctx context.Context, wcOrderID uint) (*domain.WcXeroOrderSync, error)
	UpdateSyncStatus(ctx context.Context, wcOrderID uint, status domain.XeroSyncStatus, errorMessage string) error
	UpdateSyncSuccess(ctx context.Context, wcOrderID uint, xeroInvoiceID, xeroInvoiceNo string) error
}

type wcXeroOrderSyncRepository struct {
	db *gorm.DB
}

func NewWcXeroOrderSyncRepository(db *gorm.DB) WcXeroOrderSyncRepository {
	return &wcXeroOrderSyncRepository{db: db}
}

func (r *wcXeroOrderSyncRepository) Create(ctx context.Context, sync *domain.WcXeroOrderSync) error {
	if sync.UUID == "" {
		sync.UUID = uuid.New().String()
	}
	return r.db.WithContext(ctx).Create(sync).Error
}

func (r *wcXeroOrderSyncRepository) Update(ctx context.Context, sync *domain.WcXeroOrderSync) error {
	return r.db.WithContext(ctx).Save(sync).Error
}

func (r *wcXeroOrderSyncRepository) GetByWcOrderID(ctx context.Context, wcOrderID uint) (*domain.WcXeroOrderSync, error) {
	var sync domain.WcXeroOrderSync
	err := r.db.WithContext(ctx).Where("wc_order_id = ?", wcOrderID).First(&sync).Error
	if err != nil {
		return nil, err
	}
	return &sync, nil
}

func (r *wcXeroOrderSyncRepository) UpdateSyncStatus(ctx context.Context, wcOrderID uint, status domain.XeroSyncStatus, errorMessage string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"sync_status":   status,
		"error_message": errorMessage,
		"last_sync_at":  &now,
		"updated_at":    now,
	}

	return r.db.WithContext(ctx).
		Model(&domain.WcXeroOrderSync{}).
		Where("wc_order_id = ?", wcOrderID).
		Updates(updates).Error
}

func (r *wcXeroOrderSyncRepository) UpdateSyncSuccess(ctx context.Context, wcOrderID uint, xeroInvoiceID, xeroInvoiceNo string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"sync_status":     domain.XeroSyncStatusSuccess,
		"xero_invoice_id": xeroInvoiceID,
		"xero_invoice_no": xeroInvoiceNo,
		"error_message":   "",
		"last_sync_at":    &now,
		"updated_at":      now,
	}

	return r.db.WithContext(ctx).
		Model(&domain.WcXeroOrderSync{}).
		Where("wc_order_id = ?", wcOrderID).
		Updates(updates).Error
}
