package domain

type CreateScrapRequest struct {
	ID          int64             `json:"-"`
	UUID        string            `json:"-"`
	ScrapDate   string            `json:"scrap_date"`
	Items       []CreateScrapItem `gorm:"-" json:"items"`
	CreatedByID int64             `json:"-"` // 操作人員ID
	UpdatedByID int64             `json:"-"` // 操作人員ID
}

type CreateScrapItem struct {
	ProductID   int64       `json:"-"`
	Product     ProductInfo `gorm:"-" json:"product"`
	Quantity    int         `json:"quantity"`
	Notes       string      `json:"notes"`
	CreatedByID int64       `json:"-"` // 操作人員ID
	UpdatedByID int64       `json:"-"` // 操作人員ID
}
