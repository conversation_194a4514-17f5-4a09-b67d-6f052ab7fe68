# 數據庫連接修復總結

## 問題描述
`wcOrderRepository` 使用 `wpDB`（WordPress 數據庫），而 `WcXeroOrderSync` 表在主數據庫 `db` 中，導致無法正確查詢 Xero 同步狀態。

## 修復方案

### 1. 移除錯誤的查詢
從 `backend/repository/wc_order_repo.go` 的 `GetByID` 方法中移除了錯誤的 XeroSync 查詢：

```go
// 移除了這段代碼
var xeroSync domain.WcXeroOrderSync
if err := r.db.Where("wc_order_id = ?", id).First(&xeroSync).Error; err == nil {
    order.XeroSync = &xeroSync
}
```

### 2. 在服務層添加正確的查詢
在 `backend/service/wc_order_service.go` 的 `GetByID` 方法中添加了正確的 XeroSync 查詢：

```go
// 從主數據庫查詢 Xero 同步狀態
var xeroSync domain.WcXeroOrderSync
if err := s.db.Where("wc_order_id = ?", id).First(&xeroSync).Error; err == nil {
    order.XeroSync = &xeroSync
}
```

### 3. 修正服務構造函數調用
在 `backend/routes/routes.go` 中修正了 `wcOrderService` 的構造函數調用：

```go
// 修正前
wcOrderService := service.NewWcOrderService(wpDB, wcOrderRepo)

// 修正後
wcOrderService := service.NewWcOrderService(db, wcOrderRepo)
```

## 數據庫架構說明

### WordPress 數據庫 (wpDB)
- `wp_wc_orders` - WooCommerce 訂單主表
- `wp_wc_order_addresses` - 訂單地址信息
- `wp_wc_order_stats` - 訂單統計信息
- `wp_woocommerce_order_items` - 訂單項目
- `wp_woocommerce_order_itemmeta` - 訂單項目元數據

### 主數據庫 (db)
- `wc_xero_order_syncs` - Xero 同步狀態表
- 其他應用程序表

## 修復後的數據流程

1. **獲取 WooCommerce 訂單**：
   - `wcOrderRepository.GetByID()` 使用 `wpDB` 查詢 WordPress 數據庫
   - 獲取訂單基本信息、地址、項目等

2. **獲取 Xero 同步狀態**：
   - `wcOrderService.GetByID()` 使用主數據庫 `db` 查詢 `wc_xero_order_syncs` 表
   - 將 XeroSync 數據附加到訂單對象

3. **返回完整數據**：
   - 訂單對象包含完整的 WooCommerce 數據和 Xero 同步狀態

## 驗證方法

### API 測試
```bash
GET /api/v1/wc-orders/{id}
```

預期回應應包含 `xero_sync` 字段：
```json
{
  "result": {
    "id": 123,
    "status": "wc-completed",
    "total": 100.00,
    "xero_sync": {
      "uuid": "...",
      "wc_order_id": 123,
      "sync_status": "success",
      "xero_invoice_id": "...",
      "xero_invoice_no": "INV-001"
    }
  }
}
```

### 數據庫檢查
```sql
-- 檢查 WordPress 數據庫中的訂單
SELECT * FROM wp_wc_orders WHERE id = {order_id};

-- 檢查主數據庫中的同步狀態
SELECT * FROM wc_xero_order_syncs WHERE wc_order_id = {order_id};
```

## 注意事項

1. **數據庫分離**：WordPress 數據和應用程序數據分別存儲在不同數據庫中
2. **服務層責任**：服務層負責組合來自不同數據源的數據
3. **Repository 職責**：每個 Repository 只負責自己對應的數據庫
4. **性能考慮**：XeroSync 查詢是可選的，如果沒有同步記錄不會影響訂單數據返回

修復完成後，WCOrderDetailDialog 現在可以正確顯示 Xero 同步狀態了！
