<template>
  <q-layout view="hHr Lpr lFr" class="shadow-2 rounded-borders">
    <q-header elevated>
      <q-toolbar>
        <q-btn
          flat
          round
          dense
          icon="menu"
          aria-label="Menu"
          @click="toggleMenu"
        />
        <q-toolbar-title>{{ pageInfo.pageTitle }}</q-toolbar-title>
        <!-- <q-btn flat round dense icon="notifications_none" /> -->
        <span class="q-mr-sm">{{ authStore.getUserName }}</span>
        <q-btn flat round dense icon="logout" @click="logout" />
      </q-toolbar>
    </q-header>

    <q-drawer v-model="menuOpen" bordered overlay>
      <MenuLink :link-list="linkList" />
    </q-drawer>

    <q-page-container class="q-mt-lg q-px-md" style="background-color: #fef9f2">
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { usePageInfoStore } from '@/stores/pageInfo';
import { AuthApi } from '@/api/auth';
import { useAuthStore } from '@/stores/auth-store';
import { useDialog } from '@/utils';

const router = useRouter();
const pageInfo = usePageInfoStore();
const authStore = useAuthStore();
const dialog = useDialog();
const { t } = useI18n();

const menuOpen = ref(false);

const linkList = computed(() => [
  {
    title: t('attendance.label'),
    // icon: 'punch_clock',
    link: '/attendance',
  },
  {
    title: t('order.label'),
    // icon: 'shopping_cart',
    link: '/order',
  },
]);

const toggleMenu = () => {
  menuOpen.value = !menuOpen.value;
};

const logout = () => {
  dialog.showMessage({
    message: t('logoutConfirm'),
    timeout: 0,
    ok: async () => {
      await AuthApi.logout();
      authStore.logout();
      router.push('/login');
    },
  });
};
</script>
