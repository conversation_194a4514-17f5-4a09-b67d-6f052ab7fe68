package domain

import (
	"time"

	"gorm.io/gorm"
)

type UserGroup struct {
	ID        int64          `gorm:"<-:create" json:"id"`
	Name      string         `form:"name" json:"name"`
	CreatedAt time.Time      `gorm:"<-:create" json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-"`
}

type UserGroupUpdatePayload struct {
	UserGroup       UserGroup        `json:"user_group"`
	PermissionCodes []PermissionCode `json:"permission_codes"`
	UpdatedBy       string
}
