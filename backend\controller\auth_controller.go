package controller

import (
	"cx/domain"
	"cx/middleware"
	"cx/service"
	"cx/utils"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

type AuthController struct {
	authService service.AuthService
}

func NewAuthController(r *gin.RouterGroup, authService service.AuthService, userService service.UserService) {
	authController := AuthController{authService}

	v1 := r.Group("/v1/auth")
	{
		v1.POST("/login", authController.LoginHandler)
		v1.POST("/refresh-token", authController.RefreshTokenHandler)
		v1.Use(middleware.TokenAuth(authService, userService))
		v1.POST("/logout", authController.LogoutHandler)
		v1.POST("/verify-user", authController.VerifyUserHandler)
	}
}

func (ctr *AuthController) LoginHandler(c *gin.Context) {
	var request domain.LoginRequest
	if err := c.ShouldBind(&request); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	tokenPair, err := ctr.authService.Login(c, &request)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Wrong account or password")
		return
	}

	utils.HandleSuccess(c, tokenPair)
}

func (ctr *AuthController) LogoutHandler(c *gin.Context) {
	claims, exists := c.Get("claims")
	if !exists {
		utils.HandleError(c, http.StatusBadRequest, domain.ErrTokenInvalid, "Invalid token")
		return
	}

	fmt.Printf("claims: %+v\n", claims)

	err := ctr.authService.Logout(c, claims.(*service.TokenClaims))
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to logout")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *AuthController) RefreshTokenHandler(c *gin.Context) {
	payload := domain.RefreshTokenPayload{}

	// 從Header中獲取刷新令牌
	refreshToken := c.GetHeader("X-Refresh-Token")
	if refreshToken == "" {
		utils.HandleError(c, http.StatusBadRequest, nil, "Missing refresh token")
		return
	}

	payload.RefreshToken = refreshToken
	payload.UserAgent = c.Request.UserAgent()
	payload.ClientIP = c.ClientIP()

	tokenPair, err := ctr.authService.RefreshToken(c, &payload)
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Failed to refresh token")
		return
	}

	utils.HandleSuccess(c, tokenPair)
}

func (ctr *AuthController) VerifyUserHandler(c *gin.Context) {
	var request domain.LoginRequest
	if err := c.ShouldBind(&request); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	user, err := ctr.authService.VerifyUser(c, &request)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to verify user")
		return
	}

	utils.HandleSuccess(c, user)
}
