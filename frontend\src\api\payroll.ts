import { apiWrapper } from '@/boot/axios';
import { UserInfo } from './user';
import { Pagination } from '@/types';

export interface PayrollPeriod {
  uuid: string;
  start_date: string;
  end_date: string;
  created_at: string;
}

export interface PayrollPeriodFilter {
  start_date?: string; // YYYY-MM-DD
  end_date?: string; // YYYY-MM-DD
}

// 薪資單主表
export interface Payroll {
  uuid?: string;
  user_uuid?: string;
  user: UserInfo;
  period_uuid?: string; // 薪資期間 UUID
  period: PayrollPeriod;
  pay_date?: string; // YYYY-MM-DD 發薪日期
  salary_type?: number; // 1: 月薪, 2: 時薪
  basic_salary?: number;
  hourly_salary?: number;
  work_days?: number;
  work_hours?: number;
  gross_salary?: number; // 總收入
  total_deductions?: number; // 總扣款
  net_salary: number; // 淨收入
  status?: number; // 0: 草稿: 1: 未發薪, 2: 已發薪
  note?: string;
  created_at?: string;

  payroll_details: PayrollDetail[]; // 薪資明細
  emails: PayrollEmail[]; // 發送郵件紀錄
}

export interface PayrollDetail {
  uuid?: string;
  salary_item: SalaryItem; // 薪資項目
  amount: number; // 金額
  created_at?: string;
}

export interface PayrollEmail {
  uuid: string;
  email: string;
  status: string;
  sent_at: string;
}

export interface PayrollFilter {
  period_uuid?: string; // 薪資期間 UUID
  user_uuid?: string; // 員工姓名
}

// 薪資基本設定表
export interface SalaryItem {
  id: number;
  name: string;
  type: number; // 1: 收入, 2: 扣除, 3: 獎金, 4: 不計入薪資
  is_default: boolean;
  is_required: boolean;
  default_value: number;
}

export const PayrollApi = {
  listPeriods: ({
    filter,
    pagination,
  }: {
    filter?: PayrollPeriodFilter;
    pagination?: Pagination;
  }) =>
    apiWrapper.get<{
      data: PayrollPeriod[];
      pagination: Pagination;
    }>('/v1/payrolls/periods', {
      params: {
        ...filter,
        ...pagination,
      },
    }),
  getPeriod: (uuid: string) =>
    apiWrapper.get<PayrollPeriod>(`/v1/payrolls/periods/${uuid}`),
  createPeriod: (payload: PayrollPeriod) =>
    apiWrapper.post<PayrollPeriod>('/v1/payrolls/periods', payload),
  updatePeriod: (payload: PayrollPeriod) =>
    apiWrapper.put<PayrollPeriod>(
      `/v1/payrolls/periods/${payload.uuid}`,
      payload
    ),
  deletePeriod: (uuid: string) =>
    apiWrapper.delete(`/v1/payrolls/periods/${uuid}`),
  // 薪資單
  listPayroll: ({
    filter,
    pagination,
  }: {
    filter?: PayrollFilter;
    pagination?: Pagination;
  }) =>
    apiWrapper.get<{
      data: Payroll[];
      pagination: Pagination;
    }>('/v1/payrolls', {
      params: {
        ...filter,
        ...pagination,
      },
    }),
  getPayroll: (uuid: string) => apiWrapper.get<Payroll>(`/v1/payrolls/${uuid}`),
  createPayroll: (payload: Payroll) =>
    apiWrapper.post<Payroll>('/v1/payrolls', payload),
  updatePayroll: (uuid: string, payload: Payroll) =>
    apiWrapper.put<Payroll>(`/v1/payrolls/${uuid}`, payload),

  sendMail: (period_uuid: string, user_uuids: string[]) =>
    apiWrapper.post('/v1/payrolls/mails', { period_uuid, user_uuids }),
};

export const SalaryItemApi = {
  listSalaryItems: () => apiWrapper.get<SalaryItem[]>('/v1/salary-items'),
};
