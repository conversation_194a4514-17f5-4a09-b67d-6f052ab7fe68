package domain

import (
	"encoding/json"
	"errors"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type OrderSource string

const (
	OrderSourceUnknown OrderSource = "unknown"
	OrderSourcePOS     OrderSource = "pos"
	OrderSourceWeb     OrderSource = "web"
)

type OrderStatus string

const (
	OrderStatusPending    OrderStatus = "pending"
	OrderStatusProcessing OrderStatus = "processing"
	OrderStatusOnHold     OrderStatus = "on-hold"
	OrderStatusCompleted  OrderStatus = "completed"
	OrderStatusCancelled  OrderStatus = "cancelled"
	OrderStatusRefunded   OrderStatus = "refunded"
	OrderStatusFailed     OrderStatus = "failed"
	OrderStatusVoid       OrderStatus = "void"
)

type OrderPayType string

const (
	OrderPayTypeCash       OrderPayType = "cash"
	OrderPayTypeCredit     OrderPayType = "credit"
	OrderPayTypeLinePay    OrderPayType = "line_pay"
	OrderPayTypeApplePay   OrderPayType = "apple_pay"
	OrderPayTypeGooglePay  OrderPayType = "google_pay"
	OrderPayTypeSamsungPay OrderPayType = "samsung_pay"
	OrderPayTypeWeChatPay  OrderPayType = "wechat_pay"
	OrderPayTypeAlipay     OrderPayType = "alipay"
	OrderPayTypePayPal     OrderPayType = "paypal"
)

type Order struct {
	ID            int64          `gorm:"primaryKey" json:"-"`
	UUID          string         `gorm:"<-:create" json:"uuid"`
	OrderNo       string         `gorm:"<-:create" json:"order_no"`
	CustomerID    null.Int       `gorm:"default:null" json:"-"`
	ItemQty       int            `json:"item_qty"`                      // 訂單商品總數量
	Rebate        float64        `json:"rebate"`                        // 折讓金額
	Discount      float64        `json:"discount"`                      // 折扣(%)
	Subtotal      float64        `json:"subtotal"`                      // 小計
	ServiceFee    float64        `json:"service_fee"`                   // 服務費
	Tax           float64        `json:"tax"`                           // 稅金
	TaxID         string         `json:"tax_id"`                        // 統一編號
	ShippingFee   float64        `json:"shipping_fee"`                  // 運費
	TotalDiscount float64        `json:"total_discount"`                // 折扣金額
	Total         float64        `json:"total"`                         // 總計
	PayType       OrderPayType   `gorm:"default:cash" json:"pay_type"`  // 付款方式
	Status        OrderStatus    `gorm:"default:pending" json:"status"` // 訂單狀態
	Notes         string         `json:"notes"`
	OrderAt       time.Time      `json:"order_at"`                            // 訂單日期
	Source        OrderSource    `gorm:"<-:create; default:unknown" json:"-"` // 訂單來源，pos, web(網路商店)
	UserAgent     string         `gorm:"<-:create" json:"-"`
	IPAddress     string         `gorm:"<-:create" json:"-"`
	VoidReason    string         `json:"void_reason"`
	VoidedAt      null.Time      `json:"voided_at"`
	VoidedBy      null.Int       `json:"voided_by"`
	CreatedByID   null.Int       `gorm:"<-:create;default:null" json:"-"`
	UpdatedByID   null.Int       `gorm:"default:null" json:"-"`
	CreatedAt     time.Time      `json:"-"`
	UpdatedAt     time.Time      `json:"-"`
	DeletedAt     gorm.DeletedAt `gorm:"<-:false;index" json:"-"`

	// Relations
	Customer   CustomerInfo   `gorm:"foreignKey:CustomerID" json:"customer"`
	OrderItems []OrderItem    `gorm:"foreignKey:OrderUUID;references:UUID" json:"order_items"`
	XeroSync   *XeroOrderSync `gorm:"foreignKey:OrderUUID;references:UUID" json:"xero_sync,omitempty"`
}

type OrderFilter struct {
	Search        string        `form:"search" json:"search"`
	CustomerUUID  string        `form:"customer_uuid" json:"customer_uuid"`
	Status        []OrderStatus `form:"status[]" json:"status"`
	ExcludeStatus []OrderStatus `form:"exclude_status[]" json:"exclude_status"`
	StartDate     string        `form:"start_date" json:"start_date"`
	EndDate       string        `form:"end_date" json:"end_date"`
}

type OrderItem struct {
	ID           int64          `gorm:"primaryKey" json:"-"`
	UUID         string         `gorm:"<-:create" json:"uuid"`
	OrderUUID    string         `gorm:"<-:create" json:"-"`
	ProductUUID  string         `gorm:"-" json:"product_uuid"`
	ProductID    int64          `gorm:"<-:create" json:"-"`
	Quantity     int            `json:"quantity"`      // 數量
	Price        float64        `json:"price"`         // 單價
	RegularPrice float64        `json:"regular_price"` // 原價
	Rebate       float64        `json:"rebate"`        // 折讓金額
	Discount     float64        `json:"discount"`      // 折扣
	IsFree       bool           `json:"is_free"`       // 是否免費
	Subtotal     float64        `json:"subtotal"`      // 小計
	Total        float64        `json:"total"`         // 總計
	CreatedAt    time.Time      `json:"-"`
	UpdatedAt    time.Time      `json:"-"`
	DeletedAt    gorm.DeletedAt `gorm:"<-:false;index" json:"-"`

	// Relations
	Product ProductInfo `gorm:"<-:false;foreignKey:ProductID" json:"product"`
}

type OrderItems []OrderItem

type OrderItemFilter struct {
	OrderID   int64 `form:"order_id"`
	ProductID int64 `form:"product_id"`
	Quantity  int   `form:"quantity"`
}

type OrderInfo struct {
	ID            int64        `json:"-"`
	UUID          string       `json:"uuid"`
	OrderNo       string       `json:"order_no"`
	CustomerID    int64        `json:"-"`
	Customer      CustomerInfo `gorm:"foreignKey:CustomerID" json:"customer"`
	ItemQty       int          `json:"item_qty"`                      // 訂單商品總數量
	Rebate        float64      `json:"rebate"`                        // 折讓金額
	Discount      float64      `json:"discount"`                      // 折扣(%)
	Subtotal      float64      `json:"subtotal"`                      // 小計
	ServiceFee    float64      `json:"service_fee"`                   // 服務費
	Tax           float64      `json:"tax"`                           // 稅金
	TaxID         string       `json:"tax_id"`                        // 統一編號
	ShippingFee   float64      `json:"shipping_fee"`                  // 運費
	TotalDiscount float64      `json:"total_discount"`                // 折扣金額
	Total         float64      `json:"total"`                         // 總計
	PayType       OrderPayType `gorm:"default:cash" json:"pay_type"`  // 付款方式
	Status        OrderStatus  `gorm:"default:pending" json:"status"` // 訂單狀態
	Notes         string       `json:"notes"`
	OrderAt       time.Time    `json:"order_at"` // 訂單日期
	VoidReason    string       `json:"void_reason,omitempty"`
	VoidedAt      null.Time    `json:"voided_at,omitempty"`

	XeroSync *XeroOrderSync `gorm:"foreignKey:OrderUUID;references:UUID" json:"xero_sync,omitempty"`
}

func (OrderInfo) TableName() string {
	return "orders"
}

type CheckoutPayload struct {
	OrderUUID    string       `gorm:"-" json:"-"`
	OrderAt      string       `json:"order_at"`
	CustomerUUID string       `gorm:"-" json:"customer_uuid"`
	CustomerID   null.Int     `json:"-"`
	PayType      OrderPayType `json:"pay_type"`
	OrderItems   OrderItems   `gorm:"-" json:"order_items"`
	ItemQty      int          `json:"item_qty"`
	Rebate       float64      `json:"rebate"`
	Discount     float64      `json:"discount"`
	Subtotal     float64      `json:"subtotal"`
	Total        float64      `json:"total"`
	Status       OrderStatus  `json:"-"`
	Notes        string       `json:"notes"`
	UserAgent    string
	IPAddress    string
	UpdatedByID  null.Int
}

func (CheckoutPayload) TableName() string {
	return "orders"
}

// XeroSyncStatus Xero 同步狀態枚舉
type XeroSyncStatus string

const (
	XeroSyncStatusPending XeroSyncStatus = "pending" // 待同步
	XeroSyncStatusSyncing XeroSyncStatus = "syncing" // 同步中
	XeroSyncStatusSuccess XeroSyncStatus = "success" // 同步成功
	XeroSyncStatusFailed  XeroSyncStatus = "failed"  // 同步失敗
	XeroSyncStatusVoided  XeroSyncStatus = "voided"  // 已作廢
)

// XeroOrderSync Order 與 Xero 同步記錄
type XeroOrderSync struct {
	ID            int64          `gorm:"primaryKey" json:"-"`
	UUID          string         `gorm:"<-:create" json:"uuid"`
	OrderUUID     string         `gorm:"<-:create;index" json:"order_uuid"`
	XeroInvoiceID string         `json:"xero_invoice_id,omitempty"`
	XeroInvoiceNo string         `json:"xero_invoice_no,omitempty"`
	SyncStatus    XeroSyncStatus `gorm:"default:pending" json:"sync_status"`
	ErrorMessage  string         `json:"error_message,omitempty"`
	LastSyncAt    *time.Time     `json:"last_sync_at,omitempty"`
	CreatedAt     time.Time      `gorm:"<-:create" json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
}

type VoidOrderPayload struct {
	RestoreStock bool   `json:"restore_stock"`
	Reason       string `json:"reason"`
	OtherReason  string `json:"other_reason"`
	UpdatedByID  int64  `json:"-"`
}

// UnmarshalJSON 為 OrderItems 實作自定義的 JSON 解析
func (items *OrderItems) UnmarshalJSON(data []byte) error {
	// 檢查是否為 null
	if string(data) == "null" {
		*items = nil
		return nil
	}

	// 檢查是否為陣列開頭
	if len(data) == 0 || data[0] != '[' {
		return errors.New("invalid order items format: expected array")
	}

	// 臨時的項目陣列
	var tempItems []OrderItem
	if err := json.Unmarshal(data, &tempItems); err != nil {
		return err
	}

	// 驗證每個項目
	for _, item := range tempItems {
		if item.Quantity <= 0 {
			return errors.New("quantity must be greater than 0")
		}
		if item.Price < 0 {
			return errors.New("price cannot be negative")
		}
	}

	*items = OrderItems(tempItems)
	return nil
}
