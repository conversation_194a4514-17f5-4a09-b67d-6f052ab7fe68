package service

import (
	"context"
	"cx/domain"
	"cx/repository"

	"gorm.io/gorm"
)

type UserGroupService interface {
	Create(ctx context.Context, userGroup *domain.UserGroup) error
	Update(ctx context.Context, payload *domain.UserGroupUpdatePayload) error
	Delete(ctx context.Context, id int64) error
	Fetch(ctx context.Context) ([]domain.UserGroup, error)
	GetByID(ctx context.Context, id int64) (*domain.UserGroup, error)
}

type userGroupService struct {
	db             *gorm.DB
	userGroupRepo  repository.UserGroupRepository
	permissionRepo repository.PermissionRepository
}

func NewUserGroupService(db *gorm.DB, userGroupRepo repository.UserGroupRepository, permissionRepo repository.PermissionRepository) UserGroupService {
	return &userGroupService{db, userGroupRepo, permissionRepo}
}

func (s *userGroupService) Create(ctx context.Context, userGroup *domain.UserGroup) error {
	return s.userGroupRepo.Create(ctx, userGroup)
}

func (s *userGroupService) Update(ctx context.Context, payload *domain.UserGroupUpdatePayload) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		userGroupRepo := repository.NewUserGroupRepository(tx)
		permissionRepo := repository.NewPermissionRepository(tx)
		permissionService := NewPermissionService(tx, permissionRepo)

		err := userGroupRepo.Update(ctx, &payload.UserGroup)
		if err != nil {
			return err
		}

		return permissionService.UpdateUserGroupPermissions(ctx, &domain.GroupPermissionUpdatePayload{
			GroupID:         payload.UserGroup.ID,
			PermissionCodes: payload.PermissionCodes,
			UpdatedBy:       payload.UpdatedBy,
		})
	})
}

func (s *userGroupService) Delete(ctx context.Context, id int64) error {
	return s.userGroupRepo.Delete(ctx, id)
}

func (s *userGroupService) Fetch(ctx context.Context) ([]domain.UserGroup, error) {
	return s.userGroupRepo.Fetch(ctx)
}

func (s *userGroupService) GetByID(ctx context.Context, id int64) (*domain.UserGroup, error) {
	return s.userGroupRepo.GetByID(ctx, id)
}
