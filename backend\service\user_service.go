package service

import (
	"context"
	"cx/domain"
	"cx/repository"
	"cx/utils"
	"errors"

	"gorm.io/gorm"
)

var (
	ErrUserDuplicated     = errors.New("account already exists")
	ErrUserDisabled       = errors.New("user is disabled")
	ErrInvalidCredentials = errors.New("invalid credentials")
	ErrUserNotFound       = errors.New("user not found")
)

type UserService interface {
	Create(ctx context.Context, user *domain.UserCreatePayload) error
	Update(ctx context.Context, uuid string, user *domain.UserUpdatePayload) error
	Verify(ctx context.Context, req *domain.LoginRequest) (*domain.User, error)
	Fetch(ctx context.Context) ([]domain.User, error)
	GetByID(ctx context.Context, id int64) (*domain.User, error)
	GetByUUID(ctx context.Context, uuid string) (*domain.User, error)
	GetByUsername(ctx context.Context, username string) (*domain.User, error)
	Delete(ctx context.Context, uuid string) error
}

type userService struct {
	db       *gorm.DB
	userRepo repository.UserRepository
}

func NewUserService(db *gorm.DB, userRepo repository.UserRepository) UserService {
	return &userService{db, userRepo}
}

func (s *userService) Create(ctx context.Context, user *domain.UserCreatePayload) error {
	if u, err := s.userRepo.GetByUsername(ctx, user.Username); err == nil && u != nil {
		return ErrUserDuplicated
	}

	hashedPassword, err := utils.GenerateHashedPassword(user.Password)
	if err != nil {
		return err
	}

	user.Password = hashedPassword

	return s.userRepo.Create(ctx, user)
}

func (s *userService) Update(ctx context.Context, uuid string, user *domain.UserUpdatePayload) error {
	if user.Password != "" {
		hashedPassword, err := utils.GenerateHashedPassword(user.Password)
		if err != nil {
			return err
		}

		user.Password = hashedPassword
	}

	return s.userRepo.Update(ctx, uuid, user)
}

func (s *userService) Verify(ctx context.Context, req *domain.LoginRequest) (*domain.User, error) {
	user, err := s.userRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		return nil, err
	}

	if !user.IsActive {
		return nil, ErrUserDisabled
	}

	if !utils.VerifyPassword(req.Password, user.Password) {
		return nil, ErrInvalidCredentials
	}

	return user, nil
}

func (s *userService) Fetch(ctx context.Context) ([]domain.User, error) {
	users, err := s.userRepo.Fetch(ctx)
	if err != nil {
		return nil, err
	}

	return users, nil
}

func (s *userService) GetByID(ctx context.Context, id int64) (*domain.User, error) {
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	return user, nil
}

func (s *userService) GetByUUID(ctx context.Context, uuid string) (*domain.User, error) {
	user, err := s.userRepo.GetByUUID(ctx, uuid)
	if err != nil {
		return nil, err
	}

	return user, nil
}

func (s *userService) GetByUsername(ctx context.Context, username string) (*domain.User, error) {
	user, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		return nil, err
	}

	return user, nil
}

func (s *userService) Delete(ctx context.Context, uuid string) error {
	return s.userRepo.Delete(ctx, uuid)
}
