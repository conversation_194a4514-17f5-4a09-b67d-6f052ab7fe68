import{J as s,E as P,bu as R,bv as z,d as O,aF as V,b0 as D,u as F,r as u,s as X,o as m,k as b,f as v,i as c,l as g,a as S,t as p,b as l,h as $,m as k,y as J,z as L}from"./index.b4716878.js";import{Q as Y}from"./QExpansionItem.7b8e22a8.js";import{Q as j}from"./QPage.cc993543.js";import{u as G}from"./use-quasar.59b22ad6.js";import{X as H}from"./xero.1823d800.js";import"./QItemSection.a2ef2d56.js";import"./QItemLabel.88fb9012.js";const K=[s("g",{transform:"translate(20 50)"},[s("rect",{x:"-10",y:"-30",width:"20",height:"60",fill:"currentColor",opacity:"0.6"},[s("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])]),s("g",{transform:"translate(50 50)"},[s("rect",{x:"-10",y:"-30",width:"20",height:"60",fill:"currentColor",opacity:"0.8"},[s("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0.1s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])]),s("g",{transform:"translate(80 50)"},[s("rect",{x:"-10",y:"-30",width:"20",height:"60",fill:"currentColor",opacity:"0.9"},[s("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0.2s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])])];var U=P({name:"QSpinnerFacebook",props:R,setup(r){const{cSize:n,classes:a}=z(r);return()=>s("svg",{class:a.value,width:n.value,height:n.value,viewBox:"0 0 100 100",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid"},K)}});const f=r=>{if(r!=null)return Array.isArray(r)?r[0]||void 0:r},A=r=>{const n=f(r);return typeof n=="string"&&n.length>0},W={class:"text-center"},Z={key:1,class:"text-h6 q-mt-md"},ee={key:2,class:"text-center"},te={class:"text-h6 q-mt-md text-positive"},re={class:"q-mt-md"},se={key:3,class:"text-center"},oe={class:"text-h6 q-mt-md text-negative"},ae={class:"q-mt-md text-body2"},ne={class:"text-caption"},ie={class:"q-mt-md q-gutter-sm"},ve=O({__name:"XeroRedirectPage",setup(r){const n=V(),a=D(),w=G(),{t:d}=F(),h=u(!0),C=u(!1),q=u(!1),Q=u(""),T=u(""),y=u(""),E=async()=>{var e;try{const t=f(a.query.code),I=f(a.query.state),_=f(a.query.error);if(console.log("OAuth Callback Debug:",{code:t?"present":"missing",state:I?"present":"missing",error_param:_,savedState:localStorage.getItem("xero_oauth_state")?"present":"missing"}),_)throw new Error(d("xero.redirect.error.messages.authError",{error:_}));if(!A(a.query.code)||!A(a.query.state))throw new Error(d("xero.redirect.error.messages.missingParams"));const B=t,i=I,o=localStorage.getItem("xero_oauth_state");console.log("State Validation:",{receivedState:i,savedState:o,match:o===i,receivedStateLength:i.length,savedStateLength:o==null?void 0:o.length}),o?o!==i?(console.error("State mismatch detected:",{expected:o,received:i,expectedType:typeof o,receivedType:typeof i}),console.warn("State mismatch, but continuing with backend validation")):console.log("State validation passed successfully"):console.warn("No saved state found in localStorage, attempting backend validation");const N=await H.callback(B,i);console.log("Callback Response:",N),localStorage.removeItem("xero_oauth_state"),T.value=((e=N.result)==null?void 0:e.tenant_name)||d("xero.redirect.error.messages.unknownOrg"),C.value=!0,w.notify({position:"top",type:"positive",message:d("xero.redirect.success.notification")})}catch(t){console.error("OAuth Callback Error:",t),q.value=!0,Q.value=(t==null?void 0:t.message)||d("xero.redirect.error.title"),y.value=JSON.stringify({url:window.location.href,query:a.query,savedState:localStorage.getItem("xero_oauth_state"),error:t==null?void 0:t.message,timestamp:new Date().toISOString()},null,2)}finally{h.value=!1}},x=()=>{n.push("/admin/dashboard/xero/setup")},M=()=>{localStorage.removeItem("xero_oauth_state"),w.notify({position:"top",type:"info",message:d("xero.redirect.debug.stateCleared")}),x()};return X(()=>{E()}),(e,t)=>(m(),b(j,{class:"flex flex-center"},{default:v(()=>[c("div",W,[h.value?(m(),b(U,{key:0,color:"primary",size:"50px"})):g("",!0),h.value?(m(),S("div",Z,p(e.$t("xero.redirect.processing")),1)):g("",!0),C.value?(m(),S("div",ee,[l($,{name:"check_circle",color:"positive",size:"50px"}),c("div",te,p(e.$t("xero.redirect.success.title")),1),c("div",re,p(e.$t("xero.redirect.success.connectedTo",{tenantName:T.value})),1),l(k,{color:"primary",label:e.$t("xero.redirect.success.backToSettings"),class:"q-mt-md",onClick:x},null,8,["label"])])):g("",!0),q.value?(m(),S("div",se,[l($,{name:"error",color:"negative",size:"50px"}),c("div",oe,p(e.$t("xero.redirect.error.title")),1),c("div",ae,p(Q.value),1),y.value?(m(),b(Y,{key:0,icon:"bug_report",label:e.$t("xero.redirect.debug.title"),class:"q-mt-md"},{default:v(()=>[l(L,{class:"q-mt-sm"},{default:v(()=>[l(J,{class:"text-left"},{default:v(()=>[c("pre",ne,p(y.value),1)]),_:1})]),_:1})]),_:1},8,["label"])):g("",!0),c("div",ie,[l(k,{color:"primary",label:e.$t("xero.redirect.error.retry"),onClick:x},null,8,["label"]),l(k,{color:"secondary",label:e.$t("xero.redirect.error.clearState"),onClick:M},null,8,["label"])])])):g("",!0)])]),_:1}))}});export{ve as default};
