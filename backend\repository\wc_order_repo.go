package repository

import (
	"cx/domain"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

// WcOrderRepository WooCommerce 訂單儲存庫介面
// 基於 wcapi.php 的查詢結構實現
type WcOrderRepository interface {
	GetByID(id uint) (*domain.WcOrder, error)
	List(filter *domain.WcOrderFilter) ([]domain.WcOrder, int, error)
	UpdateStatus(id uint, status string) error
	UpdateCustomerNote(id uint, customerNote string) error
	GetPendingOrders() ([]domain.WcOrder, error)
	GetHistoryOrders(filter *domain.WcOrderFilter, pagination *domain.Pagination) ([]domain.WcOrder, error)
	GetPaidOrders() ([]domain.WcOrder, error)
}

type wcOrderRepository struct {
	db *gorm.DB
}

func NewWcOrderRepository(db *gorm.DB) WcOrderRepository {
	return &wcOrderRepository{db: db}
}

// GetByID 獲取單個訂單詳情 - 對應 wcapi.php 的 getOrderDetail 方法
func (r *wcOrderRepository) GetByID(id uint) (*domain.WcOrder, error) {
	var order domain.WcOrder

	opts := []string{
		"wp_wc_orders.id",
		"wp_wc_orders.date_created_gmt as date_created",
		"wp_wc_orders.status",
		"wp_wc_orders.total_amount as total",
		"wp_wc_orders.payment_method_title",
		"wp_wc_orders.customer_note",
		"wp_wc_orders.customer_id",
		"wp_wc_orders.currency",
		"s.shipping_total",
		"s.tax_total as total_tax",
		"s.net_total",
		"a.first_name as billing_first_name",
		"a.last_name as billing_last_name",
		"a.company as billing_company",
		"a.address_1 as billing_address_1",
		"a.address_2 as billing_address_2",
		"a.city as billing_city",
		"a.state as billing_state",
		"a.postcode as billing_postcode",
		"a.country as billing_country",
		"a.email as billing_email",
		"a.phone as billing_phone",
		"a2.first_name as shipping_first_name",
		"a2.last_name as shipping_last_name",
		"a2.company as shipping_company",
		"a2.address_1 as shipping_address_1",
		"a2.address_2 as shipping_address_2",
		"a2.city as shipping_city",
		"a2.state as shipping_state",
		"a2.postcode as shipping_postcode",
		"a2.country as shipping_country",
	}

	if err := r.db.Select(opts).
		Joins("LEFT JOIN wp_wc_order_stats s ON wp_wc_orders.id = s.order_id").
		Joins("LEFT JOIN wp_wc_order_addresses a ON wp_wc_orders.id = a.order_id AND a.address_type = 'billing'").
		Joins("LEFT JOIN wp_wc_order_addresses a2 ON wp_wc_orders.id = a2.order_id AND a2.address_type = 'shipping'").
		Where("wp_wc_orders.id = ? AND wp_wc_orders.type = 'shop_order'", id).
		Find(&order).Error; err != nil {
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	fmt.Println("Address:", order.BillingAddress1)

	if order.ID == 0 {
		return nil, fmt.Errorf("order not found")
	}

	// 獲取訂單商品 - 對應 wcapi.php 中的商品查詢
	itemsQuery := `
		SELECT
			i.order_item_name as name,
			CAST(m1.meta_value AS UNSIGNED) as quantity,
			CAST(m2.meta_value AS DECIMAL(10,2)) as total,
			CASE
				WHEN CAST(m1.meta_value AS UNSIGNED) > 0
				THEN CAST(m2.meta_value AS DECIMAL(10,2)) / CAST(m1.meta_value AS UNSIGNED)
				ELSE 0
			END as price
		FROM wp_woocommerce_order_items i
		LEFT JOIN wp_woocommerce_order_itemmeta m1 ON i.order_item_id = m1.order_item_id AND m1.meta_key = '_qty'
		LEFT JOIN wp_woocommerce_order_itemmeta m2 ON i.order_item_id = m2.order_item_id AND m2.meta_key = '_line_total'
		WHERE i.order_id = ? AND i.order_item_type = 'line_item'
	`

	if err := r.db.Raw(itemsQuery, id).Scan(&order.Items).Error; err != nil {
		return nil, fmt.Errorf("failed to get order items: %w", err)
	}

	// 獲取 shipping 方式資訊 - 包含 Australia Post 詳細資訊
	shippingQuery := `
		SELECT
			i.order_item_name as shipping_method_title,
			COALESCE(m1.meta_value, '') as shipping_method,
			COALESCE(m2.meta_value, '') as shipping_method_instance_id,
			COALESCE(m3.meta_value, '') as shipping_cost,
			COALESCE(m4.meta_value, '') as shipping_taxes
		FROM wp_woocommerce_order_items i
		LEFT JOIN wp_woocommerce_order_itemmeta m1 ON i.order_item_id = m1.order_item_id AND m1.meta_key = 'method_id'
		LEFT JOIN wp_woocommerce_order_itemmeta m2 ON i.order_item_id = m2.order_item_id AND m2.meta_key = 'instance_id'
		LEFT JOIN wp_woocommerce_order_itemmeta m3 ON i.order_item_id = m3.order_item_id AND m3.meta_key = 'cost'
		LEFT JOIN wp_woocommerce_order_itemmeta m4 ON i.order_item_id = m4.order_item_id AND m4.meta_key = 'taxes'
		WHERE i.order_id = ? AND i.order_item_type = 'shipping'
		LIMIT 1
	`

	var shippingInfo struct {
		ShippingMethodTitle      string `json:"shipping_method_title"`
		ShippingMethod           string `json:"shipping_method"`
		ShippingMethodInstanceID string `json:"shipping_method_instance_id"`
		ShippingCost             string `json:"shipping_cost"`
		ShippingTaxes            string `json:"shipping_taxes"`
	}

	if err := r.db.Raw(shippingQuery, id).Scan(&shippingInfo).Error; err == nil {
		order.ShippingMethodTitle = shippingInfo.ShippingMethodTitle
		order.ShippingMethod = shippingInfo.ShippingMethod

		// 解析 Australia Post 運送方式
		order.ShippingMethod = r.parseAustraliaPostShippingMethod(shippingInfo.ShippingMethod, shippingInfo.ShippingMethodTitle)
	}

	return &order, nil
}

// List 獲取訂單列表 - 對應 wcapi.php 的 getOrders 方法
func (r *wcOrderRepository) List(filter *domain.WcOrderFilter) ([]domain.WcOrder, int, error) {
	var orders []domain.WcOrder
	var total int64

	// 基於 wcapi.php 的查詢結構
	baseQuery := `
		SELECT
			o.id,
			o.date_created_gmt as date_created,
			o.status,
			o.total_amount as total,
			o.payment_method_title,
			a.first_name as billing_first_name,
			a.last_name as billing_last_name,
			a.email as billing_email,
			COALESCE(si.order_item_name, '') as shipping_method_title,
			COALESCE(sim.meta_value, '') as shipping_method
		FROM wp_wc_orders o
		LEFT JOIN wp_wc_order_addresses a ON o.id = a.order_id AND a.address_type = 'billing'
		LEFT JOIN wp_woocommerce_order_items si ON o.id = si.order_id AND si.order_item_type = 'shipping'
		LEFT JOIN wp_woocommerce_order_itemmeta sim ON si.order_item_id = sim.order_item_id AND sim.meta_key = 'method_id'
		WHERE o.type = 'shop_order'
	`

	countQuery := `
		SELECT COUNT(*)
		FROM wp_wc_orders o
		WHERE o.type = 'shop_order'
	`

	// 構建 WHERE 條件
	whereClause := ""
	args := []interface{}{}

	if filter.Status != "" {
		whereClause += " AND o.status = ?"
		// 確保狀態包含 'wc-' 前綴
		status := filter.Status
		if !strings.HasPrefix(status, "wc-") {
			status = "wc-" + status
		}
		args = append(args, status)
	}

	if filter.CustomerID > 0 {
		whereClause += " AND o.customer_id = ?"
		args = append(args, filter.CustomerID)
	}

	if !filter.DateFrom.IsZero() {
		whereClause += " AND DATE(o.date_created_gmt) >= DATE(?)"
		args = append(args, filter.DateFrom.Format("2006-01-02"))
	}

	if !filter.DateTo.IsZero() {
		whereClause += " AND DATE(o.date_created_gmt) <= DATE(?)"
		args = append(args, filter.DateTo.Format("2006-01-02"))
	}

	// 執行計數查詢
	if err := r.db.Raw(countQuery+whereClause, args...).Scan(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count orders: %w", err)
	}

	// 添加分頁和排序
	finalQuery := baseQuery + whereClause + " ORDER BY o.date_created_gmt DESC"

	if filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		finalQuery += " LIMIT ? OFFSET ?"
		args = append(args, filter.PageSize, offset)
	}

	// 執行查詢
	if err := r.db.Raw(finalQuery, args...).Scan(&orders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list orders: %w", err)
	}

	// 處理訂單狀態 - 移除 'wc-' 前綴，並解析 shipping 方法
	for i := range orders {
		orders[i].Status = domain.OrderStatus(strings.TrimPrefix(string(orders[i].Status), "wc-"))
		orders[i].OrderNumber = fmt.Sprintf("%d", orders[i].ID)
		// 解析 Australia Post 運送方式
		orders[i].ShippingMethod = r.parseAustraliaPostShippingMethod(orders[i].ShippingMethod, orders[i].ShippingMethodTitle)
	}

	return orders, int(total), nil
}

// UpdateStatus 更新訂單狀態 - 對應 wcapi.php 的 updateOrderStatus 方法
func (r *wcOrderRepository) UpdateStatus(id uint, status string) error {
	// 確保狀態包含 'wc-' 前綴
	if !strings.HasPrefix(status, "wc-") {
		status = "wc-" + status
	}

	query := `
		UPDATE wp_wc_orders
		SET status = ?
		WHERE id = ? AND type = 'shop_order'
	`

	result := r.db.Exec(query, status, id)
	if result.Error != nil {
		return fmt.Errorf("failed to update order status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order not found or no changes made")
	}

	return nil
}

// UpdateCustomerNote 更新訂單客戶備註
func (r *wcOrderRepository) UpdateCustomerNote(id uint, customerNote string) error {
	// 先檢查訂單是否存在
	var count int64
	checkQuery := `SELECT COUNT(*) FROM wp_wc_orders WHERE id = ? AND type = 'shop_order'`
	if err := r.db.Raw(checkQuery, id).Scan(&count).Error; err != nil {
		return fmt.Errorf("failed to check order existence: %w", err)
	}

	if count == 0 {
		return fmt.Errorf("order with id %d not found", id)
	}

	// 參考 wcapi.php 的查詢條件，加上 type = 'shop_order'
	query := `
		UPDATE wp_wc_orders
		SET customer_note = ?
		WHERE id = ? AND type = 'shop_order'
	`

	result := r.db.Exec(query, customerNote, id)
	if result.Error != nil {
		return fmt.Errorf("failed to update customer note: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("order not found or no changes made")
	}

	return nil
}

// GetPendingOrders 獲取待處理訂單 - 對應 wcapi.php 的 checkNewOrders 方法
func (r *wcOrderRepository) GetPendingOrders() ([]domain.WcOrder, error) {
	var orders []domain.WcOrder

	// 基於 wcapi.php 的 checkNewOrders 查詢
	query := `
		SELECT
			o.id,
			o.date_created_gmt as date_created,
			o.status,
			o.total_amount as total,
			o.payment_method_title,
			a.first_name as billing_first_name,
			a.last_name as billing_last_name,
			a.email as billing_email,
			COALESCE(si.order_item_name, '') as shipping_method_title,
			COALESCE(sim.meta_value, '') as shipping_method
		FROM wp_wc_orders o
		LEFT JOIN wp_wc_order_addresses a ON o.id = a.order_id AND a.address_type = 'billing'
		LEFT JOIN wp_woocommerce_order_items si ON o.id = si.order_id AND si.order_item_type = 'shipping'
		LEFT JOIN wp_woocommerce_order_itemmeta sim ON si.order_item_id = sim.order_item_id AND sim.meta_key = 'method_id'
		WHERE o.type = 'shop_order'
		AND o.status NOT IN ('wc-cancelled', 'wc-completed', 'wc-refunded')
		ORDER BY o.date_created_gmt DESC
	`

	if err := r.db.Raw(query).Scan(&orders).Error; err != nil {
		return nil, fmt.Errorf("failed to get pending orders: %w", err)
	}

	// 處理訂單狀態和編號，並解析 shipping 方法
	for i := range orders {
		orders[i].Status = domain.OrderStatus(strings.TrimPrefix(string(orders[i].Status), "wc-"))
		orders[i].OrderNumber = fmt.Sprintf("%d", orders[i].ID)
		// 解析 Australia Post 運送方式
		orders[i].ShippingMethod = r.parseAustraliaPostShippingMethod(orders[i].ShippingMethod, orders[i].ShippingMethodTitle)
	}

	return orders, nil
}

// GetHistoryOrders 獲取歷史訂單 - 對應 wcapi.php 的 getHistoryOrders 方法
func (r *wcOrderRepository) GetHistoryOrders(filter *domain.WcOrderFilter, pagination *domain.Pagination) ([]domain.WcOrder, error) {
	var orders []domain.WcOrder

	// 基於 wcapi.php 的 getHistoryOrders 查詢
	// query := `
	// 	SELECT
	// 		o.id,
	// 		o.date_created_gmt as date_created,
	// 		o.status,
	// 		o.total_amount as total,
	// 		o.payment_method_title,
	// 		a.first_name as billing_first_name,
	// 		a.last_name as billing_last_name,
	// 		a.email as billing_email,
	// 		COALESCE(si.order_item_name, '') as shipping_method_title,
	// 		COALESCE(sim.meta_value, '') as shipping_method
	// 	FROM wp_wc_orders o
	// 	LEFT JOIN wp_wc_order_addresses a ON o.id = a.order_id AND a.address_type = 'billing'
	// 	LEFT JOIN wp_woocommerce_order_items si ON o.id = si.order_id AND si.order_item_type = 'shipping'
	// 	LEFT JOIN wp_woocommerce_order_itemmeta sim ON si.order_item_id = sim.order_item_id AND sim.meta_key = 'method_id'
	// 	WHERE o.type = 'shop_order'
	// 	ORDER BY o.date_created_gmt DESC
	// `

	// if err := r.db.Raw(query).Scan(&orders).Error; err != nil {
	// 	return nil, fmt.Errorf("failed to get history orders: %w", err)
	// }

	tx := r.db.Model(&domain.WcOrder{}).
		Select("wp_wc_orders.id", "wp_wc_orders.date_created_gmt as date_created", "wp_wc_orders.status",
			"wp_wc_orders.total_amount as total", "wp_wc_orders.payment_method_title",
			"wp_wc_orders.currency", "wp_wc_orders.customer_note", "wp_wc_orders.customer_id",
			"a.first_name as billing_first_name", "a.last_name as billing_last_name", "a.email as billing_email",
			"si.order_item_name as shipping_method_title", "COALESCE(sim.meta_value, '') as shipping_method").
		Joins("LEFT JOIN wp_wc_order_addresses a ON wp_wc_orders.id = a.order_id AND a.address_type = 'billing'").
		Joins("LEFT JOIN wp_woocommerce_order_items si ON wp_wc_orders.id = si.order_id AND si.order_item_type = 'shipping'").
		Joins("LEFT JOIN wp_woocommerce_order_itemmeta sim ON si.order_item_id = sim.order_item_id AND sim.meta_key = 'method_id'").
		Where("wp_wc_orders.type = 'shop_order'")

	if err := tx.Count(&pagination.RowsNumber).Error; err != nil {
		return nil, fmt.Errorf("failed to count history orders: %w", err)
	}

	tx = pagination.SetPaginationToDB(tx, "wp_wc_orders.date_created_gmt", true)

	if err := tx.Find(&orders).Error; err != nil {
		return nil, fmt.Errorf("failed to get history orders: %w", err)
	}

	// 處理訂單狀態和編號，並解析 shipping 方法
	for i := range orders {
		orders[i].Status = domain.OrderStatus(strings.TrimPrefix(string(orders[i].Status), "wc-"))
		orders[i].OrderNumber = fmt.Sprintf("%d", orders[i].ID)
		// 解析 Australia Post 運送方式
		orders[i].ShippingMethod = r.parseAustraliaPostShippingMethod(orders[i].ShippingMethod, orders[i].ShippingMethodTitle)
	}

	return orders, nil
}

// parseAustraliaPostShippingMethod 解析 Australia Post 運送方式
// 根據 method_id 和 shipping_method_title 判斷具體的運送方式
func (r *wcOrderRepository) parseAustraliaPostShippingMethod(methodID, methodTitle string) string {
	// 如果 method_id 包含 australia_post，則進一步解析
	if strings.Contains(strings.ToLower(methodID), "australia_post") {
		// 根據 method_title 判斷具體的運送方式
		titleLower := strings.ToLower(methodTitle)

		if strings.Contains(titleLower, "express") || strings.Contains(titleLower, "express post") {
			return "australia_post_express"
		} else if strings.Contains(titleLower, "parcel") || strings.Contains(titleLower, "parcel post") {
			return "australia_post_parcel"
		}

		// 如果無法從 title 判斷，檢查 method_id 的詳細資訊
		if strings.Contains(strings.ToLower(methodID), "express") {
			return "australia_post_express"
		} else if strings.Contains(strings.ToLower(methodID), "parcel") {
			return "australia_post_parcel"
		}

		// 預設返回 Australia Post 通用標識
		return "australia_post"
	}

	// 如果不是 Australia Post，返回原始的 method_id
	return methodID
}

// GetPaidOrders 獲取已付款但尚未創建Xero發票的訂單
func (r *wcOrderRepository) GetPaidOrders() ([]domain.WcOrder, error) {
	var orders []domain.WcOrder

	// 查詢已付款狀態的訂單，且沒有成功的Xero同步記錄
	query := `
		SELECT
			o.id,
			o.date_created_gmt as date_created,
			o.status,
			o.total_amount as total,
			o.payment_method_title,
			a.first_name as billing_first_name,
			a.last_name as billing_last_name,
			a.email as billing_email,
			COALESCE(si.order_item_name, '') as shipping_method_title,
			COALESCE(sim.meta_value, '') as shipping_method
		FROM wp_wc_orders o
		LEFT JOIN wp_wc_order_addresses a ON o.id = a.order_id AND a.address_type = 'billing'
		LEFT JOIN wp_woocommerce_order_items si ON o.id = si.order_id AND si.order_item_type = 'shipping'
		LEFT JOIN wp_woocommerce_order_itemmeta sim ON si.order_item_id = sim.order_item_id AND sim.meta_key = 'method_id'
		WHERE o.type = 'shop_order'
		AND o.status IN ('wc-processing', 'wc-packing', 'wc-shipping', 'wc-completed')
		ORDER BY o.date_created_gmt DESC
	`

	if err := r.db.Raw(query).Scan(&orders).Error; err != nil {
		return nil, fmt.Errorf("failed to get paid orders without Xero invoice: %w", err)
	}

	// 處理訂單狀態和編號，並解析 shipping 方法
	for i := range orders {
		orders[i].Status = domain.OrderStatus(strings.TrimPrefix(string(orders[i].Status), "wc-"))
		orders[i].OrderNumber = fmt.Sprintf("%d", orders[i].ID)
		// 解析 Australia Post 運送方式
		orders[i].ShippingMethod = r.parseAustraliaPostShippingMethod(orders[i].ShippingMethod, orders[i].ShippingMethodTitle)
	}

	return orders, nil
}
