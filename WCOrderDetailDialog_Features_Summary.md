# WCOrderDetailDialog 新增功能總結

## 概述
為 `frontend/src/pages/pos/components/WCOrderDetailDialog.vue` 添加了 Xero 同步、發票列印和發票發送功能，參考了 `frontend/src/components/OrderDetailDialog.vue` 的實現。

## 新增功能

### 1. Xero 同步狀態顯示
- 在訂單狀態為 `wc-completed` 時顯示 Xero 同步狀態
- 包含同步狀態圖標和顏色指示
- 顯示發票編號（如果已同步）
- 提供手動同步按鈕

### 2. 發票列印功能
- 只有在訂單已成功同步到 Xero 且狀態為 `wc-completed` 時才可用
- 使用 `usePrintInvoice` composable 來處理列印邏輯
- 通過 Xero API 獲取 PDF 並在新視窗中列印

### 3. 發票發送功能
- 只有在訂單已成功同步到 Xero 且狀態不是 `wc-cancelled` 時才可用
- 使用 `EmailInputDialog` 組件來輸入收件人 email
- 支援客戶 email 和預設 email 的自動填入
- 包含詳細的錯誤處理（速率限制、無效 email 等）

## 前端修改

### API 接口更新
- `frontend/src/api/order.ts`:
  - 新增 `WCXeroOrderSync` 接口
  - 在 `WCOrder` 接口中添加 `xero_sync` 字段
  - 新增 `syncWCOrderToXero` API 方法

### 組件更新
- `frontend/src/pages/pos/components/WCOrderDetailDialog.vue`:
  - 導入必要的依賴（XeroApi, usePrintInvoice, EmailInputDialog 等）
  - 添加狀態管理變數（isSyncing, emailDialogVisible, xeroConfig）
  - 實現 Xero 同步相關方法
  - 添加發票列印和發送功能
  - 更新模板以顯示新功能

## 後端修改

### API 端點
- `backend/controller/wc_order_controller.go`:
  - 新增 `SyncOrderToXeroHandler` 方法
  - 添加 `/v1/wc-orders/:id/sync-xero` 路由

### 服務層
- `backend/service/xero_service.go`:
  - 新增 `SyncWCOrderToXero` 方法
  - 實現 WooCommerce 訂單到 Xero 的同步邏輯

### 數據模型
- `backend/domain/wc_order.go`:
  - 在 `WcOrder` 結構體中添加 `XeroSync` 關聯

### 數據庫層
- `backend/repository/wc_order_repo.go`:
  - 在 `GetByID` 方法中添加 XeroSync 的預載

### 路由配置
- `backend/routes/routes.go`:
  - 更新 `NewWcOrderController` 調用以傳遞 `xeroService`

## 功能特點

### 狀態管理
- 同步狀態：pending, syncing, success, failed, voided
- 根據狀態顯示不同的圖標和顏色
- 防止重複同步已成功的訂單

### 錯誤處理
- 詳細的錯誤訊息顯示
- 特殊處理 Xero API 限制（如每日 email 發送限制）
- 用戶友好的錯誤提示

### 用戶體驗
- 載入狀態指示
- 按鈕禁用邏輯
- 自動填入 email 地址
- 成功操作的確認通知

## 使用流程

1. **查看訂單詳情**：打開 WCOrderDetailDialog
2. **同步到 Xero**：對於已完成的訂單，點擊同步按鈕
3. **列印發票**：同步成功後，可以列印發票
4. **發送 Email**：同步成功後，可以發送發票 email

## 注意事項

- 只有狀態為 `wc-completed` 的訂單才能同步到 Xero
- 發票列印和發送功能需要訂單已成功同步到 Xero
- Email 發送功能會檢查 Xero API 的限制
- 所有操作都有適當的錯誤處理和用戶反饋
