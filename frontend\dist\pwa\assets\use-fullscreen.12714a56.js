import{r as v,bs as b,w as f,bt as p,bb as F,s as y,a1 as h,K as x}from"./index.b4716878.js";let s=0;const E={fullscreen:<PERSON><PERSON>an,noRouteFullscreenExit:<PERSON>olean},g=["update:fullscreen","fullscreen"];function w(){const d=x(),{props:u,emit:m,proxy:e}=d;let n,o,a;const l=v(!1);b(d)===!0&&f(()=>e.$route.fullPath,()=>{u.noRouteFullscreenExit!==!0&&t()}),f(()=>u.fullscreen,r=>{l.value!==r&&c()}),f(l,r=>{m("update:fullscreen",r),m("fullscreen",r)});function c(){l.value===!0?t():i()}function i(){l.value!==!0&&(l.value=!0,a=e.$el.parentNode,a.replaceChild(o,e.$el),document.body.appendChild(e.$el),s++,s===1&&document.body.classList.add("q-body--fullscreen-mixin"),n={handler:t},p.add(n))}function t(){l.value===!0&&(n!==void 0&&(p.remove(n),n=void 0),a.replaceChild(e.$el,o),l.value=!1,s=Math.max(0,s-1),s===0&&(document.body.classList.remove("q-body--fullscreen-mixin"),e.$el.scrollIntoView!==void 0&&setTimeout(()=>{e.$el.scrollIntoView()})))}return F(()=>{o=document.createElement("span")}),y(()=>{u.fullscreen===!0&&i()}),h(t),Object.assign(e,{toggleFullscreen:c,setFullscreen:i,exitFullscreen:t}),{inFullscreen:l,toggleFullscreen:c}}export{g as a,w as b,E as u};
