package utils

import (
	"strconv"
	"strings"
)

func StrToInt(str string) int {
	if str == "" {
		return 0
	}

	val, _ := strconv.Atoi(str)

	return val
}

func ParseInt64(str string) int64 {
	if str == "" {
		return 0
	}

	val, _ := strconv.ParseInt(str, 10, 64)

	return val
}

func ConvertWPSlug(str string) string {
	// 移除或替換特殊字符
	// 常見的需要移除的特殊字符：+, &, %, $, #, @, !, ?, *, (, ), [, ], {, }, |, \, /, :, ;, ", ', <, >, =, ~, `
	specialChars := []string{"+", "&", "%", "$", "#", "@", "!", "?", "*", "(", ")", "[", "]", "{", "}", "|", "\\", "/", ":", ";", "\"", "'", "<", ">", "=", "~", "`"}

	for _, char := range specialChars {
		str = strings.ReplaceAll(str, char, "")
	}

	// 移除 Unicode 特殊字符（如 𝝅）
	// 只保留基本的字母、數字、空格和連字符
	var result strings.Builder
	for _, r := range str {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == ' ' || r == '-' {
			result.WriteRune(r)
		}
		// 其他字符（包括 Unicode 特殊字符）都被忽略
	}
	str = result.String()

	// 將多個連續空格替換為單個空格
	str = strings.Join(strings.Fields(str), " ")

	// 將空格替換為 -
	str = strings.ReplaceAll(str, " ", "-")

	// 將英文單字轉換為小寫
	str = strings.ToLower(str)

	// 移除開頭和結尾的連字符
	str = strings.Trim(str, "-")

	// 將多個連續的連字符替換為單個連字符
	for strings.Contains(str, "--") {
		str = strings.ReplaceAll(str, "--", "-")
	}

	return str
}
