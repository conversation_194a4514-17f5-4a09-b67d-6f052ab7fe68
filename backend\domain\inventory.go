package domain

import (
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type InventoryTransactionType string

const (
	InventoryTransactionTypeStockIn     InventoryTransactionType = "stock_in"    // 進貨
	InventoryTransactionTypeSale        InventoryTransactionType = "sale"        // 銷售
	InventoryTransactionTypeOrderVoid   InventoryTransactionType = "order_void"  // 訂單作廢
	InventoryTransactionTypeReturnIn    InventoryTransactionType = "return_in"   // 退貨入庫(客戶)
	InventoryTransactionTypeReturnOut   InventoryTransactionType = "return_out"  // 退貨出庫(供應商)
	InventoryTransactionTypeStocktaking InventoryTransactionType = "stocktaking" // 盤點
	InventoryTransactionTypeScrap       InventoryTransactionType = "scrap"       // 報廢
)

// 庫存交易記錄
type InventoryTransaction struct {
	ID              int64                    `gorm:"primaryKey" json:"-"`
	UUID            string                   `gorm:"<-:create" json:"uuid"`
	OrderID         null.Int                 `json:"-"`                               // 相關訂單ID
	Order           OrderInfo                `gorm:"foreignKey:OrderID" json:"order"` // 相關訂單
	ProductID       int64                    `json:"-"`
	Product         ProductInfo              `gorm:"foreignKey:ProductID" json:"product"`
	TransactionType InventoryTransactionType `json:"transaction_type"`
	Quantity        int                      `json:"quantity"` // 正數為增加庫存，負數為減少庫存
	BeforeQuantity  int                      `json:"before_quantity"`
	AfterQuantity   int                      `json:"after_quantity"`
	UnitPrice       float64                  `json:"unit_price"`
	TotalAmount     float64                  `json:"total_amount"`
	Notes           string                   `json:"notes"`
	CreatedByID     int64                    `json:"-"` // 操作人員ID
	UpdatedByID     int64                    `json:"-"` // 操作人員ID
	CreatedAt       time.Time                `gorm:"<-:create" json:"created_at"`
	UpdatedAt       time.Time                `json:"updated_at"`
	DeletedAt       gorm.DeletedAt           `gorm:"index" json:"-"`
}
type InventoryTransactionFilter struct {
	Search           string                     `form:"search"` // 搜索關鍵字
	ProductUUID      string                     `form:"product_uuid"`
	TransactionTypes []InventoryTransactionType `form:"transaction_types"`
}

type PurchaseOrderStatus string

const (
	PurchaseOrderStatusDraft             PurchaseOrderStatus = "draft"              // 草稿
	PurchaseOrderStatusOrdered           PurchaseOrderStatus = "ordered"            // 已下單
	PurchaseOrderStatusPartiallyReceived PurchaseOrderStatus = "partially_received" // 部分收貨
	PurchaseOrderStatusReceived          PurchaseOrderStatus = "received"           // 已收貨
	PurchaseOrderStatusCancelled         PurchaseOrderStatus = "cancelled"          // 已取消
)

// 採購單/進貨單
type PurchaseOrder struct {
	ID                  int64               `gorm:"primaryKey" json:"-"`
	UUID                string              `gorm:"<-:create" json:"uuid"`
	PONumber            string              `json:"po_number"`                                      // 採購單號
	CustomerID          null.Int            `json:"-"`                                              // 供應商 ID
	Customer            CustomerInfo        `gorm:"<-:false;foreignKey:CustomerID" json:"customer"` // 供應商
	OrderDate           string              `json:"order_date"`                                     // 訂單日期
	ExpectedArrivalDate null.Time           `json:"expected_arrival_date"`                          // 預計到貨日期
	ArrivalDate         null.Time           `json:"arrival_date"`                                   // 實際到貨日期
	Status              PurchaseOrderStatus `json:"status"`
	TotalAmount         float64             `json:"total_amount"`
	PaidAmount          float64             `json:"paid_amount"`
	Notes               string              `json:"notes"`
	CreatedByID         int64               `json:"-"` // 操作人員ID
	UpdatedByID         int64               `json:"-"` // 操作人員ID
	Items               []PurchaseOrderItem `gorm:"foreignKey:PurchaseOrderID" json:"items"`
	CreatedAt           time.Time           `gorm:"<-:create" json:"created_at"`
	UpdatedAt           time.Time           `json:"updated_at"`
	DeletedAt           gorm.DeletedAt      `gorm:"index" json:"-"`
}

// 採購單明細
type PurchaseOrderItem struct {
	ID               int64       `gorm:"primaryKey" json:"-"`
	UUID             string      `gorm:"<-:create" json:"uuid"`
	PurchaseOrderID  int64       `json:"-"`
	ProductID        int64       `json:"-"`
	Product          ProductInfo `gorm:"<-:false;foreignKey:ProductID" json:"product"`
	QuantityOrdered  int         `json:"quantity_ordered"`  // 訂購數量
	QuantityReceived int         `json:"quantity_received"` // 已收數量
	UnitPrice        float64     `json:"unit_price"`
	TotalPrice       float64     `json:"total_price"`
	Notes            string      `json:"notes"`
	CreatedByID      int64       `json:"-"` // 操作人員ID
	UpdatedByID      int64       `json:"-"` // 操作人員ID
	CreatedAt        time.Time   `gorm:"<-:create" json:"created_at"`
	UpdatedAt        time.Time   `json:"updated_at"`
}

type CreatePurchaseOrderRequest struct {
	ID                  int64               `gorm:"primaryKey" json:"-"`
	UUID                string              `json:"uuid"`
	PONumber            string              `json:"po_number"`             // 採購單號
	CustomerID          null.Int            `json:"-"`                     // 供應商 ID
	Customer            CustomerInfo        `gorm:"-" json:"customer"`     // 供應商
	OrderDate           string              `json:"order_date"`            // 訂單日期
	ExpectedArrivalDate null.Time           `json:"expected_arrival_date"` // 預計到貨日期
	ArrivalDate         null.Time           `json:"arrival_date"`          // 實際到貨日期
	Status              PurchaseOrderStatus `json:"status"`
	TotalAmount         float64             `json:"total_amount"`
	PaidAmount          float64             `json:"paid_amount"`
	Notes               string              `json:"notes"`
	CreatedByID         int64               `json:"-"` // 操作人員ID
	UpdatedByID         int64               `json:"-"` // 操作人員ID
	Items               []PurchaseOrderItem `gorm:"-" json:"items"`
}

func (CreatePurchaseOrderRequest) TableName() string {
	return "purchase_orders"
}

type InventoryAlertType string

const (
	InventoryAlertTypeLowStock InventoryAlertType = "low_stock" // 低庫存
	InventoryAlertTypeExpired  InventoryAlertType = "expired"   // 過期
)

// 庫存報警
type InventoryAlert struct {
	ID              int64              `gorm:"primaryKey" json:"-"`
	UUID            string             `gorm:"<-:create" json:"uuid"`
	ProductID       int64              `json:"-"`
	Product         Product            `gorm:"foreignKey:ProductID" json:"product"`
	AlertType       InventoryAlertType `json:"alert_type"`
	CurrentQuantity int                `json:"current_quantity"`
	Threshold       int                `json:"threshold"`
	IsResolved      bool               `json:"is_resolved"`
	ResolvedAt      time.Time          `json:"resolved_at"`
	Notes           string             `json:"notes"`
	CreatedAt       time.Time          `gorm:"<-:create" json:"created_at"`
	UpdatedAt       time.Time          `json:"updated_at"`
}

// 商品批次
type ProductBatch struct {
	ID              int64       `gorm:"primaryKey" json:"-"`
	UUID            string      `gorm:"<-:create" json:"uuid"`
	ProductID       int64       `json:"-"`
	Product         ProductInfo `gorm:"foreignKey:ProductID" json:"product"`
	BatchNumber     string      `json:"batch_number"`     // 批次號
	ManufactureDate time.Time   `json:"manufacture_date"` // 製造日期
	ExpiryDate      time.Time   `json:"expiry_date"`      // 到期日期
	Quantity        int         `json:"quantity"`
	Cost            float64     `json:"cost"` // 成本
	Notes           string      `json:"notes"`
	CreatedAt       time.Time   `gorm:"<-:create" json:"created_at"`
	UpdatedAt       time.Time   `json:"updated_at"`
}

type ProductStockResponse struct {
	UUID             string `json:"uuid"`
	Name             string `json:"name"`           // 商品名稱
	Category         string `json:"category"`       // 商品類別
	Barcode          string `json:"barcode"`        // 條碼
	StockQuantity    int    `json:"stock_qty"`      // 庫存數量
	MinStockQuantity int    `json:"min_stock_qty"`  // 最低庫存數量
	DiffStockQty     int    `json:"diff_stock_qty"` // 差異庫存數量
	Unit             string `json:"unit"`           // 單位
}

type ProductStockFilter struct {
	Search string `form:"search"` // 搜索關鍵字
	Name   string `form:"name"`   // 商品名稱
}
