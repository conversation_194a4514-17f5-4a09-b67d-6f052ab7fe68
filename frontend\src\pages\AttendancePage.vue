<template>
  <q-page>
    <q-card bordered class="card">
      <q-card-section class="q-px-lg">
        <div class="row q-mb-sm">
          <div class="col text-h6">王曉明</div>
          <div class="col text-right">員工編號：12345678</div>
        </div>
        <div class="row items-center">
          <div class="col">
            <q-input v-model="punchDate" mask="date">
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy
                    cover
                    transition-show="scale"
                    transition-hide="scale"
                  >
                    <q-date
                      v-model="punchDate"
                      :locale="twLocale"
                      :options="punchDateOpts"
                    >
                      <div class="row items-center justify-end">
                        <q-btn
                          v-close-popup
                          label="Close"
                          color="primary"
                          flat
                        />
                      </div>
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>
          <div class="col full-height text-right">
            <q-btn
              flat
              color="primary"
              label="前往查詢紀錄"
              to="/punch-record"
              icon-right="arrow_forward"
            />
          </div>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section class="q-px-lg info">
        <div class="row">
          <div class="col">
            <div class="label">出勤</div>
            <div class="text-subtitle1">20 天</div>
          </div>

          <q-separator vertical />

          <div class="col">
            <div class="label">加班</div>
            <div class="text-subtitle1">48 小時</div>
          </div>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section class="q-px-lg info">
        <div class="row">
          <div class="col">
            <div class="label">遲到</div>
            <div class="text-subtitle1">2 次</div>
          </div>

          <q-separator vertical />

          <div class="col">
            <div class="label">早退</div>
            <div class="text-subtitle1">1 次</div>
          </div>

          <q-separator vertical />

          <div class="col">
            <div class="label">未打卡</div>
            <div class="text-subtitle1">1 次</div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <q-card class="q-pa-md q-mt-lg">
      <div class="row q-mb-lg text-center">
        <div class="col">
          <q-btn round class="punch" color="green">
            <div class="d-block">
              <q-icon size="xl" name="arrow_upward" />
              <div class="text-h6">上班</div>
            </div>
          </q-btn>
        </div>
        <div class="col">
          <q-btn round class="punch" color="red">
            <div class="d-block">
              <q-icon size="xl" name="arrow_downward" />
              <div class="text-h6">下班</div>
            </div>
          </q-btn>
        </div>
      </div>
      <div class="row">
        <q-timeline class="q-mb-none">
          <q-timeline-entry title="上班打卡時間08:00"> </q-timeline-entry>
          <q-timeline-entry title="尚未打下班卡"> </q-timeline-entry>
        </q-timeline>
      </div>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { date } from 'quasar';
import { usePageInfoStore } from '@/stores/pageInfo';

const { t } = useI18n();
const { setPageTitle } = usePageInfoStore();

setPageTitle(t('attendance.label'));

const punchDate = ref(''); // 日期
const today = date.formatDate(new Date(), 'YYYY/MM/DD');

punchDate.value = today;

// 設定日期格式
const twLocale = {
  days: ['日', '一', '二', '三', '四', '五', '六'],
  daysShort: ['日', '一', '二', '三', '四', '五', '六'],
  months: [
    '1月',
    '2月',
    '3月',
    '4月',
    '5月',
    '6月',
    '7月',
    '8月',
    '9月',
    '10月',
    '11月',
    '12月',
  ],
  monthsShort: [
    '1月',
    '2月',
    '3月',
    '4月',
    '5月',
    '6月',
    '7月',
    '8月',
    '9月',
    '10月',
    '11月',
    '12月',
  ],
};

function punchDateOpts(date: string) {
  return date <= today;
}
</script>

<style lang="scss" scoped>
.q-page {
  max-width: 800px;
  margin: 0 auto;
}

.info {
  text-align: center;

  .label {
    color: #767676;
  }

  .text-subtitle1 {
    font-size: 1.375rem;
  }
}

.punch {
  width: 6rem;
  aspect-ratio: 1;
}
</style>
