package repository

import (
	"context"
	"cx/domain"
	"cx/utils"

	"gorm.io/gorm"
)

type OrderItemRepository interface {
	WithTx(tx *gorm.DB) OrderItemRepository

	Create(ctx context.Context, orderItem *domain.OrderItem) error
	Update(ctx context.Context, uuid string, orderItem *domain.OrderItem) error
	Fetch(ctx context.Context, filter *domain.OrderItemFilter) ([]domain.OrderItem, error)
	GetByID(ctx context.Context, id int64) (*domain.OrderItem, error)
	GetByUUID(ctx context.Context, uuid string) (*domain.OrderItem, error)
}

type orderItemRepository struct {
	db *gorm.DB
}

func NewOrderItemRepository(db *gorm.DB) OrderItemRepository {
	return &orderItemRepository{db}
}

func (r *orderItemRepository) WithTx(tx *gorm.DB) OrderItemRepository {
	return &orderItemRepository{tx}
}

func (r *orderItemRepository) Create(ctx context.Context, orderItem *domain.OrderItem) error {
	orderItem.UUID = utils.GenerateUUID()
	return r.db.WithContext(ctx).Create(orderItem).Error
}

func (r *orderItemRepository) Update(ctx context.Context, uuid string, orderItem *domain.OrderItem) error {
	return r.db.WithContext(ctx).Model(&domain.OrderItem{}).Where("uuid = ?", uuid).Updates(orderItem).Error
}

func (r *orderItemRepository) Fetch(ctx context.Context, filter *domain.OrderItemFilter) ([]domain.OrderItem, error) {
	var orderItems []domain.OrderItem

	tx := r.db.WithContext(ctx)

	if filter != nil {
		if filter.OrderID != 0 {
			tx = tx.Where("order_id = ?", filter.OrderID)
		}

		if filter.ProductID != 0 {
			tx = tx.Where("product_id = ?", filter.ProductID)
		}

		if filter.Quantity != 0 {
			tx = tx.Where("quantity = ?", filter.Quantity)
		}
	}

	err := tx.Model(&domain.OrderItem{}).Find(&orderItems).Error
	if err != nil {
		return nil, err
	}

	return orderItems, nil
}

func (r *orderItemRepository) GetByID(ctx context.Context, id int64) (*domain.OrderItem, error) {
	var orderItem domain.OrderItem

	err := r.db.WithContext(ctx).Where("id = ?", id).First(&orderItem).Error
	if err != nil {
		return nil, err
	}

	return &orderItem, nil
}

func (r *orderItemRepository) GetByUUID(ctx context.Context, uuid string) (*domain.OrderItem, error) {
	var orderItem domain.OrderItem

	err := r.db.WithContext(ctx).Where("uuid = ?", uuid).First(&orderItem).Error
	if err != nil {
		return nil, err
	}

	return &orderItem, nil
}
