# Australia Post Shipping Integration

## 概述

本文檔說明了在 WooCommerce 訂單系統中整合 Australia Post 運送方式的功能實現。系統現在可以正確識別和處理 Australia Post Shipping Parcel Post 和 Australia Post Shipping Express Post 兩種運送方式。

## 功能特點

### 1. 運送方式識別
系統可以自動識別以下 Australia Post 運送方式：
- **Australia Post Parcel Post** - 標準包裹郵遞
- **Australia Post Express Post** - 快遞郵遞

### 2. 智能解析
- 根據 `method_id` 和 `shipping_method_title` 進行智能解析
- 支援多種命名格式的識別
- 提供後備解析機制

### 3. 統一標識
解析後的運送方式使用統一的標識符：
- `australia_post_parcel` - 包裹郵遞
- `australia_post_express` - 快遞郵遞
- `australia_post` - 通用 Australia Post（無法具體分類時）

## 技術實現

### 資料庫查詢增強

在 `wc_order_repo.go` 中增強了 shipping 方式的查詢：

```sql
SELECT
    i.order_item_name as shipping_method_title,
    COALESCE(m1.meta_value, '') as shipping_method,
    COALESCE(m2.meta_value, '') as shipping_method_instance_id,
    COALESCE(m3.meta_value, '') as shipping_cost,
    COALESCE(m4.meta_value, '') as shipping_taxes
FROM wp_woocommerce_order_items i
LEFT JOIN wp_woocommerce_order_itemmeta m1 ON i.order_item_id = m1.order_item_id AND m1.meta_key = 'method_id'
LEFT JOIN wp_woocommerce_order_itemmeta m2 ON i.order_item_id = m2.order_item_id AND m2.meta_key = 'instance_id'
LEFT JOIN wp_woocommerce_order_itemmeta m3 ON i.order_item_id = m3.order_item_id AND m3.meta_key = 'cost'
LEFT JOIN wp_woocommerce_order_itemmeta m4 ON i.order_item_id = m4.order_item_id AND m4.meta_key = 'taxes'
WHERE i.order_id = ? AND i.order_item_type = 'shipping'
```

### 解析邏輯

新增 `parseAustraliaPostShippingMethod` 方法，實現智能解析：

1. **檢查 method_id** - 是否包含 "australia_post"
2. **分析 title** - 檢查是否包含 "express" 或 "parcel" 關鍵字
3. **分析 method_id** - 作為後備解析機制
4. **返回標準化標識符**

### 影響的方法

以下方法都已更新以支援 Australia Post 運送方式解析：

- `GetByID()` - 獲取單個訂單詳情
- `List()` - 獲取訂單列表
- `GetPendingOrders()` - 獲取待處理訂單
- `GetHistoryOrders()` - 獲取歷史訂單

## API 響應

### 訂單詳情 API

```json
{
  "id": 12345,
  "shipping_method": "australia_post_express",
  "shipping_method_title": "Australia Post Shipping Express Post",
  "shipping_total": 15.50,
  // ... 其他訂單資訊
}
```

### 訂單列表 API

```json
{
  "orders": [
    {
      "id": 12345,
      "shipping_method": "australia_post_parcel",
      "shipping_method_title": "Australia Post Shipping Parcel Post",
      // ... 其他訂單資訊
    }
  ],
  "total": 100,
  "page": 1,
  "page_size": 20
}
```

## 測試

已建立完整的單元測試 (`wc_order_repo_test.go`) 來驗證解析功能：

- 測試 Express Post 識別
- 測試 Parcel Post 識別
- 測試各種命名格式
- 測試非 Australia Post 方式的處理

## 使用方式

### 前端顯示

前端可以根據 `shipping_method` 欄位來顯示對應的運送方式圖示或說明：

```typescript
import { getShippingMethodDisplay } from '@/utils';

// 現在可以直接使用統一的函數
const displayName = getShippingMethodDisplay(order.shipping_method, order.shipping_method_title);
```

### 過濾和統計

可以根據運送方式進行訂單過濾和統計：

```sql
-- 統計 Australia Post Express 訂單數量
SELECT COUNT(*) FROM orders WHERE shipping_method = 'australia_post_express';

-- 統計各種運送方式的使用情況
SELECT shipping_method, COUNT(*) as count 
FROM orders 
WHERE shipping_method LIKE 'australia_post%' 
GROUP BY shipping_method;
```

## 注意事項

1. **向後兼容** - 非 Australia Post 的運送方式保持原有格式
2. **錯誤處理** - 解析失敗時會返回原始的 method_id
3. **性能** - 解析邏輯輕量化，不會影響查詢性能
4. **擴展性** - 可以輕鬆添加其他運送方式的解析邏輯

## 前端整合

### 已完成的前端修改

1. **API 介面更新**
   - 更新 `WCOrderInfo` 介面，添加 `shipping_method` 和 `shipping_method_title` 欄位

2. **訂單列表頁面** (`OnlineOrderPage.vue`)
   - 添加運送方式列到訂單列表表格
   - 實現 `getShippingMethodDisplay` 函數進行智能顯示
   - 支援 Australia Post 運送方式的友好顯示

3. **訂單詳情對話框** (`WCOrderDetailDialog.vue`)
   - 在訂單詳情中添加運送方式顯示
   - 位置在運費項目之前，提供清晰的運送資訊

4. **國際化支援**
   - 添加中文翻譯：`shippingMethod: '運送方式'`
   - 添加英文翻譯：`shippingMethod: 'Shipping Method'`

### 顯示效果

- **Australia Post Express** - 顯示為 "Australia Post Express"
- **Australia Post Parcel** - 顯示為 "Australia Post Parcel"
- **其他運送方式** - 顯示原始標題或方法名稱

## 測試建議

### 後端測試
```bash
go test ./backend/repository -v -run TestParseAustraliaPostShippingMethod
```

### 前端測試
1. 啟動前端應用
2. 進入線上訂單頁面 (`/admin/sale/online-orders`)
3. 檢查訂單列表是否顯示運送方式列
4. 點擊訂單查看詳情，確認運送方式正確顯示

## 未來擴展

可以考慮添加以下功能：

1. **更多運送商支援** - 如 DHL、FedEx 等
2. **運送追蹤** - 整合運送追蹤 API
3. **運費計算** - 自動計算運費
4. **運送時間預估** - 提供預計送達時間
5. **運送方式圖示** - 為不同運送方式添加視覺圖示
6. **運送方式過濾** - 在訂單列表中按運送方式過濾
