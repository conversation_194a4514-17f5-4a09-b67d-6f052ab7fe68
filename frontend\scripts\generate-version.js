const fs = require('fs');
const path = require('path');

const packageJson = require('../package.json');
const buildTime = new Date().toISOString();
const hash = Date.now().toString(36);

const versionInfo = {
  version: packageJson.version,
  buildTime: buildTime,
  hash: hash,
  nodeEnv: process.env.NODE_ENV || 'development',
};

// 寫入 public/version.json
fs.writeFileSync(
  path.join(__dirname, '../public/version.json'),
  JSON.stringify(versionInfo, null, 2)
);

// 寫入到 index.html 的 meta tag
const indexPath = path.join(__dirname, '../public/index.html');
if (fs.existsSync(indexPath)) {
  let indexContent = fs.readFileSync(indexPath, 'utf8');

  // 移除舊的版本 meta tag
  indexContent = indexContent.replace(/<meta name="version"[^>]*>/g, '');

  // 加入新的版本 meta tag
  const versionMeta = `<meta name="version" content="${versionInfo.version}" />`;
  indexContent = indexContent.replace('<head>', `<head>\n    ${versionMeta}`);

  fs.writeFileSync(indexPath, indexContent);
}
