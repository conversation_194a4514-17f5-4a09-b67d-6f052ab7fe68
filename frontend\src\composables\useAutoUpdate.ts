import { onMounted, onUnmounted } from 'vue';
import { UniversalUpdateService } from '@/services/universalUpdateService';

export function useAutoUpdate() {
  let updateService: UniversalUpdateService | null = null;

  onMounted(() => {
    updateService = new UniversalUpdateService();
  });

  onUnmounted(() => {
    updateService?.destroy();
  });

  const checkUpdate = async () => {
    await updateService?.manualCheckUpdate();
  };

  return {
    checkUpdate,
  };
}
