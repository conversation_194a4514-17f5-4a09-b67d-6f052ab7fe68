<template>
  <q-page class="row">
    <!-- left side -->
    <q-card flat square bordered class="col-4 col-md-3 column">
      <!-- title -->
      <div class="col-1">
        <div class="row items-center">
          <q-item dense class="col-grow">
            <q-item-section>
              <q-item-label header class="text-h6 text-weight-bold q-pa-sm">
                {{ t('user.menu') }}
              </q-item-label>
            </q-item-section>
          </q-item>

          <!-- add account -->
          <div
            class="col-auto q-px-sm"
            v-if="$q.screen.gt.sm && groups.length > 0"
          >
            <q-btn
              type="button"
              @click="handleUserClick('')"
              icon="add"
              color="create"
              size="sm"
              class="q-pa-sm"
            />
          </div>
        </div>
        <q-separator />
      </div>

      <!-- user list -->
      <q-scroll-area visible class="col-8 col-md-9">
        <template v-for="group in groups" :key="group.id">
          <q-item
            clickable
            @click="handleGroupClick(group.id)"
            class="group-item"
            :class="{ active: currentKey === group.id }"
          >
            <q-item-section>
              <q-item-label>{{ group.name }}</q-item-label>
            </q-item-section>
          </q-item>
          <template v-for="user in userInGroup(group)" :key="user.uuid">
            <q-item
              clickable
              @click="handleUserClick(user.uuid)"
              class="user-item"
              :class="{
                active: currentKey === user.uuid,
              }"
            >
              <q-item-section>
                <q-item-label :class="{ 'text-negative': !user.is_active }">
                  <q-icon name="star" size="xs" v-if="user.is_admin" />
                  {{ user.name }}
                  <template v-if="!user.is_active">
                    ({{ t('disabled') }})
                  </template>
                </q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </template>
      </q-scroll-area>

      <!-- actions -->
      <div class="col-2 col-md-1">
        <div class="column q-col-gutter-md q-pa-sm">
          <!-- add create -->
          <div class="col">
            <q-btn
              type="button"
              @click="handleUserClick('')"
              color="create"
              :size="$q.screen.lt.md ? 'sm' : 'md'"
              v-if="$q.screen.lt.md && groups.length > 0"
            >
              <q-icon name="add" />
              {{ t('account') }}
            </q-btn>
          </div>
          <!-- add group -->
          <div class="col">
            <q-btn
              type="button"
              @click="handleGroupClick(0)"
              color="create"
              :size="$q.screen.lt.md ? 'sm' : 'md'"
            >
              <q-icon name="add" />
              {{ t('group') }}
            </q-btn>
          </div>
        </div>
      </div>
    </q-card>

    <!-- right side -->
    <q-card flat square bordered class="col-8 col-md-9 column">
      <component
        :is="currentComponent"
        v-bind="currentComponentProps"
        @dataUpdated="updateData"
        @close="closeCurrentComponent"
      />
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, markRaw, shallowRef, ref, type Component } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { UserApi, User } from '@/api/user';
import { UserGroupApi, UserGroup } from '@/api/userGroup';
import UserProfileComponent from '@/pages/admin/user/components/UserProfile.vue';
import UserGroupComponent from '@/pages/admin/user/components/UserGroup.vue';
import { handleError } from '@/utils';

const { t } = useI18n();
const $q = useQuasar();

const users = ref<User[]>();
const groups = ref<UserGroup[]>([]);

interface ComponentProps {
  id: number | string;
  groups?: UserGroup[];
}

const currentKey = ref<string | number>('');
const currentComponent = shallowRef<Component | null>(null);
const currentComponentProps = shallowRef<ComponentProps>({
  id: '',
  groups: [],
});
const closeCurrentComponent = () => {
  currentComponent.value = null;
  currentComponentProps.value = { id: '', groups: [] };
  currentKey.value = '';
};
const updateData = ref<() => void>();

const componentMap = {
  profile: markRaw(UserProfileComponent),
  group: markRaw(UserGroupComponent),
};

const handleUserClick = (userID: string) => {
  currentComponent.value = componentMap.profile;
  currentComponentProps.value = {
    id: userID,
    groups: groups.value,
  };
  updateData.value = fetchUsers;

  if (userID) {
    currentKey.value = userID;
  } else {
    currentKey.value = 'new';
  }
};

const handleGroupClick = (groupID: number) => {
  currentComponent.value = componentMap.group;
  currentComponentProps.value = { id: groupID };
  updateData.value = fetchGroups;

  if (groupID) {
    currentKey.value = groupID;
  } else {
    currentKey.value = 'new';
  }
};

const fetchData = async () => {
  try {
    // 獲取用戶和群組數據
    await Promise.all([fetchUsers(), fetchGroups()]);
  } catch (error) {
    handleError(error);
  }
};

const fetchUsers = () => {
  return UserApi.fetch().then((res) => {
    users.value = res.result;
  });
};

const fetchGroups = () => {
  return UserGroupApi.fetch().then((res) => {
    groups.value = res.result;
  });
};

onMounted(() => {
  fetchData();
});

const userInGroup = (group: UserGroup) => {
  return users.value?.filter((user) => user.group_id === group.id) || [];
};
</script>

<style lang="scss" scoped>
.group-item {
  background-color: rgba(188, 204, 220, 0.5);
  color: #131010;

  &:not(:first-child) {
    margin-top: 0.5rem;
  }
}

.q-item {
  &.active {
    background-color: #001a6e;
    color: #fdf7f4;
  }
}
</style>
