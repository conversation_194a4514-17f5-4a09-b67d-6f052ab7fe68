import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    component: () => import('layouts/LoginLayout.vue'),
    children: [{ path: '', component: () => import('pages/LoginPage.vue') }],
  },
  {
    path: '/',
    redirect: '/login',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: 'attendance',
        component: () => import('pages/AttendancePage.vue'),
      },
    ],
  },
  {
    path: '/order',
    redirect: '/order',
    component: () => import('layouts/OrderLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/pos/OrderIndexPage.vue'),
      },
      {
        path: ':orderID', // uuid
        component: () => import('pages/pos/OrderCheckoutPage.vue'),
      },
    ],
    meta: { requiresAuth: true },
  },
  {
    path: '/admin/dashboard',
    redirect: '/admin/dashboard/user',
    component: () => import('layouts/AdminLayout.vue'),
    children: [
      {
        path: 'user',
        component: () => import('pages/admin/user/UserPage.vue'),
      },
      {
        path: 'attendance',
        component: () => import('pages/admin/user/AttendancePage.vue'),
      },
      {
        path: 'payroll',
        component: () => import('pages/admin/user/PayrollPage.vue'),
      },
      {
        path: 'customer',
        component: () => import('pages/admin/customer/CustomerPage.vue'),
      },
      {
        path: 'catalog',
        redirect: '/admin/dashboard/catalog/product',
        children: [
          {
            path: 'product',
            component: () => import('pages/admin/catalog/ProductPage.vue'),
          },
          {
            path: 'stock-history',
            component: () => import('pages/admin/catalog/StockHistoryPage.vue'),
          },
        ],
      },
      {
        path: 'order',
        redirect: '/admin/dashboard/order/onsite',
        children: [
          {
            path: 'onsite',
            component: () => import('pages/admin/sale/OnsiteOrderPage.vue'),
          },
          {
            path: 'online',
            component: () => import('pages/admin/sale/OnlineOrderPage.vue'),
          },
        ],
      },
      {
        path: 'system',
        component: () => import('pages/admin/system/SystemPage.vue'),
      },
      {
        path: 'announcement',
        component: () => import('pages/admin/system/AnnouncementPage.vue'),
      },
      {
        path: 'xero',
        redirect: '/admin/dashboard/xero/setup',
        children: [
          {
            path: 'setup',
            component: () => import('pages/admin/xero/XeroSetupPage.vue'),
          },
          {
            path: 'invoices',
            component: () => import('pages/admin/xero/XeroInvoicesPage.vue'),
          },
        ],
      },
    ],
    meta: { requiresAuth: true, requiresAdmin: true },
  },
  {
    path: '/xero/redirect',
    component: () => import('layouts/LoginLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/admin/xero/XeroRedirectPage.vue'),
      },
    ],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    redirect: '/login',
    // component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;
