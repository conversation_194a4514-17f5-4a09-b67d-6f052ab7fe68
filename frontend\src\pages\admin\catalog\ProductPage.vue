<template>
  <q-page class="row">
    <!-- left side -->
    <q-card flat square bordered class="col-4 col-md-3">
      <div class="column full-height">
        <!-- title -->
        <div class="col-1">
          <div class="row items-center">
            <q-item dense class="col-grow">
              <q-item-section>
                <q-item-label header class="text-h6 text-weight-bold q-pa-sm">
                  {{ t('product.label') }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </div>
          <q-separator />
        </div>

        <!-- product category list -->
        <q-scroll-area visible class="col-9">
          <template v-for="category in categories" :key="category.id">
            <q-item
              clickable
              @click="showProducts(category)"
              class="row items-center"
              :class="{ active: currentKey === category.id }"
            >
              <q-item-section>
                <q-item-label>{{ category.name }}</q-item-label>
              </q-item-section>

              <q-item-section side class="q-px-xs">
                <q-btn
                  type="button"
                  @click.stop="handleCategoryClick(category)"
                  icon="edit"
                  color="primary"
                  size="xs"
                  class="q-px-xs"
                  v-if="category.id !== 0"
                />
              </q-item-section>
            </q-item>
          </template>
        </q-scroll-area>

        <!-- actions -->
        <div class="col-2 col-md-1">
          <div class="column q-col-gutter-md q-pa-sm">
            <!-- add product category -->
            <div class="col">
              <q-btn
                type="button"
                @click="handleCategoryClick(null)"
                color="create"
                :size="$q.screen.lt.md ? 'sm' : 'md'"
              >
                <q-icon name="add" />
                {{ t('category') }}
              </q-btn>
            </div>
          </div>
        </div>
      </div>
    </q-card>

    <!-- right side -->
    <q-card flat square bordered class="col-8 col-md-9">
      <div class="column full-height">
        <!-- title -->
        <q-card-section class="col-1 q-pa-none" v-if="currentComponent != null">
          <q-item dense>
            <q-item-section>
              <q-item-label header class="text-h6 text-weight-bold q-pa-sm">
                {{ panelTitle }}
              </q-item-label>
            </q-item-section>
          </q-item>
          <q-separator />
        </q-card-section>

        <!-- component panel -->
        <q-card-section class="col-11">
          <q-scroll-area class="full-height">
            <component
              :is="currentComponent"
              v-bind="currentComponentProps"
              @dataUpdated="updateData"
              @close="close"
            />
          </q-scroll-area>
        </q-card-section>
      </div>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, markRaw, shallowRef, ref, type Component } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { ProductCategoryApi, ProductCategory } from '@/api/productCategory';
import ProductCategoryComponent from './components/ProductCategory.vue';
import ProductListComponent from './components/ProductList.vue';
import { handleError } from '@/utils';

const { t } = useI18n();
const $q = useQuasar();
const panelTitle = ref('');

const currentKey = ref<number | null>(null);
const currentComponent = shallowRef<Component | null>(null);
const currentComponentProps = shallowRef({});
const updateData = ref<() => Promise<void>>();
const categoryAll: ProductCategory = {
  id: 0,
  name: t('all'),
  image: {
    uuid: '',
    image_path: '',
  },
};

const componentMap = {
  category: markRaw(ProductCategoryComponent),
  product: markRaw(ProductListComponent),
};

const categories = ref<ProductCategory[]>([]);

const handleCategoryClick = (category: ProductCategory | null) => {
  currentComponent.value = componentMap.category;
  currentComponentProps.value = {
    category: category,
  };
  updateData.value = () => fetchData();

  if (category) {
    panelTitle.value = `${t('category')} - ${category.name}`;
    currentKey.value = category.id;
  } else {
    panelTitle.value = t('createCategory');
    currentKey.value = null;
  }
};

const showProducts = (category: ProductCategory) => {
  currentKey.value = category.id;
  panelTitle.value = category.name;

  currentComponent.value = componentMap.product;
  currentComponentProps.value = {
    categoryID: category.id,
  };
};

const fetchData = async () => {
  try {
    const response = await ProductCategoryApi.listCategories();
    categories.value = [categoryAll, ...response.result];
  } catch (error) {
    handleError(error);
  }
};

onMounted(() => {
  fetchData();
  showProducts(categoryAll);
});

const close = () => {
  currentComponent.value = null;
  currentKey.value = null;
  panelTitle.value = '';
};
</script>

<style lang="scss" scoped>
.q-card {
  height: calc(100vh - 50px);
}

.q-item {
  &.active {
    background-color: #001a6e;
    color: #fdf7f4;
  }
}
</style>
