package repository

import (
	"context"
	"cx/domain"
	"cx/utils"

	"gorm.io/gorm"
)

type UserRepository interface {
	WithTx(tx *gorm.DB) UserRepository

	Create(ctx context.Context, user *domain.UserCreatePayload) error
	Update(ctx context.Context, uuid string, user *domain.UserUpdatePayload) error
	Fetch(ctx context.Context) ([]domain.User, error)
	GetByID(ctx context.Context, id int64) (*domain.User, error)
	GetByUUID(ctx context.Context, uuid string) (*domain.User, error)
	GetByUsername(ctx context.Context, username string) (*domain.User, error)
	Delete(ctx context.Context, uuid string) error
}

type userRepository struct {
	db *gorm.DB
}

func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db}
}

func (r *userRepository) WithTx(tx *gorm.DB) UserRepository {
	return &userRepository{tx}
}

func (r *userRepository) Create(ctx context.Context, user *domain.UserCreatePayload) error {
	user.UUID = utils.GenerateUUID()
	return r.db.WithContext(ctx).Create(user).Error
}

func (r *userRepository) Update(ctx context.Context, uuid string, user *domain.UserUpdatePayload) error {
	return r.db.WithContext(ctx).Model(&domain.User{}).Where("uuid = ?", uuid).Updates(user).Error
}

func (r *userRepository) Fetch(ctx context.Context) ([]domain.User, error) {
	var users []domain.User

	err := r.db.WithContext(ctx).
		Where("is_developer = FALSE").
		Find(&users).Error

	return users, err
}

func (r *userRepository) GetByID(ctx context.Context, id int64) (*domain.User, error) {
	var user domain.User
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&user).Error
	return &user, err
}

func (r *userRepository) GetByUUID(ctx context.Context, uuid string) (*domain.User, error) {
	var user domain.User
	err := r.db.WithContext(ctx).Where("uuid = ?", uuid).First(&user).Error
	return &user, err
}

func (r *userRepository) GetByUsername(ctx context.Context, username string) (*domain.User, error) {
	var user domain.User
	err := r.db.WithContext(ctx).Where("username = ?", username).First(&user).Error
	return &user, err
}

func (r *userRepository) Delete(ctx context.Context, uuid string) error {
	return r.db.WithContext(ctx).Where("uuid = ?", uuid).Delete(&domain.User{}).Error
}
