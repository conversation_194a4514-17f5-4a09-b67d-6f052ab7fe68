<template>
  <q-page class="flex flex-center">
    <div class="text-center">
      <q-spinner-facebook v-if="processing" color="primary" size="50px" />

      <div v-if="processing" class="text-h6 q-mt-md">
        {{ $t('xero.redirect.processing') }}
      </div>

      <div v-if="success" class="text-center">
        <q-icon name="check_circle" color="positive" size="50px" />
        <div class="text-h6 q-mt-md text-positive">
          {{ $t('xero.redirect.success.title') }}
        </div>
        <div class="q-mt-md">
          {{ $t('xero.redirect.success.connectedTo', { tenantName }) }}
        </div>
        <q-btn
          color="primary"
          :label="$t('xero.redirect.success.backToSettings')"
          class="q-mt-md"
          @click="goToSettings"
        />
      </div>

      <div v-if="error" class="text-center">
        <q-icon name="error" color="negative" size="50px" />
        <div class="text-h6 q-mt-md text-negative">
          {{ $t('xero.redirect.error.title') }}
        </div>
        <div class="q-mt-md text-body2">
          {{ errorMessage }}
        </div>

        <!-- 調試信息 -->
        <q-expansion-item
          v-if="debugInfo"
          icon="bug_report"
          :label="$t('xero.redirect.debug.title')"
          class="q-mt-md"
        >
          <q-card class="q-mt-sm">
            <q-card-section class="text-left">
              <pre class="text-caption">{{ debugInfo }}</pre>
            </q-card-section>
          </q-card>
        </q-expansion-item>

        <div class="q-mt-md q-gutter-sm">
          <q-btn
            color="primary"
            :label="$t('xero.redirect.error.retry')"
            @click="goToSettings"
          />
          <q-btn
            color="secondary"
            :label="$t('xero.redirect.error.clearState')"
            @click="clearStateAndRetry"
          />
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { XeroApi } from '@/api/xero';
import { getQueryParam, isValidQueryParam } from '@/utils/query-params';

const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const { t } = useI18n();

const processing = ref(true);
const success = ref(false);
const error = ref(false);
const errorMessage = ref('');
const tenantName = ref('');
const debugInfo = ref('');

const handleCallback = async () => {
  try {
    const code = getQueryParam(route.query.code);
    const state = getQueryParam(route.query.state);
    const error_param = getQueryParam(route.query.error);

    console.log('OAuth Callback Debug:', {
      code: code ? 'present' : 'missing',
      state: state ? 'present' : 'missing',
      error_param,
      savedState: localStorage.getItem('xero_oauth_state')
        ? 'present'
        : 'missing',
    });

    // 檢查是否有錯誤參數
    if (error_param) {
      throw new Error(
        t('xero.redirect.error.messages.authError', { error: error_param })
      );
    }

    // 檢查必要參數
    if (
      !isValidQueryParam(route.query.code) ||
      !isValidQueryParam(route.query.state)
    ) {
      throw new Error(t('xero.redirect.error.messages.missingParams'));
    }

    // 此時 code 和 state 已經通過驗證，確保是有效的字符串
    const validCode = code as string;
    const validState = state as string;

    // 驗證state參數
    const savedState = localStorage.getItem('xero_oauth_state');
    console.log('State Validation:', {
      receivedState: validState,
      savedState: savedState,
      match: savedState === validState,
      receivedStateLength: validState.length,
      savedStateLength: savedState?.length,
    });

    // 如果沒有保存的 state，嘗試直接發送到後端驗證
    if (!savedState) {
      console.warn(
        'No saved state found in localStorage, attempting backend validation'
      );
      // 不拋出錯誤，讓後端來驗證 state
    } else if (savedState !== validState) {
      console.error('State mismatch detected:', {
        expected: savedState,
        received: validState,
        expectedType: typeof savedState,
        receivedType: typeof validState,
      });
      // 仍然嘗試發送到後端，讓後端做最終驗證
      console.warn('State mismatch, but continuing with backend validation');
    } else {
      console.log('State validation passed successfully');
    }

    // 發送回調請求到後端
    const response = await XeroApi.callback(validCode, validState);

    console.log('Callback Response:', response);

    // 清除localStorage中的state
    localStorage.removeItem('xero_oauth_state');

    // 顯示成功訊息
    tenantName.value =
      response.result?.tenant_name ||
      t('xero.redirect.error.messages.unknownOrg');
    success.value = true;

    $q.notify({
      position: 'top',
      type: 'positive',
      message: t('xero.redirect.success.notification'),
    });
  } catch (err) {
    console.error('OAuth Callback Error:', err);
    error.value = true;
    errorMessage.value =
      (err as Error)?.message || t('xero.redirect.error.title');

    // 收集調試信息
    debugInfo.value = JSON.stringify(
      {
        url: window.location.href,
        query: route.query,
        savedState: localStorage.getItem('xero_oauth_state'),
        error: (err as Error)?.message,
        timestamp: new Date().toISOString(),
      },
      null,
      2
    );
  } finally {
    processing.value = false;
  }
};

const goToSettings = () => {
  router.push('/admin/dashboard/xero/setup');
};

const clearStateAndRetry = () => {
  localStorage.removeItem('xero_oauth_state');
  $q.notify({
    position: 'top',
    type: 'info',
    message: t('xero.redirect.debug.stateCleared'),
  });
  goToSettings();
};

onMounted(() => {
  handleCallback();
});
</script>
