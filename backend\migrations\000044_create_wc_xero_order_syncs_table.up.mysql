-- 創建 wc_xero_order_syncs 表，專門用於WooCommerce訂單的Xero同步
CREATE TABLE `wc_xero_order_syncs` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `uuid` VARCHAR(36) NOT NULL UNIQUE,
    `wc_order_id` INT NOT NULL,
    `xero_invoice_id` VARCHAR(255),
    `xero_invoice_no` VARCHAR(255),
    `sync_status` ENUM('pending', 'syncing', 'success', 'failed', 'voided') NOT NULL DEFAULT 'pending',
    `error_message` TEXT,
    `last_sync_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_wc_order_id` (`wc_order_id`),
    INDEX `idx_sync_status` (`sync_status`),
    INDEX `idx_xero_invoice_id` (`xero_invoice_id`),
    
    UNIQUE KEY `unique_wc_order_sync` (`wc_order_id`)
);
