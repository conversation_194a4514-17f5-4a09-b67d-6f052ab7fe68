#!/bin/bash

# WooCommerce to Xero Invoice Scheduler 啟動腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查是否存在 .env 文件
if [ ! -f ".env.scheduler" ]; then
    log_warn ".env.scheduler file not found. Creating from example..."
    if [ -f ".env.scheduler.example" ]; then
        cp .env.scheduler.example .env.scheduler
        log_info "Please edit .env.scheduler file with your configuration"
        exit 1
    else
        log_error ".env.scheduler.example file not found"
        exit 1
    fi
fi

# 載入環境變量
source .env.scheduler

log_info "Starting WooCommerce to Xero Invoice Scheduler..."

# 檢查必要的環境變量
if [ -z "$DB_PASSWORD" ]; then
    log_error "DB_PASSWORD is not set in .env.scheduler"
    exit 1
fi

# 檢查數據庫連接
log_info "Checking database connection..."
mysql -h"${DB_HOST:-localhost}" -P"${DB_PORT:-3306}" -u"${DB_USER:-root}" -p"$DB_PASSWORD" -e "USE ${DB_NAME:-cx_pos};" 2>/dev/null
if [ $? -ne 0 ]; then
    log_error "Cannot connect to database. Please check your database configuration."
    exit 1
fi

log_info "Database connection successful"

# 構建調度器
log_info "Building scheduler..."
go build -o scheduler ./cmd/scheduler

if [ $? -ne 0 ]; then
    log_error "Failed to build scheduler"
    exit 1
fi

log_info "Scheduler built successfully"

# 運行調度器
log_info "Starting scheduler..."
log_info "Press Ctrl+C to stop the scheduler"

# 導出環境變量
export DB_HOST="${DB_HOST:-localhost}"
export DB_PORT="${DB_PORT:-3306}"
export DB_USER="${DB_USER:-root}"
export DB_PASSWORD="$DB_PASSWORD"
export DB_NAME="${DB_NAME:-cx_pos}"
export DEFAULT_EMAIL="$DEFAULT_EMAIL"

# 運行調度器
./scheduler
