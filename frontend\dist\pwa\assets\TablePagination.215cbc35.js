import{E as z,G as A,H as K,I as U,c as l,r as _,w as E,J as P,Q as G,K as H,L as M,M as J,N as W,m as R,d as X,a as Y,b as Z,o as ee}from"./index.9477d5a3.js";import{b as ae}from"./format.054b8074.js";function B(e,m){return[!0,!1].includes(e)?e:m}var te=z({name:"QPagination",props:{...A,modelValue:{type:Number,required:!0},min:{type:[Number,String],default:1},max:{type:[Number,String],required:!0},maxPages:{type:[Number,String],default:0,validator:e=>(typeof e=="string"?parseInt(e,10):e)>=0},inputStyle:[Array,String,Object],inputClass:[Array,String,Object],size:String,disable:Boolean,input:Boolean,iconPrev:String,iconNext:String,iconFirst:String,iconLast:String,toFn:Function,boundaryLinks:{type:Boolean,default:null},boundaryNumbers:{type:Boolean,default:null},directionLinks:{type:Boolean,default:null},ellipses:{type:Boolean,default:null},ripple:{type:[Boolean,Object],default:null},round:Boolean,rounded:Boolean,flat:Boolean,outline:Boolean,unelevated:Boolean,push:Boolean,glossy:Boolean,color:{type:String,default:"primary"},textColor:String,activeDesign:{type:String,default:"",values:e=>e===""||K.includes(e)},activeColor:String,activeTextColor:String,gutter:String,padding:{type:String,default:"3px 2px"}},emits:["update:modelValue"],setup(e,{emit:m}){const{proxy:s}=H(),{$q:o}=s,g=U(e,o),u=l(()=>parseInt(e.min,10)),n=l(()=>parseInt(e.max,10)),d=l(()=>parseInt(e.maxPages,10)),b=l(()=>v.value+" / "+n.value),k=l(()=>B(e.boundaryLinks,e.input)),f=l(()=>B(e.boundaryNumbers,!e.input)),q=l(()=>B(e.directionLinks,e.input)),C=l(()=>B(e.ellipses,!e.input)),h=_(null),v=l({get:()=>e.modelValue,set:a=>{if(a=parseInt(a,10),e.disable||isNaN(a))return;const t=ae(a,u.value,n.value);e.modelValue!==t&&m("update:modelValue",t)}});E(()=>`${u.value}|${n.value}`,()=>{v.value=e.modelValue});const L=l(()=>"q-pagination row no-wrap items-center"+(e.disable===!0?" disabled":"")),N=l(()=>e.gutter in M?`${M[e.gutter]}px`:e.gutter||null),T=l(()=>N.value!==null?`--q-pagination-gutter-parent:-${N.value};--q-pagination-gutter-child:${N.value}`:null),V=l(()=>{const a=[e.iconFirst||o.iconSet.pagination.first,e.iconPrev||o.iconSet.pagination.prev,e.iconNext||o.iconSet.pagination.next,e.iconLast||o.iconSet.pagination.last];return o.lang.rtl===!0?a.reverse():a}),$=l(()=>({"aria-disabled":e.disable===!0?"true":"false",role:"navigation"})),w=l(()=>J(e,"flat")),I=l(()=>({[w.value]:!0,round:e.round,rounded:e.rounded,padding:e.padding,color:e.color,textColor:e.textColor,size:e.size,ripple:e.ripple!==null?e.ripple:!0})),j=l(()=>{const a={[w.value]:!1};return e.activeDesign!==""&&(a[e.activeDesign]=!0),a}),p=l(()=>({...j.value,color:e.activeColor||e.color,textColor:e.activeTextColor||e.textColor})),y=l(()=>{let a=Math.max(d.value,1+(C.value?2:0)+(f.value?2:0));const t={pgFrom:u.value,pgTo:n.value,ellipsesStart:!1,ellipsesEnd:!1,boundaryStart:!1,boundaryEnd:!1,marginalStyle:{minWidth:`${Math.max(2,String(n.value).length)}em`}};return d.value&&a<n.value-u.value+1&&(a=1+Math.floor(a/2)*2,t.pgFrom=Math.max(u.value,Math.min(n.value-a+1,e.modelValue-Math.floor(a/2))),t.pgTo=Math.min(n.value,t.pgFrom+a-1),f.value&&(t.boundaryStart=!0,t.pgFrom++),C.value&&t.pgFrom>u.value+(f.value?1:0)&&(t.ellipsesStart=!0,t.pgFrom++),f.value&&(t.boundaryEnd=!0,t.pgTo--),C.value&&t.pgTo<n.value-(f.value?1:0)&&(t.ellipsesEnd=!0,t.pgTo--)),t});function F(a){v.value=a}function O(a){v.value=v.value+a}const Q=l(()=>{function a(){v.value=h.value,h.value=null}return{"onUpdate:modelValue":t=>{h.value=t},onKeyup:t=>{W(t,13)===!0&&a()},onBlur:a}});function r(a,t,S){const c={"aria-label":t,"aria-current":"false",...I.value,...a};return S===!0&&Object.assign(c,{"aria-current":"true",...p.value}),t!==void 0&&(e.toFn!==void 0?c.to=e.toFn(t):c.onClick=()=>{F(t)}),P(R,c)}return Object.assign(s,{set:F,setByOffset:O}),()=>{const a=[],t=[];let S;if(k.value===!0&&(a.push(r({key:"bls",disable:e.disable||e.modelValue<=u.value,icon:V.value[0]},u.value)),t.unshift(r({key:"ble",disable:e.disable||e.modelValue>=n.value,icon:V.value[3]},n.value))),q.value===!0&&(a.push(r({key:"bdp",disable:e.disable||e.modelValue<=u.value,icon:V.value[1]},e.modelValue-1)),t.unshift(r({key:"bdn",disable:e.disable||e.modelValue>=n.value,icon:V.value[2]},e.modelValue+1))),e.input!==!0){S=[];const{pgFrom:c,pgTo:D,marginalStyle:x}=y.value;if(y.value.boundaryStart===!0){const i=u.value===e.modelValue;a.push(r({key:"bns",style:x,disable:e.disable,label:u.value},u.value,i))}if(y.value.boundaryEnd===!0){const i=n.value===e.modelValue;t.unshift(r({key:"bne",style:x,disable:e.disable,label:n.value},n.value,i))}y.value.ellipsesStart===!0&&a.push(r({key:"bes",style:x,disable:e.disable,label:"\u2026",ripple:!1},c-1)),y.value.ellipsesEnd===!0&&t.unshift(r({key:"bee",style:x,disable:e.disable,label:"\u2026",ripple:!1},D+1));for(let i=c;i<=D;i++)S.push(r({key:`bpg${i}`,style:x,disable:e.disable,label:i},i,i===e.modelValue))}return P("div",{class:L.value,...$.value},[P("div",{class:"q-pagination__content row no-wrap items-center",style:T.value},[...a,e.input===!0?P(G,{class:"inline",style:{width:`${b.value.length/1.5}em`},type:"number",dense:!0,value:h.value,disable:e.disable,dark:g.value,borderless:!0,inputClass:e.inputClass,inputStyle:e.inputStyle,placeholder:b.value,min:u.value,max:n.value,...Q.value}):P("div",{class:"q-pagination__middle row justify-center"},S),...t])])}}});const le={class:"row col-12 justify-center q-mt-sm"},ie=X({__name:"TablePagination",props:{modelValue:{}},emits:["update:modelValue","getData"],setup(e,{emit:m}){const s=e,o=m,g=_(1),u=l(()=>Math.max(1,Math.ceil(s.modelValue.rowsNumber/s.modelValue.rowsPerPage))),n=d=>{o("update:modelValue",{...s.modelValue,page:d}),o("getData")};return E(()=>s.modelValue.page,d=>{g.value=d},{immediate:!0}),(d,b)=>(ee(),Y("div",le,[Z(te,{modelValue:g.value,"onUpdate:modelValue":[b[0]||(b[0]=k=>g.value=k),n],color:"grey-8",max:u.value,size:"sm"},null,8,["modelValue","max"])]))}});export{ie as _};
