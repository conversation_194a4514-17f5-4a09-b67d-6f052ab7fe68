<template>
  <q-dialog v-model="visible" class="full-dialog" no-refocus persistent>
    <q-card class="column">
      <q-card-section class="col-1">
        <div class="row">
          <!-- title -->
          <div class="text-h6">
            {{ form.title }}
          </div>
          <q-space />
          <!-- close button -->
          <q-btn
            type="button"
            icon="close"
            flat
            round
            dense
            @click="closeDialog"
          />
        </div>
      </q-card-section>

      <!-- body -->
      <q-card-section class="col-10">
        <q-scroll-area class="full-height">
          <!-- 基本資訊卡片 -->
          <q-card bordered class="q-mb-md bg-grey-3">
            <q-card-section>
              <div class="row q-col-gutter-md">
                <!-- 單號 -->
                <!-- <div class="col-12 col-md-3">
                  <q-input
                    outlined
                    dense
                    v-model="form.no_number"
                    label="No."
                    stack-label
                    :placeholder="`(${t('optional')})`"
                    class="bg-white"
                  />
                </div> -->

                <!-- 日期 -->
                <div class="col-12 col-md-3">
                  <q-input
                    outlined
                    dense
                    v-model="form.form_date"
                    mask="date"
                    :label="t('date')"
                    stack-label
                    class="bg-white"
                  >
                    <template v-slot:append>
                      <q-icon name="event" class="cursor-pointer">
                        <q-popup-proxy
                          cover
                          transition-show="scale"
                          transition-hide="scale"
                        >
                          <q-date v-model="form.form_date" mask="YYYY-MM-DD">
                            <div class="row items-center justify-end">
                              <q-btn
                                v-close-popup
                                :label="t('close')"
                                no-caps
                                flat
                              />
                            </div>
                          </q-date>
                        </q-popup-proxy>
                      </q-icon>
                    </template>
                  </q-input>
                </div>

                <!-- 退貨類型 -->
                <div
                  class="col-12 col-md-3"
                  v-if="modelValue.type === 'return'"
                >
                  <q-select
                    outlined
                    dense
                    v-model="form.return_type"
                    :options="returnTypes"
                    option-label="name"
                    option-value="value"
                    :label="t('inventory.returnType.label')"
                    map-options
                    emit-value
                    stack-label
                    class="bg-white"
                  />
                </div>

                <!-- 供應商/客戶 -->
                <div class="col-12 col-md-3" v-if="isActiveCustomerInput()">
                  <q-select
                    outlined
                    dense
                    v-model="form.customer"
                    :options="filterCustomers"
                    option-label="name"
                    option-value="uuid"
                    :label="`${t('supplier')}/${t('customer.label')}`"
                    stack-label
                    :placeholder="`(${t('optional')})`"
                    clearable
                    use-input
                    input-debounce="0"
                    @filter="customerFilterFn"
                    class="bg-white"
                  />
                </div>

                <!-- 說明 -->
                <div class="col-12">
                  <q-input
                    outlined
                    dense
                    type="textarea"
                    v-model="form.notes"
                    :label="t('note.label')"
                    stack-label
                    class="bg-white"
                  />
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- 商品列表卡片 -->
          <q-card bordered class="bg-grey-3">
            <q-card-section>
              <!-- header / actions -->
              <div class="row items-center justify-between q-mb-md">
                <div class="text-h6">
                  {{ t('products') }}
                </div>
                <div class="row">
                  <!-- 條碼掃描 -->
                  <BarcodeScannerWrapper
                    :products="productOptions"
                    barcodeField="barcode"
                    @product-found="addProductByBarcodeScan"
                  />
                  <!-- 新增商品按鈕 -->
                  <q-btn
                    color="positive"
                    icon="add"
                    class="q-pa-sm"
                    @click="openAddProductDialog"
                  />
                </div>
              </div>

              <!-- 商品表格 -->
              <q-table
                flat
                bordered
                :rows="form.items"
                :columns="columns"
                table-header-class="bg-cream"
                :rows-per-page-options="[0]"
                hide-pagination
              >
                <template v-slot:body="props">
                  <q-tr
                    clickable
                    :props="props"
                    @click="editProduct(props.row)"
                  >
                    <!-- 商品名稱 -->
                    <q-td :props="props" key="product_name">
                      {{ props.row.product.name }}
                    </q-td>
                    <!-- 商品條碼 -->
                    <q-td :props="props" key="barcode">
                      {{ props.row.product.barcode }}
                    </q-td>
                    <!-- 商品類別 -->
                    <q-td :props="props" key="category">
                      {{ props.row.product.category.name }}
                    </q-td>
                    <!-- 商品數量 -->
                    <q-td :props="props" key="quantity">
                      {{ props.row.quantity }}
                    </q-td>
                    <!-- 商品操作 -->
                    <q-td :props="props" key="actions">
                      <q-btn
                        flat
                        round
                        color="negative"
                        icon="delete"
                        @click.stop="removeItem(props.row)"
                      >
                        <q-tooltip>
                          {{ t('remove') }}
                        </q-tooltip>
                      </q-btn>
                    </q-td>
                  </q-tr>
                </template>
              </q-table>

              <!-- 總計區域 -->
              <div class="summary-section q-mt-md">
                <div class="row justify-end">
                  <div class="col-12 col-md-4">
                    <div class="summary-card">
                      <div class="row q-mb-sm">
                        <div class="col-6 text-right q-pr-md">
                          {{ t('itemNum') }}：
                        </div>
                        <div class="col-6 text-right total-quantity">
                          {{ form.items?.length || 0 }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </q-scroll-area>
      </q-card-section>

      <!-- 提交按鈕 -->
      <q-card-section class="col-1 bg-grey-2">
        <div class="row justify-end">
          <q-btn
            color="positive"
            :label="t('save')"
            @click="submitForm"
            :loading="isSubmit"
          />
        </div>
      </q-card-section>
    </q-card>

    <!-- 新增商品對話框 -->
    <q-dialog v-model="addProductDialog" no-refocus>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">
            <template v-if="isEditProduct">
              {{ t('editProduct') }}
            </template>
            <template v-else>
              {{ t('addProduct') }}
            </template>
          </div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-select
            outlined
            dense
            v-model="addProductForm.product"
            :options="productFilterOptions"
            option-label="name"
            option-value="uuid"
            :label="t('product.label')"
            use-input
            hide-selected
            fill-input
            input-debounce="0"
            @filter="productFilterFn"
            :rules="[(val: string) => !!val || t('error.required')]"
          />
          <q-input
            outlined
            dense
            type="number"
            v-model.number="addProductForm.quantity"
            :label="t('quantity')"
            class="q-mt-md"
            :rules="[(val: number) => val > 0 || t('error.minNumber', {min:1})]"
          />
        </q-card-section>

        <!-- actions -->
        <q-card-actions align="right">
          <q-btn
            dense
            type="button"
            :label="t('cancel')"
            color="grey"
            no-caps
            @click="closeAddProductDialog"
          />
          <q-btn
            dense
            :label="isEditProduct ? t('save') : t('add')"
            no-caps
            color="positive"
            @click="addProduct"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { Notify } from 'quasar';
import { InventoryForm } from './models/inventory';
import { CustomerApi, Customer } from '@/api/customer';
import { ProductApi, Product } from '@/api/product';
import { handleError } from '@/utils';
import BarcodeScannerWrapper from '@/components/BarcodeScannerWrapper.vue';

const { t } = useI18n();

const props = defineProps<{
  modelValue: InventoryForm;
  columns: Array<{
    name: string;
    label: string;
    align?: 'left' | 'right' | 'center';
    field: string | ((row: unknown) => unknown);
    sortable?: boolean;
    required?: boolean;
  }>;
  onSubmit: (form: InventoryForm) => void;
}>();

const visible = ref(false);

const emit = defineEmits(['update:modelValue']);

const form = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});
const columns = computed(() => props.columns);

const returnTypes = computed(() => [
  {
    value: 'customer_return',
    name: t('inventory.returnType.customer_return'),
  },
  {
    value: 'supplier_return',
    name: t('inventory.returnType.supplier_return'),
  },
]);

const customers = ref<Customer[]>([]);
const filterCustomers = ref<Customer[]>([]);

const getCustomers = async () => {
  try {
    const response = await CustomerApi.fetch({
      filter: {
        is_supplier: form.value.is_supplier,
      },
    });

    customers.value = response.result.data;
    filterCustomers.value = response.result.data;
  } catch (error) {
    handleError(error);
  }
};

const customerFilterFn = (
  val: string,
  update: (callback: () => void) => void
) => {
  if (val === '') {
    update(() => {
      filterCustomers.value = [...customers.value];
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    filterCustomers.value = customers.value.filter(
      (v: Customer) => v.name.toLowerCase().indexOf(needle) > -1
    );
  });
};

const productOptions = ref<Product[]>([]);
const productFilterOptions = ref<Product[]>([]);

const getProducts = async () => {
  const response = await ProductApi.listProducts();

  productOptions.value = response.result.data;
  productFilterOptions.value = response.result.data;
};

// 商品新增
const addProductForm = ref<{
  product: Product | null;
  quantity: number;
}>({
  product: null,
  quantity: 1,
});

const addProductDialog = ref(false);
const openAddProductDialog = () => {
  addProductDialog.value = true;
  resetAddProductForm();
};

const closeAddProductDialog = () => {
  addProductDialog.value = false;
  isEditProduct.value = false;
  resetAddProductForm();
};

const isEditProduct = ref(false);
const editProduct = (productData: { product: Product; quantity: number }) => {
  openAddProductDialog();
  isEditProduct.value = true;
  addProductForm.value = {
    product: productData.product,
    quantity: productData.quantity,
  };
};

const addProductByBarcodeScan = (product: Product) => {
  addProductForm.value = {
    product,
    quantity: 1,
  };
  addProduct();
};

const addProduct = () => {
  if (!addProductForm.value.product) return;

  const product = form.value.items.find(
    (item) =>
      !!item.product && item.product.uuid === addProductForm.value.product?.uuid
  );

  if (product) {
    if (isEditProduct.value) {
      product.quantity = addProductForm.value.quantity;
    } else {
      product.quantity += addProductForm.value.quantity;
    }
  } else {
    form.value.items.push(addProductForm.value);
  }

  closeAddProductDialog();
};

const resetAddProductForm = () => {
  addProductForm.value = {
    product: null,
    quantity: 1,
  };
};

const productFilterFn = (
  val: string,
  update: (callback: () => void) => void
) => {
  if (val === '') {
    update(() => {
      // 如果沒有輸入值，顯示所有商品
      productFilterOptions.value = [...productOptions.value];
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    // 根據商品名稱進行過濾
    productFilterOptions.value = productOptions.value.filter(
      (v: Product) => v.name.toLowerCase().indexOf(needle) > -1
    );
  });
};

const removeItem = (item: { product: Product; quantity: number }) => {
  const index = form.value.items.indexOf(item);
  if (index > -1) {
    form.value.items.splice(index, 1);
  }
};

const openDialog = () => {
  visible.value = true;
  getCustomers();
  getProducts();
};

const closeDialog = () => {
  visible.value = false;
};

const isSubmit = ref(false);
const submitForm = async () => {
  try {
    isSubmit.value = true;
    await props.onSubmit(form.value);

    Notify.create({
      type: 'positive',
      message: t('success'),
      position: 'top',
    });
  } catch (error) {
    handleError(error);
  } finally {
    isSubmit.value = false;
    closeDialog();
  }
};

const isActiveCustomerInput = computed(() => () => {
  const activeList = ['stock_in', 'return'];
  return activeList.includes(props.modelValue.type);
});

defineExpose({
  openDialog,
});
</script>
