import{aC as ye,E as R,c as m,J as f,T as D,dG as K,a8 as be,r as P,bD as we,dH as ke,w as O,s as W,h as G,m as L,K as X,aQ as J,b3 as Y,$ as Q,G as Ce,I as Se,dI as Be,a1 as Te,R as qe,X as xe,Y as ze}from"./index.b4716878.js";import{Q as Pe}from"./QMenu.45353d3c.js";import{Q as Le}from"./QTooltip.cf1e9eea.js";import{a as U,Q as He}from"./QItemSection.a2ef2d56.js";import{u as Oe,a as $e,b as _e}from"./use-fullscreen.12714a56.js";function Z(e,t){if(t&&e===t)return null;const o=e.nodeName.toLowerCase();if(["div","li","ul","ol","blockquote"].includes(o)===!0)return e;const l=window.getComputedStyle?window.getComputedStyle(e):e.currentStyle,r=l.display;return r==="block"||r==="table"?e:Z(e.parentNode)}function E(e,t,o){return!e||e===document.body?!1:o===!0&&e===t||(t===document?document.body:t).contains(e.parentNode)}function V(e,t,o){if(o||(o=document.createRange(),o.selectNode(e),o.setStart(e,0)),t.count===0)o.setEnd(e,t.count);else if(t.count>0)if(e.nodeType===Node.TEXT_NODE)e.textContent.length<t.count?t.count-=e.textContent.length:(o.setEnd(e,t.count),t.count=0);else for(let l=0;t.count!==0&&l<e.childNodes.length;l++)o=V(e.childNodes[l],t,o);return o}const Ae=/^https?:\/\//;class Ee{constructor(t,o){this.el=t,this.eVm=o,this._range=null}get selection(){if(this.el){const t=document.getSelection();if(E(t.anchorNode,this.el,!0)&&E(t.focusNode,this.el,!0))return t}return null}get hasSelection(){return this.selection!==null?this.selection.toString().length!==0:!1}get range(){const t=this.selection;return t!==null&&t.rangeCount?t.getRangeAt(0):this._range}get parent(){const t=this.range;if(t!==null){const o=t.startContainer;return o.nodeType===document.ELEMENT_NODE?o:o.parentNode}return null}get blockParent(){const t=this.parent;return t!==null?Z(t,this.el):null}save(t=this.range){t!==null&&(this._range=t)}restore(t=this._range){const o=document.createRange(),l=document.getSelection();t!==null?(o.setStart(t.startContainer,t.startOffset),o.setEnd(t.endContainer,t.endOffset),l.removeAllRanges(),l.addRange(o)):(l.selectAllChildren(this.el),l.collapseToEnd())}savePosition(){let t=-1,o;const l=document.getSelection(),r=this.el.parentNode;if(l.focusNode&&E(l.focusNode,r))for(o=l.focusNode,t=l.focusOffset;o&&o!==r;)o!==this.el&&o.previousSibling?(o=o.previousSibling,t+=o.textContent.length):o=o.parentNode;this.savedPos=t}restorePosition(t=0){if(this.savedPos>0&&this.savedPos<t){const o=window.getSelection(),l=V(this.el,{count:this.savedPos});l&&(l.collapse(!1),o.removeAllRanges(),o.addRange(l))}}hasParent(t,o){const l=o?this.parent:this.blockParent;return l!==null?l.nodeName.toLowerCase()===t.toLowerCase():!1}hasParents(t,o,l=this.parent){return l===null?!1:t.includes(l.nodeName.toLowerCase())===!0?!0:o===!0?this.hasParents(t,o,l.parentNode):!1}is(t,o){if(this.selection===null)return!1;switch(t){case"formatBlock":return o==="DIV"&&this.parent===this.el||this.hasParent(o,o==="PRE");case"link":return this.hasParent("A",!0);case"fontSize":return document.queryCommandValue(t)===o;case"fontName":const l=document.queryCommandValue(t);return l===`"${o}"`||l===o;case"fullscreen":return this.eVm.inFullscreen.value;case"viewsource":return this.eVm.isViewingSource.value;case void 0:return!1;default:const r=document.queryCommandState(t);return o!==void 0?r===o:r}}getParentAttribute(t){return this.parent!==null?this.parent.getAttribute(t):null}can(t){if(t==="outdent")return this.hasParents(["blockquote","li"],!0);if(t==="indent")return this.hasParents(["li"],!0);if(t==="link")return this.selection!==null||this.is("link")}apply(t,o,l=ye){if(t==="formatBlock")["BLOCKQUOTE","H1","H2","H3","H4","H5","H6"].includes(o)&&this.is(t,o)&&(t="outdent",o=null),o==="PRE"&&this.is(t,"PRE")&&(o="P");else if(t==="print"){l();const r=window.open();r.document.write(`
        <!doctype html>
        <html>
          <head>
            <title>Print - ${document.title}</title>
          </head>
          <body>
            <div>${this.el.innerHTML}</div>
          </body>
        </html>
      `),r.print(),r.close();return}else if(t==="link"){const r=this.getParentAttribute("href");if(r===null){const u=this.selectWord(this.selection),a=u?u.toString():"";if(!a.length&&(!this.range||!this.range.cloneContents().querySelector("img")))return;this.eVm.editLinkUrl.value=Ae.test(a)?a:"https://",document.execCommand("createLink",!1,this.eVm.editLinkUrl.value),this.save(u.getRangeAt(0))}else this.eVm.editLinkUrl.value=r,this.range.selectNodeContents(this.parent),this.save();return}else if(t==="fullscreen"){this.eVm.toggleFullscreen(),l();return}else if(t==="viewsource"){this.eVm.isViewingSource.value=this.eVm.isViewingSource.value===!1,this.eVm.setContent(this.eVm.props.modelValue),l();return}document.execCommand(t,!1,o),l()}selectWord(t){if(t===null||t.isCollapsed!==!0||t.modify===void 0)return t;const o=document.createRange();o.setStart(t.anchorNode,t.anchorOffset),o.setEnd(t.focusNode,t.focusOffset);const l=o.collapsed?["backward","forward"]:["forward","backward"];o.detach();const r=t.focusNode,u=t.focusOffset;return t.collapse(t.anchorNode,t.anchorOffset),t.modify("move",l[0],"character"),t.modify("move",l[1],"word"),t.extend(r,u),t.modify("extend",l[1],"character"),t.modify("extend",l[0],"word"),t}}var Ne=R({name:"QBtnGroup",props:{unelevated:Boolean,outline:Boolean,flat:Boolean,rounded:Boolean,square:Boolean,push:Boolean,stretch:Boolean,glossy:Boolean,spread:Boolean},setup(e,{slots:t}){const o=m(()=>{const l=["unelevated","outline","flat","rounded","square","push","stretch","glossy"].filter(r=>e[r]===!0).map(r=>`q-btn-group--${r}`).join(" ");return`q-btn-group row no-wrap${l.length!==0?" "+l:""}`+(e.spread===!0?" q-btn-group--spread":" inline")});return()=>f("div",{class:o.value},D(t.default))}});const Re=Object.keys(K);function De(e){return Re.reduce((t,o)=>{const l=e[o];return l!==void 0&&(t[o]=l),t},{})}var Fe=R({name:"QBtnDropdown",props:{...K,...be,modelValue:Boolean,split:Boolean,dropdownIcon:String,contentClass:[Array,String,Object],contentStyle:[Array,String,Object],cover:Boolean,persistent:Boolean,noRouteDismiss:Boolean,autoClose:Boolean,menuAnchor:{type:String,default:"bottom end"},menuSelf:{type:String,default:"top end"},menuOffset:Array,disableMainBtn:Boolean,disableDropdown:Boolean,noIconAnimation:Boolean,toggleAriaLabel:String},emits:["update:modelValue","click","beforeShow","show","beforeHide","hide"],setup(e,{slots:t,emit:o}){const{proxy:l}=X(),r=P(e.modelValue),u=P(null),a=we(),g=m(()=>{const c={"aria-expanded":r.value===!0?"true":"false","aria-haspopup":"true","aria-controls":a.value,"aria-label":e.toggleAriaLabel||l.$q.lang.label[r.value===!0?"collapse":"expand"](e.label)};return(e.disable===!0||e.split===!1&&e.disableMainBtn===!0||e.disableDropdown===!0)&&(c["aria-disabled"]="true"),c}),w=m(()=>"q-btn-dropdown__arrow"+(r.value===!0&&e.noIconAnimation===!1?" rotate-180":"")+(e.split===!1?" q-btn-dropdown__arrow-container":"")),y=m(()=>ke(e)),s=m(()=>De(e));O(()=>e.modelValue,c=>{u.value!==null&&u.value[c?"show":"hide"]()}),O(()=>e.split,B);function b(c){r.value=!0,o("beforeShow",c)}function C(c){o("show",c),o("update:modelValue",!0)}function h(c){r.value=!1,o("beforeHide",c)}function z(c){o("hide",c),o("update:modelValue",!1)}function k(c){o("click",c)}function q(c){J(c),B(),o("click",c)}function x(c){u.value!==null&&u.value.toggle(c)}function H(c){u.value!==null&&u.value.show(c)}function B(c){u.value!==null&&u.value.hide(c)}return Object.assign(l,{show:H,hide:B,toggle:x}),W(()=>{e.modelValue===!0&&H()}),()=>{const c=[f(G,{class:w.value,name:e.dropdownIcon||l.$q.iconSet.arrow.dropdown})];return e.disableDropdown!==!0&&c.push(f(Pe,{ref:u,id:a.value,class:e.contentClass,style:e.contentStyle,cover:e.cover,fit:!0,persistent:e.persistent,noRouteDismiss:e.noRouteDismiss,autoClose:e.autoClose,anchor:e.menuAnchor,self:e.menuSelf,offset:e.menuOffset,separateClosePopup:!0,transitionShow:e.transitionShow,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,onBeforeShow:b,onShow:C,onBeforeHide:h,onHide:z},t.default)),e.split===!1?f(L,{class:"q-btn-dropdown q-btn-dropdown--simple",...s.value,...g.value,disable:e.disable===!0||e.disableMainBtn===!0,noWrap:!0,round:!1,onClick:k},{default:()=>D(t.label,[]).concat(c),loading:t.loading}):f(Ne,{class:"q-btn-dropdown q-btn-dropdown--split no-wrap q-btn-item",rounded:e.rounded,square:e.square,...y.value,glossy:e.glossy,stretch:e.stretch},()=>[f(L,{class:"q-btn-dropdown--current",...s.value,disable:e.disable===!0||e.disableMainBtn===!0,noWrap:!0,round:!1,onClick:q},{default:t.label,loading:t.loading}),f(L,{class:"q-btn-dropdown__arrow-container q-anchor--skip",...g.value,...y.value,disable:e.disable===!0||e.disableDropdown===!0,rounded:e.rounded,color:e.color,textColor:e.textColor,dense:e.dense,size:e.size,padding:e.padding,ripple:e.ripple},()=>c)])}}});function ee(e,t,o){t.handler?t.handler(e,o,o.caret):o.runCmd(t.cmd,t.param)}function F(e){return f("div",{class:"q-editor__toolbar-group"},e)}function te(e,t,o,l=!1){const r=l||(t.type==="toggle"?t.toggled?t.toggled(e):t.cmd&&e.caret.is(t.cmd,t.param):!1),u=[];if(t.tip&&e.$q.platform.is.desktop){const a=t.key?f("div",[f("small",`(CTRL + ${String.fromCharCode(t.key)})`)]):null;u.push(f(Le,{delay:1e3},()=>[f("div",{innerHTML:t.tip}),a]))}return f(L,{...e.buttonProps.value,icon:t.icon!==null?t.icon:void 0,color:r?t.toggleColor||e.props.toolbarToggleColor:t.color||e.props.toolbarColor,textColor:r&&!e.props.toolbarPush?null:t.textColor||e.props.toolbarTextColor,label:t.label,disable:t.disable?typeof t.disable=="function"?t.disable(e):!0:!1,size:"sm",onClick(a){o&&o(),ee(a,t,e)}},()=>u)}function je(e,t){const o=t.list==="only-icons";let l=t.label,r=t.icon!==null?t.icon:void 0,u,a;function g(){y.component.proxy.hide()}if(o)a=t.options.map(s=>{const b=s.type===void 0?e.caret.is(s.cmd,s.param):!1;return b&&(l=s.tip,r=s.icon!==null?s.icon:void 0),te(e,s,g,b)}),u=e.toolbarBackgroundClass.value,a=[F(a)];else{const s=e.props.toolbarToggleColor!==void 0?`text-${e.props.toolbarToggleColor}`:null,b=e.props.toolbarTextColor!==void 0?`text-${e.props.toolbarTextColor}`:null,C=t.list==="no-icons";a=t.options.map(h=>{const z=h.disable?h.disable(e):!1,k=h.type===void 0?e.caret.is(h.cmd,h.param):!1;k&&(l=h.tip,r=h.icon!==null?h.icon:void 0);const q=h.htmlTip;return f(He,{active:k,activeClass:s,clickable:!0,disable:z,dense:!0,onClick(x){g(),e.contentRef.value!==null&&e.contentRef.value.focus(),e.caret.restore(),ee(x,h,e)}},()=>[C===!0?null:f(U,{class:k?s:b,side:!0},()=>f(G,{name:h.icon!==null?h.icon:void 0})),f(U,q?()=>f("div",{class:"text-no-wrap",innerHTML:h.htmlTip}):h.tip?()=>f("div",{class:"text-no-wrap"},h.tip):void 0)])}),u=[e.toolbarBackgroundClass.value,b]}const w=t.highlight&&l!==t.label,y=f(Fe,{...e.buttonProps.value,noCaps:!0,noWrap:!0,color:w?e.props.toolbarToggleColor:e.props.toolbarColor,textColor:w&&!e.props.toolbarPush?null:e.props.toolbarTextColor,label:t.fixedLabel?t.label:l,icon:t.fixedIcon?t.icon!==null?t.icon:void 0:r,contentClass:u,onShow:s=>e.emit("dropdownShow",s),onHide:s=>e.emit("dropdownHide",s),onBeforeShow:s=>e.emit("dropdownBeforeShow",s),onBeforeHide:s=>e.emit("dropdownBeforeHide",s)},()=>a);return y}function Ie(e){if(e.caret)return e.buttons.value.filter(t=>!e.isViewingSource.value||t.find(o=>o.cmd==="viewsource")).map(t=>F(t.map(o=>e.isViewingSource.value&&o.cmd!=="viewsource"?!1:o.type==="slot"?D(e.slots[o.slot]):o.type==="dropdown"?je(e,o):te(e,o))))}function Qe(e,t,o,l={}){const r=Object.keys(l);if(r.length===0)return{};const u={default_font:{cmd:"fontName",param:e,icon:o,tip:t}};return r.forEach(a=>{const g=l[a];u[a]={cmd:"fontName",param:g,icon:o,tip:g,htmlTip:`<font face="${g}">${g}</font>`}}),u}function Ue(e){if(e.caret){const t=e.props.toolbarColor||e.props.toolbarTextColor;let o=e.editLinkUrl.value;const l=()=>{e.caret.restore(),o!==e.editLinkUrl.value&&document.execCommand("createLink",!1,o===""?" ":o),e.editLinkUrl.value=null};return[f("div",{class:`q-mx-xs text-${t}`},`${e.$q.lang.editor.url}: `),f("input",{key:"qedt_btm_input",class:"col q-editor__link-input",value:o,onInput:r=>{J(r),o=r.target.value},onKeydown:r=>{if(Y(r)!==!0)switch(r.keyCode){case 13:return Q(r),l();case 27:Q(r),e.caret.restore(),(!e.editLinkUrl.value||e.editLinkUrl.value==="https://")&&document.execCommand("unlink"),e.editLinkUrl.value=null;break}}}),F([f(L,{key:"qedt_btm_rem",tabindex:-1,...e.buttonProps.value,label:e.$q.lang.label.remove,noCaps:!0,onClick:()=>{e.caret.restore(),document.execCommand("unlink"),e.editLinkUrl.value=null}}),f(L,{key:"qedt_btm_upd",...e.buttonProps.value,label:e.$q.lang.label.update,noCaps:!0,onClick:l})])]}}const Me=Object.prototype.toString,N=Object.prototype.hasOwnProperty,Ke=new Set(["Boolean","Number","String","Function","Array","Date","RegExp"].map(e=>"[object "+e+"]"));function M(e){if(e!==Object(e)||Ke.has(Me.call(e))===!0||e.constructor&&N.call(e,"constructor")===!1&&N.call(e.constructor.prototype,"isPrototypeOf")===!1)return!1;let t;for(t in e);return t===void 0||N.call(e,t)}function oe(){let e,t,o,l,r,u,a=arguments[0]||{},g=1,w=!1;const y=arguments.length;for(typeof a=="boolean"&&(w=a,a=arguments[1]||{},g=2),Object(a)!==a&&typeof a!="function"&&(a={}),y===g&&(a=this,g--);g<y;g++)if((e=arguments[g])!==null)for(t in e)o=a[t],l=e[t],a!==l&&(w===!0&&l&&((r=Array.isArray(l))||M(l)===!0)?(r===!0?u=Array.isArray(o)===!0?o:[]:u=M(o)===!0?o:{},a[t]=oe(w,u,l)):l!==void 0&&(a[t]=l));return a}var Ze=R({name:"QEditor",props:{...Ce,...Oe,modelValue:{type:String,required:!0},readonly:Boolean,disable:Boolean,minHeight:{type:String,default:"10rem"},maxHeight:String,height:String,definitions:Object,fonts:Object,placeholder:String,toolbar:{type:Array,validator:e=>e.length===0||e.every(t=>t.length),default:()=>[["left","center","right","justify"],["bold","italic","underline","strike"],["undo","redo"]]},toolbarColor:String,toolbarBg:String,toolbarTextColor:String,toolbarToggleColor:{type:String,default:"primary"},toolbarOutline:Boolean,toolbarPush:Boolean,toolbarRounded:Boolean,paragraphTag:{type:String,validator:e=>["div","p"].includes(e),default:"div"},contentStyle:Object,contentClass:[Object,Array,String],square:Boolean,flat:Boolean,dense:Boolean},emits:[...$e,"update:modelValue","keydown","click","focus","blur","dropdownShow","dropdownHide","dropdownBeforeShow","dropdownBeforeHide","linkShow","linkHide"],setup(e,{slots:t,emit:o}){const{proxy:l}=X(),{$q:r}=l,u=Se(e,r),{inFullscreen:a,toggleFullscreen:g}=_e(),w=Be(),y=P(null),s=P(null),b=P(null),C=P(!1),h=m(()=>!e.readonly&&!e.disable);let z,k,q=e.modelValue;document.execCommand("defaultParagraphSeparator",!1,e.paragraphTag),z=window.getComputedStyle(document.body).fontFamily;const x=m(()=>e.toolbarBg?` bg-${e.toolbarBg}`:""),H=m(()=>{const n=e.toolbarOutline!==!0&&e.toolbarPush!==!0;return{type:"a",flat:n,noWrap:!0,outline:e.toolbarOutline,push:e.toolbarPush,rounded:e.toolbarRounded,dense:!0,color:e.toolbarColor,disable:!h.value,size:"sm"}}),B=m(()=>{const n=r.lang.editor,i=r.iconSet.editor;return{bold:{cmd:"bold",icon:i.bold,tip:n.bold,key:66},italic:{cmd:"italic",icon:i.italic,tip:n.italic,key:73},strike:{cmd:"strikeThrough",icon:i.strikethrough,tip:n.strikethrough,key:83},underline:{cmd:"underline",icon:i.underline,tip:n.underline,key:85},unordered:{cmd:"insertUnorderedList",icon:i.unorderedList,tip:n.unorderedList},ordered:{cmd:"insertOrderedList",icon:i.orderedList,tip:n.orderedList},subscript:{cmd:"subscript",icon:i.subscript,tip:n.subscript,htmlTip:"x<subscript>2</subscript>"},superscript:{cmd:"superscript",icon:i.superscript,tip:n.superscript,htmlTip:"x<superscript>2</superscript>"},link:{cmd:"link",disable:d=>d.caret&&!d.caret.can("link"),icon:i.hyperlink,tip:n.hyperlink,key:76},fullscreen:{cmd:"fullscreen",icon:i.toggleFullscreen,tip:n.toggleFullscreen,key:70},viewsource:{cmd:"viewsource",icon:i.viewSource,tip:n.viewSource},quote:{cmd:"formatBlock",param:"BLOCKQUOTE",icon:i.quote,tip:n.quote,key:81},left:{cmd:"justifyLeft",icon:i.left,tip:n.left},center:{cmd:"justifyCenter",icon:i.center,tip:n.center},right:{cmd:"justifyRight",icon:i.right,tip:n.right},justify:{cmd:"justifyFull",icon:i.justify,tip:n.justify},print:{type:"no-state",cmd:"print",icon:i.print,tip:n.print,key:80},outdent:{type:"no-state",disable:d=>d.caret&&!d.caret.can("outdent"),cmd:"outdent",icon:i.outdent,tip:n.outdent},indent:{type:"no-state",disable:d=>d.caret&&!d.caret.can("indent"),cmd:"indent",icon:i.indent,tip:n.indent},removeFormat:{type:"no-state",cmd:"removeFormat",icon:i.removeFormat,tip:n.removeFormat},hr:{type:"no-state",cmd:"insertHorizontalRule",icon:i.hr,tip:n.hr},undo:{type:"no-state",cmd:"undo",icon:i.undo,tip:n.undo,key:90},redo:{type:"no-state",cmd:"redo",icon:i.redo,tip:n.redo,key:89},h1:{cmd:"formatBlock",param:"H1",icon:i.heading1||i.heading,tip:n.heading1,htmlTip:`<h1 class="q-ma-none">${n.heading1}</h1>`},h2:{cmd:"formatBlock",param:"H2",icon:i.heading2||i.heading,tip:n.heading2,htmlTip:`<h2 class="q-ma-none">${n.heading2}</h2>`},h3:{cmd:"formatBlock",param:"H3",icon:i.heading3||i.heading,tip:n.heading3,htmlTip:`<h3 class="q-ma-none">${n.heading3}</h3>`},h4:{cmd:"formatBlock",param:"H4",icon:i.heading4||i.heading,tip:n.heading4,htmlTip:`<h4 class="q-ma-none">${n.heading4}</h4>`},h5:{cmd:"formatBlock",param:"H5",icon:i.heading5||i.heading,tip:n.heading5,htmlTip:`<h5 class="q-ma-none">${n.heading5}</h5>`},h6:{cmd:"formatBlock",param:"H6",icon:i.heading6||i.heading,tip:n.heading6,htmlTip:`<h6 class="q-ma-none">${n.heading6}</h6>`},p:{cmd:"formatBlock",param:e.paragraphTag,icon:i.heading,tip:n.paragraph},code:{cmd:"formatBlock",param:"PRE",icon:i.code,htmlTip:`<code>${n.code}</code>`},"size-1":{cmd:"fontSize",param:"1",icon:i.size1||i.size,tip:n.size1,htmlTip:`<font size="1">${n.size1}</font>`},"size-2":{cmd:"fontSize",param:"2",icon:i.size2||i.size,tip:n.size2,htmlTip:`<font size="2">${n.size2}</font>`},"size-3":{cmd:"fontSize",param:"3",icon:i.size3||i.size,tip:n.size3,htmlTip:`<font size="3">${n.size3}</font>`},"size-4":{cmd:"fontSize",param:"4",icon:i.size4||i.size,tip:n.size4,htmlTip:`<font size="4">${n.size4}</font>`},"size-5":{cmd:"fontSize",param:"5",icon:i.size5||i.size,tip:n.size5,htmlTip:`<font size="5">${n.size5}</font>`},"size-6":{cmd:"fontSize",param:"6",icon:i.size6||i.size,tip:n.size6,htmlTip:`<font size="6">${n.size6}</font>`},"size-7":{cmd:"fontSize",param:"7",icon:i.size7||i.size,tip:n.size7,htmlTip:`<font size="7">${n.size7}</font>`}}}),c=m(()=>{const n=e.definitions||{},i=e.definitions||e.fonts?oe(!0,{},B.value,n,Qe(z,r.lang.editor.defaultFont,r.iconSet.editor.font,e.fonts)):B.value;return e.toolbar.map(d=>d.map(p=>{if(p.options)return{type:"dropdown",icon:p.icon,label:p.label,size:"sm",dense:!0,fixedLabel:p.fixedLabel,fixedIcon:p.fixedIcon,highlight:p.highlight,list:p.list,options:p.options.map(ve=>i[ve])};const S=i[p];return S?S.type==="no-state"||n[p]&&(S.cmd===void 0||B.value[S.cmd]&&B.value[S.cmd].type==="no-state")?S:Object.assign({type:"toggle"},S):{type:"slot",slot:p}}))}),v={$q:r,props:e,slots:t,emit:o,inFullscreen:a,toggleFullscreen:g,runCmd:_,isViewingSource:C,editLinkUrl:b,toolbarBackgroundClass:x,buttonProps:H,contentRef:s,buttons:c,setContent:$};O(()=>e.modelValue,n=>{q!==n&&(q=n,$(n,!0))}),O(b,n=>{o(`link${n?"Show":"Hide"}`)});const ne=m(()=>e.toolbar&&e.toolbar.length!==0),ie=m(()=>{const n={},i=d=>{d.key&&(n[d.key]={cmd:d.cmd,param:d.param})};return c.value.forEach(d=>{d.forEach(p=>{p.options?p.options.forEach(i):i(p)})}),n}),le=m(()=>a.value?e.contentStyle:[{minHeight:e.minHeight,height:e.height,maxHeight:e.maxHeight},e.contentStyle]),re=m(()=>`q-editor q-editor--${C.value===!0?"source":"default"}`+(e.disable===!0?" disabled":"")+(a.value===!0?" fullscreen column":"")+(e.square===!0?" q-editor--square no-border-radius":"")+(e.flat===!0?" q-editor--flat":"")+(e.dense===!0?" q-editor--dense":"")+(u.value===!0?" q-editor--dark q-dark":"")),ae=m(()=>[e.contentClass,"q-editor__content",{col:a.value,"overflow-auto":a.value||e.maxHeight}]),se=m(()=>e.disable===!0?{"aria-disabled":"true"}:{});function ue(){if(s.value!==null){const n=`inner${C.value===!0?"Text":"HTML"}`,i=s.value[n];i!==e.modelValue&&(q=i,o("update:modelValue",i))}}function ce(n){if(o("keydown",n),n.ctrlKey!==!0||Y(n)===!0){T();return}const i=n.keyCode,d=ie.value[i];if(d!==void 0){const{cmd:p,param:S}=d;qe(n),_(p,S,!1)}}function de(n){T(),o("click",n)}function fe(n){if(s.value!==null){const{scrollTop:i,scrollHeight:d}=s.value;k=d-i}v.caret.save(),o("blur",n)}function he(n){xe(()=>{s.value!==null&&k!==void 0&&(s.value.scrollTop=s.value.scrollHeight-k)}),o("focus",n)}function ge(n){const i=y.value;if(i!==null&&i.contains(n.target)===!0&&(n.relatedTarget===null||i.contains(n.relatedTarget)!==!0)){const d=`inner${C.value===!0?"Text":"HTML"}`;v.caret.restorePosition(s.value[d].length),T()}}function pe(n){const i=y.value;i!==null&&i.contains(n.target)===!0&&(n.relatedTarget===null||i.contains(n.relatedTarget)!==!0)&&(v.caret.savePosition(),T())}function j(){k=void 0}function I(n){v.caret.save()}function $(n,i){if(s.value!==null){i===!0&&v.caret.savePosition();const d=`inner${C.value===!0?"Text":"HTML"}`;s.value[d]=n,i===!0&&(v.caret.restorePosition(s.value[d].length),T())}}function _(n,i,d=!0){A(),v.caret.restore(),v.caret.apply(n,i,()=>{A(),v.caret.save(),d&&T()})}function T(){setTimeout(()=>{b.value=null,l.$forceUpdate()},1)}function A(){ze(()=>{s.value!==null&&s.value.focus({preventScroll:!0})})}function me(){return s.value}return W(()=>{v.caret=l.caret=new Ee(s.value,v),$(e.modelValue),T(),document.addEventListener("selectionchange",I)}),Te(()=>{document.removeEventListener("selectionchange",I)}),Object.assign(l,{runCmd:_,refreshToolbar:T,focus:A,getContentEl:me}),()=>{let n;if(ne.value){const i=[f("div",{key:"qedt_top",class:"q-editor__toolbar row no-wrap scroll-x"+x.value},Ie(v))];b.value!==null&&i.push(f("div",{key:"qedt_btm",class:"q-editor__toolbar row no-wrap items-center scroll-x"+x.value},Ue(v))),n=f("div",{key:"toolbar_ctainer",class:"q-editor__toolbars-container"},i)}return f("div",{ref:y,class:re.value,style:{height:a.value===!0?"100%":null},...se.value,onFocusin:ge,onFocusout:pe},[n,f("div",{ref:s,style:le.value,class:ae.value,contenteditable:h.value,placeholder:e.placeholder,...w.listeners.value,onInput:ue,onKeydown:ce,onClick:de,onBlur:fe,onFocus:he,onMousedown:j,onTouchstartPassive:j})])}}});export{Ze as Q};
