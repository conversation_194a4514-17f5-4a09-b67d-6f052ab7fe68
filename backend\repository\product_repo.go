package repository

import (
	"context"

	"gorm.io/gorm"

	"cx/domain"
	"cx/utils"
)

type ProductRepository interface {
	WithTx(tx *gorm.DB) ProductRepository

	Create(ctx context.Context, product *domain.Product) error
	Update(ctx context.Context, uuid string, product *domain.ProductUpdatePayload) error
	UpdateStatus(ctx context.Context, uuid string, isActive bool) error
	ListProducts(ctx context.Context, filter *domain.ProductFilter, pagination *domain.Pagination) ([]domain.Product, error)
	GetByID(ctx context.Context, id int64) (*domain.Product, error)
	GetByUUID(ctx context.Context, uuid string) (*domain.Product, error)
	Delete(ctx context.Context, uuid string) error

	GetImageByUUID(ctx context.Context, imageUUID string) (*domain.ProductImage, error)
	GetImageLastSortOrder(ctx context.Context, productID int64) (int, error)
	CreateImage(ctx context.Context, image *domain.ProductImage) error
	UpdateImageSortOrder(ctx context.Context, imageUUID string, sortOrder int) error
	UpdateImageWcID(ctx context.Context, imageID int64, wcID int64) error
	DeleteImage(ctx context.Context, imageUUID string) error

	AddSupplier(ctx context.Context, productID, supplierID int64) error
	RemoveSupplier(ctx context.Context, productID, supplierID int64) error
}

type productRepository struct {
	db *gorm.DB
}

func NewProductRepository(db *gorm.DB) ProductRepository {
	return &productRepository{db}
}

func (r *productRepository) WithTx(tx *gorm.DB) ProductRepository {
	return &productRepository{tx}
}

func (r *productRepository) Create(ctx context.Context, product *domain.Product) error {
	product.UUID = utils.GenerateUUID()
	return r.db.WithContext(ctx).Create(product).Error
}

func (r *productRepository) Update(ctx context.Context, uuid string, product *domain.ProductUpdatePayload) error {
	return r.db.WithContext(ctx).Model(&domain.Product{}).Where("uuid = ?", uuid).Updates(product).Error
}

func (r *productRepository) UpdateStatus(ctx context.Context, uuid string, isActive bool) error {
	return r.db.WithContext(ctx).Model(&domain.Product{}).Where("uuid = ?", uuid).Update("is_active", isActive).Error
}

func (r *productRepository) ListProducts(ctx context.Context, filter *domain.ProductFilter, pagination *domain.Pagination) ([]domain.Product, error) {
	var products []domain.Product

	tx := r.db.WithContext(ctx).Model(&domain.Product{})

	if filter.Search != "" {
		tx = tx.Where("name LIKE ? OR barcode LIKE ?", "%"+filter.Search+"%", "%"+filter.Search+"%")
	}

	if filter.CategoryID != 0 {
		tx = tx.Where("category_id = ?", filter.CategoryID)
	}

	if filter.Name != "" {
		tx = tx.Where("name LIKE ?", "%"+filter.Name+"%")
	}

	if filter.Barcode != "" {
		tx = tx.Where("barcode = ?", filter.Barcode)
	}

	// 總數量
	if err := tx.Count(&pagination.RowsNumber).Error; err != nil {
		return nil, err
	}

	tx = pagination.SetPaginationToDB(tx, "created_at", true)

	err := tx.Preload("Category").Preload("Images", func(db *gorm.DB) *gorm.DB {
		return db.Order("sort_order ASC")
	}).Preload("Suppliers").Find(&products).Error

	return products, err
}

func (r *productRepository) GetByID(ctx context.Context, id int64) (*domain.Product, error) {
	var product domain.Product
	err := r.db.WithContext(ctx).Preload("Category").Where("id = ?", id).First(&product).Error
	return &product, err
}

func (r *productRepository) GetByUUID(ctx context.Context, uuid string) (*domain.Product, error) {
	var product domain.Product
	err := r.db.WithContext(ctx).Preload("Category").Preload("Images", func(db *gorm.DB) *gorm.DB {
		return db.Order("sort_order ASC")
	}).Preload("Suppliers").Where("uuid = ?", uuid).First(&product).Error
	return &product, err
}

func (r *productRepository) Delete(ctx context.Context, uuid string) error {
	return r.db.WithContext(ctx).Where("uuid = ?", uuid).Delete(&domain.Product{}).Error
}

func (r *productRepository) GetImageByUUID(ctx context.Context, imageUUID string) (*domain.ProductImage, error) {
	var image domain.ProductImage

	err := r.db.WithContext(ctx).Where("uuid = ?", imageUUID).First(&image).Error
	return &image, err
}

func (r *productRepository) GetImageLastSortOrder(ctx context.Context, productID int64) (int, error) {
	var lastSortOrder int

	tx := r.db.WithContext(ctx).Model(&domain.ProductImage{})

	err := tx.Select("COALESCE(MAX(sort_order), 0)").
		Where("product_id = ?", productID).
		Scan(&lastSortOrder).Error

	return lastSortOrder, err
}

func (r *productRepository) CreateImage(ctx context.Context, image *domain.ProductImage) error {
	return r.db.WithContext(ctx).Create(image).Error
}

func (r *productRepository) UpdateImageSortOrder(ctx context.Context, imageUUID string, sortOrder int) error {
	return r.db.WithContext(ctx).Model(&domain.ProductImage{}).
		Where("uuid = ?", imageUUID).
		Update("sort_order", sortOrder).Error
}

func (r *productRepository) UpdateImageWcID(ctx context.Context, imageID int64, wcID int64) error {
	return r.db.WithContext(ctx).Model(&domain.ProductImage{}).Where("id = ?", imageID).Update("wc_id", wcID).Error
}

func (r *productRepository) DeleteImage(ctx context.Context, imageUUID string) error {
	return r.db.WithContext(ctx).Where("uuid = ?", imageUUID).Delete(&domain.ProductImage{}).Error
}

func (r *productRepository) AddSupplier(ctx context.Context, productID, supplierID int64) error {
	tx := r.db.WithContext(ctx)

	return tx.Model(&domain.ProductSupplier{}).Create(&domain.ProductSupplier{
		ProductID:  productID,
		CustomerID: supplierID,
	}).Error
}

func (r *productRepository) RemoveSupplier(ctx context.Context, productID, supplierID int64) error {
	tx := r.db.WithContext(ctx)

	return tx.Where("product_id = ? AND customer_id = ?", productID, supplierID).Delete(&domain.ProductSupplier{}).Error
}
