-- 創建 xero_order_sync 表
CREATE TABLE `xero_order_syncs` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `uuid` VARCHAR(36) NOT NULL UNIQUE,
    `order_uuid` VARCHAR(36) NOT NULL,
    `xero_invoice_id` VARCHAR(255),
    `xero_invoice_no` VARCHAR(255),
    `sync_status` ENUM('pending', 'syncing', 'success', 'failed', 'voided') NOT NULL DEFAULT 'pending',
    `error_message` TEXT,
    `last_sync_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_order_uuid` (`order_uuid`),
    INDEX `idx_sync_status` (`sync_status`),
    INDEX `idx_xero_invoice_id` (`xero_invoice_id`),
    
    FOREIGN KEY (`order_uuid`) REFERENCES `orders`(`uuid`) ON DELETE CASCADE
);
