package repository

import (
	"cx/config"
	"encoding/json"
	"image"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gorm.io/gorm"
)

func InsertMediaToWordpress(wpDB *gorm.DB, imagePath string) (int64, error) {
	var attachmentID int64

	fileName := filepath.Base(imagePath)
	fileExt := filepath.Ext(fileName)
	mimeType := getMimeType(fileExt)

	// 建立 WordPress attachment post
	attachmentPost := map[string]interface{}{
		"post_author":           1,
		"post_date":             time.Now().Format("2006-01-02 15:04:05"),
		"post_date_gmt":         time.Now().UTC().Format("2006-01-02 15:04:05"),
		"post_content":          "",
		"post_title":            fileName,
		"post_excerpt":          "",
		"post_status":           "inherit",
		"post_name":             strings.ToLower(strings.ReplaceAll(fileName, " ", "-")),
		"to_ping":               "",
		"pinged":                "",
		"post_content_filtered": "",
		"post_modified":         time.Now().Format("2006-01-02 15:04:05"),
		"post_modified_gmt":     time.Now().UTC().Format("2006-01-02 15:04:05"),
		"guid":                  config.AppConfig.Server.AppDomain + "/api/" + imagePath, // WordPress上傳URL路徑
		"post_type":             "attachment",
		"post_mime_type":        mimeType,
	}

	// 插入 wp_posts 表
	if err := wpDB.Table("wp_posts").Create(attachmentPost).Error; err != nil {
		return 0, err
	}

	if err := wpDB.Table("wp_posts").Where("guid = ?", attachmentPost["guid"]).Select("ID").Scan(&attachmentID).Error; err != nil {
		return 0, err
	}

	// 插入必要的媒體庫相關 meta 數據
	metaData := []map[string]interface{}{
		{
			"post_id":    attachmentID,
			"meta_key":   "_wp_attached_file",
			"meta_value": "api/" + imagePath,
		},
		{
			"post_id":    attachmentID,
			"meta_key":   "_wp_attachment_metadata",
			"meta_value": generateAttachmentMetadata(imagePath), // 生成媒體庫需要的元數據
		},
	}

	for _, meta := range metaData {
		if err := wpDB.Table("wp_postmeta").Create(meta).Error; err != nil {
			return 0, err
		}
	}

	return attachmentID, nil
}

// 輔助函數：根據副檔名取得MIME類型
func getMimeType(ext string) string {
	switch strings.ToLower(ext) {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".webp":
		return "image/webp"
	default:
		return "image/jpeg"
	}
}

// 輔助函數：生成WordPress附件元數據 (簡化版)
func generateAttachmentMetadata(imagePath string) string {
	// 實際應用中，這裡應該讀取圖片並獲取尺寸信息
	// 以及生成不同尺寸的縮圖信息
	// 這裡只返回一個簡化的JSON字符串
	file, _ := os.Open(imagePath)
	defer file.Close()

	config, _, _ := image.DecodeConfig(file)

	metadata := map[string]interface{}{
		"width":  config.Width,
		"height": config.Height,
		"file":   filepath.Base(imagePath),
		"sizes": map[string]interface{}{
			"thumbnail": map[string]interface{}{
				"file":      "thumbnail-" + filepath.Base(imagePath),
				"width":     150,
				"height":    150,
				"mime-type": getMimeType(filepath.Ext(imagePath)),
			},
			// 其他尺寸...
		},
	}

	metadataJSON, _ := json.Marshal(metadata)
	return string(metadataJSON)
}
