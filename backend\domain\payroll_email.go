package domain

import "time"

type EmailStatus string

const (
	EmailStatusSent   EmailStatus = "sent"
	EmailStatusFailed EmailStatus = "failed"
)

type PayrollEmail struct {
	ID        int64       `gorm:"primary_key" json:"-"`
	UUID      string      `gorm:"<-:create" json:"uuid"`
	PayrollID int64       `json:"-"`
	UserID    int64       `json:"-"`
	Email     string      `json:"email"`
	Subject   string      `json:"subject"`
	Body      string      `json:"body"`
	Status    EmailStatus `json:"status"`
	SentAt    time.Time   `json:"sent_at"`
	CreatedAt time.Time   `json:"created_at"`
	UpdatedAt time.Time   `json:"updated_at"`
}

type PayrollEmailInfo struct {
	UUID      string      `json:"uuid"`
	PayrollID int64       `json:"-"`
	UserID    int64       `json:"-"`
	Email     string      `json:"email"`
	Status    EmailStatus `json:"status"`
	SentAt    string      `json:"sent_at"`
}

func (PayrollEmailInfo) TableName() string {
	return "payroll_emails"
}

type PayrollEmailCreateRequest struct {
	PeriodUUID string   `form:"period_uuid" json:"period_uuid"`
	UserUUIDs  []string `form:"user_uuids[]" json:"user_uuids"`
}

type PayrollEmailCreatePayload struct {
	ID        int64
	UUID      string
	PayrollID int64
	UserID    int64
	User      *User `gorm:"-"`
	Email     string
	Subject   string
	Body      string
	Status    EmailStatus
	SentAt    time.Time
	CreatedAt time.Time
	UpdatedAt time.Time
}

func (PayrollEmailCreatePayload) TableName() string {
	return "payroll_emails"
}

type PayrollDetailView struct {
	Name   string  `json:"name"`
	Amount float64 `json:"amount"`
}

type PayrollEmailTemplate struct {
	UserName        string              `json:"user_name"`
	PayrollPeriod   string              `json:"payroll_period"`
	PayDate         string              `json:"pay_date"`
	SalaryType      string              `json:"salary_type"`
	BasicSalary     float64             `json:"basic_salary"`
	HourlySalary    float64             `json:"hourly_salary"`
	WorkDays        int                 `json:"work_days"`
	WorkHours       int                 `json:"work_hours"`
	OvertimeHours   int                 `json:"overtime_hours"`
	IncomeItems     []PayrollDetailView `json:"income_items"`
	DeductionItems  []PayrollDetailView `json:"deduction_items"`
	BonusItems      []PayrollDetailView `json:"bonus_items"`
	GrossSalary     float64             `json:"gross_salary"`
	TotalDeductions float64             `json:"total_deductions"`
	NetSalary       float64             `json:"net_salary"`
	Notes           string              `json:"notes"`
}
