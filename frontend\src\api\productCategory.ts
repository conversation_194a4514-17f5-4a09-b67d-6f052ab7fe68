import { apiWrapper } from '@/boot/axios';
import { CreateResponse } from './modules/response';

export interface ProductCategory {
  id: number;
  name: string;
  image: ProductCategoryImage;
}

export interface ProductCategoryImage {
  uuid: string;
  image_path: string;
}

export const ProductCategoryApi = {
  listCategories: () =>
    apiWrapper.get<ProductCategory[]>('v1/product-categories'),
  get: (id: number) =>
    apiWrapper.get<ProductCategory>(`v1/product-categories/${id}`),
  create: (productCategory: ProductCategory) =>
    apiWrapper.post<CreateResponse>('v1/product-categories', productCategory),
  update: (productCategory: ProductCategory) =>
    apiWrapper.put(
      `v1/product-categories/${productCategory.id}`,
      productCategory
    ),
  delete: (id: number) => apiWrapper.delete(`v1/product-categories/${id}`),

  uploadImage: (productCategoryID: number, file: File) =>
    apiWrapper.uploadFile(
      `v1/product-categories/${productCategoryID}/images`,
      file
    ),
  deleteImage: (productCategoryID: number, imageUUID: string) =>
    apiWrapper.delete(
      `v1/product-categories/${productCategoryID}/images/${imageUUID}`
    ),
};
