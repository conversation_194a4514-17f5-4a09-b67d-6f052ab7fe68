package repository

import (
	"context"
	"cx/domain"
	"cx/utils"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type InventoryRepository interface {
	ListProductStocks(ctx context.Context, filter *domain.ProductStockFilter, pagination *domain.Pagination) ([]domain.ProductStockResponse, error)
	GetProductStock(ctx context.Context, productID int64) (int, int, error)
	UpdateProductStock(ctx context.Context, productID int64, quantity int) error

	CreateInventoryTransaction(ctx context.Context, transaction *domain.InventoryTransaction) error
	ListInventoryTransactions(ctx context.Context, filter *domain.InventoryTransactionFilter, pagination *domain.Pagination) ([]domain.InventoryTransaction, error)
}

type inventoryRepository struct {
	db   *gorm.DB
	wpDB *gorm.DB
}

func NewInventoryRepository(db *gorm.DB, wpDB *gorm.DB) InventoryRepository {
	return &inventoryRepository{
		db:   db,
		wpDB: wpDB,
	}
}

// 獲取產品庫存列表
func (r *inventoryRepository) ListProductStocks(ctx context.Context, filter *domain.ProductStockFilter, pagination *domain.Pagination) ([]domain.ProductStockResponse, error) {
	var productStocks []domain.ProductStockResponse

	tx := r.db.WithContext(ctx).Model(&domain.Product{}).
		Select("products.uuid", "products.name", "products.barcode", "products.stock_quantity", "products.min_stock_quantity",
			"(products.stock_quantity - products.min_stock_quantity) AS diff_stock_qty", "products.unit",
			"product_categories.name AS category").
		Joins("LEFT JOIN product_categories ON product_categories.id = products.category_id").
		Where("products.is_active = TRUE")

	// 過濾條件
	if filter.Search != "" {
		tx = tx.Where("products.name LIKE ? OR products.barcode LIKE ?", "%"+filter.Search+"%", "%"+filter.Search+"%")
	}

	if filter.Name != "" {
		tx = tx.Where("products.name LIKE ?", "%"+filter.Name+"%")
	}

	// 總數量
	if err := tx.Count(&pagination.RowsNumber).Error; err != nil {
		return productStocks, err
	}

	tx = pagination.SetPaginationToDB(tx, "diff_stock_qty", false)

	if err := tx.Find(&productStocks).Error; err != nil {
		return productStocks, err
	}

	return productStocks, nil
}

// 獲取產品庫存數量
// @return 庫存數量, 最小庫存數量, 錯誤
func (r *inventoryRepository) GetProductStock(ctx context.Context, productID int64) (int, int, error) {
	var product domain.Product
	err := r.db.WithContext(ctx).
		Select("stock_quantity", "min_stock_quantity").
		Where("id = ?", productID).
		First(&product).Error

	if err != nil {
		return 0, 0, err
	}

	return product.StockQuantity, product.MinStockQuantity, nil
}

// 更新庫存數量
func (r *inventoryRepository) UpdateProductStock(ctx context.Context, productID int64, quantity int) error {
	tx := r.db.WithContext(ctx)

	// 查詢產品
	var product domain.Product
	// 先查詢並鎖定該記錄，防止其他交易同時修改
	if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", productID).
		First(&product).Error; err != nil {
		return err
	}

	newStockQty := product.StockQuantity + quantity
	if err := tx.Model(&product).Update("stock_quantity", newStockQty).Error; err != nil {
		return err
	}

	if product.WcID != 0 {
		wcProductRepo := NewWcProductRepository(r.wpDB)
		wcProductRepo.UpdateStockQuantity(product.WcID, newStockQty)
	}

	return nil
}

// 創建庫存交易紀錄
func (r *inventoryRepository) CreateInventoryTransaction(ctx context.Context, transaction *domain.InventoryTransaction) error {
	if transaction.UUID == "" {
		transaction.UUID = utils.GenerateUUID()
	}

	return r.db.WithContext(ctx).Create(transaction).Error
}

// 獲取庫存交易紀錄列表
func (r *inventoryRepository) ListInventoryTransactions(ctx context.Context, filter *domain.InventoryTransactionFilter, pagination *domain.Pagination) ([]domain.InventoryTransaction, error) {
	var transactions []domain.InventoryTransaction

	tx := r.db.WithContext(ctx).Model(&domain.InventoryTransaction{})

	if filter.Search != "" {
		tx = tx.Where("product_id IN (?)",
			tx.Table("products").Select("id").Where("name LIKE ? OR barcode LIKE ?", "%"+filter.Search+"%", "%"+filter.Search+"%"))
	}

	if filter.ProductUUID != "" {
		tx = tx.Where("product_id IN (?)",
			tx.Table("products").Select("id").Where("uuid = ?", filter.ProductUUID))
	}

	if len(filter.TransactionTypes) > 0 {
		tx = tx.Where("transaction_type IN (?)", filter.TransactionTypes)
	}

	// 總數量
	if err := tx.Count(&pagination.RowsNumber).Error; err != nil {
		return nil, err
	}

	tx = pagination.SetPaginationToDB(tx, "created_at", true)

	if err := tx.Preload("Order").Preload("Product.Category").Find(&transactions).Error; err != nil {
		return nil, err
	}

	return transactions, nil
}
