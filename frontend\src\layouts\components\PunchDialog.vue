<template>
  <q-dialog v-model="visible" persistent no-refocus class="card-dialog">
    <q-card class="column">
      <q-card-section class="col-1 q-py-sm">
        <div class="row q-mb-sm">
          <!-- tabs -->
          <q-tabs
            v-model="tab"
            dense
            class="text-grey"
            active-color="primary"
            indicator-color="primary"
            align="justify"
            narrow-indicator
          >
            <q-tab name="punch" :label="t('punch')" />
            <q-tab name="history" :label="t('history')" />
          </q-tabs>

          <q-space />

          <!-- close button -->
          <q-btn
            type="button"
            icon="close"
            flat
            round
            dense
            @click="emit('update:modelValue', false)"
          />
        </div>
      </q-card-section>

      <q-card-section class="col-11">
        <q-tab-panels v-model="tab" class="full-height">
          <!-- Punch -->
          <q-tab-panel name="punch">
            <PunchForm :userInfo="localUser" @change-user="changeUser" />
          </q-tab-panel>
          <!-- History -->
          <q-tab-panel name="history">
            <q-scroll-area class="full-height q-px-md">
              <PunchHistoryList :userInfo="localUser" />
            </q-scroll-area>
          </q-tab-panel>
        </q-tab-panels>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { UserInfo } from '@/api/user';
import { useAuthStore } from '@/stores/auth-store';
import PunchForm from './PunchForm.vue';
import PunchHistoryList from './PunchHistoryList.vue';

const { t } = useI18n();

const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits(['update:modelValue']);

const authStore = useAuthStore();
const localUser = ref<UserInfo>(authStore.getUserInfo);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const tab = ref('punch');

const changeUser = (userInfo: UserInfo) => {
  localUser.value = userInfo;
};
</script>
