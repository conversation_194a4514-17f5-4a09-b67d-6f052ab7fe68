import{E as ge,a7 as ke,G as qe,aa as xe,I as Be,ac as Te,at as Ce,au as H,av as Se,r as k,c as i,ae as Me,aJ as $e,w as n,X as Z,s as Oe,a1 as De,C as Le,J as $,aK as p,T as We,K as Ae,aL as Pe}from"./index.9477d5a3.js";import{T as K}from"./TouchPan.ddd1c5b8.js";import{b as O}from"./format.054b8074.js";const ee=150;var Qe=ge({name:"QDrawer",inheritAttrs:!1,props:{...ke,...qe,side:{type:String,default:"left",validator:a=>["left","right"].includes(a)},width:{type:Number,default:300},mini:Boolean,miniToOverlay:Boolean,miniWidth:{type:Number,default:57},noMiniAnimation:Boolean,breakpoint:{type:Number,default:1023},showIfAbove:Boolean,behavior:{type:String,validator:a=>["default","desktop","mobile"].includes(a),default:"default"},bordered:Boolean,elevated:Boolean,overlay:Boolean,persistent:Boolean,noSwipeOpen:Boolean,noSwipeClose:Boolean,noSwipeBackdrop:Boolean},emits:[...xe,"onLayout","miniState"],setup(a,{slots:D,emit:y,attrs:N}){const f=Ae(),{proxy:{$q:d}}=f,ae=Be(a,d),{preventBodyScroll:L}=Pe(),{registerTimeout:R,removeTimeout:te}=Te(),t=Ce(Se,H);if(t===H)return console.error("QDrawer needs to be child of QLayout"),H;let W,b=null,q;const o=k(a.behavior==="mobile"||a.behavior!=="desktop"&&t.totalWidth.value<=a.breakpoint),x=i(()=>a.mini===!0&&o.value!==!0),s=i(()=>x.value===!0?a.miniWidth:a.width),u=k(a.showIfAbove===!0&&o.value===!1?!0:a.modelValue===!0),j=i(()=>a.persistent!==!0&&(o.value===!0||ie.value===!0));function E(e,l){if(le(),e!==!1&&t.animate(),v(0),o.value===!0){const r=t.instances[S.value];r!==void 0&&r.belowBreakpoint===!0&&r.hide(!1),m(1),t.isContainer.value!==!0&&L(!0)}else m(0),e!==!1&&F(!1);R(()=>{e!==!1&&F(!0),l!==!0&&y("show",e)},ee)}function J(e,l){ue(),e!==!1&&t.animate(),m(0),v(w.value*s.value),Q(),l!==!0?R(()=>{y("hide",e)},ee):te()}const{show:A,hide:B}=Me({showing:u,hideOnRouteChange:j,handleShow:E,handleHide:J}),{addToHistory:le,removeFromHistory:ue}=$e(u,B,j),C={belowBreakpoint:o,hide:B},c=i(()=>a.side==="right"),w=i(()=>(d.lang.rtl===!0?-1:1)*(c.value===!0?1:-1)),U=k(0),g=k(!1),P=k(!1),X=k(s.value*w.value),S=i(()=>c.value===!0?"left":"right"),I=i(()=>u.value===!0&&o.value===!1&&a.overlay===!1?a.miniToOverlay===!0?a.miniWidth:s.value:0),_=i(()=>a.overlay===!0||a.miniToOverlay===!0||t.view.value.indexOf(c.value?"R":"L")!==-1||d.platform.is.ios===!0&&t.isContainer.value===!0),T=i(()=>a.overlay===!1&&u.value===!0&&o.value===!1),ie=i(()=>a.overlay===!0&&u.value===!0&&o.value===!1),oe=i(()=>"fullscreen q-drawer__backdrop"+(u.value===!1&&g.value===!1?" hidden":"")),ne=i(()=>({backgroundColor:`rgba(0,0,0,${U.value*.4})`})),G=i(()=>c.value===!0?t.rows.value.top[2]==="r":t.rows.value.top[0]==="l"),re=i(()=>c.value===!0?t.rows.value.bottom[2]==="r":t.rows.value.bottom[0]==="l"),se=i(()=>{const e={};return t.header.space===!0&&G.value===!1&&(_.value===!0?e.top=`${t.header.offset}px`:t.header.space===!0&&(e.top=`${t.header.size}px`)),t.footer.space===!0&&re.value===!1&&(_.value===!0?e.bottom=`${t.footer.offset}px`:t.footer.space===!0&&(e.bottom=`${t.footer.size}px`)),e}),ve=i(()=>{const e={width:`${s.value}px`,transform:`translateX(${X.value}px)`};return o.value===!0?e:Object.assign(e,se.value)}),de=i(()=>"q-drawer__content fit "+(t.isContainer.value!==!0?"scroll":"overflow-auto")),ce=i(()=>`q-drawer q-drawer--${a.side}`+(P.value===!0?" q-drawer--mini-animate":"")+(a.bordered===!0?" q-drawer--bordered":"")+(ae.value===!0?" q-drawer--dark q-dark":"")+(g.value===!0?" no-transition":u.value===!0?"":" q-layout--prevent-focus")+(o.value===!0?" fixed q-drawer--on-top q-drawer--mobile q-drawer--top-padding":` q-drawer--${x.value===!0?"mini":"standard"}`+(_.value===!0||T.value!==!0?" fixed":"")+(a.overlay===!0||a.miniToOverlay===!0?" q-drawer--on-top":"")+(G.value===!0?" q-drawer--top-padding":""))),fe=i(()=>{const e=d.lang.rtl===!0?a.side:S.value;return[[K,be,void 0,{[e]:!0,mouse:!0}]]}),me=i(()=>{const e=d.lang.rtl===!0?S.value:a.side;return[[K,Y,void 0,{[e]:!0,mouse:!0}]]}),he=i(()=>{const e=d.lang.rtl===!0?S.value:a.side;return[[K,Y,void 0,{[e]:!0,mouse:!0,mouseAllDir:!0}]]});function z(){we(o,a.behavior==="mobile"||a.behavior!=="desktop"&&t.totalWidth.value<=a.breakpoint)}n(o,e=>{e===!0?(W=u.value,u.value===!0&&B(!1)):a.overlay===!1&&a.behavior!=="mobile"&&W!==!1&&(u.value===!0?(v(0),m(0),Q()):A(!1))}),n(()=>a.side,(e,l)=>{t.instances[l]===C&&(t.instances[l]=void 0,t[l].space=!1,t[l].offset=0),t.instances[e]=C,t[e].size=s.value,t[e].space=T.value,t[e].offset=I.value}),n(t.totalWidth,()=>{(t.isContainer.value===!0||document.qScrollPrevented!==!0)&&z()}),n(()=>a.behavior+a.breakpoint,z),n(t.isContainer,e=>{u.value===!0&&L(e!==!0),e===!0&&z()}),n(t.scrollbarWidth,()=>{v(u.value===!0?0:void 0)}),n(I,e=>{h("offset",e)}),n(T,e=>{y("onLayout",e),h("space",e)}),n(c,()=>{v()}),n(s,e=>{v(),V(a.miniToOverlay,e)}),n(()=>a.miniToOverlay,e=>{V(e,s.value)}),n(()=>d.lang.rtl,()=>{v()}),n(()=>a.mini,()=>{a.noMiniAnimation||a.modelValue===!0&&(ye(),t.animate())}),n(x,e=>{y("miniState",e)});function v(e){e===void 0?Z(()=>{e=u.value===!0?0:s.value,v(w.value*e)}):(t.isContainer.value===!0&&c.value===!0&&(o.value===!0||Math.abs(e)===s.value)&&(e+=w.value*t.scrollbarWidth.value),X.value=e)}function m(e){U.value=e}function F(e){const l=e===!0?"remove":t.isContainer.value!==!0?"add":"";l!==""&&document.body.classList[l]("q-body--drawer-toggle")}function ye(){b!==null&&clearTimeout(b),f.proxy&&f.proxy.$el&&f.proxy.$el.classList.add("q-drawer--mini-animate"),P.value=!0,b=setTimeout(()=>{b=null,P.value=!1,f&&f.proxy&&f.proxy.$el&&f.proxy.$el.classList.remove("q-drawer--mini-animate")},150)}function be(e){if(u.value!==!1)return;const l=s.value,r=O(e.distance.x,0,l);if(e.isFinal===!0){r>=Math.min(75,l)===!0?A():(t.animate(),m(0),v(w.value*l)),g.value=!1;return}v((d.lang.rtl===!0?c.value!==!0:c.value)?Math.max(l-r,0):Math.min(0,r-l)),m(O(r/l,0,1)),e.isFirst===!0&&(g.value=!0)}function Y(e){if(u.value!==!0)return;const l=s.value,r=e.direction===a.side,M=(d.lang.rtl===!0?r!==!0:r)?O(e.distance.x,0,l):0;if(e.isFinal===!0){Math.abs(M)<Math.min(75,l)===!0?(t.animate(),m(1),v(0)):B(),g.value=!1;return}v(w.value*M),m(O(1-M/l,0,1)),e.isFirst===!0&&(g.value=!0)}function Q(){L(!1),F(!0)}function h(e,l){t.update(a.side,e,l)}function we(e,l){e.value!==l&&(e.value=l)}function V(e,l){h("size",e===!0?a.miniWidth:l)}return t.instances[a.side]=C,V(a.miniToOverlay,s.value),h("space",T.value),h("offset",I.value),a.showIfAbove===!0&&a.modelValue!==!0&&u.value===!0&&a["onUpdate:modelValue"]!==void 0&&y("update:modelValue",!0),Oe(()=>{y("onLayout",T.value),y("miniState",x.value),W=a.showIfAbove===!0;const e=()=>{(u.value===!0?E:J)(!1,!0)};if(t.totalWidth.value!==0){Z(e);return}q=n(t.totalWidth,()=>{q(),q=void 0,u.value===!1&&a.showIfAbove===!0&&o.value===!1?A(!1):e()})}),De(()=>{q!==void 0&&q(),b!==null&&(clearTimeout(b),b=null),u.value===!0&&Q(),t.instances[a.side]===C&&(t.instances[a.side]=void 0,h("size",0),h("offset",0),h("space",!1))}),()=>{const e=[];o.value===!0&&(a.noSwipeOpen===!1&&e.push(Le($("div",{key:"open",class:`q-drawer__opener fixed-${a.side}`,"aria-hidden":"true"}),fe.value)),e.push(p("div",{ref:"backdrop",class:oe.value,style:ne.value,"aria-hidden":"true",onClick:B},void 0,"backdrop",a.noSwipeBackdrop!==!0&&u.value===!0,()=>he.value)));const l=x.value===!0&&D.mini!==void 0,r=[$("div",{...N,key:""+l,class:[de.value,N.class]},l===!0?D.mini():We(D.default))];return a.elevated===!0&&u.value===!0&&r.push($("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),e.push(p("aside",{ref:"content",class:ce.value,style:ve.value},r,"contentclose",a.noSwipeClose!==!0&&o.value===!0,()=>me.value)),$("div",{class:"q-drawer-container"},e)}}});export{Qe as Q};
