import{d as An,u as er,r as Bt,c as Ln,w as br,a as Rn,b as ie,e as ns,f as ce,g as Zn,Q as yr,o as pt,h as Qn,i as Lt,F as Tr,j as di,k as nn,l as qn,m as Ye,n as gi,t as Kn,p as it,q as xn,s as Nr,v as rs,x as is,y as Kr,z as ss,A as os,B as as,C as ls,D as cs}from"./index.b4716878.js";import{_ as xi}from"./plugin-vue_export-helper.21dcd24c.js";import{Q as wi,a as vn}from"./QItemSection.a2ef2d56.js";import{Q as Ai}from"./QItemLabel.88fb9012.js";import{_ as us}from"./TablePagination.0058568d.js";import{Q as hs}from"./QSpace.455aad5c.js";import{c as Dn,g as fs}from"./_commonjsHelpers.8402d862.js";import{Q as ds}from"./QForm.b737edd8.js";import{Q as gs}from"./QList.67451ef5.js";import{Q as xs}from"./QMenu.45353d3c.js";import{Q as ws}from"./QTooltip.cf1e9eea.js";import{C as As}from"./ClosePopup.050f93d1.js";import"./format.054b8074.js";import"./selection.7371c306.js";function Cs(h,u){return u.forEach(function(c){c&&typeof c!="string"&&!Array.isArray(c)&&Object.keys(c).forEach(function(w){if(w!=="default"&&!(w in h)){var m=Object.getOwnPropertyDescriptor(c,w);Object.defineProperty(h,w,m.get?m:{enumerable:!0,get:function(){return c[w]}})}})}),Object.freeze(Object.defineProperty(h,Symbol.toStringTag,{value:"Module"}))}const ms={class:"number-input"},ps=An({__name:"NumberInput",props:{modelValue:{type:[Number,String],default:0},label:{type:String,default:void 0},hint:{type:String,default:void 0},min:{type:Number,default:Number.NEGATIVE_INFINITY},max:{type:Number,default:Number.POSITIVE_INFINITY},step:{type:Number,default:1},precision:{type:Number,default:0},mask:{type:String,default:void 0},prefix:{type:String,default:void 0},suffix:{type:String,default:void 0},prepend:{type:String,default:void 0},append:{type:String,default:void 0},disable:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},required:{type:Boolean,default:!1},square:{type:Boolean,default:!1},dense:{type:Boolean,default:!1},outlined:{type:Boolean,default:!1},inputClass:{type:[String,Array,Object],default:""}},emits:["update:modelValue","change"],setup(h,{emit:u}){const{t:c}=er(),w=h,m=u,d=Bt(w.modelValue),T=Ln(()=>[A=>A!==null&&A!==""||!w.required||c("error.required"),A=>A>=w.min||c("error.minNumber",{min:w.min}),A=>A<=w.max||c("error.maxNumber",{max:w.max})]),R=A=>{const S=["0","1","2","3","4","5","6","7","8","9",".","-"],M=[8,46,37,39];!S.includes(A.key)&&!M.includes(A.keyCode)&&A.preventDefault(),A.key==="."&&(d.value.toString().includes(".")||w.step%1===0)&&A.preventDefault(),A.key==="-"&&(w.min>=0||d.value.toString().includes("-")?A.preventDefault():A.target instanceof HTMLInputElement&&(d.value!==0?A.target.selectionStart!==0&&A.preventDefault():(_("-"),A.preventDefault())))},_=A=>{let S=Number(A);if(A==="-"){d.value="-",m("update:modelValue",0),m("change",0);return}if(isNaN(S))S=0;else if(!isFinite(S)){d.value="",m("update:modelValue",0),m("change",0);return}S=Math.max(w.min,Math.min(w.max,S)),S=Number(S.toFixed(w.precision)),d.value=S,m("update:modelValue",S),m("change",S)};return br(()=>w.modelValue,A=>{d.value=A}),(A,S)=>(pt(),Rn("div",ms,[ie(yr,{modelValue:d.value,"onUpdate:modelValue":[S[0]||(S[0]=M=>d.value=M),_],label:h.label,step:h.step,hint:h.hint,rules:T.value,disable:h.disable,readonly:h.readonly,mask:h.mask,prefix:h.prefix,suffix:h.suffix,"stack-label":"",inputmode:"numeric",pattern:"[0-9]*",square:h.square,dense:h.dense,outlined:h.outlined,"input-class":h.inputClass,onKeypress:R,onClick:S[1]||(S[1]=Zn(()=>{},["stop"])),"hide-bottom-space":"","lazy-rules":""},ns({_:2},[h.prepend?{name:"prepend",fn:ce(()=>[ie(Qn,{name:h.prepend},null,8,["name"])]),key:"0"}:void 0,h.append?{name:"append",fn:ce(()=>[ie(Qn,{name:h.append},null,8,["name"])]),key:"1"}:void 0]),1032,["modelValue","label","step","hint","rules","disable","readonly","mask","prefix","suffix","square","dense","outlined","input-class"])]))}});const Es={class:"input"},Is={class:"number-pad"},ys={class:"number-row"},Ts={class:"number-row"},Ss={class:"number-row"},bs={class:"number-row"},Ns=An({__name:"NumberPad",props:{minLength:{},maxLength:{},isEnter:{type:Boolean}},emits:["update:minLength","update:maxLength","update:isEnter","enterInput"],setup(h,{emit:u}){const{t:c}=er(),w=h,m=u,d=Bt(""),T=Ln({get:()=>w.minLength,set:X=>m("update:minLength",X)}),R=Ln({get:()=>w.maxLength,set:X=>m("update:maxLength",X)}),_=Bt(!1),A=X=>{_.value=!1,!(d.value.length>=R.value)&&(d.value+=X)},S=()=>{d.value=""},M=()=>{if(d.value.length<T.value){_.value=!0;return}m("enterInput",d.value)};return(X,P)=>{const De=gi("q-form-hint");return pt(),Rn(Tr,null,[Lt("div",Es,[(pt(!0),Rn(Tr,null,di(d.value,W=>(pt(),Rn("span",{key:W,class:"number-input"},Kn(W),1))),128))]),d.value.length<T.value&&X.isEnter?(pt(),nn(De,{key:0,color:"negative",class:"q-mt-sm"},{default:ce(()=>[it(Kn(xn(c)("error.min",{min:T.value})),1)]),_:1})):qn("",!0),Lt("div",Is,[Lt("div",ys,[ie(Ye,{class:"number",onClick:P[0]||(P[0]=W=>A(7))},{default:ce(()=>P[10]||(P[10]=[it("7")])),_:1}),ie(Ye,{class:"number",onClick:P[1]||(P[1]=W=>A(8))},{default:ce(()=>P[11]||(P[11]=[it("8")])),_:1}),ie(Ye,{class:"number",onClick:P[2]||(P[2]=W=>A(9))},{default:ce(()=>P[12]||(P[12]=[it("9")])),_:1})]),Lt("div",Ts,[ie(Ye,{class:"number",onClick:P[3]||(P[3]=W=>A(4))},{default:ce(()=>P[13]||(P[13]=[it("4")])),_:1}),ie(Ye,{class:"number",onClick:P[4]||(P[4]=W=>A(5))},{default:ce(()=>P[14]||(P[14]=[it("5")])),_:1}),ie(Ye,{class:"number",onClick:P[5]||(P[5]=W=>A(6))},{default:ce(()=>P[15]||(P[15]=[it("6")])),_:1})]),Lt("div",Ss,[ie(Ye,{class:"number",onClick:P[6]||(P[6]=W=>A(1))},{default:ce(()=>P[16]||(P[16]=[it("1")])),_:1}),ie(Ye,{class:"number",onClick:P[7]||(P[7]=W=>A(2))},{default:ce(()=>P[17]||(P[17]=[it("2")])),_:1}),ie(Ye,{class:"number",onClick:P[8]||(P[8]=W=>A(3))},{default:ce(()=>P[18]||(P[18]=[it("3")])),_:1})]),Lt("div",bs,[ie(Ye,{class:"number",onClick:S},{default:ce(()=>P[19]||(P[19]=[it("C")])),_:1}),ie(Ye,{class:"number",onClick:P[9]||(P[9]=W=>A(0))},{default:ce(()=>P[20]||(P[20]=[it("0")])),_:1}),ie(Ye,{class:"number",onClick:M},{default:ce(()=>P[21]||(P[21]=[it("Enter")])),_:1})])])],64)}}});var Ms=xi(Ns,[["__scopeId","data-v-2476f3b9"]]);const _s=An({__name:"ItemQuantityInput",props:{item:{type:Object,required:!0},canLessThanZero:{type:Boolean,default:!0}},emits:["remove-item","update-quantity"],setup(h,{emit:u}){const c=h,w=u,m=Bt(c.item);Nr(()=>{m.value=c.item}),br(()=>c.item,_=>{m.value=_},{deep:!0});const d=()=>{if(m.value.quantity<=1)if(c.canLessThanZero){w("remove-item",m.value);return}else return;const _=m.value.quantity-1;w("update-quantity",m.value,_)},T=()=>{const _=m.value.quantity+1;w("update-quantity",m.value,_)},R=_=>{if(_<1)if(c.canLessThanZero){w("remove-item",m.value);return}else return;w("update-quantity",m.value,_)};return(_,A)=>{const S=gi("NumberInput");return pt(),nn(wi,{class:"item-quantity-input q-px-none q-pb-none"},{default:ce(()=>[ie(vn,{side:"",class:"q-px-none"},{default:ce(()=>[ie(Ye,{type:"button",unelevated:"",square:"",color:"negative",icon:"remove",align:"center",onClick:Zn(d,["stop"]),class:"full-height"})]),_:1}),ie(vn,null,{default:ce(()=>[ie(Ai,null,{default:ce(()=>[ie(S,{modelValue:m.value.quantity,"onUpdate:modelValue":[A[0]||(A[0]=M=>m.value.quantity=M),R],min:1,step:1,square:"",dense:"",outlined:"","input-class":["text-center","text-h6"]},null,8,["modelValue"])]),_:1})]),_:1}),ie(vn,{side:"",style:{"padding-left":"0"}},{default:ce(()=>[ie(Ye,{type:"button",unelevated:"",square:"",color:"positive",icon:"add",align:"center",onClick:Zn(T,["stop"]),class:"full-height"})]),_:1})]),_:1})}}});var j;(function(h){h[h.QR_CODE=0]="QR_CODE",h[h.AZTEC=1]="AZTEC",h[h.CODABAR=2]="CODABAR",h[h.CODE_39=3]="CODE_39",h[h.CODE_93=4]="CODE_93",h[h.CODE_128=5]="CODE_128",h[h.DATA_MATRIX=6]="DATA_MATRIX",h[h.MAXICODE=7]="MAXICODE",h[h.ITF=8]="ITF",h[h.EAN_13=9]="EAN_13",h[h.EAN_8=10]="EAN_8",h[h.PDF_417=11]="PDF_417",h[h.RSS_14=12]="RSS_14",h[h.RSS_EXPANDED=13]="RSS_EXPANDED",h[h.UPC_A=14]="UPC_A",h[h.UPC_E=15]="UPC_E",h[h.UPC_EAN_EXTENSION=16]="UPC_EAN_EXTENSION"})(j||(j={}));var Jr=new Map([[j.QR_CODE,"QR_CODE"],[j.AZTEC,"AZTEC"],[j.CODABAR,"CODABAR"],[j.CODE_39,"CODE_39"],[j.CODE_93,"CODE_93"],[j.CODE_128,"CODE_128"],[j.DATA_MATRIX,"DATA_MATRIX"],[j.MAXICODE,"MAXICODE"],[j.ITF,"ITF"],[j.EAN_13,"EAN_13"],[j.EAN_8,"EAN_8"],[j.PDF_417,"PDF_417"],[j.RSS_14,"RSS_14"],[j.RSS_EXPANDED,"RSS_EXPANDED"],[j.UPC_A,"UPC_A"],[j.UPC_E,"UPC_E"],[j.UPC_EAN_EXTENSION,"UPC_EAN_EXTENSION"]]),$r;(function(h){h[h.UNKNOWN=0]="UNKNOWN",h[h.URL=1]="URL"})($r||($r={}));function Os(h){return Object.values(j).includes(h)}var Zt;(function(h){h[h.SCAN_TYPE_CAMERA=0]="SCAN_TYPE_CAMERA",h[h.SCAN_TYPE_FILE=1]="SCAN_TYPE_FILE"})(Zt||(Zt={}));var Tt=function(){function h(){}return h.GITHUB_PROJECT_URL="https://github.com/mebjas/html5-qrcode",h.SCAN_DEFAULT_FPS=2,h.DEFAULT_DISABLE_FLIP=!1,h.DEFAULT_REMEMBER_LAST_CAMERA_USED=!0,h.DEFAULT_SUPPORTED_SCAN_TYPE=[Zt.SCAN_TYPE_CAMERA,Zt.SCAN_TYPE_FILE],h}(),Ci=function(){function h(u,c){this.format=u,this.formatName=c}return h.prototype.toString=function(){return this.formatName},h.create=function(u){if(!Jr.has(u))throw"".concat(u," not in html5QrcodeSupportedFormatsTextMap");return new h(u,Jr.get(u))},h}(),ei=function(){function h(){}return h.createFromText=function(u){var c={text:u};return{decodedText:u,result:c}},h.createFromQrcodeResult=function(u){return{decodedText:u.text,result:u}},h}(),Sr;(function(h){h[h.UNKWOWN_ERROR=0]="UNKWOWN_ERROR",h[h.IMPLEMENTATION_ERROR=1]="IMPLEMENTATION_ERROR",h[h.NO_CODE_FOUND_ERROR=2]="NO_CODE_FOUND_ERROR"})(Sr||(Sr={}));var mi=function(){function h(){}return h.createFrom=function(u){return{errorMessage:u,type:Sr.UNKWOWN_ERROR}},h}(),pi=function(){function h(u){this.verbose=u}return h.prototype.log=function(u){this.verbose&&console.log(u)},h.prototype.warn=function(u){this.verbose&&console.warn(u)},h.prototype.logError=function(u,c){(this.verbose||c===!0)&&console.error(u)},h.prototype.logErrors=function(u){if(u.length===0)throw"Logger#logError called without arguments";this.verbose&&console.error(u)},h}();function Rt(h){return typeof h=="undefined"||h===null}function Ds(h,u,c){return h>c?c:h<u?u:h}var wn=function(){function h(){}return h.codeParseError=function(u){return"QR code parse error, error = ".concat(u)},h.errorGettingUserMedia=function(u){return"Error getting userMedia, error = ".concat(u)},h.onlyDeviceSupportedError=function(){return"The device doesn't support navigator.mediaDevices , only supported cameraIdOrConfig in this case is deviceId parameter (string)."},h.cameraStreamingNotSupported=function(){return"Camera streaming not supported by the browser."},h.unableToQuerySupportedDevices=function(){return"Unable to query supported devices, unknown error."},h.insecureContextCameraQueryError=function(){return"Camera access is only supported in secure context like https or localhost."},h.scannerPaused=function(){return"Scanner paused"},h}(),Ae=function(){function h(){}return h.scanningStatus=function(){return"Scanning"},h.idleStatus=function(){return"Idle"},h.errorStatus=function(){return"Error"},h.permissionStatus=function(){return"Permission"},h.noCameraFoundErrorStatus=function(){return"No Cameras"},h.lastMatch=function(u){return"Last Match: ".concat(u)},h.codeScannerTitle=function(){return"Code Scanner"},h.cameraPermissionTitle=function(){return"Request Camera Permissions"},h.cameraPermissionRequesting=function(){return"Requesting camera permissions..."},h.noCameraFound=function(){return"No camera found"},h.scanButtonStopScanningText=function(){return"Stop Scanning"},h.scanButtonStartScanningText=function(){return"Start Scanning"},h.torchOnButton=function(){return"Switch On Torch"},h.torchOffButton=function(){return"Switch Off Torch"},h.torchOnFailedMessage=function(){return"Failed to turn on torch"},h.torchOffFailedMessage=function(){return"Failed to turn off torch"},h.scanButtonScanningStarting=function(){return"Launching Camera..."},h.textIfCameraScanSelected=function(){return"Scan an Image File"},h.textIfFileScanSelected=function(){return"Scan using camera directly"},h.selectCamera=function(){return"Select Camera"},h.fileSelectionChooseImage=function(){return"Choose Image"},h.fileSelectionChooseAnother=function(){return"Choose Another"},h.fileSelectionNoImageSelected=function(){return"No image choosen"},h.anonymousCameraPrefix=function(){return"Anonymous Camera"},h.dragAndDropMessage=function(){return"Or drop an image to scan"},h.dragAndDropMessageOnlyImages=function(){return"Or drop an image to scan (other files not supported)"},h.zoom=function(){return"zoom"},h.loadingImage=function(){return"Loading image..."},h.cameraScanAltText=function(){return"Camera based scan"},h.fileScanAltText=function(){return"Fule based scan"},h}(),ti=function(){function h(){}return h.poweredBy=function(){return"Powered by "},h.reportIssues=function(){return"Report issues"},h}(),Ei=function(){function h(){}return h.isMediaStreamConstraintsValid=function(u,c){if(typeof u!="object"){var w=typeof u;return c.logError("videoConstraints should be of type object, the "+"object passed is of type ".concat(w,"."),!0),!1}for(var m=["autoGainControl","channelCount","echoCancellation","latency","noiseSuppression","sampleRate","sampleSize","volume"],d=new Set(m),T=Object.keys(u),R=0,_=T;R<_.length;R++){var A=_[R];if(d.has(A))return c.logError("".concat(A," is not supported videoConstaints."),!0),!1}return!0},h}(),Te={exports:{}};(function(h,u){(function(c,w){w(u)})(Dn,function(c){function w(g){return g==null}var m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(g,e){g.__proto__=e}||function(g,e){for(var t in e)e.hasOwnProperty(t)&&(g[t]=e[t])};function d(g,e){m(g,e);function t(){this.constructor=g}g.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}function T(g,e){var t=Object.setPrototypeOf;t?t(g,e):g.__proto__=e}function R(g,e){e===void 0&&(e=g.constructor);var t=Error.captureStackTrace;t&&t(g,e)}var _=function(g){d(e,g);function e(t){var n=this.constructor,r=g.call(this,t)||this;return Object.defineProperty(r,"name",{value:n.name,enumerable:!1}),T(r,n.prototype),R(r),r}return e}(Error);class A extends _{constructor(e=void 0){super(e),this.message=e}getKind(){return this.constructor.kind}}A.kind="Exception";class S extends A{}S.kind="ArgumentException";class M extends A{}M.kind="IllegalArgumentException";class X{constructor(e){if(this.binarizer=e,e===null)throw new M("Binarizer must be non-null.")}getWidth(){return this.binarizer.getWidth()}getHeight(){return this.binarizer.getHeight()}getBlackRow(e,t){return this.binarizer.getBlackRow(e,t)}getBlackMatrix(){return(this.matrix===null||this.matrix===void 0)&&(this.matrix=this.binarizer.getBlackMatrix()),this.matrix}isCropSupported(){return this.binarizer.getLuminanceSource().isCropSupported()}crop(e,t,n,r){const i=this.binarizer.getLuminanceSource().crop(e,t,n,r);return new X(this.binarizer.createBinarizer(i))}isRotateSupported(){return this.binarizer.getLuminanceSource().isRotateSupported()}rotateCounterClockwise(){const e=this.binarizer.getLuminanceSource().rotateCounterClockwise();return new X(this.binarizer.createBinarizer(e))}rotateCounterClockwise45(){const e=this.binarizer.getLuminanceSource().rotateCounterClockwise45();return new X(this.binarizer.createBinarizer(e))}toString(){try{return this.getBlackMatrix().toString()}catch{return""}}}class P extends A{static getChecksumInstance(){return new P}}P.kind="ChecksumException";class De{constructor(e){this.source=e}getLuminanceSource(){return this.source}getWidth(){return this.source.getWidth()}getHeight(){return this.source.getHeight()}}class W{static arraycopy(e,t,n,r,i){for(;i--;)n[r++]=e[t++]}static currentTimeMillis(){return Date.now()}}class Se extends A{}Se.kind="IndexOutOfBoundsException";class Ke extends Se{constructor(e=void 0,t=void 0){super(t),this.index=e,this.message=t}}Ke.kind="ArrayIndexOutOfBoundsException";class he{static fill(e,t){for(let n=0,r=e.length;n<r;n++)e[n]=t}static fillWithin(e,t,n,r){he.rangeCheck(e.length,t,n);for(let i=t;i<n;i++)e[i]=r}static rangeCheck(e,t,n){if(t>n)throw new M("fromIndex("+t+") > toIndex("+n+")");if(t<0)throw new Ke(t);if(n>e)throw new Ke(n)}static asList(...e){return e}static create(e,t,n){return Array.from({length:e}).map(i=>Array.from({length:t}).fill(n))}static createInt32Array(e,t,n){return Array.from({length:e}).map(i=>Int32Array.from({length:t}).fill(n))}static equals(e,t){if(!e||!t||!e.length||!t.length||e.length!==t.length)return!1;for(let n=0,r=e.length;n<r;n++)if(e[n]!==t[n])return!1;return!0}static hashCode(e){if(e===null)return 0;let t=1;for(const n of e)t=31*t+n;return t}static fillUint8Array(e,t){for(let n=0;n!==e.length;n++)e[n]=t}static copyOf(e,t){return e.slice(0,t)}static copyOfUint8Array(e,t){if(e.length<=t){const n=new Uint8Array(t);return n.set(e),n}return e.slice(0,t)}static copyOfRange(e,t,n){const r=n-t,i=new Int32Array(r);return W.arraycopy(e,t,i,0,r),i}static binarySearch(e,t,n){n===void 0&&(n=he.numberComparator);let r=0,i=e.length-1;for(;r<=i;){const s=i+r>>1,o=n(t,e[s]);if(o>0)r=s+1;else if(o<0)i=s-1;else return s}return-r-1}static numberComparator(e,t){return e-t}}class J{static numberOfTrailingZeros(e){let t;if(e===0)return 32;let n=31;return t=e<<16,t!==0&&(n-=16,e=t),t=e<<8,t!==0&&(n-=8,e=t),t=e<<4,t!==0&&(n-=4,e=t),t=e<<2,t!==0&&(n-=2,e=t),n-(e<<1>>>31)}static numberOfLeadingZeros(e){if(e===0)return 32;let t=1;return e>>>16===0&&(t+=16,e<<=16),e>>>24===0&&(t+=8,e<<=8),e>>>28===0&&(t+=4,e<<=4),e>>>30===0&&(t+=2,e<<=2),t-=e>>>31,t}static toHexString(e){return e.toString(16)}static toBinaryString(e){return String(parseInt(String(e),2))}static bitCount(e){return e=e-(e>>>1&1431655765),e=(e&858993459)+(e>>>2&858993459),e=e+(e>>>4)&252645135,e=e+(e>>>8),e=e+(e>>>16),e&63}static truncDivision(e,t){return Math.trunc(e/t)}static parseInt(e,t=void 0){return parseInt(e,t)}}J.MIN_VALUE_32_BITS=-2147483648,J.MAX_VALUE=Number.MAX_SAFE_INTEGER;class ue{constructor(e,t){e===void 0?(this.size=0,this.bits=new Int32Array(1)):(this.size=e,t==null?this.bits=ue.makeArray(e):this.bits=t)}getSize(){return this.size}getSizeInBytes(){return Math.floor((this.size+7)/8)}ensureCapacity(e){if(e>this.bits.length*32){const t=ue.makeArray(e);W.arraycopy(this.bits,0,t,0,this.bits.length),this.bits=t}}get(e){return(this.bits[Math.floor(e/32)]&1<<(e&31))!==0}set(e){this.bits[Math.floor(e/32)]|=1<<(e&31)}flip(e){this.bits[Math.floor(e/32)]^=1<<(e&31)}getNextSet(e){const t=this.size;if(e>=t)return t;const n=this.bits;let r=Math.floor(e/32),i=n[r];i&=~((1<<(e&31))-1);const s=n.length;for(;i===0;){if(++r===s)return t;i=n[r]}const o=r*32+J.numberOfTrailingZeros(i);return o>t?t:o}getNextUnset(e){const t=this.size;if(e>=t)return t;const n=this.bits;let r=Math.floor(e/32),i=~n[r];i&=~((1<<(e&31))-1);const s=n.length;for(;i===0;){if(++r===s)return t;i=~n[r]}const o=r*32+J.numberOfTrailingZeros(i);return o>t?t:o}setBulk(e,t){this.bits[Math.floor(e/32)]=t}setRange(e,t){if(t<e||e<0||t>this.size)throw new M;if(t===e)return;t--;const n=Math.floor(e/32),r=Math.floor(t/32),i=this.bits;for(let s=n;s<=r;s++){const o=s>n?0:e&31,a=s<r?31:t&31,l=(2<<a)-(1<<o);i[s]|=l}}clear(){const e=this.bits.length,t=this.bits;for(let n=0;n<e;n++)t[n]=0}isRange(e,t,n){if(t<e||e<0||t>this.size)throw new M;if(t===e)return!0;t--;const r=Math.floor(e/32),i=Math.floor(t/32),s=this.bits;for(let o=r;o<=i;o++){const a=o>r?0:e&31,l=o<i?31:t&31,f=(2<<l)-(1<<a)&4294967295;if((s[o]&f)!==(n?f:0))return!1}return!0}appendBit(e){this.ensureCapacity(this.size+1),e&&(this.bits[Math.floor(this.size/32)]|=1<<(this.size&31)),this.size++}appendBits(e,t){if(t<0||t>32)throw new M("Num bits must be between 0 and 32");this.ensureCapacity(this.size+t);for(let n=t;n>0;n--)this.appendBit((e>>n-1&1)===1)}appendBitArray(e){const t=e.size;this.ensureCapacity(this.size+t);for(let n=0;n<t;n++)this.appendBit(e.get(n))}xor(e){if(this.size!==e.size)throw new M("Sizes don't match");const t=this.bits;for(let n=0,r=t.length;n<r;n++)t[n]^=e.bits[n]}toBytes(e,t,n,r){for(let i=0;i<r;i++){let s=0;for(let o=0;o<8;o++)this.get(e)&&(s|=1<<7-o),e++;t[n+i]=s}}getBitArray(){return this.bits}reverse(){const e=new Int32Array(this.bits.length),t=Math.floor((this.size-1)/32),n=t+1,r=this.bits;for(let i=0;i<n;i++){let s=r[i];s=s>>1&1431655765|(s&1431655765)<<1,s=s>>2&858993459|(s&858993459)<<2,s=s>>4&252645135|(s&252645135)<<4,s=s>>8&16711935|(s&16711935)<<8,s=s>>16&65535|(s&65535)<<16,e[t-i]=s}if(this.size!==n*32){const i=n*32-this.size;let s=e[0]>>>i;for(let o=1;o<n;o++){const a=e[o];s|=a<<32-i,e[o-1]=s,s=a>>>i}e[n-1]=s}this.bits=e}static makeArray(e){return new Int32Array(Math.floor((e+31)/32))}equals(e){if(!(e instanceof ue))return!1;const t=e;return this.size===t.size&&he.equals(this.bits,t.bits)}hashCode(){return 31*this.size+he.hashCode(this.bits)}toString(){let e="";for(let t=0,n=this.size;t<n;t++)(t&7)===0&&(e+=" "),e+=this.get(t)?"X":".";return e}clone(){return new ue(this.size,this.bits.slice())}}var Ft;(function(g){g[g.OTHER=0]="OTHER",g[g.PURE_BARCODE=1]="PURE_BARCODE",g[g.POSSIBLE_FORMATS=2]="POSSIBLE_FORMATS",g[g.TRY_HARDER=3]="TRY_HARDER",g[g.CHARACTER_SET=4]="CHARACTER_SET",g[g.ALLOWED_LENGTHS=5]="ALLOWED_LENGTHS",g[g.ASSUME_CODE_39_CHECK_DIGIT=6]="ASSUME_CODE_39_CHECK_DIGIT",g[g.ASSUME_GS1=7]="ASSUME_GS1",g[g.RETURN_CODABAR_START_END=8]="RETURN_CODABAR_START_END",g[g.NEED_RESULT_POINT_CALLBACK=9]="NEED_RESULT_POINT_CALLBACK",g[g.ALLOWED_EAN_EXTENSIONS=10]="ALLOWED_EAN_EXTENSIONS"})(Ft||(Ft={}));var Ce=Ft;class V extends A{static getFormatInstance(){return new V}}V.kind="FormatException";var we;(function(g){g[g.Cp437=0]="Cp437",g[g.ISO8859_1=1]="ISO8859_1",g[g.ISO8859_2=2]="ISO8859_2",g[g.ISO8859_3=3]="ISO8859_3",g[g.ISO8859_4=4]="ISO8859_4",g[g.ISO8859_5=5]="ISO8859_5",g[g.ISO8859_6=6]="ISO8859_6",g[g.ISO8859_7=7]="ISO8859_7",g[g.ISO8859_8=8]="ISO8859_8",g[g.ISO8859_9=9]="ISO8859_9",g[g.ISO8859_10=10]="ISO8859_10",g[g.ISO8859_11=11]="ISO8859_11",g[g.ISO8859_13=12]="ISO8859_13",g[g.ISO8859_14=13]="ISO8859_14",g[g.ISO8859_15=14]="ISO8859_15",g[g.ISO8859_16=15]="ISO8859_16",g[g.SJIS=16]="SJIS",g[g.Cp1250=17]="Cp1250",g[g.Cp1251=18]="Cp1251",g[g.Cp1252=19]="Cp1252",g[g.Cp1256=20]="Cp1256",g[g.UnicodeBigUnmarked=21]="UnicodeBigUnmarked",g[g.UTF8=22]="UTF8",g[g.ASCII=23]="ASCII",g[g.Big5=24]="Big5",g[g.GB18030=25]="GB18030",g[g.EUC_KR=26]="EUC_KR"})(we||(we={}));class U{constructor(e,t,n,...r){this.valueIdentifier=e,this.name=n,typeof t=="number"?this.values=Int32Array.from([t]):this.values=t,this.otherEncodingNames=r,U.VALUE_IDENTIFIER_TO_ECI.set(e,this),U.NAME_TO_ECI.set(n,this);const i=this.values;for(let s=0,o=i.length;s!==o;s++){const a=i[s];U.VALUES_TO_ECI.set(a,this)}for(const s of r)U.NAME_TO_ECI.set(s,this)}getValueIdentifier(){return this.valueIdentifier}getName(){return this.name}getValue(){return this.values[0]}static getCharacterSetECIByValue(e){if(e<0||e>=900)throw new V("incorect value");const t=U.VALUES_TO_ECI.get(e);if(t===void 0)throw new V("incorect value");return t}static getCharacterSetECIByName(e){const t=U.NAME_TO_ECI.get(e);if(t===void 0)throw new V("incorect value");return t}equals(e){if(!(e instanceof U))return!1;const t=e;return this.getName()===t.getName()}}U.VALUE_IDENTIFIER_TO_ECI=new Map,U.VALUES_TO_ECI=new Map,U.NAME_TO_ECI=new Map,U.Cp437=new U(we.Cp437,Int32Array.from([0,2]),"Cp437"),U.ISO8859_1=new U(we.ISO8859_1,Int32Array.from([1,3]),"ISO-8859-1","ISO88591","ISO8859_1"),U.ISO8859_2=new U(we.ISO8859_2,4,"ISO-8859-2","ISO88592","ISO8859_2"),U.ISO8859_3=new U(we.ISO8859_3,5,"ISO-8859-3","ISO88593","ISO8859_3"),U.ISO8859_4=new U(we.ISO8859_4,6,"ISO-8859-4","ISO88594","ISO8859_4"),U.ISO8859_5=new U(we.ISO8859_5,7,"ISO-8859-5","ISO88595","ISO8859_5"),U.ISO8859_6=new U(we.ISO8859_6,8,"ISO-8859-6","ISO88596","ISO8859_6"),U.ISO8859_7=new U(we.ISO8859_7,9,"ISO-8859-7","ISO88597","ISO8859_7"),U.ISO8859_8=new U(we.ISO8859_8,10,"ISO-8859-8","ISO88598","ISO8859_8"),U.ISO8859_9=new U(we.ISO8859_9,11,"ISO-8859-9","ISO88599","ISO8859_9"),U.ISO8859_10=new U(we.ISO8859_10,12,"ISO-8859-10","ISO885910","ISO8859_10"),U.ISO8859_11=new U(we.ISO8859_11,13,"ISO-8859-11","ISO885911","ISO8859_11"),U.ISO8859_13=new U(we.ISO8859_13,15,"ISO-8859-13","ISO885913","ISO8859_13"),U.ISO8859_14=new U(we.ISO8859_14,16,"ISO-8859-14","ISO885914","ISO8859_14"),U.ISO8859_15=new U(we.ISO8859_15,17,"ISO-8859-15","ISO885915","ISO8859_15"),U.ISO8859_16=new U(we.ISO8859_16,18,"ISO-8859-16","ISO885916","ISO8859_16"),U.SJIS=new U(we.SJIS,20,"SJIS","Shift_JIS"),U.Cp1250=new U(we.Cp1250,21,"Cp1250","windows-1250"),U.Cp1251=new U(we.Cp1251,22,"Cp1251","windows-1251"),U.Cp1252=new U(we.Cp1252,23,"Cp1252","windows-1252"),U.Cp1256=new U(we.Cp1256,24,"Cp1256","windows-1256"),U.UnicodeBigUnmarked=new U(we.UnicodeBigUnmarked,25,"UnicodeBigUnmarked","UTF-16BE","UnicodeBig"),U.UTF8=new U(we.UTF8,26,"UTF8","UTF-8"),U.ASCII=new U(we.ASCII,Int32Array.from([27,170]),"ASCII","US-ASCII"),U.Big5=new U(we.Big5,28,"Big5"),U.GB18030=new U(we.GB18030,29,"GB18030","GB2312","EUC_CN","GBK"),U.EUC_KR=new U(we.EUC_KR,30,"EUC_KR","EUC-KR");class rn extends A{}rn.kind="UnsupportedOperationException";class st{static decode(e,t){const n=this.encodingName(t);return this.customDecoder?this.customDecoder(e,n):typeof TextDecoder=="undefined"||this.shouldDecodeOnFallback(n)?this.decodeFallback(e,n):new TextDecoder(n).decode(e)}static shouldDecodeOnFallback(e){return!st.isBrowser()&&e==="ISO-8859-1"}static encode(e,t){const n=this.encodingName(t);return this.customEncoder?this.customEncoder(e,n):typeof TextEncoder=="undefined"?this.encodeFallback(e):new TextEncoder().encode(e)}static isBrowser(){return typeof window!="undefined"&&{}.toString.call(window)==="[object Window]"}static encodingName(e){return typeof e=="string"?e:e.getName()}static encodingCharacterSet(e){return e instanceof U?e:U.getCharacterSetECIByName(e)}static decodeFallback(e,t){const n=this.encodingCharacterSet(t);if(st.isDecodeFallbackSupported(n)){let r="";for(let i=0,s=e.length;i<s;i++){let o=e[i].toString(16);o.length<2&&(o="0"+o),r+="%"+o}return decodeURIComponent(r)}if(n.equals(U.UnicodeBigUnmarked))return String.fromCharCode.apply(null,new Uint16Array(e.buffer));throw new rn(`Encoding ${this.encodingName(t)} not supported by fallback.`)}static isDecodeFallbackSupported(e){return e.equals(U.UTF8)||e.equals(U.ISO8859_1)||e.equals(U.ASCII)}static encodeFallback(e){const n=btoa(unescape(encodeURIComponent(e))).split(""),r=[];for(let i=0;i<n.length;i++)r.push(n[i].charCodeAt(0));return new Uint8Array(r)}}class ${static castAsNonUtf8Char(e,t=null){const n=t?t.getName():this.ISO88591;return st.decode(new Uint8Array([e]),n)}static guessEncoding(e,t){if(t!=null&&t.get(Ce.CHARACTER_SET)!==void 0)return t.get(Ce.CHARACTER_SET).toString();const n=e.length;let r=!0,i=!0,s=!0,o=0,a=0,l=0,f=0,x=0,C=0,E=0,I=0,y=0,b=0,D=0;const F=e.length>3&&e[0]===239&&e[1]===187&&e[2]===191;for(let k=0;k<n&&(r||i||s);k++){const B=e[k]&255;s&&(o>0?(B&128)===0?s=!1:o--:(B&128)!==0&&((B&64)===0?s=!1:(o++,(B&32)===0?a++:(o++,(B&16)===0?l++:(o++,(B&8)===0?f++:s=!1))))),r&&(B>127&&B<160?r=!1:B>159&&(B<192||B===215||B===247)&&D++),i&&(x>0?B<64||B===127||B>252?i=!1:x--:B===128||B===160||B>239?i=!1:B>160&&B<224?(C++,I=0,E++,E>y&&(y=E)):B>127?(x++,E=0,I++,I>b&&(b=I)):(E=0,I=0))}return s&&o>0&&(s=!1),i&&x>0&&(i=!1),s&&(F||a+l+f>0)?$.UTF8:i&&($.ASSUME_SHIFT_JIS||y>=3||b>=3)?$.SHIFT_JIS:r&&i?y===2&&C===2||D*10>=n?$.SHIFT_JIS:$.ISO88591:r?$.ISO88591:i?$.SHIFT_JIS:s?$.UTF8:$.PLATFORM_DEFAULT_ENCODING}static format(e,...t){let n=-1;function r(s,o,a,l,f,x){if(s==="%%")return"%";if(t[++n]===void 0)return;s=l?parseInt(l.substr(1)):void 0;let C=f?parseInt(f.substr(1)):void 0,E;switch(x){case"s":E=t[n];break;case"c":E=t[n][0];break;case"f":E=parseFloat(t[n]).toFixed(s);break;case"p":E=parseFloat(t[n]).toPrecision(s);break;case"e":E=parseFloat(t[n]).toExponential(s);break;case"x":E=parseInt(t[n]).toString(C||16);break;case"d":E=parseFloat(parseInt(t[n],C||10).toPrecision(s)).toFixed(0);break}E=typeof E=="object"?JSON.stringify(E):(+E).toString(C);let I=parseInt(a),y=a&&a[0]+""=="0"?"0":" ";for(;E.length<I;)E=o!==void 0?E+y:y+E;return E}let i=/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;return e.replace(i,r)}static getBytes(e,t){return st.encode(e,t)}static getCharCode(e,t=0){return e.charCodeAt(t)}static getCharAt(e){return String.fromCharCode(e)}}$.SHIFT_JIS=U.SJIS.getName(),$.GB2312="GB2312",$.ISO88591=U.ISO8859_1.getName(),$.EUC_JP="EUC_JP",$.UTF8=U.UTF8.getName(),$.PLATFORM_DEFAULT_ENCODING=$.UTF8,$.ASSUME_SHIFT_JIS=!1;class me{constructor(e=""){this.value=e}enableDecoding(e){return this.encoding=e,this}append(e){return typeof e=="string"?this.value+=e.toString():this.encoding?this.value+=$.castAsNonUtf8Char(e,this.encoding):this.value+=String.fromCharCode(e),this}appendChars(e,t,n){for(let r=t;t<t+n;r++)this.append(e[r]);return this}length(){return this.value.length}charAt(e){return this.value.charAt(e)}deleteCharAt(e){this.value=this.value.substr(0,e)+this.value.substring(e+1)}setCharAt(e,t){this.value=this.value.substr(0,e)+t+this.value.substr(e+1)}substring(e,t){return this.value.substring(e,t)}setLengthToZero(){this.value=""}toString(){return this.value}insert(e,t){this.value=this.value.substr(0,e)+t+this.value.substr(e+t.length)}}class He{constructor(e,t,n,r){if(this.width=e,this.height=t,this.rowSize=n,this.bits=r,t==null&&(t=e),this.height=t,e<1||t<1)throw new M("Both dimensions must be greater than 0");n==null&&(n=Math.floor((e+31)/32)),this.rowSize=n,r==null&&(this.bits=new Int32Array(this.rowSize*this.height))}static parseFromBooleanArray(e){const t=e.length,n=e[0].length,r=new He(n,t);for(let i=0;i<t;i++){const s=e[i];for(let o=0;o<n;o++)s[o]&&r.set(o,i)}return r}static parseFromString(e,t,n){if(e===null)throw new M("stringRepresentation cannot be null");const r=new Array(e.length);let i=0,s=0,o=-1,a=0,l=0;for(;l<e.length;)if(e.charAt(l)===`
`||e.charAt(l)==="\r"){if(i>s){if(o===-1)o=i-s;else if(i-s!==o)throw new M("row lengths do not match");s=i,a++}l++}else if(e.substring(l,l+t.length)===t)l+=t.length,r[i]=!0,i++;else if(e.substring(l,l+n.length)===n)l+=n.length,r[i]=!1,i++;else throw new M("illegal character encountered: "+e.substring(l));if(i>s){if(o===-1)o=i-s;else if(i-s!==o)throw new M("row lengths do not match");a++}const f=new He(o,a);for(let x=0;x<i;x++)r[x]&&f.set(Math.floor(x%o),Math.floor(x/o));return f}get(e,t){const n=t*this.rowSize+Math.floor(e/32);return(this.bits[n]>>>(e&31)&1)!==0}set(e,t){const n=t*this.rowSize+Math.floor(e/32);this.bits[n]|=1<<(e&31)&4294967295}unset(e,t){const n=t*this.rowSize+Math.floor(e/32);this.bits[n]&=~(1<<(e&31)&4294967295)}flip(e,t){const n=t*this.rowSize+Math.floor(e/32);this.bits[n]^=1<<(e&31)&4294967295}xor(e){if(this.width!==e.getWidth()||this.height!==e.getHeight()||this.rowSize!==e.getRowSize())throw new M("input matrix dimensions do not match");const t=new ue(Math.floor(this.width/32)+1),n=this.rowSize,r=this.bits;for(let i=0,s=this.height;i<s;i++){const o=i*n,a=e.getRow(i,t).getBitArray();for(let l=0;l<n;l++)r[o+l]^=a[l]}}clear(){const e=this.bits,t=e.length;for(let n=0;n<t;n++)e[n]=0}setRegion(e,t,n,r){if(t<0||e<0)throw new M("Left and top must be nonnegative");if(r<1||n<1)throw new M("Height and width must be at least 1");const i=e+n,s=t+r;if(s>this.height||i>this.width)throw new M("The region must fit inside the matrix");const o=this.rowSize,a=this.bits;for(let l=t;l<s;l++){const f=l*o;for(let x=e;x<i;x++)a[f+Math.floor(x/32)]|=1<<(x&31)&4294967295}}getRow(e,t){t==null||t.getSize()<this.width?t=new ue(this.width):t.clear();const n=this.rowSize,r=this.bits,i=e*n;for(let s=0;s<n;s++)t.setBulk(s*32,r[i+s]);return t}setRow(e,t){W.arraycopy(t.getBitArray(),0,this.bits,e*this.rowSize,this.rowSize)}rotate180(){const e=this.getWidth(),t=this.getHeight();let n=new ue(e),r=new ue(e);for(let i=0,s=Math.floor((t+1)/2);i<s;i++)n=this.getRow(i,n),r=this.getRow(t-1-i,r),n.reverse(),r.reverse(),this.setRow(i,r),this.setRow(t-1-i,n)}getEnclosingRectangle(){const e=this.width,t=this.height,n=this.rowSize,r=this.bits;let i=e,s=t,o=-1,a=-1;for(let l=0;l<t;l++)for(let f=0;f<n;f++){const x=r[l*n+f];if(x!==0){if(l<s&&(s=l),l>a&&(a=l),f*32<i){let C=0;for(;(x<<31-C&4294967295)===0;)C++;f*32+C<i&&(i=f*32+C)}if(f*32+31>o){let C=31;for(;x>>>C===0;)C--;f*32+C>o&&(o=f*32+C)}}}return o<i||a<s?null:Int32Array.from([i,s,o-i+1,a-s+1])}getTopLeftOnBit(){const e=this.rowSize,t=this.bits;let n=0;for(;n<t.length&&t[n]===0;)n++;if(n===t.length)return null;const r=n/e;let i=n%e*32;const s=t[n];let o=0;for(;(s<<31-o&4294967295)===0;)o++;return i+=o,Int32Array.from([i,r])}getBottomRightOnBit(){const e=this.rowSize,t=this.bits;let n=t.length-1;for(;n>=0&&t[n]===0;)n--;if(n<0)return null;const r=Math.floor(n/e);let i=Math.floor(n%e)*32;const s=t[n];let o=31;for(;s>>>o===0;)o--;return i+=o,Int32Array.from([i,r])}getWidth(){return this.width}getHeight(){return this.height}getRowSize(){return this.rowSize}equals(e){if(!(e instanceof He))return!1;const t=e;return this.width===t.width&&this.height===t.height&&this.rowSize===t.rowSize&&he.equals(this.bits,t.bits)}hashCode(){let e=this.width;return e=31*e+this.width,e=31*e+this.height,e=31*e+this.rowSize,e=31*e+he.hashCode(this.bits),e}toString(e="X ",t="  ",n=`
`){return this.buildToString(e,t,n)}buildToString(e,t,n){let r=new me;for(let i=0,s=this.height;i<s;i++){for(let o=0,a=this.width;o<a;o++)r.append(this.get(o,i)?e:t);r.append(n)}return r.toString()}clone(){return new He(this.width,this.height,this.rowSize,this.bits.slice())}}class v extends A{static getNotFoundInstance(){return new v}}v.kind="NotFoundException";class ze extends De{constructor(e){super(e),this.luminances=ze.EMPTY,this.buckets=new Int32Array(ze.LUMINANCE_BUCKETS)}getBlackRow(e,t){const n=this.getLuminanceSource(),r=n.getWidth();t==null||t.getSize()<r?t=new ue(r):t.clear(),this.initArrays(r);const i=n.getRow(e,this.luminances),s=this.buckets;for(let a=0;a<r;a++)s[(i[a]&255)>>ze.LUMINANCE_SHIFT]++;const o=ze.estimateBlackPoint(s);if(r<3)for(let a=0;a<r;a++)(i[a]&255)<o&&t.set(a);else{let a=i[0]&255,l=i[1]&255;for(let f=1;f<r-1;f++){const x=i[f+1]&255;(l*4-a-x)/2<o&&t.set(f),a=l,l=x}}return t}getBlackMatrix(){const e=this.getLuminanceSource(),t=e.getWidth(),n=e.getHeight(),r=new He(t,n);this.initArrays(t);const i=this.buckets;for(let a=1;a<5;a++){const l=Math.floor(n*a/5),f=e.getRow(l,this.luminances),x=Math.floor(t*4/5);for(let C=Math.floor(t/5);C<x;C++){const E=f[C]&255;i[E>>ze.LUMINANCE_SHIFT]++}}const s=ze.estimateBlackPoint(i),o=e.getMatrix();for(let a=0;a<n;a++){const l=a*t;for(let f=0;f<t;f++)(o[l+f]&255)<s&&r.set(f,a)}return r}createBinarizer(e){return new ze(e)}initArrays(e){this.luminances.length<e&&(this.luminances=new Uint8ClampedArray(e));const t=this.buckets;for(let n=0;n<ze.LUMINANCE_BUCKETS;n++)t[n]=0}static estimateBlackPoint(e){const t=e.length;let n=0,r=0,i=0;for(let f=0;f<t;f++)e[f]>i&&(r=f,i=e[f]),e[f]>n&&(n=e[f]);let s=0,o=0;for(let f=0;f<t;f++){const x=f-r,C=e[f]*x*x;C>o&&(s=f,o=C)}if(r>s){const f=r;r=s,s=f}if(s-r<=t/16)throw new v;let a=s-1,l=-1;for(let f=s-1;f>r;f--){const x=f-r,C=x*x*(s-f)*(n-e[f]);C>l&&(a=f,l=C)}return a<<ze.LUMINANCE_SHIFT}}ze.LUMINANCE_BITS=5,ze.LUMINANCE_SHIFT=8-ze.LUMINANCE_BITS,ze.LUMINANCE_BUCKETS=1<<ze.LUMINANCE_BITS,ze.EMPTY=Uint8ClampedArray.from([0]);class te extends ze{constructor(e){super(e),this.matrix=null}getBlackMatrix(){if(this.matrix!==null)return this.matrix;const e=this.getLuminanceSource(),t=e.getWidth(),n=e.getHeight();if(t>=te.MINIMUM_DIMENSION&&n>=te.MINIMUM_DIMENSION){const r=e.getMatrix();let i=t>>te.BLOCK_SIZE_POWER;(t&te.BLOCK_SIZE_MASK)!==0&&i++;let s=n>>te.BLOCK_SIZE_POWER;(n&te.BLOCK_SIZE_MASK)!==0&&s++;const o=te.calculateBlackPoints(r,i,s,t,n),a=new He(t,n);te.calculateThresholdForBlock(r,i,s,t,n,o,a),this.matrix=a}else this.matrix=super.getBlackMatrix();return this.matrix}createBinarizer(e){return new te(e)}static calculateThresholdForBlock(e,t,n,r,i,s,o){const a=i-te.BLOCK_SIZE,l=r-te.BLOCK_SIZE;for(let f=0;f<n;f++){let x=f<<te.BLOCK_SIZE_POWER;x>a&&(x=a);const C=te.cap(f,2,n-3);for(let E=0;E<t;E++){let I=E<<te.BLOCK_SIZE_POWER;I>l&&(I=l);const y=te.cap(E,2,t-3);let b=0;for(let F=-2;F<=2;F++){const k=s[C+F];b+=k[y-2]+k[y-1]+k[y]+k[y+1]+k[y+2]}const D=b/25;te.thresholdBlock(e,I,x,D,r,o)}}}static cap(e,t,n){return e<t?t:e>n?n:e}static thresholdBlock(e,t,n,r,i,s){for(let o=0,a=n*i+t;o<te.BLOCK_SIZE;o++,a+=i)for(let l=0;l<te.BLOCK_SIZE;l++)(e[a+l]&255)<=r&&s.set(t+l,n+o)}static calculateBlackPoints(e,t,n,r,i){const s=i-te.BLOCK_SIZE,o=r-te.BLOCK_SIZE,a=new Array(n);for(let l=0;l<n;l++){a[l]=new Int32Array(t);let f=l<<te.BLOCK_SIZE_POWER;f>s&&(f=s);for(let x=0;x<t;x++){let C=x<<te.BLOCK_SIZE_POWER;C>o&&(C=o);let E=0,I=255,y=0;for(let D=0,F=f*r+C;D<te.BLOCK_SIZE;D++,F+=r){for(let k=0;k<te.BLOCK_SIZE;k++){const B=e[F+k]&255;E+=B,B<I&&(I=B),B>y&&(y=B)}if(y-I>te.MIN_DYNAMIC_RANGE)for(D++,F+=r;D<te.BLOCK_SIZE;D++,F+=r)for(let k=0;k<te.BLOCK_SIZE;k++)E+=e[F+k]&255}let b=E>>te.BLOCK_SIZE_POWER*2;if(y-I<=te.MIN_DYNAMIC_RANGE&&(b=I/2,l>0&&x>0)){const D=(a[l-1][x]+2*a[l][x-1]+a[l-1][x-1])/4;I<D&&(b=D)}a[l][x]=b}}return a}}te.BLOCK_SIZE_POWER=3,te.BLOCK_SIZE=1<<te.BLOCK_SIZE_POWER,te.BLOCK_SIZE_MASK=te.BLOCK_SIZE-1,te.MINIMUM_DIMENSION=te.BLOCK_SIZE*5,te.MIN_DYNAMIC_RANGE=24;class Cn{constructor(e,t){this.width=e,this.height=t}getWidth(){return this.width}getHeight(){return this.height}isCropSupported(){return!1}crop(e,t,n,r){throw new rn("This luminance source does not support cropping.")}isRotateSupported(){return!1}rotateCounterClockwise(){throw new rn("This luminance source does not support rotation by 90 degrees.")}rotateCounterClockwise45(){throw new rn("This luminance source does not support rotation by 45 degrees.")}toString(){const e=new Uint8ClampedArray(this.width);let t=new me;for(let n=0;n<this.height;n++){const r=this.getRow(n,e);for(let i=0;i<this.width;i++){const s=r[i]&255;let o;s<64?o="#":s<128?o="+":s<192?o=".":o=" ",t.append(o)}t.append(`
`)}return t.toString()}}class kt extends Cn{constructor(e){super(e.getWidth(),e.getHeight()),this.delegate=e}getRow(e,t){const n=this.delegate.getRow(e,t),r=this.getWidth();for(let i=0;i<r;i++)n[i]=255-(n[i]&255);return n}getMatrix(){const e=this.delegate.getMatrix(),t=this.getWidth()*this.getHeight(),n=new Uint8ClampedArray(t);for(let r=0;r<t;r++)n[r]=255-(e[r]&255);return n}isCropSupported(){return this.delegate.isCropSupported()}crop(e,t,n,r){return new kt(this.delegate.crop(e,t,n,r))}isRotateSupported(){return this.delegate.isRotateSupported()}invert(){return this.delegate}rotateCounterClockwise(){return new kt(this.delegate.rotateCounterClockwise())}rotateCounterClockwise45(){return new kt(this.delegate.rotateCounterClockwise45())}}class Ut extends Cn{constructor(e){super(e.width,e.height),this.canvas=e,this.tempCanvasElement=null,this.buffer=Ut.makeBufferFromCanvasImageData(e)}static makeBufferFromCanvasImageData(e){const t=e.getContext("2d").getImageData(0,0,e.width,e.height);return Ut.toGrayscaleBuffer(t.data,e.width,e.height)}static toGrayscaleBuffer(e,t,n){const r=new Uint8ClampedArray(t*n);for(let i=0,s=0,o=e.length;i<o;i+=4,s++){let a;if(e[i+3]===0)a=255;else{const f=e[i],x=e[i+1],C=e[i+2];a=306*f+601*x+117*C+512>>10}r[s]=a}return r}getRow(e,t){if(e<0||e>=this.getHeight())throw new M("Requested row is outside the image: "+e);const n=this.getWidth(),r=e*n;return t===null?t=this.buffer.slice(r,r+n):(t.length<n&&(t=new Uint8ClampedArray(n)),t.set(this.buffer.slice(r,r+n))),t}getMatrix(){return this.buffer}isCropSupported(){return!0}crop(e,t,n,r){return super.crop(e,t,n,r),this}isRotateSupported(){return!0}rotateCounterClockwise(){return this.rotate(-90),this}rotateCounterClockwise45(){return this.rotate(-45),this}getTempCanvasElement(){if(this.tempCanvasElement===null){const e=this.canvas.ownerDocument.createElement("canvas");e.width=this.canvas.width,e.height=this.canvas.height,this.tempCanvasElement=e}return this.tempCanvasElement}rotate(e){const t=this.getTempCanvasElement(),n=t.getContext("2d"),r=e*Ut.DEGREE_TO_RADIANS,i=this.canvas.width,s=this.canvas.height,o=Math.ceil(Math.abs(Math.cos(r))*i+Math.abs(Math.sin(r))*s),a=Math.ceil(Math.abs(Math.sin(r))*i+Math.abs(Math.cos(r))*s);return t.width=o,t.height=a,n.translate(o/2,a/2),n.rotate(r),n.drawImage(this.canvas,i/-2,s/-2),this.buffer=Ut.makeBufferFromCanvasImageData(t),this}invert(){return new kt(this)}}Ut.DEGREE_TO_RADIANS=Math.PI/180;class Or{constructor(e,t,n){this.deviceId=e,this.label=t,this.kind="videoinput",this.groupId=n||void 0}toJSON(){return{kind:this.kind,groupId:this.groupId,deviceId:this.deviceId,label:this.label}}}var et=(globalThis||Dn||self||window||void 0)&&(globalThis||Dn||self||window||void 0).__awaiter||function(g,e,t,n){function r(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function o(f){try{l(n.next(f))}catch(x){s(x)}}function a(f){try{l(n.throw(f))}catch(x){s(x)}}function l(f){f.done?i(f.value):r(f.value).then(o,a)}l((n=n.apply(g,e||[])).next())})};class Qt{constructor(e,t=500,n){this.reader=e,this.timeBetweenScansMillis=t,this._hints=n,this._stopContinuousDecode=!1,this._stopAsyncDecode=!1,this._timeBetweenDecodingAttempts=0}get hasNavigator(){return typeof navigator!="undefined"}get isMediaDevicesSuported(){return this.hasNavigator&&!!navigator.mediaDevices}get canEnumerateDevices(){return!!(this.isMediaDevicesSuported&&navigator.mediaDevices.enumerateDevices)}get timeBetweenDecodingAttempts(){return this._timeBetweenDecodingAttempts}set timeBetweenDecodingAttempts(e){this._timeBetweenDecodingAttempts=e<0?0:e}set hints(e){this._hints=e||null}get hints(){return this._hints}listVideoInputDevices(){return et(this,void 0,void 0,function*(){if(!this.hasNavigator)throw new Error("Can't enumerate devices, navigator is not present.");if(!this.canEnumerateDevices)throw new Error("Can't enumerate devices, method not supported.");const e=yield navigator.mediaDevices.enumerateDevices(),t=[];for(const n of e){const r=n.kind==="video"?"videoinput":n.kind;if(r!=="videoinput")continue;const i=n.deviceId||n.id,s=n.label||`Video device ${t.length+1}`,o=n.groupId,a={deviceId:i,label:s,kind:r,groupId:o};t.push(a)}return t})}getVideoInputDevices(){return et(this,void 0,void 0,function*(){return(yield this.listVideoInputDevices()).map(t=>new Or(t.deviceId,t.label))})}findDeviceById(e){return et(this,void 0,void 0,function*(){const t=yield this.listVideoInputDevices();return t?t.find(n=>n.deviceId===e):null})}decodeFromInputVideoDevice(e,t){return et(this,void 0,void 0,function*(){return yield this.decodeOnceFromVideoDevice(e,t)})}decodeOnceFromVideoDevice(e,t){return et(this,void 0,void 0,function*(){this.reset();let n;e?n={deviceId:{exact:e}}:n={facingMode:"environment"};const r={video:n};return yield this.decodeOnceFromConstraints(r,t)})}decodeOnceFromConstraints(e,t){return et(this,void 0,void 0,function*(){const n=yield navigator.mediaDevices.getUserMedia(e);return yield this.decodeOnceFromStream(n,t)})}decodeOnceFromStream(e,t){return et(this,void 0,void 0,function*(){this.reset();const n=yield this.attachStreamToVideo(e,t);return yield this.decodeOnce(n)})}decodeFromInputVideoDeviceContinuously(e,t,n){return et(this,void 0,void 0,function*(){return yield this.decodeFromVideoDevice(e,t,n)})}decodeFromVideoDevice(e,t,n){return et(this,void 0,void 0,function*(){let r;e?r={deviceId:{exact:e}}:r={facingMode:"environment"};const i={video:r};return yield this.decodeFromConstraints(i,t,n)})}decodeFromConstraints(e,t,n){return et(this,void 0,void 0,function*(){const r=yield navigator.mediaDevices.getUserMedia(e);return yield this.decodeFromStream(r,t,n)})}decodeFromStream(e,t,n){return et(this,void 0,void 0,function*(){this.reset();const r=yield this.attachStreamToVideo(e,t);return yield this.decodeContinuously(r,n)})}stopAsyncDecode(){this._stopAsyncDecode=!0}stopContinuousDecode(){this._stopContinuousDecode=!0}attachStreamToVideo(e,t){return et(this,void 0,void 0,function*(){const n=this.prepareVideoElement(t);return this.addVideoSource(n,e),this.videoElement=n,this.stream=e,yield this.playVideoOnLoadAsync(n),n})}playVideoOnLoadAsync(e){return new Promise((t,n)=>this.playVideoOnLoad(e,()=>t()))}playVideoOnLoad(e,t){this.videoEndedListener=()=>this.stopStreams(),this.videoCanPlayListener=()=>this.tryPlayVideo(e),e.addEventListener("ended",this.videoEndedListener),e.addEventListener("canplay",this.videoCanPlayListener),e.addEventListener("playing",t),this.tryPlayVideo(e)}isVideoPlaying(e){return e.currentTime>0&&!e.paused&&!e.ended&&e.readyState>2}tryPlayVideo(e){return et(this,void 0,void 0,function*(){if(this.isVideoPlaying(e)){console.warn("Trying to play video that is already playing.");return}try{yield e.play()}catch{console.warn("It was not possible to play the video.")}})}getMediaElement(e,t){const n=document.getElementById(e);if(!n)throw new S(`element with id '${e}' not found`);if(n.nodeName.toLowerCase()!==t.toLowerCase())throw new S(`element with id '${e}' must be an ${t} element`);return n}decodeFromImage(e,t){if(!e&&!t)throw new S("either imageElement with a src set or an url must be provided");return t&&!e?this.decodeFromImageUrl(t):this.decodeFromImageElement(e)}decodeFromVideo(e,t){if(!e&&!t)throw new S("Either an element with a src set or an URL must be provided");return t&&!e?this.decodeFromVideoUrl(t):this.decodeFromVideoElement(e)}decodeFromVideoContinuously(e,t,n){if(e===void 0&&t===void 0)throw new S("Either an element with a src set or an URL must be provided");return t&&!e?this.decodeFromVideoUrlContinuously(t,n):this.decodeFromVideoElementContinuously(e,n)}decodeFromImageElement(e){if(!e)throw new S("An image element must be provided.");this.reset();const t=this.prepareImageElement(e);this.imageElement=t;let n;return this.isImageLoaded(t)?n=this.decodeOnce(t,!1,!0):n=this._decodeOnLoadImage(t),n}decodeFromVideoElement(e){const t=this._decodeFromVideoElementSetup(e);return this._decodeOnLoadVideo(t)}decodeFromVideoElementContinuously(e,t){const n=this._decodeFromVideoElementSetup(e);return this._decodeOnLoadVideoContinuously(n,t)}_decodeFromVideoElementSetup(e){if(!e)throw new S("A video element must be provided.");this.reset();const t=this.prepareVideoElement(e);return this.videoElement=t,t}decodeFromImageUrl(e){if(!e)throw new S("An URL must be provided.");this.reset();const t=this.prepareImageElement();this.imageElement=t;const n=this._decodeOnLoadImage(t);return t.src=e,n}decodeFromVideoUrl(e){if(!e)throw new S("An URL must be provided.");this.reset();const t=this.prepareVideoElement(),n=this.decodeFromVideoElement(t);return t.src=e,n}decodeFromVideoUrlContinuously(e,t){if(!e)throw new S("An URL must be provided.");this.reset();const n=this.prepareVideoElement(),r=this.decodeFromVideoElementContinuously(n,t);return n.src=e,r}_decodeOnLoadImage(e){return new Promise((t,n)=>{this.imageLoadedListener=()=>this.decodeOnce(e,!1,!0).then(t,n),e.addEventListener("load",this.imageLoadedListener)})}_decodeOnLoadVideo(e){return et(this,void 0,void 0,function*(){return yield this.playVideoOnLoadAsync(e),yield this.decodeOnce(e)})}_decodeOnLoadVideoContinuously(e,t){return et(this,void 0,void 0,function*(){yield this.playVideoOnLoadAsync(e),this.decodeContinuously(e,t)})}isImageLoaded(e){return!(!e.complete||e.naturalWidth===0)}prepareImageElement(e){let t;return typeof e=="undefined"&&(t=document.createElement("img"),t.width=200,t.height=200),typeof e=="string"&&(t=this.getMediaElement(e,"img")),e instanceof HTMLImageElement&&(t=e),t}prepareVideoElement(e){let t;return!e&&typeof document!="undefined"&&(t=document.createElement("video"),t.width=200,t.height=200),typeof e=="string"&&(t=this.getMediaElement(e,"video")),e instanceof HTMLVideoElement&&(t=e),t.setAttribute("autoplay","true"),t.setAttribute("muted","true"),t.setAttribute("playsinline","true"),t}decodeOnce(e,t=!0,n=!0){this._stopAsyncDecode=!1;const r=(i,s)=>{if(this._stopAsyncDecode){s(new v("Video stream has ended before any code could be detected.")),this._stopAsyncDecode=void 0;return}try{const o=this.decode(e);i(o)}catch(o){const a=t&&o instanceof v,f=(o instanceof P||o instanceof V)&&n;if(a||f)return setTimeout(r,this._timeBetweenDecodingAttempts,i,s);s(o)}};return new Promise((i,s)=>r(i,s))}decodeContinuously(e,t){this._stopContinuousDecode=!1;const n=()=>{if(this._stopContinuousDecode){this._stopContinuousDecode=void 0;return}try{const r=this.decode(e);t(r,null),setTimeout(n,this.timeBetweenScansMillis)}catch(r){t(null,r);const i=r instanceof P||r instanceof V,s=r instanceof v;(i||s)&&setTimeout(n,this._timeBetweenDecodingAttempts)}};n()}decode(e){const t=this.createBinaryBitmap(e);return this.decodeBitmap(t)}_isHTMLVideoElement(e){return e.videoWidth!==0}drawFrameOnCanvas(e,t,n){t||(t={sx:0,sy:0,sWidth:e.videoWidth,sHeight:e.videoHeight,dx:0,dy:0,dWidth:e.videoWidth,dHeight:e.videoHeight}),n||(n=this.captureCanvasContext),n.drawImage(e,t.sx,t.sy,t.sWidth,t.sHeight,t.dx,t.dy,t.dWidth,t.dHeight)}drawImageOnCanvas(e,t,n=this.captureCanvasContext){t||(t={sx:0,sy:0,sWidth:e.naturalWidth,sHeight:e.naturalHeight,dx:0,dy:0,dWidth:e.naturalWidth,dHeight:e.naturalHeight}),n||(n=this.captureCanvasContext),n.drawImage(e,t.sx,t.sy,t.sWidth,t.sHeight,t.dx,t.dy,t.dWidth,t.dHeight)}createBinaryBitmap(e){this.getCaptureCanvasContext(e),this._isHTMLVideoElement(e)?this.drawFrameOnCanvas(e):this.drawImageOnCanvas(e);const t=this.getCaptureCanvas(e),n=new Ut(t),r=new te(n);return new X(r)}getCaptureCanvasContext(e){if(!this.captureCanvasContext){const n=this.getCaptureCanvas(e).getContext("2d");this.captureCanvasContext=n}return this.captureCanvasContext}getCaptureCanvas(e){if(!this.captureCanvas){const t=this.createCaptureCanvas(e);this.captureCanvas=t}return this.captureCanvas}decodeBitmap(e){return this.reader.decode(e,this._hints)}createCaptureCanvas(e){if(typeof document=="undefined")return this._destroyCaptureCanvas(),null;const t=document.createElement("canvas");let n,r;return typeof e!="undefined"&&(e instanceof HTMLVideoElement?(n=e.videoWidth,r=e.videoHeight):e instanceof HTMLImageElement&&(n=e.naturalWidth||e.width,r=e.naturalHeight||e.height)),t.style.width=n+"px",t.style.height=r+"px",t.width=n,t.height=r,t}stopStreams(){this.stream&&(this.stream.getVideoTracks().forEach(e=>e.stop()),this.stream=void 0),this._stopAsyncDecode===!1&&this.stopAsyncDecode(),this._stopContinuousDecode===!1&&this.stopContinuousDecode()}reset(){this.stopStreams(),this._destroyVideoElement(),this._destroyImageElement(),this._destroyCaptureCanvas()}_destroyVideoElement(){!this.videoElement||(typeof this.videoEndedListener!="undefined"&&this.videoElement.removeEventListener("ended",this.videoEndedListener),typeof this.videoPlayingEventListener!="undefined"&&this.videoElement.removeEventListener("playing",this.videoPlayingEventListener),typeof this.videoCanPlayListener!="undefined"&&this.videoElement.removeEventListener("loadedmetadata",this.videoCanPlayListener),this.cleanVideoSource(this.videoElement),this.videoElement=void 0)}_destroyImageElement(){!this.imageElement||(this.imageLoadedListener!==void 0&&this.imageElement.removeEventListener("load",this.imageLoadedListener),this.imageElement.src=void 0,this.imageElement.removeAttribute("src"),this.imageElement=void 0)}_destroyCaptureCanvas(){this.captureCanvasContext=void 0,this.captureCanvas=void 0}addVideoSource(e,t){try{e.srcObject=t}catch{e.src=URL.createObjectURL(t)}}cleanVideoSource(e){try{e.srcObject=null}catch{e.src=""}this.videoElement.removeAttribute("src")}}class tt{constructor(e,t,n=t==null?0:8*t.length,r,i,s=W.currentTimeMillis()){this.text=e,this.rawBytes=t,this.numBits=n,this.resultPoints=r,this.format=i,this.timestamp=s,this.text=e,this.rawBytes=t,n==null?this.numBits=t==null?0:8*t.length:this.numBits=n,this.resultPoints=r,this.format=i,this.resultMetadata=null,s==null?this.timestamp=W.currentTimeMillis():this.timestamp=s}getText(){return this.text}getRawBytes(){return this.rawBytes}getNumBits(){return this.numBits}getResultPoints(){return this.resultPoints}getBarcodeFormat(){return this.format}getResultMetadata(){return this.resultMetadata}putMetadata(e,t){this.resultMetadata===null&&(this.resultMetadata=new Map),this.resultMetadata.set(e,t)}putAllMetadata(e){e!==null&&(this.resultMetadata===null?this.resultMetadata=e:this.resultMetadata=new Map(e))}addResultPoints(e){const t=this.resultPoints;if(t===null)this.resultPoints=e;else if(e!==null&&e.length>0){const n=new Array(t.length+e.length);W.arraycopy(t,0,n,0,t.length),W.arraycopy(e,0,n,t.length,e.length),this.resultPoints=n}}getTimestamp(){return this.timestamp}toString(){return this.text}}var tr;(function(g){g[g.AZTEC=0]="AZTEC",g[g.CODABAR=1]="CODABAR",g[g.CODE_39=2]="CODE_39",g[g.CODE_93=3]="CODE_93",g[g.CODE_128=4]="CODE_128",g[g.DATA_MATRIX=5]="DATA_MATRIX",g[g.EAN_8=6]="EAN_8",g[g.EAN_13=7]="EAN_13",g[g.ITF=8]="ITF",g[g.MAXICODE=9]="MAXICODE",g[g.PDF_417=10]="PDF_417",g[g.QR_CODE=11]="QR_CODE",g[g.RSS_14=12]="RSS_14",g[g.RSS_EXPANDED=13]="RSS_EXPANDED",g[g.UPC_A=14]="UPC_A",g[g.UPC_E=15]="UPC_E",g[g.UPC_EAN_EXTENSION=16]="UPC_EAN_EXTENSION"})(tr||(tr={}));var Q=tr,nr;(function(g){g[g.OTHER=0]="OTHER",g[g.ORIENTATION=1]="ORIENTATION",g[g.BYTE_SEGMENTS=2]="BYTE_SEGMENTS",g[g.ERROR_CORRECTION_LEVEL=3]="ERROR_CORRECTION_LEVEL",g[g.ISSUE_NUMBER=4]="ISSUE_NUMBER",g[g.SUGGESTED_PRICE=5]="SUGGESTED_PRICE",g[g.POSSIBLE_COUNTRY=6]="POSSIBLE_COUNTRY",g[g.UPC_EAN_EXTENSION=7]="UPC_EAN_EXTENSION",g[g.PDF417_EXTRA_METADATA=8]="PDF417_EXTRA_METADATA",g[g.STRUCTURED_APPEND_SEQUENCE=9]="STRUCTURED_APPEND_SEQUENCE",g[g.STRUCTURED_APPEND_PARITY=10]="STRUCTURED_APPEND_PARITY"})(nr||(nr={}));var je=nr;class mn{constructor(e,t,n,r,i=-1,s=-1){this.rawBytes=e,this.text=t,this.byteSegments=n,this.ecLevel=r,this.structuredAppendSequenceNumber=i,this.structuredAppendParity=s,this.numBits=e==null?0:8*e.length}getRawBytes(){return this.rawBytes}getNumBits(){return this.numBits}setNumBits(e){this.numBits=e}getText(){return this.text}getByteSegments(){return this.byteSegments}getECLevel(){return this.ecLevel}getErrorsCorrected(){return this.errorsCorrected}setErrorsCorrected(e){this.errorsCorrected=e}getErasures(){return this.erasures}setErasures(e){this.erasures=e}getOther(){return this.other}setOther(e){this.other=e}hasStructuredAppend(){return this.structuredAppendParity>=0&&this.structuredAppendSequenceNumber>=0}getStructuredAppendParity(){return this.structuredAppendParity}getStructuredAppendSequenceNumber(){return this.structuredAppendSequenceNumber}}class pn{exp(e){return this.expTable[e]}log(e){if(e===0)throw new M;return this.logTable[e]}static addOrSubtract(e,t){return e^t}}class ot{constructor(e,t){if(t.length===0)throw new M;this.field=e;const n=t.length;if(n>1&&t[0]===0){let r=1;for(;r<n&&t[r]===0;)r++;r===n?this.coefficients=Int32Array.from([0]):(this.coefficients=new Int32Array(n-r),W.arraycopy(t,r,this.coefficients,0,this.coefficients.length))}else this.coefficients=t}getCoefficients(){return this.coefficients}getDegree(){return this.coefficients.length-1}isZero(){return this.coefficients[0]===0}getCoefficient(e){return this.coefficients[this.coefficients.length-1-e]}evaluateAt(e){if(e===0)return this.getCoefficient(0);const t=this.coefficients;let n;if(e===1){n=0;for(let s=0,o=t.length;s!==o;s++){const a=t[s];n=pn.addOrSubtract(n,a)}return n}n=t[0];const r=t.length,i=this.field;for(let s=1;s<r;s++)n=pn.addOrSubtract(i.multiply(e,n),t[s]);return n}addOrSubtract(e){if(!this.field.equals(e.field))throw new M("GenericGFPolys do not have same GenericGF field");if(this.isZero())return e;if(e.isZero())return this;let t=this.coefficients,n=e.coefficients;if(t.length>n.length){const s=t;t=n,n=s}let r=new Int32Array(n.length);const i=n.length-t.length;W.arraycopy(n,0,r,0,i);for(let s=i;s<n.length;s++)r[s]=pn.addOrSubtract(t[s-i],n[s]);return new ot(this.field,r)}multiply(e){if(!this.field.equals(e.field))throw new M("GenericGFPolys do not have same GenericGF field");if(this.isZero()||e.isZero())return this.field.getZero();const t=this.coefficients,n=t.length,r=e.coefficients,i=r.length,s=new Int32Array(n+i-1),o=this.field;for(let a=0;a<n;a++){const l=t[a];for(let f=0;f<i;f++)s[a+f]=pn.addOrSubtract(s[a+f],o.multiply(l,r[f]))}return new ot(o,s)}multiplyScalar(e){if(e===0)return this.field.getZero();if(e===1)return this;const t=this.coefficients.length,n=this.field,r=new Int32Array(t),i=this.coefficients;for(let s=0;s<t;s++)r[s]=n.multiply(i[s],e);return new ot(n,r)}multiplyByMonomial(e,t){if(e<0)throw new M;if(t===0)return this.field.getZero();const n=this.coefficients,r=n.length,i=new Int32Array(r+e),s=this.field;for(let o=0;o<r;o++)i[o]=s.multiply(n[o],t);return new ot(s,i)}divide(e){if(!this.field.equals(e.field))throw new M("GenericGFPolys do not have same GenericGF field");if(e.isZero())throw new M("Divide by 0");const t=this.field;let n=t.getZero(),r=this;const i=e.getCoefficient(e.getDegree()),s=t.inverse(i);for(;r.getDegree()>=e.getDegree()&&!r.isZero();){const o=r.getDegree()-e.getDegree(),a=t.multiply(r.getCoefficient(r.getDegree()),s),l=e.multiplyByMonomial(o,a),f=t.buildMonomial(o,a);n=n.addOrSubtract(f),r=r.addOrSubtract(l)}return[n,r]}toString(){let e="";for(let t=this.getDegree();t>=0;t--){let n=this.getCoefficient(t);if(n!==0){if(n<0?(e+=" - ",n=-n):e.length>0&&(e+=" + "),t===0||n!==1){const r=this.field.log(n);r===0?e+="1":r===1?e+="a":(e+="a^",e+=r)}t!==0&&(t===1?e+="x":(e+="x^",e+=t))}}return e}}class Bn extends A{}Bn.kind="ArithmeticException";class fe extends pn{constructor(e,t,n){super(),this.primitive=e,this.size=t,this.generatorBase=n;const r=new Int32Array(t);let i=1;for(let o=0;o<t;o++)r[o]=i,i*=2,i>=t&&(i^=e,i&=t-1);this.expTable=r;const s=new Int32Array(t);for(let o=0;o<t-1;o++)s[r[o]]=o;this.logTable=s,this.zero=new ot(this,Int32Array.from([0])),this.one=new ot(this,Int32Array.from([1]))}getZero(){return this.zero}getOne(){return this.one}buildMonomial(e,t){if(e<0)throw new M;if(t===0)return this.zero;const n=new Int32Array(e+1);return n[0]=t,new ot(this,n)}inverse(e){if(e===0)throw new Bn;return this.expTable[this.size-this.logTable[e]-1]}multiply(e,t){return e===0||t===0?0:this.expTable[(this.logTable[e]+this.logTable[t])%(this.size-1)]}getSize(){return this.size}getGeneratorBase(){return this.generatorBase}toString(){return"GF(0x"+J.toHexString(this.primitive)+","+this.size+")"}equals(e){return e===this}}fe.AZTEC_DATA_12=new fe(4201,4096,1),fe.AZTEC_DATA_10=new fe(1033,1024,1),fe.AZTEC_DATA_6=new fe(67,64,1),fe.AZTEC_PARAM=new fe(19,16,1),fe.QR_CODE_FIELD_256=new fe(285,256,0),fe.DATA_MATRIX_FIELD_256=new fe(301,256,1),fe.AZTEC_DATA_8=fe.DATA_MATRIX_FIELD_256,fe.MAXICODE_FIELD_64=fe.AZTEC_DATA_6;class sn extends A{}sn.kind="ReedSolomonException";class St extends A{}St.kind="IllegalStateException";class En{constructor(e){this.field=e}decode(e,t){const n=this.field,r=new ot(n,e),i=new Int32Array(t);let s=!0;for(let E=0;E<t;E++){const I=r.evaluateAt(n.exp(E+n.getGeneratorBase()));i[i.length-1-E]=I,I!==0&&(s=!1)}if(s)return;const o=new ot(n,i),a=this.runEuclideanAlgorithm(n.buildMonomial(t,1),o,t),l=a[0],f=a[1],x=this.findErrorLocations(l),C=this.findErrorMagnitudes(f,x);for(let E=0;E<x.length;E++){const I=e.length-1-n.log(x[E]);if(I<0)throw new sn("Bad error location");e[I]=fe.addOrSubtract(e[I],C[E])}}runEuclideanAlgorithm(e,t,n){if(e.getDegree()<t.getDegree()){const E=e;e=t,t=E}const r=this.field;let i=e,s=t,o=r.getZero(),a=r.getOne();for(;s.getDegree()>=(n/2|0);){let E=i,I=o;if(i=s,o=a,i.isZero())throw new sn("r_{i-1} was zero");s=E;let y=r.getZero();const b=i.getCoefficient(i.getDegree()),D=r.inverse(b);for(;s.getDegree()>=i.getDegree()&&!s.isZero();){const F=s.getDegree()-i.getDegree(),k=r.multiply(s.getCoefficient(s.getDegree()),D);y=y.addOrSubtract(r.buildMonomial(F,k)),s=s.addOrSubtract(i.multiplyByMonomial(F,k))}if(a=y.multiply(o).addOrSubtract(I),s.getDegree()>=i.getDegree())throw new St("Division algorithm failed to reduce polynomial?")}const l=a.getCoefficient(0);if(l===0)throw new sn("sigmaTilde(0) was zero");const f=r.inverse(l),x=a.multiplyScalar(f),C=s.multiplyScalar(f);return[x,C]}findErrorLocations(e){const t=e.getDegree();if(t===1)return Int32Array.from([e.getCoefficient(1)]);const n=new Int32Array(t);let r=0;const i=this.field;for(let s=1;s<i.getSize()&&r<t;s++)e.evaluateAt(s)===0&&(n[r]=i.inverse(s),r++);if(r!==t)throw new sn("Error locator degree does not match number of roots");return n}findErrorMagnitudes(e,t){const n=t.length,r=new Int32Array(n),i=this.field;for(let s=0;s<n;s++){const o=i.inverse(t[s]);let a=1;for(let l=0;l<n;l++)if(s!==l){const f=i.multiply(t[l],o),x=(f&1)===0?f|1:f&-2;a=i.multiply(a,x)}r[s]=i.multiply(e.evaluateAt(o),i.inverse(a)),i.getGeneratorBase()!==0&&(r[s]=i.multiply(r[s],o))}return r}}var Xe;(function(g){g[g.UPPER=0]="UPPER",g[g.LOWER=1]="LOWER",g[g.MIXED=2]="MIXED",g[g.DIGIT=3]="DIGIT",g[g.PUNCT=4]="PUNCT",g[g.BINARY=5]="BINARY"})(Xe||(Xe={}));class be{decode(e){this.ddata=e;let t=e.getBits(),n=this.extractBits(t),r=this.correctBits(n),i=be.convertBoolArrayToByteArray(r),s=be.getEncodedData(r),o=new mn(i,s,null,null);return o.setNumBits(r.length),o}static highLevelDecode(e){return this.getEncodedData(e)}static getEncodedData(e){let t=e.length,n=Xe.UPPER,r=Xe.UPPER,i="",s=0;for(;s<t;)if(r===Xe.BINARY){if(t-s<5)break;let o=be.readCode(e,s,5);if(s+=5,o===0){if(t-s<11)break;o=be.readCode(e,s,11)+31,s+=11}for(let a=0;a<o;a++){if(t-s<8){s=t;break}const l=be.readCode(e,s,8);i+=$.castAsNonUtf8Char(l),s+=8}r=n}else{let o=r===Xe.DIGIT?4:5;if(t-s<o)break;let a=be.readCode(e,s,o);s+=o;let l=be.getCharacter(r,a);l.startsWith("CTRL_")?(n=r,r=be.getTable(l.charAt(5)),l.charAt(6)==="L"&&(n=r)):(i+=l,r=n)}return i}static getTable(e){switch(e){case"L":return Xe.LOWER;case"P":return Xe.PUNCT;case"M":return Xe.MIXED;case"D":return Xe.DIGIT;case"B":return Xe.BINARY;case"U":default:return Xe.UPPER}}static getCharacter(e,t){switch(e){case Xe.UPPER:return be.UPPER_TABLE[t];case Xe.LOWER:return be.LOWER_TABLE[t];case Xe.MIXED:return be.MIXED_TABLE[t];case Xe.PUNCT:return be.PUNCT_TABLE[t];case Xe.DIGIT:return be.DIGIT_TABLE[t];default:throw new St("Bad table")}}correctBits(e){let t,n;this.ddata.getNbLayers()<=2?(n=6,t=fe.AZTEC_DATA_6):this.ddata.getNbLayers()<=8?(n=8,t=fe.AZTEC_DATA_8):this.ddata.getNbLayers()<=22?(n=10,t=fe.AZTEC_DATA_10):(n=12,t=fe.AZTEC_DATA_12);let r=this.ddata.getNbDatablocks(),i=e.length/n;if(i<r)throw new V;let s=e.length%n,o=new Int32Array(i);for(let C=0;C<i;C++,s+=n)o[C]=be.readCode(e,s,n);try{new En(t).decode(o,i-r)}catch(C){throw new V(C)}let a=(1<<n)-1,l=0;for(let C=0;C<r;C++){let E=o[C];if(E===0||E===a)throw new V;(E===1||E===a-1)&&l++}let f=new Array(r*n-l),x=0;for(let C=0;C<r;C++){let E=o[C];if(E===1||E===a-1)f.fill(E>1,x,x+n-1),x+=n-1;else for(let I=n-1;I>=0;--I)f[x++]=(E&1<<I)!==0}return f}extractBits(e){let t=this.ddata.isCompact(),n=this.ddata.getNbLayers(),r=(t?11:14)+n*4,i=new Int32Array(r),s=new Array(this.totalBitsInLayer(n,t));if(t)for(let o=0;o<i.length;o++)i[o]=o;else{let o=r+1+2*J.truncDivision(J.truncDivision(r,2)-1,15),a=r/2,l=J.truncDivision(o,2);for(let f=0;f<a;f++){let x=f+J.truncDivision(f,15);i[a-f-1]=l-x-1,i[a+f]=l+x+1}}for(let o=0,a=0;o<n;o++){let l=(n-o)*4+(t?9:12),f=o*2,x=r-1-f;for(let C=0;C<l;C++){let E=C*2;for(let I=0;I<2;I++)s[a+E+I]=e.get(i[f+I],i[f+C]),s[a+2*l+E+I]=e.get(i[f+C],i[x-I]),s[a+4*l+E+I]=e.get(i[x-I],i[x-C]),s[a+6*l+E+I]=e.get(i[x-C],i[f+I])}a+=l*8}return s}static readCode(e,t,n){let r=0;for(let i=t;i<t+n;i++)r<<=1,e[i]&&(r|=1);return r}static readByte(e,t){let n=e.length-t;return n>=8?be.readCode(e,t,8):be.readCode(e,t,n)<<8-n}static convertBoolArrayToByteArray(e){let t=new Uint8Array((e.length+7)/8);for(let n=0;n<t.length;n++)t[n]=be.readByte(e,8*n);return t}totalBitsInLayer(e,t){return((t?88:112)+16*e)*e}}be.UPPER_TABLE=["CTRL_PS"," ","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","CTRL_LL","CTRL_ML","CTRL_DL","CTRL_BS"],be.LOWER_TABLE=["CTRL_PS"," ","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","CTRL_US","CTRL_ML","CTRL_DL","CTRL_BS"],be.MIXED_TABLE=["CTRL_PS"," ","\\1","\\2","\\3","\\4","\\5","\\6","\\7","\b","	",`
`,"\\13","\f","\r","\\33","\\34","\\35","\\36","\\37","@","\\","^","_","`","|","~","\\177","CTRL_LL","CTRL_UL","CTRL_PL","CTRL_BS"],be.PUNCT_TABLE=["","\r",`\r
`,". ",", ",": ","!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}","CTRL_UL"],be.DIGIT_TABLE=["CTRL_PS"," ","0","1","2","3","4","5","6","7","8","9",",",".","CTRL_UL","CTRL_US"];class ae{constructor(){}static round(e){return e===NaN?0:e<=Number.MIN_SAFE_INTEGER?Number.MIN_SAFE_INTEGER:e>=Number.MAX_SAFE_INTEGER?Number.MAX_SAFE_INTEGER:e+(e<0?-.5:.5)|0}static distance(e,t,n,r){const i=e-n,s=t-r;return Math.sqrt(i*i+s*s)}static sum(e){let t=0;for(let n=0,r=e.length;n!==r;n++)t+=e[n];return t}}class Pn{static floatToIntBits(e){return e}}Pn.MAX_VALUE=Number.MAX_SAFE_INTEGER;class G{constructor(e,t){this.x=e,this.y=t}getX(){return this.x}getY(){return this.y}equals(e){if(e instanceof G){const t=e;return this.x===t.x&&this.y===t.y}return!1}hashCode(){return 31*Pn.floatToIntBits(this.x)+Pn.floatToIntBits(this.y)}toString(){return"("+this.x+","+this.y+")"}static orderBestPatterns(e){const t=this.distance(e[0],e[1]),n=this.distance(e[1],e[2]),r=this.distance(e[0],e[2]);let i,s,o;if(n>=t&&n>=r?(s=e[0],i=e[1],o=e[2]):r>=n&&r>=t?(s=e[1],i=e[0],o=e[2]):(s=e[2],i=e[0],o=e[1]),this.crossProductZ(i,s,o)<0){const a=i;i=o,o=a}e[0]=i,e[1]=s,e[2]=o}static distance(e,t){return ae.distance(e.x,e.y,t.x,t.y)}static crossProductZ(e,t,n){const r=t.x,i=t.y;return(n.x-r)*(e.y-i)-(n.y-i)*(e.x-r)}}class Fn{constructor(e,t){this.bits=e,this.points=t}getBits(){return this.bits}getPoints(){return this.points}}class Dr extends Fn{constructor(e,t,n,r,i){super(e,t),this.compact=n,this.nbDatablocks=r,this.nbLayers=i}getNbLayers(){return this.nbLayers}getNbDatablocks(){return this.nbDatablocks}isCompact(){return this.compact}}class vt{constructor(e,t,n,r){this.image=e,this.height=e.getHeight(),this.width=e.getWidth(),t==null&&(t=vt.INIT_SIZE),n==null&&(n=e.getWidth()/2|0),r==null&&(r=e.getHeight()/2|0);const i=t/2|0;if(this.leftInit=n-i,this.rightInit=n+i,this.upInit=r-i,this.downInit=r+i,this.upInit<0||this.leftInit<0||this.downInit>=this.height||this.rightInit>=this.width)throw new v}detect(){let e=this.leftInit,t=this.rightInit,n=this.upInit,r=this.downInit,i=!1,s=!0,o=!1,a=!1,l=!1,f=!1,x=!1;const C=this.width,E=this.height;for(;s;){s=!1;let I=!0;for(;(I||!a)&&t<C;)I=this.containsBlackPoint(n,r,t,!1),I?(t++,s=!0,a=!0):a||t++;if(t>=C){i=!0;break}let y=!0;for(;(y||!l)&&r<E;)y=this.containsBlackPoint(e,t,r,!0),y?(r++,s=!0,l=!0):l||r++;if(r>=E){i=!0;break}let b=!0;for(;(b||!f)&&e>=0;)b=this.containsBlackPoint(n,r,e,!1),b?(e--,s=!0,f=!0):f||e--;if(e<0){i=!0;break}let D=!0;for(;(D||!x)&&n>=0;)D=this.containsBlackPoint(e,t,n,!0),D?(n--,s=!0,x=!0):x||n--;if(n<0){i=!0;break}s&&(o=!0)}if(!i&&o){const I=t-e;let y=null;for(let k=1;y===null&&k<I;k++)y=this.getBlackPointOnSegment(e,r-k,e+k,r);if(y==null)throw new v;let b=null;for(let k=1;b===null&&k<I;k++)b=this.getBlackPointOnSegment(e,n+k,e+k,n);if(b==null)throw new v;let D=null;for(let k=1;D===null&&k<I;k++)D=this.getBlackPointOnSegment(t,n+k,t-k,n);if(D==null)throw new v;let F=null;for(let k=1;F===null&&k<I;k++)F=this.getBlackPointOnSegment(t,r-k,t-k,r);if(F==null)throw new v;return this.centerEdges(F,y,D,b)}else throw new v}getBlackPointOnSegment(e,t,n,r){const i=ae.round(ae.distance(e,t,n,r)),s=(n-e)/i,o=(r-t)/i,a=this.image;for(let l=0;l<i;l++){const f=ae.round(e+l*s),x=ae.round(t+l*o);if(a.get(f,x))return new G(f,x)}return null}centerEdges(e,t,n,r){const i=e.getX(),s=e.getY(),o=t.getX(),a=t.getY(),l=n.getX(),f=n.getY(),x=r.getX(),C=r.getY(),E=vt.CORR;return i<this.width/2?[new G(x-E,C+E),new G(o+E,a+E),new G(l-E,f-E),new G(i+E,s-E)]:[new G(x+E,C+E),new G(o+E,a-E),new G(l-E,f+E),new G(i-E,s-E)]}containsBlackPoint(e,t,n,r){const i=this.image;if(r){for(let s=e;s<=t;s++)if(i.get(s,n))return!0}else for(let s=e;s<=t;s++)if(i.get(n,s))return!0;return!1}}vt.INIT_SIZE=10,vt.CORR=1;class rr{static checkAndNudgePoints(e,t){const n=e.getWidth(),r=e.getHeight();let i=!0;for(let s=0;s<t.length&&i;s+=2){const o=Math.floor(t[s]),a=Math.floor(t[s+1]);if(o<-1||o>n||a<-1||a>r)throw new v;i=!1,o===-1?(t[s]=0,i=!0):o===n&&(t[s]=n-1,i=!0),a===-1?(t[s+1]=0,i=!0):a===r&&(t[s+1]=r-1,i=!0)}i=!0;for(let s=t.length-2;s>=0&&i;s-=2){const o=Math.floor(t[s]),a=Math.floor(t[s+1]);if(o<-1||o>n||a<-1||a>r)throw new v;i=!1,o===-1?(t[s]=0,i=!0):o===n&&(t[s]=n-1,i=!0),a===-1?(t[s+1]=0,i=!0):a===r&&(t[s+1]=r-1,i=!0)}}}class Et{constructor(e,t,n,r,i,s,o,a,l){this.a11=e,this.a21=t,this.a31=n,this.a12=r,this.a22=i,this.a32=s,this.a13=o,this.a23=a,this.a33=l}static quadrilateralToQuadrilateral(e,t,n,r,i,s,o,a,l,f,x,C,E,I,y,b){const D=Et.quadrilateralToSquare(e,t,n,r,i,s,o,a);return Et.squareToQuadrilateral(l,f,x,C,E,I,y,b).times(D)}transformPoints(e){const t=e.length,n=this.a11,r=this.a12,i=this.a13,s=this.a21,o=this.a22,a=this.a23,l=this.a31,f=this.a32,x=this.a33;for(let C=0;C<t;C+=2){const E=e[C],I=e[C+1],y=i*E+a*I+x;e[C]=(n*E+s*I+l)/y,e[C+1]=(r*E+o*I+f)/y}}transformPointsWithValues(e,t){const n=this.a11,r=this.a12,i=this.a13,s=this.a21,o=this.a22,a=this.a23,l=this.a31,f=this.a32,x=this.a33,C=e.length;for(let E=0;E<C;E++){const I=e[E],y=t[E],b=i*I+a*y+x;e[E]=(n*I+s*y+l)/b,t[E]=(r*I+o*y+f)/b}}static squareToQuadrilateral(e,t,n,r,i,s,o,a){const l=e-n+i-o,f=t-r+s-a;if(l===0&&f===0)return new Et(n-e,i-n,e,r-t,s-r,t,0,0,1);{const x=n-i,C=o-i,E=r-s,I=a-s,y=x*I-C*E,b=(l*I-C*f)/y,D=(x*f-l*E)/y;return new Et(n-e+b*n,o-e+D*o,e,r-t+b*r,a-t+D*a,t,b,D,1)}}static quadrilateralToSquare(e,t,n,r,i,s,o,a){return Et.squareToQuadrilateral(e,t,n,r,i,s,o,a).buildAdjoint()}buildAdjoint(){return new Et(this.a22*this.a33-this.a23*this.a32,this.a23*this.a31-this.a21*this.a33,this.a21*this.a32-this.a22*this.a31,this.a13*this.a32-this.a12*this.a33,this.a11*this.a33-this.a13*this.a31,this.a12*this.a31-this.a11*this.a32,this.a12*this.a23-this.a13*this.a22,this.a13*this.a21-this.a11*this.a23,this.a11*this.a22-this.a12*this.a21)}times(e){return new Et(this.a11*e.a11+this.a21*e.a12+this.a31*e.a13,this.a11*e.a21+this.a21*e.a22+this.a31*e.a23,this.a11*e.a31+this.a21*e.a32+this.a31*e.a33,this.a12*e.a11+this.a22*e.a12+this.a32*e.a13,this.a12*e.a21+this.a22*e.a22+this.a32*e.a23,this.a12*e.a31+this.a22*e.a32+this.a32*e.a33,this.a13*e.a11+this.a23*e.a12+this.a33*e.a13,this.a13*e.a21+this.a23*e.a22+this.a33*e.a23,this.a13*e.a31+this.a23*e.a32+this.a33*e.a33)}}class Rr extends rr{sampleGrid(e,t,n,r,i,s,o,a,l,f,x,C,E,I,y,b,D,F,k){const B=Et.quadrilateralToQuadrilateral(r,i,s,o,a,l,f,x,C,E,I,y,b,D,F,k);return this.sampleGridWithTransform(e,t,n,B)}sampleGridWithTransform(e,t,n,r){if(t<=0||n<=0)throw new v;const i=new He(t,n),s=new Float32Array(2*t);for(let o=0;o<n;o++){const a=s.length,l=o+.5;for(let f=0;f<a;f+=2)s[f]=f/2+.5,s[f+1]=l;r.transformPoints(s),rr.checkAndNudgePoints(e,s);try{for(let f=0;f<a;f+=2)e.get(Math.floor(s[f]),Math.floor(s[f+1]))&&i.set(f/2,o)}catch{throw new v}}return i}}class Vt{static setGridSampler(e){Vt.gridSampler=e}static getInstance(){return Vt.gridSampler}}Vt.gridSampler=new Rr;class nt{constructor(e,t){this.x=e,this.y=t}toResultPoint(){return new G(this.getX(),this.getY())}getX(){return this.x}getY(){return this.y}}class vr{constructor(e){this.EXPECTED_CORNER_BITS=new Int32Array([3808,476,2107,1799]),this.image=e}detect(){return this.detectMirror(!1)}detectMirror(e){let t=this.getMatrixCenter(),n=this.getBullsEyeCorners(t);if(e){let s=n[0];n[0]=n[2],n[2]=s}this.extractParameters(n);let r=this.sampleGrid(this.image,n[this.shift%4],n[(this.shift+1)%4],n[(this.shift+2)%4],n[(this.shift+3)%4]),i=this.getMatrixCornerPoints(n);return new Dr(r,i,this.compact,this.nbDataBlocks,this.nbLayers)}extractParameters(e){if(!this.isValidPoint(e[0])||!this.isValidPoint(e[1])||!this.isValidPoint(e[2])||!this.isValidPoint(e[3]))throw new v;let t=2*this.nbCenterLayers,n=new Int32Array([this.sampleLine(e[0],e[1],t),this.sampleLine(e[1],e[2],t),this.sampleLine(e[2],e[3],t),this.sampleLine(e[3],e[0],t)]);this.shift=this.getRotation(n,t);let r=0;for(let s=0;s<4;s++){let o=n[(this.shift+s)%4];this.compact?(r<<=7,r+=o>>1&127):(r<<=10,r+=(o>>2&31<<5)+(o>>1&31))}let i=this.getCorrectedParameterData(r,this.compact);this.compact?(this.nbLayers=(i>>6)+1,this.nbDataBlocks=(i&63)+1):(this.nbLayers=(i>>11)+1,this.nbDataBlocks=(i&2047)+1)}getRotation(e,t){let n=0;e.forEach((r,i,s)=>{let o=(r>>t-2<<1)+(r&1);n=(n<<3)+o}),n=((n&1)<<11)+(n>>1);for(let r=0;r<4;r++)if(J.bitCount(n^this.EXPECTED_CORNER_BITS[r])<=2)return r;throw new v}getCorrectedParameterData(e,t){let n,r;t?(n=7,r=2):(n=10,r=4);let i=n-r,s=new Int32Array(n);for(let a=n-1;a>=0;--a)s[a]=e&15,e>>=4;try{new En(fe.AZTEC_PARAM).decode(s,i)}catch{throw new v}let o=0;for(let a=0;a<r;a++)o=(o<<4)+s[a];return o}getBullsEyeCorners(e){let t=e,n=e,r=e,i=e,s=!0;for(this.nbCenterLayers=1;this.nbCenterLayers<9;this.nbCenterLayers++){let x=this.getFirstDifferent(t,s,1,-1),C=this.getFirstDifferent(n,s,1,1),E=this.getFirstDifferent(r,s,-1,1),I=this.getFirstDifferent(i,s,-1,-1);if(this.nbCenterLayers>2){let y=this.distancePoint(I,x)*this.nbCenterLayers/(this.distancePoint(i,t)*(this.nbCenterLayers+2));if(y<.75||y>1.25||!this.isWhiteOrBlackRectangle(x,C,E,I))break}t=x,n=C,r=E,i=I,s=!s}if(this.nbCenterLayers!==5&&this.nbCenterLayers!==7)throw new v;this.compact=this.nbCenterLayers===5;let o=new G(t.getX()+.5,t.getY()-.5),a=new G(n.getX()+.5,n.getY()+.5),l=new G(r.getX()-.5,r.getY()+.5),f=new G(i.getX()-.5,i.getY()-.5);return this.expandSquare([o,a,l,f],2*this.nbCenterLayers-3,2*this.nbCenterLayers)}getMatrixCenter(){let e,t,n,r;try{let o=new vt(this.image).detect();e=o[0],t=o[1],n=o[2],r=o[3]}catch{let a=this.image.getWidth()/2,l=this.image.getHeight()/2;e=this.getFirstDifferent(new nt(a+7,l-7),!1,1,-1).toResultPoint(),t=this.getFirstDifferent(new nt(a+7,l+7),!1,1,1).toResultPoint(),n=this.getFirstDifferent(new nt(a-7,l+7),!1,-1,1).toResultPoint(),r=this.getFirstDifferent(new nt(a-7,l-7),!1,-1,-1).toResultPoint()}let i=ae.round((e.getX()+r.getX()+t.getX()+n.getX())/4),s=ae.round((e.getY()+r.getY()+t.getY()+n.getY())/4);try{let o=new vt(this.image,15,i,s).detect();e=o[0],t=o[1],n=o[2],r=o[3]}catch{e=this.getFirstDifferent(new nt(i+7,s-7),!1,1,-1).toResultPoint(),t=this.getFirstDifferent(new nt(i+7,s+7),!1,1,1).toResultPoint(),n=this.getFirstDifferent(new nt(i-7,s+7),!1,-1,1).toResultPoint(),r=this.getFirstDifferent(new nt(i-7,s-7),!1,-1,-1).toResultPoint()}return i=ae.round((e.getX()+r.getX()+t.getX()+n.getX())/4),s=ae.round((e.getY()+r.getY()+t.getY()+n.getY())/4),new nt(i,s)}getMatrixCornerPoints(e){return this.expandSquare(e,2*this.nbCenterLayers,this.getDimension())}sampleGrid(e,t,n,r,i){let s=Vt.getInstance(),o=this.getDimension(),a=o/2-this.nbCenterLayers,l=o/2+this.nbCenterLayers;return s.sampleGrid(e,o,o,a,a,l,a,l,l,a,l,t.getX(),t.getY(),n.getX(),n.getY(),r.getX(),r.getY(),i.getX(),i.getY())}sampleLine(e,t,n){let r=0,i=this.distanceResultPoint(e,t),s=i/n,o=e.getX(),a=e.getY(),l=s*(t.getX()-e.getX())/i,f=s*(t.getY()-e.getY())/i;for(let x=0;x<n;x++)this.image.get(ae.round(o+x*l),ae.round(a+x*f))&&(r|=1<<n-x-1);return r}isWhiteOrBlackRectangle(e,t,n,r){let i=3;e=new nt(e.getX()-i,e.getY()+i),t=new nt(t.getX()-i,t.getY()-i),n=new nt(n.getX()+i,n.getY()-i),r=new nt(r.getX()+i,r.getY()+i);let s=this.getColor(r,e);if(s===0)return!1;let o=this.getColor(e,t);return o!==s||(o=this.getColor(t,n),o!==s)?!1:(o=this.getColor(n,r),o===s)}getColor(e,t){let n=this.distancePoint(e,t),r=(t.getX()-e.getX())/n,i=(t.getY()-e.getY())/n,s=0,o=e.getX(),a=e.getY(),l=this.image.get(e.getX(),e.getY()),f=Math.ceil(n);for(let C=0;C<f;C++)o+=r,a+=i,this.image.get(ae.round(o),ae.round(a))!==l&&s++;let x=s/n;return x>.1&&x<.9?0:x<=.1===l?1:-1}getFirstDifferent(e,t,n,r){let i=e.getX()+n,s=e.getY()+r;for(;this.isValid(i,s)&&this.image.get(i,s)===t;)i+=n,s+=r;for(i-=n,s-=r;this.isValid(i,s)&&this.image.get(i,s)===t;)i+=n;for(i-=n;this.isValid(i,s)&&this.image.get(i,s)===t;)s+=r;return s-=r,new nt(i,s)}expandSquare(e,t,n){let r=n/(2*t),i=e[0].getX()-e[2].getX(),s=e[0].getY()-e[2].getY(),o=(e[0].getX()+e[2].getX())/2,a=(e[0].getY()+e[2].getY())/2,l=new G(o+r*i,a+r*s),f=new G(o-r*i,a-r*s);i=e[1].getX()-e[3].getX(),s=e[1].getY()-e[3].getY(),o=(e[1].getX()+e[3].getX())/2,a=(e[1].getY()+e[3].getY())/2;let x=new G(o+r*i,a+r*s),C=new G(o-r*i,a-r*s);return[l,x,f,C]}isValid(e,t){return e>=0&&e<this.image.getWidth()&&t>0&&t<this.image.getHeight()}isValidPoint(e){let t=ae.round(e.getX()),n=ae.round(e.getY());return this.isValid(t,n)}distancePoint(e,t){return ae.distance(e.getX(),e.getY(),t.getX(),t.getY())}distanceResultPoint(e,t){return ae.distance(e.getX(),e.getY(),t.getX(),t.getY())}getDimension(){return this.compact?4*this.nbLayers+11:this.nbLayers<=4?4*this.nbLayers+15:4*this.nbLayers+2*(J.truncDivision(this.nbLayers-4,8)+1)+15}}class kn{decode(e,t=null){let n=null,r=new vr(e.getBlackMatrix()),i=null,s=null;try{let f=r.detectMirror(!1);i=f.getPoints(),this.reportFoundResultPoints(t,i),s=new be().decode(f)}catch(f){n=f}if(s==null)try{let f=r.detectMirror(!0);i=f.getPoints(),this.reportFoundResultPoints(t,i),s=new be().decode(f)}catch(f){throw n!=null?n:f}let o=new tt(s.getText(),s.getRawBytes(),s.getNumBits(),i,Q.AZTEC,W.currentTimeMillis()),a=s.getByteSegments();a!=null&&o.putMetadata(je.BYTE_SEGMENTS,a);let l=s.getECLevel();return l!=null&&o.putMetadata(je.ERROR_CORRECTION_LEVEL,l),o}reportFoundResultPoints(e,t){if(e!=null){let n=e.get(Ce.NEED_RESULT_POINT_CALLBACK);n!=null&&t.forEach((r,i,s)=>{n.foundPossibleResultPoint(r)})}}reset(){}}class Si extends Qt{constructor(e=500){super(new kn,e)}}class Fe{decode(e,t){try{return this.doDecode(e,t)}catch{if(t&&t.get(Ce.TRY_HARDER)===!0&&e.isRotateSupported()){const i=e.rotateCounterClockwise(),s=this.doDecode(i,t),o=s.getResultMetadata();let a=270;o!==null&&o.get(je.ORIENTATION)===!0&&(a=a+o.get(je.ORIENTATION)%360),s.putMetadata(je.ORIENTATION,a);const l=s.getResultPoints();if(l!==null){const f=i.getHeight();for(let x=0;x<l.length;x++)l[x]=new G(f-l[x].getY()-1,l[x].getX())}return s}else throw new v}}reset(){}doDecode(e,t){const n=e.getWidth(),r=e.getHeight();let i=new ue(n);const s=t&&t.get(Ce.TRY_HARDER)===!0,o=Math.max(1,r>>(s?8:5));let a;s?a=r:a=15;const l=Math.trunc(r/2);for(let f=0;f<a;f++){const x=Math.trunc((f+1)/2),C=(f&1)===0,E=l+o*(C?x:-x);if(E<0||E>=r)break;try{i=e.getBlackRow(E,i)}catch{continue}for(let I=0;I<2;I++){if(I===1&&(i.reverse(),t&&t.get(Ce.NEED_RESULT_POINT_CALLBACK)===!0)){const y=new Map;t.forEach((b,D)=>y.set(D,b)),y.delete(Ce.NEED_RESULT_POINT_CALLBACK),t=y}try{const y=this.decodeRow(E,i,t);if(I===1){y.putMetadata(je.ORIENTATION,180);const b=y.getResultPoints();b!==null&&(b[0]=new G(n-b[0].getX()-1,b[0].getY()),b[1]=new G(n-b[1].getX()-1,b[1].getY()))}return y}catch{}}}throw new v}static recordPattern(e,t,n){const r=n.length;for(let l=0;l<r;l++)n[l]=0;const i=e.getSize();if(t>=i)throw new v;let s=!e.get(t),o=0,a=t;for(;a<i;){if(e.get(a)!==s)n[o]++;else{if(++o===r)break;n[o]=1,s=!s}a++}if(!(o===r||o===r-1&&a===i))throw new v}static recordPatternInReverse(e,t,n){let r=n.length,i=e.get(t);for(;t>0&&r>=0;)e.get(--t)!==i&&(r--,i=!i);if(r>=0)throw new v;Fe.recordPattern(e,t+1,n)}static patternMatchVariance(e,t,n){const r=e.length;let i=0,s=0;for(let l=0;l<r;l++)i+=e[l],s+=t[l];if(i<s)return Number.POSITIVE_INFINITY;const o=i/s;n*=o;let a=0;for(let l=0;l<r;l++){const f=e[l],x=t[l]*o,C=f>x?f-x:x-f;if(C>n)return Number.POSITIVE_INFINITY;a+=C}return a/i}}class H extends Fe{static findStartPattern(e){const t=e.getSize(),n=e.getNextSet(0);let r=0,i=Int32Array.from([0,0,0,0,0,0]),s=n,o=!1;const a=6;for(let l=n;l<t;l++)if(e.get(l)!==o)i[r]++;else{if(r===a-1){let f=H.MAX_AVG_VARIANCE,x=-1;for(let C=H.CODE_START_A;C<=H.CODE_START_C;C++){const E=Fe.patternMatchVariance(i,H.CODE_PATTERNS[C],H.MAX_INDIVIDUAL_VARIANCE);E<f&&(f=E,x=C)}if(x>=0&&e.isRange(Math.max(0,s-(l-s)/2),s,!1))return Int32Array.from([s,l,x]);s+=i[0]+i[1],i=i.slice(2,i.length-1),i[r-1]=0,i[r]=0,r--}else r++;i[r]=1,o=!o}throw new v}static decodeCode(e,t,n){Fe.recordPattern(e,n,t);let r=H.MAX_AVG_VARIANCE,i=-1;for(let s=0;s<H.CODE_PATTERNS.length;s++){const o=H.CODE_PATTERNS[s],a=this.patternMatchVariance(t,o,H.MAX_INDIVIDUAL_VARIANCE);a<r&&(r=a,i=s)}if(i>=0)return i;throw new v}decodeRow(e,t,n){const r=n&&n.get(Ce.ASSUME_GS1)===!0,i=H.findStartPattern(t),s=i[2];let o=0;const a=new Uint8Array(20);a[o++]=s;let l;switch(s){case H.CODE_START_A:l=H.CODE_CODE_A;break;case H.CODE_START_B:l=H.CODE_CODE_B;break;case H.CODE_START_C:l=H.CODE_CODE_C;break;default:throw new V}let f=!1,x=!1,C="",E=i[0],I=i[1];const y=Int32Array.from([0,0,0,0,0,0]);let b=0,D=0,F=s,k=0,B=!0,se=!1,ee=!1;for(;!f;){const gn=x;switch(x=!1,b=D,D=H.decodeCode(t,y,I),a[o++]=D,D!==H.CODE_STOP&&(B=!0),D!==H.CODE_STOP&&(k++,F+=k*D),E=I,I+=y.reduce((es,ts)=>es+ts,0),D){case H.CODE_START_A:case H.CODE_START_B:case H.CODE_START_C:throw new V}switch(l){case H.CODE_CODE_A:if(D<64)ee===se?C+=String.fromCharCode(" ".charCodeAt(0)+D):C+=String.fromCharCode(" ".charCodeAt(0)+D+128),ee=!1;else if(D<96)ee===se?C+=String.fromCharCode(D-64):C+=String.fromCharCode(D+64),ee=!1;else switch(D!==H.CODE_STOP&&(B=!1),D){case H.CODE_FNC_1:r&&(C.length===0?C+="]C1":C+=String.fromCharCode(29));break;case H.CODE_FNC_2:case H.CODE_FNC_3:break;case H.CODE_FNC_4_A:!se&&ee?(se=!0,ee=!1):se&&ee?(se=!1,ee=!1):ee=!0;break;case H.CODE_SHIFT:x=!0,l=H.CODE_CODE_B;break;case H.CODE_CODE_B:l=H.CODE_CODE_B;break;case H.CODE_CODE_C:l=H.CODE_CODE_C;break;case H.CODE_STOP:f=!0;break}break;case H.CODE_CODE_B:if(D<96)ee===se?C+=String.fromCharCode(" ".charCodeAt(0)+D):C+=String.fromCharCode(" ".charCodeAt(0)+D+128),ee=!1;else switch(D!==H.CODE_STOP&&(B=!1),D){case H.CODE_FNC_1:r&&(C.length===0?C+="]C1":C+=String.fromCharCode(29));break;case H.CODE_FNC_2:case H.CODE_FNC_3:break;case H.CODE_FNC_4_B:!se&&ee?(se=!0,ee=!1):se&&ee?(se=!1,ee=!1):ee=!0;break;case H.CODE_SHIFT:x=!0,l=H.CODE_CODE_A;break;case H.CODE_CODE_A:l=H.CODE_CODE_A;break;case H.CODE_CODE_C:l=H.CODE_CODE_C;break;case H.CODE_STOP:f=!0;break}break;case H.CODE_CODE_C:if(D<100)D<10&&(C+="0"),C+=D;else switch(D!==H.CODE_STOP&&(B=!1),D){case H.CODE_FNC_1:r&&(C.length===0?C+="]C1":C+=String.fromCharCode(29));break;case H.CODE_CODE_A:l=H.CODE_CODE_A;break;case H.CODE_CODE_B:l=H.CODE_CODE_B;break;case H.CODE_STOP:f=!0;break}break}gn&&(l=l===H.CODE_CODE_A?H.CODE_CODE_B:H.CODE_CODE_A)}const ut=I-E;if(I=t.getNextUnset(I),!t.isRange(I,Math.min(t.getSize(),I+(I-E)/2),!1))throw new v;if(F-=k*b,F%103!==b)throw new P;const yt=C.length;if(yt===0)throw new v;yt>0&&B&&(l===H.CODE_CODE_C?C=C.substring(0,yt-2):C=C.substring(0,yt-1));const ht=(i[1]+i[0])/2,ye=E+ut/2,qe=a.length,ft=new Uint8Array(qe);for(let gn=0;gn<qe;gn++)ft[gn]=a[gn];const dn=[new G(ht,e),new G(ye,e)];return new tt(C,ft,0,dn,Q.CODE_128,new Date().getTime())}}H.CODE_PATTERNS=[Int32Array.from([2,1,2,2,2,2]),Int32Array.from([2,2,2,1,2,2]),Int32Array.from([2,2,2,2,2,1]),Int32Array.from([1,2,1,2,2,3]),Int32Array.from([1,2,1,3,2,2]),Int32Array.from([1,3,1,2,2,2]),Int32Array.from([1,2,2,2,1,3]),Int32Array.from([1,2,2,3,1,2]),Int32Array.from([1,3,2,2,1,2]),Int32Array.from([2,2,1,2,1,3]),Int32Array.from([2,2,1,3,1,2]),Int32Array.from([2,3,1,2,1,2]),Int32Array.from([1,1,2,2,3,2]),Int32Array.from([1,2,2,1,3,2]),Int32Array.from([1,2,2,2,3,1]),Int32Array.from([1,1,3,2,2,2]),Int32Array.from([1,2,3,1,2,2]),Int32Array.from([1,2,3,2,2,1]),Int32Array.from([2,2,3,2,1,1]),Int32Array.from([2,2,1,1,3,2]),Int32Array.from([2,2,1,2,3,1]),Int32Array.from([2,1,3,2,1,2]),Int32Array.from([2,2,3,1,1,2]),Int32Array.from([3,1,2,1,3,1]),Int32Array.from([3,1,1,2,2,2]),Int32Array.from([3,2,1,1,2,2]),Int32Array.from([3,2,1,2,2,1]),Int32Array.from([3,1,2,2,1,2]),Int32Array.from([3,2,2,1,1,2]),Int32Array.from([3,2,2,2,1,1]),Int32Array.from([2,1,2,1,2,3]),Int32Array.from([2,1,2,3,2,1]),Int32Array.from([2,3,2,1,2,1]),Int32Array.from([1,1,1,3,2,3]),Int32Array.from([1,3,1,1,2,3]),Int32Array.from([1,3,1,3,2,1]),Int32Array.from([1,1,2,3,1,3]),Int32Array.from([1,3,2,1,1,3]),Int32Array.from([1,3,2,3,1,1]),Int32Array.from([2,1,1,3,1,3]),Int32Array.from([2,3,1,1,1,3]),Int32Array.from([2,3,1,3,1,1]),Int32Array.from([1,1,2,1,3,3]),Int32Array.from([1,1,2,3,3,1]),Int32Array.from([1,3,2,1,3,1]),Int32Array.from([1,1,3,1,2,3]),Int32Array.from([1,1,3,3,2,1]),Int32Array.from([1,3,3,1,2,1]),Int32Array.from([3,1,3,1,2,1]),Int32Array.from([2,1,1,3,3,1]),Int32Array.from([2,3,1,1,3,1]),Int32Array.from([2,1,3,1,1,3]),Int32Array.from([2,1,3,3,1,1]),Int32Array.from([2,1,3,1,3,1]),Int32Array.from([3,1,1,1,2,3]),Int32Array.from([3,1,1,3,2,1]),Int32Array.from([3,3,1,1,2,1]),Int32Array.from([3,1,2,1,1,3]),Int32Array.from([3,1,2,3,1,1]),Int32Array.from([3,3,2,1,1,1]),Int32Array.from([3,1,4,1,1,1]),Int32Array.from([2,2,1,4,1,1]),Int32Array.from([4,3,1,1,1,1]),Int32Array.from([1,1,1,2,2,4]),Int32Array.from([1,1,1,4,2,2]),Int32Array.from([1,2,1,1,2,4]),Int32Array.from([1,2,1,4,2,1]),Int32Array.from([1,4,1,1,2,2]),Int32Array.from([1,4,1,2,2,1]),Int32Array.from([1,1,2,2,1,4]),Int32Array.from([1,1,2,4,1,2]),Int32Array.from([1,2,2,1,1,4]),Int32Array.from([1,2,2,4,1,1]),Int32Array.from([1,4,2,1,1,2]),Int32Array.from([1,4,2,2,1,1]),Int32Array.from([2,4,1,2,1,1]),Int32Array.from([2,2,1,1,1,4]),Int32Array.from([4,1,3,1,1,1]),Int32Array.from([2,4,1,1,1,2]),Int32Array.from([1,3,4,1,1,1]),Int32Array.from([1,1,1,2,4,2]),Int32Array.from([1,2,1,1,4,2]),Int32Array.from([1,2,1,2,4,1]),Int32Array.from([1,1,4,2,1,2]),Int32Array.from([1,2,4,1,1,2]),Int32Array.from([1,2,4,2,1,1]),Int32Array.from([4,1,1,2,1,2]),Int32Array.from([4,2,1,1,1,2]),Int32Array.from([4,2,1,2,1,1]),Int32Array.from([2,1,2,1,4,1]),Int32Array.from([2,1,4,1,2,1]),Int32Array.from([4,1,2,1,2,1]),Int32Array.from([1,1,1,1,4,3]),Int32Array.from([1,1,1,3,4,1]),Int32Array.from([1,3,1,1,4,1]),Int32Array.from([1,1,4,1,1,3]),Int32Array.from([1,1,4,3,1,1]),Int32Array.from([4,1,1,1,1,3]),Int32Array.from([4,1,1,3,1,1]),Int32Array.from([1,1,3,1,4,1]),Int32Array.from([1,1,4,1,3,1]),Int32Array.from([3,1,1,1,4,1]),Int32Array.from([4,1,1,1,3,1]),Int32Array.from([2,1,1,4,1,2]),Int32Array.from([2,1,1,2,1,4]),Int32Array.from([2,1,1,2,3,2]),Int32Array.from([2,3,3,1,1,1,2])],H.MAX_AVG_VARIANCE=.25,H.MAX_INDIVIDUAL_VARIANCE=.7,H.CODE_SHIFT=98,H.CODE_CODE_C=99,H.CODE_CODE_B=100,H.CODE_CODE_A=101,H.CODE_FNC_1=102,H.CODE_FNC_2=97,H.CODE_FNC_3=96,H.CODE_FNC_4_A=101,H.CODE_FNC_4_B=100,H.CODE_START_A=103,H.CODE_START_B=104,H.CODE_START_C=105,H.CODE_STOP=106;class ke extends Fe{constructor(e=!1,t=!1){super(),this.usingCheckDigit=e,this.extendedMode=t,this.decodeRowResult="",this.counters=new Int32Array(9)}decodeRow(e,t,n){let r=this.counters;r.fill(0),this.decodeRowResult="";let i=ke.findAsteriskPattern(t,r),s=t.getNextSet(i[1]),o=t.getSize(),a,l;do{ke.recordPattern(t,s,r);let y=ke.toNarrowWidePattern(r);if(y<0)throw new v;a=ke.patternToChar(y),this.decodeRowResult+=a,l=s;for(let b of r)s+=b;s=t.getNextSet(s)}while(a!=="*");this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);let f=0;for(let y of r)f+=y;let x=s-l-f;if(s!==o&&x*2<f)throw new v;if(this.usingCheckDigit){let y=this.decodeRowResult.length-1,b=0;for(let D=0;D<y;D++)b+=ke.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(D));if(this.decodeRowResult.charAt(y)!==ke.ALPHABET_STRING.charAt(b%43))throw new P;this.decodeRowResult=this.decodeRowResult.substring(0,y)}if(this.decodeRowResult.length===0)throw new v;let C;this.extendedMode?C=ke.decodeExtended(this.decodeRowResult):C=this.decodeRowResult;let E=(i[1]+i[0])/2,I=l+f/2;return new tt(C,null,0,[new G(E,e),new G(I,e)],Q.CODE_39,new Date().getTime())}static findAsteriskPattern(e,t){let n=e.getSize(),r=e.getNextSet(0),i=0,s=r,o=!1,a=t.length;for(let l=r;l<n;l++)if(e.get(l)!==o)t[i]++;else{if(i===a-1){if(this.toNarrowWidePattern(t)===ke.ASTERISK_ENCODING&&e.isRange(Math.max(0,s-Math.floor((l-s)/2)),s,!1))return[s,l];s+=t[0]+t[1],t.copyWithin(0,2,2+i-1),t[i-1]=0,t[i]=0,i--}else i++;t[i]=1,o=!o}throw new v}static toNarrowWidePattern(e){let t=e.length,n=0,r;do{let i=2147483647;for(let a of e)a<i&&a>n&&(i=a);n=i,r=0;let s=0,o=0;for(let a=0;a<t;a++){let l=e[a];l>n&&(o|=1<<t-1-a,r++,s+=l)}if(r===3){for(let a=0;a<t&&r>0;a++){let l=e[a];if(l>n&&(r--,l*2>=s))return-1}return o}}while(r>3);return-1}static patternToChar(e){for(let t=0;t<ke.CHARACTER_ENCODINGS.length;t++)if(ke.CHARACTER_ENCODINGS[t]===e)return ke.ALPHABET_STRING.charAt(t);if(e===ke.ASTERISK_ENCODING)return"*";throw new v}static decodeExtended(e){let t=e.length,n="";for(let r=0;r<t;r++){let i=e.charAt(r);if(i==="+"||i==="$"||i==="%"||i==="/"){let s=e.charAt(r+1),o="\0";switch(i){case"+":if(s>="A"&&s<="Z")o=String.fromCharCode(s.charCodeAt(0)+32);else throw new V;break;case"$":if(s>="A"&&s<="Z")o=String.fromCharCode(s.charCodeAt(0)-64);else throw new V;break;case"%":if(s>="A"&&s<="E")o=String.fromCharCode(s.charCodeAt(0)-38);else if(s>="F"&&s<="J")o=String.fromCharCode(s.charCodeAt(0)-11);else if(s>="K"&&s<="O")o=String.fromCharCode(s.charCodeAt(0)+16);else if(s>="P"&&s<="T")o=String.fromCharCode(s.charCodeAt(0)+43);else if(s==="U")o="\0";else if(s==="V")o="@";else if(s==="W")o="`";else if(s==="X"||s==="Y"||s==="Z")o="\x7F";else throw new V;break;case"/":if(s>="A"&&s<="O")o=String.fromCharCode(s.charCodeAt(0)-32);else if(s==="Z")o=":";else throw new V;break}n+=o,r++}else n+=i}return n}}ke.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%",ke.CHARACTER_ENCODINGS=[52,289,97,352,49,304,112,37,292,100,265,73,328,25,280,88,13,268,76,28,259,67,322,19,274,82,7,262,70,22,385,193,448,145,400,208,133,388,196,168,162,138,42],ke.ASTERISK_ENCODING=148;class Ee extends Fe{constructor(){super(...arguments),this.narrowLineWidth=-1}decodeRow(e,t,n){let r=this.decodeStart(t),i=this.decodeEnd(t),s=new me;Ee.decodeMiddle(t,r[1],i[0],s);let o=s.toString(),a=null;n!=null&&(a=n.get(Ce.ALLOWED_LENGTHS)),a==null&&(a=Ee.DEFAULT_ALLOWED_LENGTHS);let l=o.length,f=!1,x=0;for(let I of a){if(l===I){f=!0;break}I>x&&(x=I)}if(!f&&l>x&&(f=!0),!f)throw new V;const C=[new G(r[1],e),new G(i[0],e)];return new tt(o,null,0,C,Q.ITF,new Date().getTime())}static decodeMiddle(e,t,n,r){let i=new Int32Array(10),s=new Int32Array(5),o=new Int32Array(5);for(i.fill(0),s.fill(0),o.fill(0);t<n;){Fe.recordPattern(e,t,i);for(let l=0;l<5;l++){let f=2*l;s[l]=i[f],o[l]=i[f+1]}let a=Ee.decodeDigit(s);r.append(a.toString()),a=this.decodeDigit(o),r.append(a.toString()),i.forEach(function(l){t+=l})}}decodeStart(e){let t=Ee.skipWhiteSpace(e),n=Ee.findGuardPattern(e,t,Ee.START_PATTERN);return this.narrowLineWidth=(n[1]-n[0])/4,this.validateQuietZone(e,n[0]),n}validateQuietZone(e,t){let n=this.narrowLineWidth*10;n=n<t?n:t;for(let r=t-1;n>0&&r>=0&&!e.get(r);r--)n--;if(n!==0)throw new v}static skipWhiteSpace(e){const t=e.getSize(),n=e.getNextSet(0);if(n===t)throw new v;return n}decodeEnd(e){e.reverse();try{let t=Ee.skipWhiteSpace(e),n;try{n=Ee.findGuardPattern(e,t,Ee.END_PATTERN_REVERSED[0])}catch(i){i instanceof v&&(n=Ee.findGuardPattern(e,t,Ee.END_PATTERN_REVERSED[1]))}this.validateQuietZone(e,n[0]);let r=n[0];return n[0]=e.getSize()-n[1],n[1]=e.getSize()-r,n}finally{e.reverse()}}static findGuardPattern(e,t,n){let r=n.length,i=new Int32Array(r),s=e.getSize(),o=!1,a=0,l=t;i.fill(0);for(let f=t;f<s;f++)if(e.get(f)!==o)i[a]++;else{if(a===r-1){if(Fe.patternMatchVariance(i,n,Ee.MAX_INDIVIDUAL_VARIANCE)<Ee.MAX_AVG_VARIANCE)return[l,f];l+=i[0]+i[1],W.arraycopy(i,2,i,0,a-1),i[a-1]=0,i[a]=0,a--}else a++;i[a]=1,o=!o}throw new v}static decodeDigit(e){let t=Ee.MAX_AVG_VARIANCE,n=-1,r=Ee.PATTERNS.length;for(let i=0;i<r;i++){let s=Ee.PATTERNS[i],o=Fe.patternMatchVariance(e,s,Ee.MAX_INDIVIDUAL_VARIANCE);o<t?(t=o,n=i):o===t&&(n=-1)}if(n>=0)return n%10;throw new v}}Ee.PATTERNS=[Int32Array.from([1,1,2,2,1]),Int32Array.from([2,1,1,1,2]),Int32Array.from([1,2,1,1,2]),Int32Array.from([2,2,1,1,1]),Int32Array.from([1,1,2,1,2]),Int32Array.from([2,1,2,1,1]),Int32Array.from([1,2,2,1,1]),Int32Array.from([1,1,1,2,2]),Int32Array.from([2,1,1,2,1]),Int32Array.from([1,2,1,2,1]),Int32Array.from([1,1,3,3,1]),Int32Array.from([3,1,1,1,3]),Int32Array.from([1,3,1,1,3]),Int32Array.from([3,3,1,1,1]),Int32Array.from([1,1,3,1,3]),Int32Array.from([3,1,3,1,1]),Int32Array.from([1,3,3,1,1]),Int32Array.from([1,1,1,3,3]),Int32Array.from([3,1,1,3,1]),Int32Array.from([1,3,1,3,1])],Ee.MAX_AVG_VARIANCE=.38,Ee.MAX_INDIVIDUAL_VARIANCE=.5,Ee.DEFAULT_ALLOWED_LENGTHS=[6,8,10,12,14],Ee.START_PATTERN=Int32Array.from([1,1,1,1]),Ee.END_PATTERN_REVERSED=[Int32Array.from([1,1,2]),Int32Array.from([1,1,3])];class Re extends Fe{constructor(){super(...arguments),this.decodeRowStringBuffer=""}static findStartGuardPattern(e){let t=!1,n,r=0,i=Int32Array.from([0,0,0]);for(;!t;){i=Int32Array.from([0,0,0]),n=Re.findGuardPattern(e,r,!1,this.START_END_PATTERN,i);let s=n[0];r=n[1];let o=s-(r-s);o>=0&&(t=e.isRange(o,s,!1))}return n}static checkChecksum(e){return Re.checkStandardUPCEANChecksum(e)}static checkStandardUPCEANChecksum(e){let t=e.length;if(t===0)return!1;let n=parseInt(e.charAt(t-1),10);return Re.getStandardUPCEANChecksum(e.substring(0,t-1))===n}static getStandardUPCEANChecksum(e){let t=e.length,n=0;for(let r=t-1;r>=0;r-=2){let i=e.charAt(r).charCodeAt(0)-"0".charCodeAt(0);if(i<0||i>9)throw new V;n+=i}n*=3;for(let r=t-2;r>=0;r-=2){let i=e.charAt(r).charCodeAt(0)-"0".charCodeAt(0);if(i<0||i>9)throw new V;n+=i}return(1e3-n)%10}static decodeEnd(e,t){return Re.findGuardPattern(e,t,!1,Re.START_END_PATTERN,new Int32Array(Re.START_END_PATTERN.length).fill(0))}static findGuardPatternWithoutCounters(e,t,n,r){return this.findGuardPattern(e,t,n,r,new Int32Array(r.length))}static findGuardPattern(e,t,n,r,i){let s=e.getSize();t=n?e.getNextUnset(t):e.getNextSet(t);let o=0,a=t,l=r.length,f=n;for(let x=t;x<s;x++)if(e.get(x)!==f)i[o]++;else{if(o===l-1){if(Fe.patternMatchVariance(i,r,Re.MAX_INDIVIDUAL_VARIANCE)<Re.MAX_AVG_VARIANCE)return Int32Array.from([a,x]);a+=i[0]+i[1];let C=i.slice(2,i.length-1);for(let E=0;E<o-1;E++)i[E]=C[E];i[o-1]=0,i[o]=0,o--}else o++;i[o]=1,f=!f}throw new v}static decodeDigit(e,t,n,r){this.recordPattern(e,n,t);let i=this.MAX_AVG_VARIANCE,s=-1,o=r.length;for(let a=0;a<o;a++){let l=r[a],f=Fe.patternMatchVariance(t,l,Re.MAX_INDIVIDUAL_VARIANCE);f<i&&(i=f,s=a)}if(s>=0)return s;throw new v}}Re.MAX_AVG_VARIANCE=.48,Re.MAX_INDIVIDUAL_VARIANCE=.7,Re.START_END_PATTERN=Int32Array.from([1,1,1]),Re.MIDDLE_PATTERN=Int32Array.from([1,1,1,1,1]),Re.END_PATTERN=Int32Array.from([1,1,1,1,1,1]),Re.L_PATTERNS=[Int32Array.from([3,2,1,1]),Int32Array.from([2,2,2,1]),Int32Array.from([2,1,2,2]),Int32Array.from([1,4,1,1]),Int32Array.from([1,1,3,2]),Int32Array.from([1,2,3,1]),Int32Array.from([1,1,1,4]),Int32Array.from([1,3,1,2]),Int32Array.from([1,2,1,3]),Int32Array.from([3,1,1,2])];class In{constructor(){this.CHECK_DIGIT_ENCODINGS=[24,20,18,17,12,6,3,10,9,5],this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}decodeRow(e,t,n){let r=this.decodeRowStringBuffer,i=this.decodeMiddle(t,n,r),s=r.toString(),o=In.parseExtensionString(s),a=[new G((n[0]+n[1])/2,e),new G(i,e)],l=new tt(s,null,0,a,Q.UPC_EAN_EXTENSION,new Date().getTime());return o!=null&&l.putAllMetadata(o),l}decodeMiddle(e,t,n){let r=this.decodeMiddleCounters;r[0]=0,r[1]=0,r[2]=0,r[3]=0;let i=e.getSize(),s=t[1],o=0;for(let l=0;l<5&&s<i;l++){let f=Re.decodeDigit(e,r,s,Re.L_AND_G_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+f%10);for(let x of r)s+=x;f>=10&&(o|=1<<4-l),l!==4&&(s=e.getNextSet(s),s=e.getNextUnset(s))}if(n.length!==5)throw new v;let a=this.determineCheckDigit(o);if(In.extensionChecksum(n.toString())!==a)throw new v;return s}static extensionChecksum(e){let t=e.length,n=0;for(let r=t-2;r>=0;r-=2)n+=e.charAt(r).charCodeAt(0)-"0".charCodeAt(0);n*=3;for(let r=t-1;r>=0;r-=2)n+=e.charAt(r).charCodeAt(0)-"0".charCodeAt(0);return n*=3,n%10}determineCheckDigit(e){for(let t=0;t<10;t++)if(e===this.CHECK_DIGIT_ENCODINGS[t])return t;throw new v}static parseExtensionString(e){if(e.length!==5)return null;let t=In.parseExtension5String(e);return t==null?null:new Map([[je.SUGGESTED_PRICE,t]])}static parseExtension5String(e){let t;switch(e.charAt(0)){case"0":t="\xA3";break;case"5":t="$";break;case"9":switch(e){case"90000":return null;case"99991":return"0.00";case"99990":return"Used"}t="";break;default:t="";break}let n=parseInt(e.substring(1)),r=(n/100).toString(),i=n%100,s=i<10?"0"+i:i.toString();return t+r+"."+s}}class ir{constructor(){this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}decodeRow(e,t,n){let r=this.decodeRowStringBuffer,i=this.decodeMiddle(t,n,r),s=r.toString(),o=ir.parseExtensionString(s),a=[new G((n[0]+n[1])/2,e),new G(i,e)],l=new tt(s,null,0,a,Q.UPC_EAN_EXTENSION,new Date().getTime());return o!=null&&l.putAllMetadata(o),l}decodeMiddle(e,t,n){let r=this.decodeMiddleCounters;r[0]=0,r[1]=0,r[2]=0,r[3]=0;let i=e.getSize(),s=t[1],o=0;for(let a=0;a<2&&s<i;a++){let l=Re.decodeDigit(e,r,s,Re.L_AND_G_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+l%10);for(let f of r)s+=f;l>=10&&(o|=1<<1-a),a!==1&&(s=e.getNextSet(s),s=e.getNextUnset(s))}if(n.length!==2)throw new v;if(parseInt(n.toString())%4!==o)throw new v;return s}static parseExtensionString(e){return e.length!==2?null:new Map([[je.ISSUE_NUMBER,parseInt(e)]])}}class Lr{static decodeRow(e,t,n){let r=Re.findGuardPattern(t,n,!1,this.EXTENSION_START_PATTERN,new Int32Array(this.EXTENSION_START_PATTERN.length).fill(0));try{return new In().decodeRow(e,t,r)}catch{return new ir().decodeRow(e,t,r)}}}Lr.EXTENSION_START_PATTERN=Int32Array.from([1,1,2]);class de extends Re{constructor(){super(),this.decodeRowStringBuffer="",de.L_AND_G_PATTERNS=de.L_PATTERNS.map(e=>Int32Array.from(e));for(let e=10;e<20;e++){let t=de.L_PATTERNS[e-10],n=new Int32Array(t.length);for(let r=0;r<t.length;r++)n[r]=t[t.length-r-1];de.L_AND_G_PATTERNS[e]=n}}decodeRow(e,t,n){let r=de.findStartGuardPattern(t),i=n==null?null:n.get(Ce.NEED_RESULT_POINT_CALLBACK);if(i!=null){const B=new G((r[0]+r[1])/2,e);i.foundPossibleResultPoint(B)}let s=this.decodeMiddle(t,r,this.decodeRowStringBuffer),o=s.rowOffset,a=s.resultString;if(i!=null){const B=new G(o,e);i.foundPossibleResultPoint(B)}let l=this.decodeEnd(t,o);if(i!=null){const B=new G((l[0]+l[1])/2,e);i.foundPossibleResultPoint(B)}let f=l[1],x=f+(f-l[0]);if(x>=t.getSize()||!t.isRange(f,x,!1))throw new v;let C=a.toString();if(C.length<8)throw new V;if(!de.checkChecksum(C))throw new P;let E=(r[1]+r[0])/2,I=(l[1]+l[0])/2,y=this.getBarcodeFormat(),b=[new G(E,e),new G(I,e)],D=new tt(C,null,0,b,y,new Date().getTime()),F=0;try{let B=Lr.decodeRow(e,t,l[1]);D.putMetadata(je.UPC_EAN_EXTENSION,B.getText()),D.putAllMetadata(B.getResultMetadata()),D.addResultPoints(B.getResultPoints()),F=B.getText().length}catch{}let k=n==null?null:n.get(Ce.ALLOWED_EAN_EXTENSIONS);if(k!=null){let B=!1;for(let se in k)if(F.toString()===se){B=!0;break}if(!B)throw new v}return D}decodeEnd(e,t){return de.findGuardPattern(e,t,!1,de.START_END_PATTERN,new Int32Array(de.START_END_PATTERN.length).fill(0))}static checkChecksum(e){return de.checkStandardUPCEANChecksum(e)}static checkStandardUPCEANChecksum(e){let t=e.length;if(t===0)return!1;let n=parseInt(e.charAt(t-1),10);return de.getStandardUPCEANChecksum(e.substring(0,t-1))===n}static getStandardUPCEANChecksum(e){let t=e.length,n=0;for(let r=t-1;r>=0;r-=2){let i=e.charAt(r).charCodeAt(0)-"0".charCodeAt(0);if(i<0||i>9)throw new V;n+=i}n*=3;for(let r=t-2;r>=0;r-=2){let i=e.charAt(r).charCodeAt(0)-"0".charCodeAt(0);if(i<0||i>9)throw new V;n+=i}return(1e3-n)%10}}class qt extends de{constructor(){super(),this.decodeMiddleCounters=Int32Array.from([0,0,0,0])}decodeMiddle(e,t,n){let r=this.decodeMiddleCounters;r[0]=0,r[1]=0,r[2]=0,r[3]=0;let i=e.getSize(),s=t[1],o=0;for(let l=0;l<6&&s<i;l++){let f=de.decodeDigit(e,r,s,de.L_AND_G_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+f%10);for(let x of r)s+=x;f>=10&&(o|=1<<5-l)}n=qt.determineFirstDigit(n,o),s=de.findGuardPattern(e,s,!0,de.MIDDLE_PATTERN,new Int32Array(de.MIDDLE_PATTERN.length).fill(0))[1];for(let l=0;l<6&&s<i;l++){let f=de.decodeDigit(e,r,s,de.L_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+f);for(let x of r)s+=x}return{rowOffset:s,resultString:n}}getBarcodeFormat(){return Q.EAN_13}static determineFirstDigit(e,t){for(let n=0;n<10;n++)if(t===this.FIRST_DIGIT_ENCODINGS[n])return e=String.fromCharCode("0".charCodeAt(0)+n)+e,e;throw new v}}qt.FIRST_DIGIT_ENCODINGS=[0,11,13,14,19,25,28,21,22,26];class Br extends de{constructor(){super(),this.decodeMiddleCounters=Int32Array.from([0,0,0,0])}decodeMiddle(e,t,n){const r=this.decodeMiddleCounters;r[0]=0,r[1]=0,r[2]=0,r[3]=0;let i=e.getSize(),s=t[1];for(let a=0;a<4&&s<i;a++){let l=de.decodeDigit(e,r,s,de.L_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+l);for(let f of r)s+=f}s=de.findGuardPattern(e,s,!0,de.MIDDLE_PATTERN,new Int32Array(de.MIDDLE_PATTERN.length).fill(0))[1];for(let a=0;a<4&&s<i;a++){let l=de.decodeDigit(e,r,s,de.L_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+l);for(let f of r)s+=f}return{rowOffset:s,resultString:n}}getBarcodeFormat(){return Q.EAN_8}}class Pr extends de{constructor(){super(...arguments),this.ean13Reader=new qt}getBarcodeFormat(){return Q.UPC_A}decode(e,t){return this.maybeReturnResult(this.ean13Reader.decode(e))}decodeRow(e,t,n){return this.maybeReturnResult(this.ean13Reader.decodeRow(e,t,n))}decodeMiddle(e,t,n){return this.ean13Reader.decodeMiddle(e,t,n)}maybeReturnResult(e){let t=e.getText();if(t.charAt(0)==="0"){let n=new tt(t.substring(1),null,null,e.getResultPoints(),Q.UPC_A);return e.getResultMetadata()!=null&&n.putAllMetadata(e.getResultMetadata()),n}else throw new v}reset(){this.ean13Reader.reset()}}class It extends de{constructor(){super(),this.decodeMiddleCounters=new Int32Array(4)}decodeMiddle(e,t,n){const r=this.decodeMiddleCounters.map(l=>l);r[0]=0,r[1]=0,r[2]=0,r[3]=0;const i=e.getSize();let s=t[1],o=0;for(let l=0;l<6&&s<i;l++){const f=It.decodeDigit(e,r,s,It.L_AND_G_PATTERNS);n+=String.fromCharCode("0".charCodeAt(0)+f%10);for(let x of r)s+=x;f>=10&&(o|=1<<5-l)}let a=It.determineNumSysAndCheckDigit(n,o);return{rowOffset:s,resultString:a}}decodeEnd(e,t){return It.findGuardPatternWithoutCounters(e,t,!0,It.MIDDLE_END_PATTERN)}checkChecksum(e){return de.checkChecksum(It.convertUPCEtoUPCA(e))}static determineNumSysAndCheckDigit(e,t){for(let n=0;n<=1;n++)for(let r=0;r<10;r++)if(t===this.NUMSYS_AND_CHECK_DIGIT_PATTERNS[n][r]){let i=String.fromCharCode("0".charCodeAt(0)+n),s=String.fromCharCode("0".charCodeAt(0)+r);return i+e+s}throw v.getNotFoundInstance()}getBarcodeFormat(){return Q.UPC_E}static convertUPCEtoUPCA(e){const t=e.slice(1,7).split("").map(i=>i.charCodeAt(0)),n=new me;n.append(e.charAt(0));let r=t[5];switch(r){case 0:case 1:case 2:n.appendChars(t,0,2),n.append(r),n.append("0000"),n.appendChars(t,2,3);break;case 3:n.appendChars(t,0,3),n.append("00000"),n.appendChars(t,3,2);break;case 4:n.appendChars(t,0,4),n.append("00000"),n.append(t[4]);break;default:n.appendChars(t,0,5),n.append("0000"),n.append(r);break}return e.length>=8&&n.append(e.charAt(7)),n.toString()}}It.MIDDLE_END_PATTERN=Int32Array.from([1,1,1,1,1,1]),It.NUMSYS_AND_CHECK_DIGIT_PATTERNS=[Int32Array.from([56,52,50,49,44,38,35,42,41,37]),Int32Array.from([7,11,13,14,19,25,28,21,22,26])];class sr extends Fe{constructor(e){super();let t=e==null?null:e.get(Ce.POSSIBLE_FORMATS),n=[];w(t)?(n.push(new qt),n.push(new Pr),n.push(new Br),n.push(new It)):(t.indexOf(Q.EAN_13)>-1&&n.push(new qt),t.indexOf(Q.UPC_A)>-1&&n.push(new Pr),t.indexOf(Q.EAN_8)>-1&&n.push(new Br),t.indexOf(Q.UPC_E)>-1&&n.push(new It)),this.readers=n}decodeRow(e,t,n){for(let r of this.readers)try{const i=r.decodeRow(e,t,n),s=i.getBarcodeFormat()===Q.EAN_13&&i.getText().charAt(0)==="0",o=n==null?null:n.get(Ce.POSSIBLE_FORMATS),a=o==null||o.includes(Q.UPC_A);if(s&&a){const l=i.getRawBytes(),f=new tt(i.getText().substring(1),l,l?l.length:null,i.getResultPoints(),Q.UPC_A);return f.putAllMetadata(i.getResultMetadata()),f}return i}catch{}throw new v}reset(){for(let e of this.readers)e.reset()}}class Je extends Fe{constructor(){super(),this.decodeFinderCounters=new Int32Array(4),this.dataCharacterCounters=new Int32Array(8),this.oddRoundingErrors=new Array(4),this.evenRoundingErrors=new Array(4),this.oddCounts=new Array(this.dataCharacterCounters.length/2),this.evenCounts=new Array(this.dataCharacterCounters.length/2)}getDecodeFinderCounters(){return this.decodeFinderCounters}getDataCharacterCounters(){return this.dataCharacterCounters}getOddRoundingErrors(){return this.oddRoundingErrors}getEvenRoundingErrors(){return this.evenRoundingErrors}getOddCounts(){return this.oddCounts}getEvenCounts(){return this.evenCounts}parseFinderValue(e,t){for(let n=0;n<t.length;n++)if(Fe.patternMatchVariance(e,t[n],Je.MAX_INDIVIDUAL_VARIANCE)<Je.MAX_AVG_VARIANCE)return n;throw new v}static count(e){return ae.sum(new Int32Array(e))}static increment(e,t){let n=0,r=t[0];for(let i=1;i<e.length;i++)t[i]>r&&(r=t[i],n=i);e[n]++}static decrement(e,t){let n=0,r=t[0];for(let i=1;i<e.length;i++)t[i]<r&&(r=t[i],n=i);e[n]--}static isFinderPattern(e){let t=e[0]+e[1],n=t+e[2]+e[3],r=t/n;if(r>=Je.MIN_FINDER_PATTERN_RATIO&&r<=Je.MAX_FINDER_PATTERN_RATIO){let i=Number.MAX_SAFE_INTEGER,s=Number.MIN_SAFE_INTEGER;for(let o of e)o>s&&(s=o),o<i&&(i=o);return s<10*i}return!1}}Je.MAX_AVG_VARIANCE=.2,Je.MAX_INDIVIDUAL_VARIANCE=.45,Je.MIN_FINDER_PATTERN_RATIO=9.5/12,Je.MAX_FINDER_PATTERN_RATIO=12.5/14;class on{constructor(e,t){this.value=e,this.checksumPortion=t}getValue(){return this.value}getChecksumPortion(){return this.checksumPortion}toString(){return this.value+"("+this.checksumPortion+")"}equals(e){if(!(e instanceof on))return!1;const t=e;return this.value===t.value&&this.checksumPortion===t.checksumPortion}hashCode(){return this.value^this.checksumPortion}}class Un{constructor(e,t,n,r,i){this.value=e,this.startEnd=t,this.value=e,this.startEnd=t,this.resultPoints=new Array,this.resultPoints.push(new G(n,i)),this.resultPoints.push(new G(r,i))}getValue(){return this.value}getStartEnd(){return this.startEnd}getResultPoints(){return this.resultPoints}equals(e){if(!(e instanceof Un))return!1;const t=e;return this.value===t.value}hashCode(){return this.value}}class bt{constructor(){}static getRSSvalue(e,t,n){let r=0;for(let a of e)r+=a;let i=0,s=0,o=e.length;for(let a=0;a<o-1;a++){let l;for(l=1,s|=1<<a;l<e[a];l++,s&=~(1<<a)){let f=bt.combins(r-l-1,o-a-2);if(n&&s===0&&r-l-(o-a-1)>=o-a-1&&(f-=bt.combins(r-l-(o-a),o-a-2)),o-a-1>1){let x=0;for(let C=r-l-(o-a-2);C>t;C--)x+=bt.combins(r-l-C-1,o-a-3);f-=x*(o-1-a)}else r-l>t&&f--;i+=f}r-=l}return i}static combins(e,t){let n,r;e-t>t?(r=t,n=e-t):(r=e-t,n=t);let i=1,s=1;for(let o=e;o>n;o--)i*=o,s<=r&&(i/=s,s++);for(;s<=r;)i/=s,s++;return i}}class bi{static buildBitArray(e){let t=e.length*2-1;e[e.length-1].getRightChar()==null&&(t-=1);let n=12*t,r=new ue(n),i=0,o=e[0].getRightChar().getValue();for(let a=11;a>=0;--a)(o&1<<a)!=0&&r.set(i),i++;for(let a=1;a<e.length;++a){let l=e[a],f=l.getLeftChar().getValue();for(let x=11;x>=0;--x)(f&1<<x)!=0&&r.set(i),i++;if(l.getRightChar()!=null){let x=l.getRightChar().getValue();for(let C=11;C>=0;--C)(x&1<<C)!=0&&r.set(i),i++}}return r}}class Kt{constructor(e,t){t?this.decodedInformation=null:(this.finished=e,this.decodedInformation=t)}getDecodedInformation(){return this.decodedInformation}isFinished(){return this.finished}}class or{constructor(e){this.newPosition=e}getNewPosition(){return this.newPosition}}class at extends or{constructor(e,t){super(e),this.value=t}getValue(){return this.value}isFNC1(){return this.value===at.FNC1}}at.FNC1="$";class Jt extends or{constructor(e,t,n){super(e),n?(this.remaining=!0,this.remainingValue=this.remainingValue):(this.remaining=!1,this.remainingValue=0),this.newString=t}getNewString(){return this.newString}isRemaining(){return this.remaining}getRemainingValue(){return this.remainingValue}}class wt extends or{constructor(e,t,n){if(super(e),t<0||t>10||n<0||n>10)throw new V;this.firstDigit=t,this.secondDigit=n}getFirstDigit(){return this.firstDigit}getSecondDigit(){return this.secondDigit}getValue(){return this.firstDigit*10+this.secondDigit}isFirstDigitFNC1(){return this.firstDigit===wt.FNC1}isSecondDigitFNC1(){return this.secondDigit===wt.FNC1}isAnyFNC1(){return this.firstDigit===wt.FNC1||this.secondDigit===wt.FNC1}}wt.FNC1=10;class z{constructor(){}static parseFieldsInGeneralPurpose(e){if(!e)return null;if(e.length<2)throw new v;let t=e.substring(0,2);for(let i of z.TWO_DIGIT_DATA_LENGTH)if(i[0]===t)return i[1]===z.VARIABLE_LENGTH?z.processVariableAI(2,i[2],e):z.processFixedAI(2,i[1],e);if(e.length<3)throw new v;let n=e.substring(0,3);for(let i of z.THREE_DIGIT_DATA_LENGTH)if(i[0]===n)return i[1]===z.VARIABLE_LENGTH?z.processVariableAI(3,i[2],e):z.processFixedAI(3,i[1],e);for(let i of z.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH)if(i[0]===n)return i[1]===z.VARIABLE_LENGTH?z.processVariableAI(4,i[2],e):z.processFixedAI(4,i[1],e);if(e.length<4)throw new v;let r=e.substring(0,4);for(let i of z.FOUR_DIGIT_DATA_LENGTH)if(i[0]===r)return i[1]===z.VARIABLE_LENGTH?z.processVariableAI(4,i[2],e):z.processFixedAI(4,i[1],e);throw new v}static processFixedAI(e,t,n){if(n.length<e)throw new v;let r=n.substring(0,e);if(n.length<e+t)throw new v;let i=n.substring(e,e+t),s=n.substring(e+t),o="("+r+")"+i,a=z.parseFieldsInGeneralPurpose(s);return a==null?o:o+a}static processVariableAI(e,t,n){let r=n.substring(0,e),i;n.length<e+t?i=n.length:i=e+t;let s=n.substring(e,i),o=n.substring(i),a="("+r+")"+s,l=z.parseFieldsInGeneralPurpose(o);return l==null?a:a+l}}z.VARIABLE_LENGTH=[],z.TWO_DIGIT_DATA_LENGTH=[["00",18],["01",14],["02",14],["10",z.VARIABLE_LENGTH,20],["11",6],["12",6],["13",6],["15",6],["17",6],["20",2],["21",z.VARIABLE_LENGTH,20],["22",z.VARIABLE_LENGTH,29],["30",z.VARIABLE_LENGTH,8],["37",z.VARIABLE_LENGTH,8],["90",z.VARIABLE_LENGTH,30],["91",z.VARIABLE_LENGTH,30],["92",z.VARIABLE_LENGTH,30],["93",z.VARIABLE_LENGTH,30],["94",z.VARIABLE_LENGTH,30],["95",z.VARIABLE_LENGTH,30],["96",z.VARIABLE_LENGTH,30],["97",z.VARIABLE_LENGTH,3],["98",z.VARIABLE_LENGTH,30],["99",z.VARIABLE_LENGTH,30]],z.THREE_DIGIT_DATA_LENGTH=[["240",z.VARIABLE_LENGTH,30],["241",z.VARIABLE_LENGTH,30],["242",z.VARIABLE_LENGTH,6],["250",z.VARIABLE_LENGTH,30],["251",z.VARIABLE_LENGTH,30],["253",z.VARIABLE_LENGTH,17],["254",z.VARIABLE_LENGTH,20],["400",z.VARIABLE_LENGTH,30],["401",z.VARIABLE_LENGTH,30],["402",17],["403",z.VARIABLE_LENGTH,30],["410",13],["411",13],["412",13],["413",13],["414",13],["420",z.VARIABLE_LENGTH,20],["421",z.VARIABLE_LENGTH,15],["422",3],["423",z.VARIABLE_LENGTH,15],["424",3],["425",3],["426",3]],z.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH=[["310",6],["311",6],["312",6],["313",6],["314",6],["315",6],["316",6],["320",6],["321",6],["322",6],["323",6],["324",6],["325",6],["326",6],["327",6],["328",6],["329",6],["330",6],["331",6],["332",6],["333",6],["334",6],["335",6],["336",6],["340",6],["341",6],["342",6],["343",6],["344",6],["345",6],["346",6],["347",6],["348",6],["349",6],["350",6],["351",6],["352",6],["353",6],["354",6],["355",6],["356",6],["357",6],["360",6],["361",6],["362",6],["363",6],["364",6],["365",6],["366",6],["367",6],["368",6],["369",6],["390",z.VARIABLE_LENGTH,15],["391",z.VARIABLE_LENGTH,18],["392",z.VARIABLE_LENGTH,15],["393",z.VARIABLE_LENGTH,18],["703",z.VARIABLE_LENGTH,30]],z.FOUR_DIGIT_DATA_LENGTH=[["7001",13],["7002",z.VARIABLE_LENGTH,30],["7003",10],["8001",14],["8002",z.VARIABLE_LENGTH,20],["8003",z.VARIABLE_LENGTH,30],["8004",z.VARIABLE_LENGTH,30],["8005",6],["8006",18],["8007",z.VARIABLE_LENGTH,30],["8008",z.VARIABLE_LENGTH,12],["8018",18],["8020",z.VARIABLE_LENGTH,25],["8100",6],["8101",10],["8102",2],["8110",z.VARIABLE_LENGTH,70],["8200",z.VARIABLE_LENGTH,70]];class an{constructor(e){this.buffer=new me,this.information=e}decodeAllCodes(e,t){let n=t,r=null;do{let i=this.decodeGeneralPurposeField(n,r),s=z.parseFieldsInGeneralPurpose(i.getNewString());if(s!=null&&e.append(s),i.isRemaining()?r=""+i.getRemainingValue():r=null,n===i.getNewPosition())break;n=i.getNewPosition()}while(!0);return e.toString()}isStillNumeric(e){if(e+7>this.information.getSize())return e+4<=this.information.getSize();for(let t=e;t<e+3;++t)if(this.information.get(t))return!0;return this.information.get(e+3)}decodeNumeric(e){if(e+7>this.information.getSize()){let i=this.extractNumericValueFromBitArray(e,4);return i===0?new wt(this.information.getSize(),wt.FNC1,wt.FNC1):new wt(this.information.getSize(),i-1,wt.FNC1)}let t=this.extractNumericValueFromBitArray(e,7),n=(t-8)/11,r=(t-8)%11;return new wt(e+7,n,r)}extractNumericValueFromBitArray(e,t){return an.extractNumericValueFromBitArray(this.information,e,t)}static extractNumericValueFromBitArray(e,t,n){let r=0;for(let i=0;i<n;++i)e.get(t+i)&&(r|=1<<n-i-1);return r}decodeGeneralPurposeField(e,t){this.buffer.setLengthToZero(),t!=null&&this.buffer.append(t),this.current.setPosition(e);let n=this.parseBlocks();return n!=null&&n.isRemaining()?new Jt(this.current.getPosition(),this.buffer.toString(),n.getRemainingValue()):new Jt(this.current.getPosition(),this.buffer.toString())}parseBlocks(){let e,t;do{let n=this.current.getPosition();if(this.current.isAlpha()?(t=this.parseAlphaBlock(),e=t.isFinished()):this.current.isIsoIec646()?(t=this.parseIsoIec646Block(),e=t.isFinished()):(t=this.parseNumericBlock(),e=t.isFinished()),!(n!==this.current.getPosition())&&!e)break}while(!e);return t.getDecodedInformation()}parseNumericBlock(){for(;this.isStillNumeric(this.current.getPosition());){let e=this.decodeNumeric(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFirstDigitFNC1()){let t;return e.isSecondDigitFNC1()?t=new Jt(this.current.getPosition(),this.buffer.toString()):t=new Jt(this.current.getPosition(),this.buffer.toString(),e.getSecondDigit()),new Kt(!0,t)}if(this.buffer.append(e.getFirstDigit()),e.isSecondDigitFNC1()){let t=new Jt(this.current.getPosition(),this.buffer.toString());return new Kt(!0,t)}this.buffer.append(e.getSecondDigit())}return this.isNumericToAlphaNumericLatch(this.current.getPosition())&&(this.current.setAlpha(),this.current.incrementPosition(4)),new Kt(!1)}parseIsoIec646Block(){for(;this.isStillIsoIec646(this.current.getPosition());){let e=this.decodeIsoIec646(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFNC1()){let t=new Jt(this.current.getPosition(),this.buffer.toString());return new Kt(!0,t)}this.buffer.append(e.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setAlpha()),new Kt(!1)}parseAlphaBlock(){for(;this.isStillAlpha(this.current.getPosition());){let e=this.decodeAlphanumeric(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFNC1()){let t=new Jt(this.current.getPosition(),this.buffer.toString());return new Kt(!0,t)}this.buffer.append(e.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setIsoIec646()),new Kt(!1)}isStillIsoIec646(e){if(e+5>this.information.getSize())return!1;let t=this.extractNumericValueFromBitArray(e,5);if(t>=5&&t<16)return!0;if(e+7>this.information.getSize())return!1;let n=this.extractNumericValueFromBitArray(e,7);if(n>=64&&n<116)return!0;if(e+8>this.information.getSize())return!1;let r=this.extractNumericValueFromBitArray(e,8);return r>=232&&r<253}decodeIsoIec646(e){let t=this.extractNumericValueFromBitArray(e,5);if(t===15)return new at(e+5,at.FNC1);if(t>=5&&t<15)return new at(e+5,"0"+(t-5));let n=this.extractNumericValueFromBitArray(e,7);if(n>=64&&n<90)return new at(e+7,""+(n+1));if(n>=90&&n<116)return new at(e+7,""+(n+7));let r=this.extractNumericValueFromBitArray(e,8),i;switch(r){case 232:i="!";break;case 233:i='"';break;case 234:i="%";break;case 235:i="&";break;case 236:i="'";break;case 237:i="(";break;case 238:i=")";break;case 239:i="*";break;case 240:i="+";break;case 241:i=",";break;case 242:i="-";break;case 243:i=".";break;case 244:i="/";break;case 245:i=":";break;case 246:i=";";break;case 247:i="<";break;case 248:i="=";break;case 249:i=">";break;case 250:i="?";break;case 251:i="_";break;case 252:i=" ";break;default:throw new V}return new at(e+8,i)}isStillAlpha(e){if(e+5>this.information.getSize())return!1;let t=this.extractNumericValueFromBitArray(e,5);if(t>=5&&t<16)return!0;if(e+6>this.information.getSize())return!1;let n=this.extractNumericValueFromBitArray(e,6);return n>=16&&n<63}decodeAlphanumeric(e){let t=this.extractNumericValueFromBitArray(e,5);if(t===15)return new at(e+5,at.FNC1);if(t>=5&&t<15)return new at(e+5,"0"+(t-5));let n=this.extractNumericValueFromBitArray(e,6);if(n>=32&&n<58)return new at(e+6,""+(n+33));let r;switch(n){case 58:r="*";break;case 59:r=",";break;case 60:r="-";break;case 61:r=".";break;case 62:r="/";break;default:throw new St("Decoding invalid alphanumeric value: "+n)}return new at(e+6,r)}isAlphaTo646ToAlphaLatch(e){if(e+1>this.information.getSize())return!1;for(let t=0;t<5&&t+e<this.information.getSize();++t)if(t===2){if(!this.information.get(e+2))return!1}else if(this.information.get(e+t))return!1;return!0}isAlphaOr646ToNumericLatch(e){if(e+3>this.information.getSize())return!1;for(let t=e;t<e+3;++t)if(this.information.get(t))return!1;return!0}isNumericToAlphaNumericLatch(e){if(e+1>this.information.getSize())return!1;for(let t=0;t<4&&t+e<this.information.getSize();++t)if(this.information.get(e+t))return!1;return!0}}class ar{constructor(e){this.information=e,this.generalDecoder=new an(e)}getInformation(){return this.information}getGeneralDecoder(){return this.generalDecoder}}class lt extends ar{constructor(e){super(e)}encodeCompressedGtin(e,t){e.append("(01)");let n=e.length();e.append("9"),this.encodeCompressedGtinWithoutAI(e,t,n)}encodeCompressedGtinWithoutAI(e,t,n){for(let r=0;r<4;++r){let i=this.getGeneralDecoder().extractNumericValueFromBitArray(t+10*r,10);i/100===0&&e.append("0"),i/10===0&&e.append("0"),e.append(i)}lt.appendCheckDigit(e,n)}static appendCheckDigit(e,t){let n=0;for(let r=0;r<13;r++){let i=e.charAt(r+t).charCodeAt(0)-"0".charCodeAt(0);n+=(r&1)===0?3*i:i}n=10-n%10,n===10&&(n=0),e.append(n)}}lt.GTIN_SIZE=40;class ln extends lt{constructor(e){super(e)}parseInformation(){let e=new me;e.append("(01)");let t=e.length(),n=this.getGeneralDecoder().extractNumericValueFromBitArray(ln.HEADER_SIZE,4);return e.append(n),this.encodeCompressedGtinWithoutAI(e,ln.HEADER_SIZE+4,t),this.getGeneralDecoder().decodeAllCodes(e,ln.HEADER_SIZE+44)}}ln.HEADER_SIZE=1+1+2;class Vn extends ar{constructor(e){super(e)}parseInformation(){let e=new me;return this.getGeneralDecoder().decodeAllCodes(e,Vn.HEADER_SIZE)}}Vn.HEADER_SIZE=2+1+2;class Hn extends lt{constructor(e){super(e)}encodeCompressedWeight(e,t,n){let r=this.getGeneralDecoder().extractNumericValueFromBitArray(t,n);this.addWeightCode(e,r);let i=this.checkWeight(r),s=1e5;for(let o=0;o<5;++o)i/s===0&&e.append("0"),s/=10;e.append(i)}}class Nt extends Hn{constructor(e){super(e)}parseInformation(){if(this.getInformation().getSize()!=Nt.HEADER_SIZE+Hn.GTIN_SIZE+Nt.WEIGHT_SIZE)throw new v;let e=new me;return this.encodeCompressedGtin(e,Nt.HEADER_SIZE),this.encodeCompressedWeight(e,Nt.HEADER_SIZE+Hn.GTIN_SIZE,Nt.WEIGHT_SIZE),e.toString()}}Nt.HEADER_SIZE=4+1,Nt.WEIGHT_SIZE=15;class Ni extends Nt{constructor(e){super(e)}addWeightCode(e,t){e.append("(3103)")}checkWeight(e){return e}}class Mi extends Nt{constructor(e){super(e)}addWeightCode(e,t){t<1e4?e.append("(3202)"):e.append("(3203)")}checkWeight(e){return e<1e4?e:e-1e4}}class Mt extends lt{constructor(e){super(e)}parseInformation(){if(this.getInformation().getSize()<Mt.HEADER_SIZE+lt.GTIN_SIZE)throw new v;let e=new me;this.encodeCompressedGtin(e,Mt.HEADER_SIZE);let t=this.getGeneralDecoder().extractNumericValueFromBitArray(Mt.HEADER_SIZE+lt.GTIN_SIZE,Mt.LAST_DIGIT_SIZE);e.append("(392"),e.append(t),e.append(")");let n=this.getGeneralDecoder().decodeGeneralPurposeField(Mt.HEADER_SIZE+lt.GTIN_SIZE+Mt.LAST_DIGIT_SIZE,null);return e.append(n.getNewString()),e.toString()}}Mt.HEADER_SIZE=5+1+2,Mt.LAST_DIGIT_SIZE=2;class rt extends lt{constructor(e){super(e)}parseInformation(){if(this.getInformation().getSize()<rt.HEADER_SIZE+lt.GTIN_SIZE)throw new v;let e=new me;this.encodeCompressedGtin(e,rt.HEADER_SIZE);let t=this.getGeneralDecoder().extractNumericValueFromBitArray(rt.HEADER_SIZE+lt.GTIN_SIZE,rt.LAST_DIGIT_SIZE);e.append("(393"),e.append(t),e.append(")");let n=this.getGeneralDecoder().extractNumericValueFromBitArray(rt.HEADER_SIZE+lt.GTIN_SIZE+rt.LAST_DIGIT_SIZE,rt.FIRST_THREE_DIGITS_SIZE);n/100==0&&e.append("0"),n/10==0&&e.append("0"),e.append(n);let r=this.getGeneralDecoder().decodeGeneralPurposeField(rt.HEADER_SIZE+lt.GTIN_SIZE+rt.LAST_DIGIT_SIZE+rt.FIRST_THREE_DIGITS_SIZE,null);return e.append(r.getNewString()),e.toString()}}rt.HEADER_SIZE=5+1+2,rt.LAST_DIGIT_SIZE=2,rt.FIRST_THREE_DIGITS_SIZE=10;class _e extends Hn{constructor(e,t,n){super(e),this.dateCode=n,this.firstAIdigits=t}parseInformation(){if(this.getInformation().getSize()!=_e.HEADER_SIZE+_e.GTIN_SIZE+_e.WEIGHT_SIZE+_e.DATE_SIZE)throw new v;let e=new me;return this.encodeCompressedGtin(e,_e.HEADER_SIZE),this.encodeCompressedWeight(e,_e.HEADER_SIZE+_e.GTIN_SIZE,_e.WEIGHT_SIZE),this.encodeCompressedDate(e,_e.HEADER_SIZE+_e.GTIN_SIZE+_e.WEIGHT_SIZE),e.toString()}encodeCompressedDate(e,t){let n=this.getGeneralDecoder().extractNumericValueFromBitArray(t,_e.DATE_SIZE);if(n==38400)return;e.append("("),e.append(this.dateCode),e.append(")");let r=n%32;n/=32;let i=n%12+1;n/=12;let s=n;s/10==0&&e.append("0"),e.append(s),i/10==0&&e.append("0"),e.append(i),r/10==0&&e.append("0"),e.append(r)}addWeightCode(e,t){e.append("("),e.append(this.firstAIdigits),e.append(t/1e5),e.append(")")}checkWeight(e){return e%1e5}}_e.HEADER_SIZE=7+1,_e.WEIGHT_SIZE=20,_e.DATE_SIZE=16;function Fr(g){try{if(g.get(1))return new ln(g);if(!g.get(2))return new Vn(g);switch(an.extractNumericValueFromBitArray(g,1,4)){case 4:return new Ni(g);case 5:return new Mi(g)}switch(an.extractNumericValueFromBitArray(g,1,5)){case 12:return new Mt(g);case 13:return new rt(g)}switch(an.extractNumericValueFromBitArray(g,1,7)){case 56:return new _e(g,"310","11");case 57:return new _e(g,"320","11");case 58:return new _e(g,"310","13");case 59:return new _e(g,"320","13");case 60:return new _e(g,"310","15");case 61:return new _e(g,"320","15");case 62:return new _e(g,"310","17");case 63:return new _e(g,"320","17")}}catch(e){throw console.log(e),new St("unknown decoder: "+g)}}class Ht{constructor(e,t,n,r){this.leftchar=e,this.rightchar=t,this.finderpattern=n,this.maybeLast=r}mayBeLast(){return this.maybeLast}getLeftChar(){return this.leftchar}getRightChar(){return this.rightchar}getFinderPattern(){return this.finderpattern}mustBeLast(){return this.rightchar==null}toString(){return"[ "+this.leftchar+", "+this.rightchar+" : "+(this.finderpattern==null?"null":this.finderpattern.getValue())+" ]"}static equals(e,t){return e instanceof Ht?Ht.equalsOrNull(e.leftchar,t.leftchar)&&Ht.equalsOrNull(e.rightchar,t.rightchar)&&Ht.equalsOrNull(e.finderpattern,t.finderpattern):!1}static equalsOrNull(e,t){return e===null?t===null:Ht.equals(e,t)}hashCode(){return this.leftchar.getValue()^this.rightchar.getValue()^this.finderpattern.getValue()}}class lr{constructor(e,t,n){this.pairs=e,this.rowNumber=t,this.wasReversed=n}getPairs(){return this.pairs}getRowNumber(){return this.rowNumber}isReversed(){return this.wasReversed}isEquivalent(e){return this.checkEqualitity(this,e)}toString(){return"{ "+this.pairs+" }"}equals(e,t){return e instanceof lr?this.checkEqualitity(e,t)&&e.wasReversed===t.wasReversed:!1}checkEqualitity(e,t){if(!e||!t)return;let n;return e.forEach((r,i)=>{t.forEach(s=>{r.getLeftChar().getValue()===s.getLeftChar().getValue()&&r.getRightChar().getValue()===s.getRightChar().getValue()&&r.getFinderPatter().getValue()===s.getFinderPatter().getValue()&&(n=!0)})}),n}}class L extends Je{constructor(e){super(...arguments),this.pairs=new Array(L.MAX_PAIRS),this.rows=new Array,this.startEnd=[2],this.verbose=e===!0}decodeRow(e,t,n){this.pairs.length=0,this.startFromEven=!1;try{return L.constructResult(this.decodeRow2pairs(e,t))}catch(r){this.verbose&&console.log(r)}return this.pairs.length=0,this.startFromEven=!0,L.constructResult(this.decodeRow2pairs(e,t))}reset(){this.pairs.length=0,this.rows.length=0}decodeRow2pairs(e,t){let n=!1;for(;!n;)try{this.pairs.push(this.retrieveNextPair(t,this.pairs,e))}catch(i){if(i instanceof v){if(!this.pairs.length)throw new v;n=!0}}if(this.checkChecksum())return this.pairs;let r;if(this.rows.length?r=!0:r=!1,this.storeRow(e,!1),r){let i=this.checkRowsBoolean(!1);if(i!=null||(i=this.checkRowsBoolean(!0),i!=null))return i}throw new v}checkRowsBoolean(e){if(this.rows.length>25)return this.rows.length=0,null;this.pairs.length=0,e&&(this.rows=this.rows.reverse());let t=null;try{t=this.checkRows(new Array,0)}catch(n){this.verbose&&console.log(n)}return e&&(this.rows=this.rows.reverse()),t}checkRows(e,t){for(let n=t;n<this.rows.length;n++){let r=this.rows[n];this.pairs.length=0;for(let s of e)this.pairs.push(s.getPairs());if(this.pairs.push(r.getPairs()),!L.isValidSequence(this.pairs))continue;if(this.checkChecksum())return this.pairs;let i=new Array(e);i.push(r);try{return this.checkRows(i,n+1)}catch(s){this.verbose&&console.log(s)}}throw new v}static isValidSequence(e){for(let t of L.FINDER_PATTERN_SEQUENCES){if(e.length>t.length)continue;let n=!0;for(let r=0;r<e.length;r++)if(e[r].getFinderPattern().getValue()!=t[r]){n=!1;break}if(n)return!0}return!1}storeRow(e,t){let n=0,r=!1,i=!1;for(;n<this.rows.length;){let s=this.rows[n];if(s.getRowNumber()>e){i=s.isEquivalent(this.pairs);break}r=s.isEquivalent(this.pairs),n++}i||r||L.isPartialRow(this.pairs,this.rows)||(this.rows.push(n,new lr(this.pairs,e,t)),this.removePartialRows(this.pairs,this.rows))}removePartialRows(e,t){for(let n of t)if(n.getPairs().length!==e.length){for(let r of n.getPairs())for(let i of e)if(Ht.equals(r,i))break}}static isPartialRow(e,t){for(let n of t){let r=!0;for(let i of e){let s=!1;for(let o of n.getPairs())if(i.equals(o)){s=!0;break}if(!s){r=!1;break}}if(r)return!0}return!1}getRows(){return this.rows}static constructResult(e){let t=bi.buildBitArray(e),r=Fr(t).parseInformation(),i=e[0].getFinderPattern().getResultPoints(),s=e[e.length-1].getFinderPattern().getResultPoints(),o=[i[0],i[1],s[0],s[1]];return new tt(r,null,null,o,Q.RSS_EXPANDED,null)}checkChecksum(){let e=this.pairs.get(0),t=e.getLeftChar(),n=e.getRightChar();if(n==null)return!1;let r=n.getChecksumPortion(),i=2;for(let o=1;o<this.pairs.size();++o){let a=this.pairs.get(o);r+=a.getLeftChar().getChecksumPortion(),i++;let l=a.getRightChar();l!=null&&(r+=l.getChecksumPortion(),i++)}return r%=211,211*(i-4)+r==t.getValue()}static getNextSecondBar(e,t){let n;return e.get(t)?(n=e.getNextUnset(t),n=e.getNextSet(n)):(n=e.getNextSet(t),n=e.getNextUnset(n)),n}retrieveNextPair(e,t,n){let r=t.length%2==0;this.startFromEven&&(r=!r);let i,s=!0,o=-1;do this.findNextPair(e,t,o),i=this.parseFoundFinderPattern(e,n,r),i==null?o=L.getNextSecondBar(e,this.startEnd[0]):s=!1;while(s);let a=this.decodeDataCharacter(e,i,r,!0);if(!this.isEmptyPair(t)&&t[t.length-1].mustBeLast())throw new v;let l;try{l=this.decodeDataCharacter(e,i,r,!1)}catch(f){l=null,this.verbose&&console.log(f)}return new Ht(a,l,i,!0)}isEmptyPair(e){return e.length===0}findNextPair(e,t,n){let r=this.getDecodeFinderCounters();r[0]=0,r[1]=0,r[2]=0,r[3]=0;let i=e.getSize(),s;n>=0?s=n:this.isEmptyPair(t)?s=0:s=t[t.length-1].getFinderPattern().getStartEnd()[1];let o=t.length%2!=0;this.startFromEven&&(o=!o);let a=!1;for(;s<i&&(a=!e.get(s),!!a);)s++;let l=0,f=s;for(let x=s;x<i;x++)if(e.get(x)!=a)r[l]++;else{if(l==3){if(o&&L.reverseCounters(r),L.isFinderPattern(r)){this.startEnd[0]=f,this.startEnd[1]=x;return}o&&L.reverseCounters(r),f+=r[0]+r[1],r[0]=r[2],r[1]=r[3],r[2]=0,r[3]=0,l--}else l++;r[l]=1,a=!a}throw new v}static reverseCounters(e){let t=e.length;for(let n=0;n<t/2;++n){let r=e[n];e[n]=e[t-n-1],e[t-n-1]=r}}parseFoundFinderPattern(e,t,n){let r,i,s;if(n){let l=this.startEnd[0]-1;for(;l>=0&&!e.get(l);)l--;l++,r=this.startEnd[0]-l,i=l,s=this.startEnd[1]}else i=this.startEnd[0],s=e.getNextUnset(this.startEnd[1]+1),r=s-this.startEnd[1];let o=this.getDecodeFinderCounters();W.arraycopy(o,0,o,1,o.length-1),o[0]=r;let a;try{a=this.parseFinderValue(o,L.FINDER_PATTERNS)}catch{return null}return new Un(a,[i,s],i,s,t)}decodeDataCharacter(e,t,n,r){let i=this.getDataCharacterCounters();for(let ye=0;ye<i.length;ye++)i[ye]=0;if(r)L.recordPatternInReverse(e,t.getStartEnd()[0],i);else{L.recordPattern(e,t.getStartEnd()[1],i);for(let ye=0,qe=i.length-1;ye<qe;ye++,qe--){let ft=i[ye];i[ye]=i[qe],i[qe]=ft}}let s=17,o=ae.sum(new Int32Array(i))/s,a=(t.getStartEnd()[1]-t.getStartEnd()[0])/15;if(Math.abs(o-a)/a>.3)throw new v;let l=this.getOddCounts(),f=this.getEvenCounts(),x=this.getOddRoundingErrors(),C=this.getEvenRoundingErrors();for(let ye=0;ye<i.length;ye++){let qe=1*i[ye]/o,ft=qe+.5;if(ft<1){if(qe<.3)throw new v;ft=1}else if(ft>8){if(qe>8.7)throw new v;ft=8}let dn=ye/2;(ye&1)==0?(l[dn]=ft,x[dn]=qe-ft):(f[dn]=ft,C[dn]=qe-ft)}this.adjustOddEvenCounts(s);let E=4*t.getValue()+(n?0:2)+(r?0:1)-1,I=0,y=0;for(let ye=l.length-1;ye>=0;ye--){if(L.isNotA1left(t,n,r)){let qe=L.WEIGHTS[E][2*ye];y+=l[ye]*qe}I+=l[ye]}let b=0;for(let ye=f.length-1;ye>=0;ye--)if(L.isNotA1left(t,n,r)){let qe=L.WEIGHTS[E][2*ye+1];b+=f[ye]*qe}let D=y+b;if((I&1)!=0||I>13||I<4)throw new v;let F=(13-I)/2,k=L.SYMBOL_WIDEST[F],B=9-k,se=bt.getRSSvalue(l,k,!0),ee=bt.getRSSvalue(f,B,!1),ut=L.EVEN_TOTAL_SUBSET[F],yt=L.GSUM[F],ht=se*ut+ee+yt;return new on(ht,D)}static isNotA1left(e,t,n){return!(e.getValue()==0&&t&&n)}adjustOddEvenCounts(e){let t=ae.sum(new Int32Array(this.getOddCounts())),n=ae.sum(new Int32Array(this.getEvenCounts())),r=!1,i=!1;t>13?i=!0:t<4&&(r=!0);let s=!1,o=!1;n>13?o=!0:n<4&&(s=!0);let a=t+n-e,l=(t&1)==1,f=(n&1)==0;if(a==1)if(l){if(f)throw new v;i=!0}else{if(!f)throw new v;o=!0}else if(a==-1)if(l){if(f)throw new v;r=!0}else{if(!f)throw new v;s=!0}else if(a==0){if(l){if(!f)throw new v;t<n?(r=!0,o=!0):(i=!0,s=!0)}else if(f)throw new v}else throw new v;if(r){if(i)throw new v;L.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(i&&L.decrement(this.getOddCounts(),this.getOddRoundingErrors()),s){if(o)throw new v;L.increment(this.getEvenCounts(),this.getOddRoundingErrors())}o&&L.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())}}L.SYMBOL_WIDEST=[7,5,4,3,1],L.EVEN_TOTAL_SUBSET=[4,20,52,104,204],L.GSUM=[0,348,1388,2948,3988],L.FINDER_PATTERNS=[Int32Array.from([1,8,4,1]),Int32Array.from([3,6,4,1]),Int32Array.from([3,4,6,1]),Int32Array.from([3,2,8,1]),Int32Array.from([2,6,5,1]),Int32Array.from([2,2,9,1])],L.WEIGHTS=[[1,3,9,27,81,32,96,77],[20,60,180,118,143,7,21,63],[189,145,13,39,117,140,209,205],[193,157,49,147,19,57,171,91],[62,186,136,197,169,85,44,132],[185,133,188,142,4,12,36,108],[113,128,173,97,80,29,87,50],[150,28,84,41,123,158,52,156],[46,138,203,187,139,206,196,166],[76,17,51,153,37,111,122,155],[43,129,176,106,107,110,119,146],[16,48,144,10,30,90,59,177],[109,116,137,200,178,112,125,164],[70,210,208,202,184,130,179,115],[134,191,151,31,93,68,204,190],[148,22,66,198,172,94,71,2],[6,18,54,162,64,192,154,40],[120,149,25,75,14,42,126,167],[79,26,78,23,69,207,199,175],[103,98,83,38,114,131,182,124],[161,61,183,127,170,88,53,159],[55,165,73,8,24,72,5,15],[45,135,194,160,58,174,100,89]],L.FINDER_PAT_A=0,L.FINDER_PAT_B=1,L.FINDER_PAT_C=2,L.FINDER_PAT_D=3,L.FINDER_PAT_E=4,L.FINDER_PAT_F=5,L.FINDER_PATTERN_SEQUENCES=[[L.FINDER_PAT_A,L.FINDER_PAT_A],[L.FINDER_PAT_A,L.FINDER_PAT_B,L.FINDER_PAT_B],[L.FINDER_PAT_A,L.FINDER_PAT_C,L.FINDER_PAT_B,L.FINDER_PAT_D],[L.FINDER_PAT_A,L.FINDER_PAT_E,L.FINDER_PAT_B,L.FINDER_PAT_D,L.FINDER_PAT_C],[L.FINDER_PAT_A,L.FINDER_PAT_E,L.FINDER_PAT_B,L.FINDER_PAT_D,L.FINDER_PAT_D,L.FINDER_PAT_F],[L.FINDER_PAT_A,L.FINDER_PAT_E,L.FINDER_PAT_B,L.FINDER_PAT_D,L.FINDER_PAT_E,L.FINDER_PAT_F,L.FINDER_PAT_F],[L.FINDER_PAT_A,L.FINDER_PAT_A,L.FINDER_PAT_B,L.FINDER_PAT_B,L.FINDER_PAT_C,L.FINDER_PAT_C,L.FINDER_PAT_D,L.FINDER_PAT_D],[L.FINDER_PAT_A,L.FINDER_PAT_A,L.FINDER_PAT_B,L.FINDER_PAT_B,L.FINDER_PAT_C,L.FINDER_PAT_C,L.FINDER_PAT_D,L.FINDER_PAT_E,L.FINDER_PAT_E],[L.FINDER_PAT_A,L.FINDER_PAT_A,L.FINDER_PAT_B,L.FINDER_PAT_B,L.FINDER_PAT_C,L.FINDER_PAT_C,L.FINDER_PAT_D,L.FINDER_PAT_E,L.FINDER_PAT_F,L.FINDER_PAT_F],[L.FINDER_PAT_A,L.FINDER_PAT_A,L.FINDER_PAT_B,L.FINDER_PAT_B,L.FINDER_PAT_C,L.FINDER_PAT_D,L.FINDER_PAT_D,L.FINDER_PAT_E,L.FINDER_PAT_E,L.FINDER_PAT_F,L.FINDER_PAT_F]],L.MAX_PAIRS=11;class _i extends on{constructor(e,t,n){super(e,t),this.count=0,this.finderPattern=n}getFinderPattern(){return this.finderPattern}getCount(){return this.count}incrementCount(){this.count++}}class ve extends Je{constructor(){super(...arguments),this.possibleLeftPairs=[],this.possibleRightPairs=[]}decodeRow(e,t,n){const r=this.decodePair(t,!1,e,n);ve.addOrTally(this.possibleLeftPairs,r),t.reverse();let i=this.decodePair(t,!0,e,n);ve.addOrTally(this.possibleRightPairs,i),t.reverse();for(let s of this.possibleLeftPairs)if(s.getCount()>1){for(let o of this.possibleRightPairs)if(o.getCount()>1&&ve.checkChecksum(s,o))return ve.constructResult(s,o)}throw new v}static addOrTally(e,t){if(t==null)return;let n=!1;for(let r of e)if(r.getValue()===t.getValue()){r.incrementCount(),n=!0;break}n||e.push(t)}reset(){this.possibleLeftPairs.length=0,this.possibleRightPairs.length=0}static constructResult(e,t){let n=4537077*e.getValue()+t.getValue(),r=new String(n).toString(),i=new me;for(let l=13-r.length;l>0;l--)i.append("0");i.append(r);let s=0;for(let l=0;l<13;l++){let f=i.charAt(l).charCodeAt(0)-"0".charCodeAt(0);s+=(l&1)===0?3*f:f}s=10-s%10,s===10&&(s=0),i.append(s.toString());let o=e.getFinderPattern().getResultPoints(),a=t.getFinderPattern().getResultPoints();return new tt(i.toString(),null,0,[o[0],o[1],a[0],a[1]],Q.RSS_14,new Date().getTime())}static checkChecksum(e,t){let n=(e.getChecksumPortion()+16*t.getChecksumPortion())%79,r=9*e.getFinderPattern().getValue()+t.getFinderPattern().getValue();return r>72&&r--,r>8&&r--,n===r}decodePair(e,t,n,r){try{let i=this.findFinderPattern(e,t),s=this.parseFoundFinderPattern(e,n,t,i),o=r==null?null:r.get(Ce.NEED_RESULT_POINT_CALLBACK);if(o!=null){let f=(i[0]+i[1])/2;t&&(f=e.getSize()-1-f),o.foundPossibleResultPoint(new G(f,n))}let a=this.decodeDataCharacter(e,s,!0),l=this.decodeDataCharacter(e,s,!1);return new _i(1597*a.getValue()+l.getValue(),a.getChecksumPortion()+4*l.getChecksumPortion(),s)}catch{return null}}decodeDataCharacter(e,t,n){let r=this.getDataCharacterCounters();for(let b=0;b<r.length;b++)r[b]=0;if(n)Fe.recordPatternInReverse(e,t.getStartEnd()[0],r);else{Fe.recordPattern(e,t.getStartEnd()[1]+1,r);for(let b=0,D=r.length-1;b<D;b++,D--){let F=r[b];r[b]=r[D],r[D]=F}}let i=n?16:15,s=ae.sum(new Int32Array(r))/i,o=this.getOddCounts(),a=this.getEvenCounts(),l=this.getOddRoundingErrors(),f=this.getEvenRoundingErrors();for(let b=0;b<r.length;b++){let D=r[b]/s,F=Math.floor(D+.5);F<1?F=1:F>8&&(F=8);let k=Math.floor(b/2);(b&1)===0?(o[k]=F,l[k]=D-F):(a[k]=F,f[k]=D-F)}this.adjustOddEvenCounts(n,i);let x=0,C=0;for(let b=o.length-1;b>=0;b--)C*=9,C+=o[b],x+=o[b];let E=0,I=0;for(let b=a.length-1;b>=0;b--)E*=9,E+=a[b],I+=a[b];let y=C+3*E;if(n){if((x&1)!==0||x>12||x<4)throw new v;let b=(12-x)/2,D=ve.OUTSIDE_ODD_WIDEST[b],F=9-D,k=bt.getRSSvalue(o,D,!1),B=bt.getRSSvalue(a,F,!0),se=ve.OUTSIDE_EVEN_TOTAL_SUBSET[b],ee=ve.OUTSIDE_GSUM[b];return new on(k*se+B+ee,y)}else{if((I&1)!==0||I>10||I<4)throw new v;let b=(10-I)/2,D=ve.INSIDE_ODD_WIDEST[b],F=9-D,k=bt.getRSSvalue(o,D,!0),B=bt.getRSSvalue(a,F,!1),se=ve.INSIDE_ODD_TOTAL_SUBSET[b],ee=ve.INSIDE_GSUM[b];return new on(B*se+k+ee,y)}}findFinderPattern(e,t){let n=this.getDecodeFinderCounters();n[0]=0,n[1]=0,n[2]=0,n[3]=0;let r=e.getSize(),i=!1,s=0;for(;s<r&&(i=!e.get(s),t!==i);)s++;let o=0,a=s;for(let l=s;l<r;l++)if(e.get(l)!==i)n[o]++;else{if(o===3){if(Je.isFinderPattern(n))return[a,l];a+=n[0]+n[1],n[0]=n[2],n[1]=n[3],n[2]=0,n[3]=0,o--}else o++;n[o]=1,i=!i}throw new v}parseFoundFinderPattern(e,t,n,r){let i=e.get(r[0]),s=r[0]-1;for(;s>=0&&i!==e.get(s);)s--;s++;const o=r[0]-s,a=this.getDecodeFinderCounters(),l=new Int32Array(a.length);W.arraycopy(a,0,l,1,a.length-1),l[0]=o;const f=this.parseFinderValue(l,ve.FINDER_PATTERNS);let x=s,C=r[1];return n&&(x=e.getSize()-1-x,C=e.getSize()-1-C),new Un(f,[s,r[1]],x,C,t)}adjustOddEvenCounts(e,t){let n=ae.sum(new Int32Array(this.getOddCounts())),r=ae.sum(new Int32Array(this.getEvenCounts())),i=!1,s=!1,o=!1,a=!1;e?(n>12?s=!0:n<4&&(i=!0),r>12?a=!0:r<4&&(o=!0)):(n>11?s=!0:n<5&&(i=!0),r>10?a=!0:r<4&&(o=!0));let l=n+r-t,f=(n&1)===(e?1:0),x=(r&1)===1;if(l===1)if(f){if(x)throw new v;s=!0}else{if(!x)throw new v;a=!0}else if(l===-1)if(f){if(x)throw new v;i=!0}else{if(!x)throw new v;o=!0}else if(l===0){if(f){if(!x)throw new v;n<r?(i=!0,a=!0):(s=!0,o=!0)}else if(x)throw new v}else throw new v;if(i){if(s)throw new v;Je.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(s&&Je.decrement(this.getOddCounts(),this.getOddRoundingErrors()),o){if(a)throw new v;Je.increment(this.getEvenCounts(),this.getOddRoundingErrors())}a&&Je.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())}}ve.OUTSIDE_EVEN_TOTAL_SUBSET=[1,10,34,70,126],ve.INSIDE_ODD_TOTAL_SUBSET=[4,20,48,81],ve.OUTSIDE_GSUM=[0,161,961,2015,2715],ve.INSIDE_GSUM=[0,336,1036,1516],ve.OUTSIDE_ODD_WIDEST=[8,6,4,3,1],ve.INSIDE_ODD_WIDEST=[2,4,6,8],ve.FINDER_PATTERNS=[Int32Array.from([3,8,2,1]),Int32Array.from([3,5,5,1]),Int32Array.from([3,3,7,1]),Int32Array.from([3,1,9,1]),Int32Array.from([2,7,4,1]),Int32Array.from([2,5,6,1]),Int32Array.from([2,3,8,1]),Int32Array.from([1,5,7,1]),Int32Array.from([1,3,9,1])];class cn extends Fe{constructor(e,t){super(),this.readers=[],this.verbose=t===!0;const n=e?e.get(Ce.POSSIBLE_FORMATS):null,r=e&&e.get(Ce.ASSUME_CODE_39_CHECK_DIGIT)!==void 0;n?((n.includes(Q.EAN_13)||n.includes(Q.UPC_A)||n.includes(Q.EAN_8)||n.includes(Q.UPC_E))&&this.readers.push(new sr(e)),n.includes(Q.CODE_39)&&this.readers.push(new ke(r)),n.includes(Q.CODE_128)&&this.readers.push(new H),n.includes(Q.ITF)&&this.readers.push(new Ee),n.includes(Q.RSS_14)&&this.readers.push(new ve),n.includes(Q.RSS_EXPANDED)&&this.readers.push(new L(this.verbose))):(this.readers.push(new sr(e)),this.readers.push(new ke),this.readers.push(new sr(e)),this.readers.push(new H),this.readers.push(new Ee),this.readers.push(new ve),this.readers.push(new L(this.verbose)))}decodeRow(e,t,n){for(let r=0;r<this.readers.length;r++)try{return this.readers[r].decodeRow(e,t,n)}catch{}throw new v}reset(){this.readers.forEach(e=>e.reset())}}class Oi extends Qt{constructor(e=500,t){super(new cn(t),e,t)}}class xe{constructor(e,t,n){this.ecCodewords=e,this.ecBlocks=[t],n&&this.ecBlocks.push(n)}getECCodewords(){return this.ecCodewords}getECBlocks(){return this.ecBlocks}}class ge{constructor(e,t){this.count=e,this.dataCodewords=t}getCount(){return this.count}getDataCodewords(){return this.dataCodewords}}class oe{constructor(e,t,n,r,i,s){this.versionNumber=e,this.symbolSizeRows=t,this.symbolSizeColumns=n,this.dataRegionSizeRows=r,this.dataRegionSizeColumns=i,this.ecBlocks=s;let o=0;const a=s.getECCodewords(),l=s.getECBlocks();for(let f of l)o+=f.getCount()*(f.getDataCodewords()+a);this.totalCodewords=o}getVersionNumber(){return this.versionNumber}getSymbolSizeRows(){return this.symbolSizeRows}getSymbolSizeColumns(){return this.symbolSizeColumns}getDataRegionSizeRows(){return this.dataRegionSizeRows}getDataRegionSizeColumns(){return this.dataRegionSizeColumns}getTotalCodewords(){return this.totalCodewords}getECBlocks(){return this.ecBlocks}static getVersionForDimensions(e,t){if((e&1)!==0||(t&1)!==0)throw new V;for(let n of oe.VERSIONS)if(n.symbolSizeRows===e&&n.symbolSizeColumns===t)return n;throw new V}toString(){return""+this.versionNumber}static buildVersions(){return[new oe(1,10,10,8,8,new xe(5,new ge(1,3))),new oe(2,12,12,10,10,new xe(7,new ge(1,5))),new oe(3,14,14,12,12,new xe(10,new ge(1,8))),new oe(4,16,16,14,14,new xe(12,new ge(1,12))),new oe(5,18,18,16,16,new xe(14,new ge(1,18))),new oe(6,20,20,18,18,new xe(18,new ge(1,22))),new oe(7,22,22,20,20,new xe(20,new ge(1,30))),new oe(8,24,24,22,22,new xe(24,new ge(1,36))),new oe(9,26,26,24,24,new xe(28,new ge(1,44))),new oe(10,32,32,14,14,new xe(36,new ge(1,62))),new oe(11,36,36,16,16,new xe(42,new ge(1,86))),new oe(12,40,40,18,18,new xe(48,new ge(1,114))),new oe(13,44,44,20,20,new xe(56,new ge(1,144))),new oe(14,48,48,22,22,new xe(68,new ge(1,174))),new oe(15,52,52,24,24,new xe(42,new ge(2,102))),new oe(16,64,64,14,14,new xe(56,new ge(2,140))),new oe(17,72,72,16,16,new xe(36,new ge(4,92))),new oe(18,80,80,18,18,new xe(48,new ge(4,114))),new oe(19,88,88,20,20,new xe(56,new ge(4,144))),new oe(20,96,96,22,22,new xe(68,new ge(4,174))),new oe(21,104,104,24,24,new xe(56,new ge(6,136))),new oe(22,120,120,18,18,new xe(68,new ge(6,175))),new oe(23,132,132,20,20,new xe(62,new ge(8,163))),new oe(24,144,144,22,22,new xe(62,new ge(8,156),new ge(2,155))),new oe(25,8,18,6,16,new xe(7,new ge(1,5))),new oe(26,8,32,6,14,new xe(11,new ge(1,10))),new oe(27,12,26,10,24,new xe(14,new ge(1,16))),new oe(28,12,36,10,16,new xe(18,new ge(1,22))),new oe(29,16,36,14,16,new xe(24,new ge(1,32))),new oe(30,16,48,14,22,new xe(28,new ge(1,49)))]}}oe.VERSIONS=oe.buildVersions();class cr{constructor(e){const t=e.getHeight();if(t<8||t>144||(t&1)!==0)throw new V;this.version=cr.readVersion(e),this.mappingBitMatrix=this.extractDataRegion(e),this.readMappingMatrix=new He(this.mappingBitMatrix.getWidth(),this.mappingBitMatrix.getHeight())}getVersion(){return this.version}static readVersion(e){const t=e.getHeight(),n=e.getWidth();return oe.getVersionForDimensions(t,n)}readCodewords(){const e=new Int8Array(this.version.getTotalCodewords());let t=0,n=4,r=0;const i=this.mappingBitMatrix.getHeight(),s=this.mappingBitMatrix.getWidth();let o=!1,a=!1,l=!1,f=!1;do if(n===i&&r===0&&!o)e[t++]=this.readCorner1(i,s)&255,n-=2,r+=2,o=!0;else if(n===i-2&&r===0&&(s&3)!==0&&!a)e[t++]=this.readCorner2(i,s)&255,n-=2,r+=2,a=!0;else if(n===i+4&&r===2&&(s&7)===0&&!l)e[t++]=this.readCorner3(i,s)&255,n-=2,r+=2,l=!0;else if(n===i-2&&r===0&&(s&7)===4&&!f)e[t++]=this.readCorner4(i,s)&255,n-=2,r+=2,f=!0;else{do n<i&&r>=0&&!this.readMappingMatrix.get(r,n)&&(e[t++]=this.readUtah(n,r,i,s)&255),n-=2,r+=2;while(n>=0&&r<s);n+=1,r+=3;do n>=0&&r<s&&!this.readMappingMatrix.get(r,n)&&(e[t++]=this.readUtah(n,r,i,s)&255),n+=2,r-=2;while(n<i&&r>=0);n+=3,r+=1}while(n<i||r<s);if(t!==this.version.getTotalCodewords())throw new V;return e}readModule(e,t,n,r){return e<0&&(e+=n,t+=4-(n+4&7)),t<0&&(t+=r,e+=4-(r+4&7)),this.readMappingMatrix.set(t,e),this.mappingBitMatrix.get(t,e)}readUtah(e,t,n,r){let i=0;return this.readModule(e-2,t-2,n,r)&&(i|=1),i<<=1,this.readModule(e-2,t-1,n,r)&&(i|=1),i<<=1,this.readModule(e-1,t-2,n,r)&&(i|=1),i<<=1,this.readModule(e-1,t-1,n,r)&&(i|=1),i<<=1,this.readModule(e-1,t,n,r)&&(i|=1),i<<=1,this.readModule(e,t-2,n,r)&&(i|=1),i<<=1,this.readModule(e,t-1,n,r)&&(i|=1),i<<=1,this.readModule(e,t,n,r)&&(i|=1),i}readCorner1(e,t){let n=0;return this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,1,e,t)&&(n|=1),n<<=1,this.readModule(e-1,2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n<<=1,this.readModule(2,t-1,e,t)&&(n|=1),n<<=1,this.readModule(3,t-1,e,t)&&(n|=1),n}readCorner2(e,t){let n=0;return this.readModule(e-3,0,e,t)&&(n|=1),n<<=1,this.readModule(e-2,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(0,t-4,e,t)&&(n|=1),n<<=1,this.readModule(0,t-3,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n}readCorner3(e,t){let n=0;return this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,t-1,e,t)&&(n|=1),n<<=1,this.readModule(0,t-3,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-3,e,t)&&(n|=1),n<<=1,this.readModule(1,t-2,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n}readCorner4(e,t){let n=0;return this.readModule(e-3,0,e,t)&&(n|=1),n<<=1,this.readModule(e-2,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n<<=1,this.readModule(2,t-1,e,t)&&(n|=1),n<<=1,this.readModule(3,t-1,e,t)&&(n|=1),n}extractDataRegion(e){const t=this.version.getSymbolSizeRows(),n=this.version.getSymbolSizeColumns();if(e.getHeight()!==t)throw new M("Dimension of bitMatrix must match the version size");const r=this.version.getDataRegionSizeRows(),i=this.version.getDataRegionSizeColumns(),s=t/r|0,o=n/i|0,a=s*r,l=o*i,f=new He(l,a);for(let x=0;x<s;++x){const C=x*r;for(let E=0;E<o;++E){const I=E*i;for(let y=0;y<r;++y){const b=x*(r+2)+1+y,D=C+y;for(let F=0;F<i;++F){const k=E*(i+2)+1+F;if(e.get(k,b)){const B=I+F;f.set(B,D)}}}}}return f}}class ur{constructor(e,t){this.numDataCodewords=e,this.codewords=t}static getDataBlocks(e,t){const n=t.getECBlocks();let r=0;const i=n.getECBlocks();for(let y of i)r+=y.getCount();const s=new Array(r);let o=0;for(let y of i)for(let b=0;b<y.getCount();b++){const D=y.getDataCodewords(),F=n.getECCodewords()+D;s[o++]=new ur(D,new Uint8Array(F))}const l=s[0].codewords.length-n.getECCodewords(),f=l-1;let x=0;for(let y=0;y<f;y++)for(let b=0;b<o;b++)s[b].codewords[y]=e[x++];const C=t.getVersionNumber()===24,E=C?8:o;for(let y=0;y<E;y++)s[y].codewords[l-1]=e[x++];const I=s[0].codewords.length;for(let y=l;y<I;y++)for(let b=0;b<o;b++){const D=C?(b+8)%o:b,F=C&&D>7?y-1:y;s[D].codewords[F]=e[x++]}if(x!==e.length)throw new M;return s}getNumDataCodewords(){return this.numDataCodewords}getCodewords(){return this.codewords}}class hr{constructor(e){this.bytes=e,this.byteOffset=0,this.bitOffset=0}getBitOffset(){return this.bitOffset}getByteOffset(){return this.byteOffset}readBits(e){if(e<1||e>32||e>this.available())throw new M(""+e);let t=0,n=this.bitOffset,r=this.byteOffset;const i=this.bytes;if(n>0){const s=8-n,o=e<s?e:s,a=s-o,l=255>>8-o<<a;t=(i[r]&l)>>a,e-=o,n+=o,n===8&&(n=0,r++)}if(e>0){for(;e>=8;)t=t<<8|i[r]&255,r++,e-=8;if(e>0){const s=8-e,o=255>>s<<s;t=t<<e|(i[r]&o)>>s,n+=e}}return this.bitOffset=n,this.byteOffset=r,t}available(){return 8*(this.bytes.length-this.byteOffset)-this.bitOffset}}var Ue;(function(g){g[g.PAD_ENCODE=0]="PAD_ENCODE",g[g.ASCII_ENCODE=1]="ASCII_ENCODE",g[g.C40_ENCODE=2]="C40_ENCODE",g[g.TEXT_ENCODE=3]="TEXT_ENCODE",g[g.ANSIX12_ENCODE=4]="ANSIX12_ENCODE",g[g.EDIFACT_ENCODE=5]="EDIFACT_ENCODE",g[g.BASE256_ENCODE=6]="BASE256_ENCODE"})(Ue||(Ue={}));class zt{static decode(e){const t=new hr(e),n=new me,r=new me,i=new Array;let s=Ue.ASCII_ENCODE;do if(s===Ue.ASCII_ENCODE)s=this.decodeAsciiSegment(t,n,r);else{switch(s){case Ue.C40_ENCODE:this.decodeC40Segment(t,n);break;case Ue.TEXT_ENCODE:this.decodeTextSegment(t,n);break;case Ue.ANSIX12_ENCODE:this.decodeAnsiX12Segment(t,n);break;case Ue.EDIFACT_ENCODE:this.decodeEdifactSegment(t,n);break;case Ue.BASE256_ENCODE:this.decodeBase256Segment(t,n,i);break;default:throw new V}s=Ue.ASCII_ENCODE}while(s!==Ue.PAD_ENCODE&&t.available()>0);return r.length()>0&&n.append(r.toString()),new mn(e,n.toString(),i.length===0?null:i,null)}static decodeAsciiSegment(e,t,n){let r=!1;do{let i=e.readBits(8);if(i===0)throw new V;if(i<=128)return r&&(i+=128),t.append(String.fromCharCode(i-1)),Ue.ASCII_ENCODE;if(i===129)return Ue.PAD_ENCODE;if(i<=229){const s=i-130;s<10&&t.append("0"),t.append(""+s)}else switch(i){case 230:return Ue.C40_ENCODE;case 231:return Ue.BASE256_ENCODE;case 232:t.append(String.fromCharCode(29));break;case 233:case 234:break;case 235:r=!0;break;case 236:t.append("[)>05"),n.insert(0,"");break;case 237:t.append("[)>06"),n.insert(0,"");break;case 238:return Ue.ANSIX12_ENCODE;case 239:return Ue.TEXT_ENCODE;case 240:return Ue.EDIFACT_ENCODE;case 241:break;default:if(i!==254||e.available()!==0)throw new V;break}}while(e.available()>0);return Ue.ASCII_ENCODE}static decodeC40Segment(e,t){let n=!1;const r=[];let i=0;do{if(e.available()===8)return;const s=e.readBits(8);if(s===254)return;this.parseTwoBytes(s,e.readBits(8),r);for(let o=0;o<3;o++){const a=r[o];switch(i){case 0:if(a<3)i=a+1;else if(a<this.C40_BASIC_SET_CHARS.length){const l=this.C40_BASIC_SET_CHARS[a];n?(t.append(String.fromCharCode(l.charCodeAt(0)+128)),n=!1):t.append(l)}else throw new V;break;case 1:n?(t.append(String.fromCharCode(a+128)),n=!1):t.append(String.fromCharCode(a)),i=0;break;case 2:if(a<this.C40_SHIFT2_SET_CHARS.length){const l=this.C40_SHIFT2_SET_CHARS[a];n?(t.append(String.fromCharCode(l.charCodeAt(0)+128)),n=!1):t.append(l)}else switch(a){case 27:t.append(String.fromCharCode(29));break;case 30:n=!0;break;default:throw new V}i=0;break;case 3:n?(t.append(String.fromCharCode(a+224)),n=!1):t.append(String.fromCharCode(a+96)),i=0;break;default:throw new V}}}while(e.available()>0)}static decodeTextSegment(e,t){let n=!1,r=[],i=0;do{if(e.available()===8)return;const s=e.readBits(8);if(s===254)return;this.parseTwoBytes(s,e.readBits(8),r);for(let o=0;o<3;o++){const a=r[o];switch(i){case 0:if(a<3)i=a+1;else if(a<this.TEXT_BASIC_SET_CHARS.length){const l=this.TEXT_BASIC_SET_CHARS[a];n?(t.append(String.fromCharCode(l.charCodeAt(0)+128)),n=!1):t.append(l)}else throw new V;break;case 1:n?(t.append(String.fromCharCode(a+128)),n=!1):t.append(String.fromCharCode(a)),i=0;break;case 2:if(a<this.TEXT_SHIFT2_SET_CHARS.length){const l=this.TEXT_SHIFT2_SET_CHARS[a];n?(t.append(String.fromCharCode(l.charCodeAt(0)+128)),n=!1):t.append(l)}else switch(a){case 27:t.append(String.fromCharCode(29));break;case 30:n=!0;break;default:throw new V}i=0;break;case 3:if(a<this.TEXT_SHIFT3_SET_CHARS.length){const l=this.TEXT_SHIFT3_SET_CHARS[a];n?(t.append(String.fromCharCode(l.charCodeAt(0)+128)),n=!1):t.append(l),i=0}else throw new V;break;default:throw new V}}}while(e.available()>0)}static decodeAnsiX12Segment(e,t){const n=[];do{if(e.available()===8)return;const r=e.readBits(8);if(r===254)return;this.parseTwoBytes(r,e.readBits(8),n);for(let i=0;i<3;i++){const s=n[i];switch(s){case 0:t.append("\r");break;case 1:t.append("*");break;case 2:t.append(">");break;case 3:t.append(" ");break;default:if(s<14)t.append(String.fromCharCode(s+44));else if(s<40)t.append(String.fromCharCode(s+51));else throw new V;break}}}while(e.available()>0)}static parseTwoBytes(e,t,n){let r=(e<<8)+t-1,i=Math.floor(r/1600);n[0]=i,r-=i*1600,i=Math.floor(r/40),n[1]=i,n[2]=r-i*40}static decodeEdifactSegment(e,t){do{if(e.available()<=16)return;for(let n=0;n<4;n++){let r=e.readBits(6);if(r===31){const i=8-e.getBitOffset();i!==8&&e.readBits(i);return}(r&32)===0&&(r|=64),t.append(String.fromCharCode(r))}}while(e.available()>0)}static decodeBase256Segment(e,t,n){let r=1+e.getByteOffset();const i=this.unrandomize255State(e.readBits(8),r++);let s;if(i===0?s=e.available()/8|0:i<250?s=i:s=250*(i-249)+this.unrandomize255State(e.readBits(8),r++),s<0)throw new V;const o=new Uint8Array(s);for(let a=0;a<s;a++){if(e.available()<8)throw new V;o[a]=this.unrandomize255State(e.readBits(8),r++)}n.push(o);try{t.append(st.decode(o,$.ISO88591))}catch(a){throw new St("Platform does not support required encoding: "+a.message)}}static unrandomize255State(e,t){const n=149*t%255+1,r=e-n;return r>=0?r:r+256}}zt.C40_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],zt.C40_SHIFT2_SET_CHARS=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_"],zt.TEXT_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],zt.TEXT_SHIFT2_SET_CHARS=zt.C40_SHIFT2_SET_CHARS,zt.TEXT_SHIFT3_SET_CHARS=["`","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","{","|","}","~",String.fromCharCode(127)];class Di{constructor(){this.rsDecoder=new En(fe.DATA_MATRIX_FIELD_256)}decode(e){const t=new cr(e),n=t.getVersion(),r=t.readCodewords(),i=ur.getDataBlocks(r,n);let s=0;for(let l of i)s+=l.getNumDataCodewords();const o=new Uint8Array(s),a=i.length;for(let l=0;l<a;l++){const f=i[l],x=f.getCodewords(),C=f.getNumDataCodewords();this.correctErrors(x,C);for(let E=0;E<C;E++)o[E*a+l]=x[E]}return zt.decode(o)}correctErrors(e,t){const n=new Int32Array(e);try{this.rsDecoder.decode(n,e.length-t)}catch{throw new P}for(let r=0;r<t;r++)e[r]=n[r]}}class Pe{constructor(e){this.image=e,this.rectangleDetector=new vt(this.image)}detect(){const e=this.rectangleDetector.detect();let t=this.detectSolid1(e);if(t=this.detectSolid2(t),t[3]=this.correctTopRight(t),!t[3])throw new v;t=this.shiftToModuleCenter(t);const n=t[0],r=t[1],i=t[2],s=t[3];let o=this.transitionsBetween(n,s)+1,a=this.transitionsBetween(i,s)+1;(o&1)===1&&(o+=1),(a&1)===1&&(a+=1),4*o<7*a&&4*a<7*o&&(o=a=Math.max(o,a));let l=Pe.sampleGrid(this.image,n,r,i,s,o,a);return new Fn(l,[n,r,i,s])}static shiftPoint(e,t,n){let r=(t.getX()-e.getX())/(n+1),i=(t.getY()-e.getY())/(n+1);return new G(e.getX()+r,e.getY()+i)}static moveAway(e,t,n){let r=e.getX(),i=e.getY();return r<t?r-=1:r+=1,i<n?i-=1:i+=1,new G(r,i)}detectSolid1(e){let t=e[0],n=e[1],r=e[3],i=e[2],s=this.transitionsBetween(t,n),o=this.transitionsBetween(n,r),a=this.transitionsBetween(r,i),l=this.transitionsBetween(i,t),f=s,x=[i,t,n,r];return f>o&&(f=o,x[0]=t,x[1]=n,x[2]=r,x[3]=i),f>a&&(f=a,x[0]=n,x[1]=r,x[2]=i,x[3]=t),f>l&&(x[0]=r,x[1]=i,x[2]=t,x[3]=n),x}detectSolid2(e){let t=e[0],n=e[1],r=e[2],i=e[3],s=this.transitionsBetween(t,i),o=Pe.shiftPoint(n,r,(s+1)*4),a=Pe.shiftPoint(r,n,(s+1)*4),l=this.transitionsBetween(o,t),f=this.transitionsBetween(a,i);return l<f?(e[0]=t,e[1]=n,e[2]=r,e[3]=i):(e[0]=n,e[1]=r,e[2]=i,e[3]=t),e}correctTopRight(e){let t=e[0],n=e[1],r=e[2],i=e[3],s=this.transitionsBetween(t,i),o=this.transitionsBetween(n,i),a=Pe.shiftPoint(t,n,(o+1)*4),l=Pe.shiftPoint(r,n,(s+1)*4);s=this.transitionsBetween(a,i),o=this.transitionsBetween(l,i);let f=new G(i.getX()+(r.getX()-n.getX())/(s+1),i.getY()+(r.getY()-n.getY())/(s+1)),x=new G(i.getX()+(t.getX()-n.getX())/(o+1),i.getY()+(t.getY()-n.getY())/(o+1));if(!this.isValid(f))return this.isValid(x)?x:null;if(!this.isValid(x))return f;let C=this.transitionsBetween(a,f)+this.transitionsBetween(l,f),E=this.transitionsBetween(a,x)+this.transitionsBetween(l,x);return C>E?f:x}shiftToModuleCenter(e){let t=e[0],n=e[1],r=e[2],i=e[3],s=this.transitionsBetween(t,i)+1,o=this.transitionsBetween(r,i)+1,a=Pe.shiftPoint(t,n,o*4),l=Pe.shiftPoint(r,n,s*4);s=this.transitionsBetween(a,i)+1,o=this.transitionsBetween(l,i)+1,(s&1)===1&&(s+=1),(o&1)===1&&(o+=1);let f=(t.getX()+n.getX()+r.getX()+i.getX())/4,x=(t.getY()+n.getY()+r.getY()+i.getY())/4;t=Pe.moveAway(t,f,x),n=Pe.moveAway(n,f,x),r=Pe.moveAway(r,f,x),i=Pe.moveAway(i,f,x);let C,E;return a=Pe.shiftPoint(t,n,o*4),a=Pe.shiftPoint(a,i,s*4),C=Pe.shiftPoint(n,t,o*4),C=Pe.shiftPoint(C,r,s*4),l=Pe.shiftPoint(r,i,o*4),l=Pe.shiftPoint(l,n,s*4),E=Pe.shiftPoint(i,r,o*4),E=Pe.shiftPoint(E,t,s*4),[a,C,l,E]}isValid(e){return e.getX()>=0&&e.getX()<this.image.getWidth()&&e.getY()>0&&e.getY()<this.image.getHeight()}static sampleGrid(e,t,n,r,i,s,o){return Vt.getInstance().sampleGrid(e,s,o,.5,.5,s-.5,.5,s-.5,o-.5,.5,o-.5,t.getX(),t.getY(),i.getX(),i.getY(),r.getX(),r.getY(),n.getX(),n.getY())}transitionsBetween(e,t){let n=Math.trunc(e.getX()),r=Math.trunc(e.getY()),i=Math.trunc(t.getX()),s=Math.trunc(t.getY()),o=Math.abs(s-r)>Math.abs(i-n);if(o){let y=n;n=r,r=y,y=i,i=s,s=y}let a=Math.abs(i-n),l=Math.abs(s-r),f=-a/2,x=r<s?1:-1,C=n<i?1:-1,E=0,I=this.image.get(o?r:n,o?n:r);for(let y=n,b=r;y!==i;y+=C){let D=this.image.get(o?b:y,o?y:b);if(D!==I&&(E++,I=D),f+=l,f>0){if(b===s)break;b+=x,f-=a}}return E}}class jt{constructor(){this.decoder=new Di}decode(e,t=null){let n,r;if(t!=null&&t.has(Ce.PURE_BARCODE)){const l=jt.extractPureBits(e.getBlackMatrix());n=this.decoder.decode(l),r=jt.NO_POINTS}else{const l=new Pe(e.getBlackMatrix()).detect();n=this.decoder.decode(l.getBits()),r=l.getPoints()}const i=n.getRawBytes(),s=new tt(n.getText(),i,8*i.length,r,Q.DATA_MATRIX,W.currentTimeMillis()),o=n.getByteSegments();o!=null&&s.putMetadata(je.BYTE_SEGMENTS,o);const a=n.getECLevel();return a!=null&&s.putMetadata(je.ERROR_CORRECTION_LEVEL,a),s}reset(){}static extractPureBits(e){const t=e.getTopLeftOnBit(),n=e.getBottomRightOnBit();if(t==null||n==null)throw new v;const r=this.moduleSize(t,e);let i=t[1];const s=n[1];let o=t[0];const l=(n[0]-o+1)/r,f=(s-i+1)/r;if(l<=0||f<=0)throw new v;const x=r/2;i+=x,o+=x;const C=new He(l,f);for(let E=0;E<f;E++){const I=i+E*r;for(let y=0;y<l;y++)e.get(o+y*r,I)&&C.set(y,E)}return C}static moduleSize(e,t){const n=t.getWidth();let r=e[0];const i=e[1];for(;r<n&&t.get(r,i);)r++;if(r===n)throw new v;const s=r-e[0];if(s===0)throw new v;return s}}jt.NO_POINTS=[];class Ri extends Qt{constructor(e=500){super(new jt,e)}}var un;(function(g){g[g.L=0]="L",g[g.M=1]="M",g[g.Q=2]="Q",g[g.H=3]="H"})(un||(un={}));class Ne{constructor(e,t,n){this.value=e,this.stringValue=t,this.bits=n,Ne.FOR_BITS.set(n,this),Ne.FOR_VALUE.set(e,this)}getValue(){return this.value}getBits(){return this.bits}static fromString(e){switch(e){case"L":return Ne.L;case"M":return Ne.M;case"Q":return Ne.Q;case"H":return Ne.H;default:throw new S(e+"not available")}}toString(){return this.stringValue}equals(e){if(!(e instanceof Ne))return!1;const t=e;return this.value===t.value}static forBits(e){if(e<0||e>=Ne.FOR_BITS.size)throw new M;return Ne.FOR_BITS.get(e)}}Ne.FOR_BITS=new Map,Ne.FOR_VALUE=new Map,Ne.L=new Ne(un.L,"L",1),Ne.M=new Ne(un.M,"M",0),Ne.Q=new Ne(un.Q,"Q",3),Ne.H=new Ne(un.H,"H",2);class $e{constructor(e){this.errorCorrectionLevel=Ne.forBits(e>>3&3),this.dataMask=e&7}static numBitsDiffering(e,t){return J.bitCount(e^t)}static decodeFormatInformation(e,t){const n=$e.doDecodeFormatInformation(e,t);return n!==null?n:$e.doDecodeFormatInformation(e^$e.FORMAT_INFO_MASK_QR,t^$e.FORMAT_INFO_MASK_QR)}static doDecodeFormatInformation(e,t){let n=Number.MAX_SAFE_INTEGER,r=0;for(const i of $e.FORMAT_INFO_DECODE_LOOKUP){const s=i[0];if(s===e||s===t)return new $e(i[1]);let o=$e.numBitsDiffering(e,s);o<n&&(r=i[1],n=o),e!==t&&(o=$e.numBitsDiffering(t,s),o<n&&(r=i[1],n=o))}return n<=3?new $e(r):null}getErrorCorrectionLevel(){return this.errorCorrectionLevel}getDataMask(){return this.dataMask}hashCode(){return this.errorCorrectionLevel.getBits()<<3|this.dataMask}equals(e){if(!(e instanceof $e))return!1;const t=e;return this.errorCorrectionLevel===t.errorCorrectionLevel&&this.dataMask===t.dataMask}}$e.FORMAT_INFO_MASK_QR=21522,$e.FORMAT_INFO_DECODE_LOOKUP=[Int32Array.from([21522,0]),Int32Array.from([20773,1]),Int32Array.from([24188,2]),Int32Array.from([23371,3]),Int32Array.from([17913,4]),Int32Array.from([16590,5]),Int32Array.from([20375,6]),Int32Array.from([19104,7]),Int32Array.from([30660,8]),Int32Array.from([29427,9]),Int32Array.from([32170,10]),Int32Array.from([30877,11]),Int32Array.from([26159,12]),Int32Array.from([25368,13]),Int32Array.from([27713,14]),Int32Array.from([26998,15]),Int32Array.from([5769,16]),Int32Array.from([5054,17]),Int32Array.from([7399,18]),Int32Array.from([6608,19]),Int32Array.from([1890,20]),Int32Array.from([597,21]),Int32Array.from([3340,22]),Int32Array.from([2107,23]),Int32Array.from([13663,24]),Int32Array.from([12392,25]),Int32Array.from([16177,26]),Int32Array.from([14854,27]),Int32Array.from([9396,28]),Int32Array.from([8579,29]),Int32Array.from([11994,30]),Int32Array.from([11245,31])];class N{constructor(e,...t){this.ecCodewordsPerBlock=e,this.ecBlocks=t}getECCodewordsPerBlock(){return this.ecCodewordsPerBlock}getNumBlocks(){let e=0;const t=this.ecBlocks;for(const n of t)e+=n.getCount();return e}getTotalECCodewords(){return this.ecCodewordsPerBlock*this.getNumBlocks()}getECBlocks(){return this.ecBlocks}}class p{constructor(e,t){this.count=e,this.dataCodewords=t}getCount(){return this.count}getDataCodewords(){return this.dataCodewords}}class Y{constructor(e,t,...n){this.versionNumber=e,this.alignmentPatternCenters=t,this.ecBlocks=n;let r=0;const i=n[0].getECCodewordsPerBlock(),s=n[0].getECBlocks();for(const o of s)r+=o.getCount()*(o.getDataCodewords()+i);this.totalCodewords=r}getVersionNumber(){return this.versionNumber}getAlignmentPatternCenters(){return this.alignmentPatternCenters}getTotalCodewords(){return this.totalCodewords}getDimensionForVersion(){return 17+4*this.versionNumber}getECBlocksForLevel(e){return this.ecBlocks[e.getValue()]}static getProvisionalVersionForDimension(e){if(e%4!==1)throw new V;try{return this.getVersionForNumber((e-17)/4)}catch{throw new V}}static getVersionForNumber(e){if(e<1||e>40)throw new M;return Y.VERSIONS[e-1]}static decodeVersionInformation(e){let t=Number.MAX_SAFE_INTEGER,n=0;for(let r=0;r<Y.VERSION_DECODE_INFO.length;r++){const i=Y.VERSION_DECODE_INFO[r];if(i===e)return Y.getVersionForNumber(r+7);const s=$e.numBitsDiffering(e,i);s<t&&(n=r+7,t=s)}return t<=3?Y.getVersionForNumber(n):null}buildFunctionPattern(){const e=this.getDimensionForVersion(),t=new He(e);t.setRegion(0,0,9,9),t.setRegion(e-8,0,8,9),t.setRegion(0,e-8,9,8);const n=this.alignmentPatternCenters.length;for(let r=0;r<n;r++){const i=this.alignmentPatternCenters[r]-2;for(let s=0;s<n;s++)r===0&&(s===0||s===n-1)||r===n-1&&s===0||t.setRegion(this.alignmentPatternCenters[s]-2,i,5,5)}return t.setRegion(6,9,1,e-17),t.setRegion(9,6,e-17,1),this.versionNumber>6&&(t.setRegion(e-11,0,3,6),t.setRegion(0,e-11,6,3)),t}toString(){return""+this.versionNumber}}Y.VERSION_DECODE_INFO=Int32Array.from([31892,34236,39577,42195,48118,51042,55367,58893,63784,68472,70749,76311,79154,84390,87683,92361,96236,102084,102881,110507,110734,117786,119615,126325,127568,133589,136944,141498,145311,150283,152622,158308,161089,167017]),Y.VERSIONS=[new Y(1,new Int32Array(0),new N(7,new p(1,19)),new N(10,new p(1,16)),new N(13,new p(1,13)),new N(17,new p(1,9))),new Y(2,Int32Array.from([6,18]),new N(10,new p(1,34)),new N(16,new p(1,28)),new N(22,new p(1,22)),new N(28,new p(1,16))),new Y(3,Int32Array.from([6,22]),new N(15,new p(1,55)),new N(26,new p(1,44)),new N(18,new p(2,17)),new N(22,new p(2,13))),new Y(4,Int32Array.from([6,26]),new N(20,new p(1,80)),new N(18,new p(2,32)),new N(26,new p(2,24)),new N(16,new p(4,9))),new Y(5,Int32Array.from([6,30]),new N(26,new p(1,108)),new N(24,new p(2,43)),new N(18,new p(2,15),new p(2,16)),new N(22,new p(2,11),new p(2,12))),new Y(6,Int32Array.from([6,34]),new N(18,new p(2,68)),new N(16,new p(4,27)),new N(24,new p(4,19)),new N(28,new p(4,15))),new Y(7,Int32Array.from([6,22,38]),new N(20,new p(2,78)),new N(18,new p(4,31)),new N(18,new p(2,14),new p(4,15)),new N(26,new p(4,13),new p(1,14))),new Y(8,Int32Array.from([6,24,42]),new N(24,new p(2,97)),new N(22,new p(2,38),new p(2,39)),new N(22,new p(4,18),new p(2,19)),new N(26,new p(4,14),new p(2,15))),new Y(9,Int32Array.from([6,26,46]),new N(30,new p(2,116)),new N(22,new p(3,36),new p(2,37)),new N(20,new p(4,16),new p(4,17)),new N(24,new p(4,12),new p(4,13))),new Y(10,Int32Array.from([6,28,50]),new N(18,new p(2,68),new p(2,69)),new N(26,new p(4,43),new p(1,44)),new N(24,new p(6,19),new p(2,20)),new N(28,new p(6,15),new p(2,16))),new Y(11,Int32Array.from([6,30,54]),new N(20,new p(4,81)),new N(30,new p(1,50),new p(4,51)),new N(28,new p(4,22),new p(4,23)),new N(24,new p(3,12),new p(8,13))),new Y(12,Int32Array.from([6,32,58]),new N(24,new p(2,92),new p(2,93)),new N(22,new p(6,36),new p(2,37)),new N(26,new p(4,20),new p(6,21)),new N(28,new p(7,14),new p(4,15))),new Y(13,Int32Array.from([6,34,62]),new N(26,new p(4,107)),new N(22,new p(8,37),new p(1,38)),new N(24,new p(8,20),new p(4,21)),new N(22,new p(12,11),new p(4,12))),new Y(14,Int32Array.from([6,26,46,66]),new N(30,new p(3,115),new p(1,116)),new N(24,new p(4,40),new p(5,41)),new N(20,new p(11,16),new p(5,17)),new N(24,new p(11,12),new p(5,13))),new Y(15,Int32Array.from([6,26,48,70]),new N(22,new p(5,87),new p(1,88)),new N(24,new p(5,41),new p(5,42)),new N(30,new p(5,24),new p(7,25)),new N(24,new p(11,12),new p(7,13))),new Y(16,Int32Array.from([6,26,50,74]),new N(24,new p(5,98),new p(1,99)),new N(28,new p(7,45),new p(3,46)),new N(24,new p(15,19),new p(2,20)),new N(30,new p(3,15),new p(13,16))),new Y(17,Int32Array.from([6,30,54,78]),new N(28,new p(1,107),new p(5,108)),new N(28,new p(10,46),new p(1,47)),new N(28,new p(1,22),new p(15,23)),new N(28,new p(2,14),new p(17,15))),new Y(18,Int32Array.from([6,30,56,82]),new N(30,new p(5,120),new p(1,121)),new N(26,new p(9,43),new p(4,44)),new N(28,new p(17,22),new p(1,23)),new N(28,new p(2,14),new p(19,15))),new Y(19,Int32Array.from([6,30,58,86]),new N(28,new p(3,113),new p(4,114)),new N(26,new p(3,44),new p(11,45)),new N(26,new p(17,21),new p(4,22)),new N(26,new p(9,13),new p(16,14))),new Y(20,Int32Array.from([6,34,62,90]),new N(28,new p(3,107),new p(5,108)),new N(26,new p(3,41),new p(13,42)),new N(30,new p(15,24),new p(5,25)),new N(28,new p(15,15),new p(10,16))),new Y(21,Int32Array.from([6,28,50,72,94]),new N(28,new p(4,116),new p(4,117)),new N(26,new p(17,42)),new N(28,new p(17,22),new p(6,23)),new N(30,new p(19,16),new p(6,17))),new Y(22,Int32Array.from([6,26,50,74,98]),new N(28,new p(2,111),new p(7,112)),new N(28,new p(17,46)),new N(30,new p(7,24),new p(16,25)),new N(24,new p(34,13))),new Y(23,Int32Array.from([6,30,54,78,102]),new N(30,new p(4,121),new p(5,122)),new N(28,new p(4,47),new p(14,48)),new N(30,new p(11,24),new p(14,25)),new N(30,new p(16,15),new p(14,16))),new Y(24,Int32Array.from([6,28,54,80,106]),new N(30,new p(6,117),new p(4,118)),new N(28,new p(6,45),new p(14,46)),new N(30,new p(11,24),new p(16,25)),new N(30,new p(30,16),new p(2,17))),new Y(25,Int32Array.from([6,32,58,84,110]),new N(26,new p(8,106),new p(4,107)),new N(28,new p(8,47),new p(13,48)),new N(30,new p(7,24),new p(22,25)),new N(30,new p(22,15),new p(13,16))),new Y(26,Int32Array.from([6,30,58,86,114]),new N(28,new p(10,114),new p(2,115)),new N(28,new p(19,46),new p(4,47)),new N(28,new p(28,22),new p(6,23)),new N(30,new p(33,16),new p(4,17))),new Y(27,Int32Array.from([6,34,62,90,118]),new N(30,new p(8,122),new p(4,123)),new N(28,new p(22,45),new p(3,46)),new N(30,new p(8,23),new p(26,24)),new N(30,new p(12,15),new p(28,16))),new Y(28,Int32Array.from([6,26,50,74,98,122]),new N(30,new p(3,117),new p(10,118)),new N(28,new p(3,45),new p(23,46)),new N(30,new p(4,24),new p(31,25)),new N(30,new p(11,15),new p(31,16))),new Y(29,Int32Array.from([6,30,54,78,102,126]),new N(30,new p(7,116),new p(7,117)),new N(28,new p(21,45),new p(7,46)),new N(30,new p(1,23),new p(37,24)),new N(30,new p(19,15),new p(26,16))),new Y(30,Int32Array.from([6,26,52,78,104,130]),new N(30,new p(5,115),new p(10,116)),new N(28,new p(19,47),new p(10,48)),new N(30,new p(15,24),new p(25,25)),new N(30,new p(23,15),new p(25,16))),new Y(31,Int32Array.from([6,30,56,82,108,134]),new N(30,new p(13,115),new p(3,116)),new N(28,new p(2,46),new p(29,47)),new N(30,new p(42,24),new p(1,25)),new N(30,new p(23,15),new p(28,16))),new Y(32,Int32Array.from([6,34,60,86,112,138]),new N(30,new p(17,115)),new N(28,new p(10,46),new p(23,47)),new N(30,new p(10,24),new p(35,25)),new N(30,new p(19,15),new p(35,16))),new Y(33,Int32Array.from([6,30,58,86,114,142]),new N(30,new p(17,115),new p(1,116)),new N(28,new p(14,46),new p(21,47)),new N(30,new p(29,24),new p(19,25)),new N(30,new p(11,15),new p(46,16))),new Y(34,Int32Array.from([6,34,62,90,118,146]),new N(30,new p(13,115),new p(6,116)),new N(28,new p(14,46),new p(23,47)),new N(30,new p(44,24),new p(7,25)),new N(30,new p(59,16),new p(1,17))),new Y(35,Int32Array.from([6,30,54,78,102,126,150]),new N(30,new p(12,121),new p(7,122)),new N(28,new p(12,47),new p(26,48)),new N(30,new p(39,24),new p(14,25)),new N(30,new p(22,15),new p(41,16))),new Y(36,Int32Array.from([6,24,50,76,102,128,154]),new N(30,new p(6,121),new p(14,122)),new N(28,new p(6,47),new p(34,48)),new N(30,new p(46,24),new p(10,25)),new N(30,new p(2,15),new p(64,16))),new Y(37,Int32Array.from([6,28,54,80,106,132,158]),new N(30,new p(17,122),new p(4,123)),new N(28,new p(29,46),new p(14,47)),new N(30,new p(49,24),new p(10,25)),new N(30,new p(24,15),new p(46,16))),new Y(38,Int32Array.from([6,32,58,84,110,136,162]),new N(30,new p(4,122),new p(18,123)),new N(28,new p(13,46),new p(32,47)),new N(30,new p(48,24),new p(14,25)),new N(30,new p(42,15),new p(32,16))),new Y(39,Int32Array.from([6,26,54,82,110,138,166]),new N(30,new p(20,117),new p(4,118)),new N(28,new p(40,47),new p(7,48)),new N(30,new p(43,24),new p(22,25)),new N(30,new p(10,15),new p(67,16))),new Y(40,Int32Array.from([6,30,58,86,114,142,170]),new N(30,new p(19,118),new p(6,119)),new N(28,new p(18,47),new p(31,48)),new N(30,new p(34,24),new p(34,25)),new N(30,new p(20,15),new p(61,16)))];var Ge;(function(g){g[g.DATA_MASK_000=0]="DATA_MASK_000",g[g.DATA_MASK_001=1]="DATA_MASK_001",g[g.DATA_MASK_010=2]="DATA_MASK_010",g[g.DATA_MASK_011=3]="DATA_MASK_011",g[g.DATA_MASK_100=4]="DATA_MASK_100",g[g.DATA_MASK_101=5]="DATA_MASK_101",g[g.DATA_MASK_110=6]="DATA_MASK_110",g[g.DATA_MASK_111=7]="DATA_MASK_111"})(Ge||(Ge={}));class At{constructor(e,t){this.value=e,this.isMasked=t}unmaskBitMatrix(e,t){for(let n=0;n<t;n++)for(let r=0;r<t;r++)this.isMasked(n,r)&&e.flip(r,n)}}At.values=new Map([[Ge.DATA_MASK_000,new At(Ge.DATA_MASK_000,(g,e)=>(g+e&1)===0)],[Ge.DATA_MASK_001,new At(Ge.DATA_MASK_001,(g,e)=>(g&1)===0)],[Ge.DATA_MASK_010,new At(Ge.DATA_MASK_010,(g,e)=>e%3===0)],[Ge.DATA_MASK_011,new At(Ge.DATA_MASK_011,(g,e)=>(g+e)%3===0)],[Ge.DATA_MASK_100,new At(Ge.DATA_MASK_100,(g,e)=>(Math.floor(g/2)+Math.floor(e/3)&1)===0)],[Ge.DATA_MASK_101,new At(Ge.DATA_MASK_101,(g,e)=>g*e%6===0)],[Ge.DATA_MASK_110,new At(Ge.DATA_MASK_110,(g,e)=>g*e%6<3)],[Ge.DATA_MASK_111,new At(Ge.DATA_MASK_111,(g,e)=>(g+e+g*e%3&1)===0)]]);class vi{constructor(e){const t=e.getHeight();if(t<21||(t&3)!==1)throw new V;this.bitMatrix=e}readFormatInformation(){if(this.parsedFormatInfo!==null&&this.parsedFormatInfo!==void 0)return this.parsedFormatInfo;let e=0;for(let i=0;i<6;i++)e=this.copyBit(i,8,e);e=this.copyBit(7,8,e),e=this.copyBit(8,8,e),e=this.copyBit(8,7,e);for(let i=5;i>=0;i--)e=this.copyBit(8,i,e);const t=this.bitMatrix.getHeight();let n=0;const r=t-7;for(let i=t-1;i>=r;i--)n=this.copyBit(8,i,n);for(let i=t-8;i<t;i++)n=this.copyBit(i,8,n);if(this.parsedFormatInfo=$e.decodeFormatInformation(e,n),this.parsedFormatInfo!==null)return this.parsedFormatInfo;throw new V}readVersion(){if(this.parsedVersion!==null&&this.parsedVersion!==void 0)return this.parsedVersion;const e=this.bitMatrix.getHeight(),t=Math.floor((e-17)/4);if(t<=6)return Y.getVersionForNumber(t);let n=0;const r=e-11;for(let s=5;s>=0;s--)for(let o=e-9;o>=r;o--)n=this.copyBit(o,s,n);let i=Y.decodeVersionInformation(n);if(i!==null&&i.getDimensionForVersion()===e)return this.parsedVersion=i,i;n=0;for(let s=5;s>=0;s--)for(let o=e-9;o>=r;o--)n=this.copyBit(s,o,n);if(i=Y.decodeVersionInformation(n),i!==null&&i.getDimensionForVersion()===e)return this.parsedVersion=i,i;throw new V}copyBit(e,t,n){return(this.isMirror?this.bitMatrix.get(t,e):this.bitMatrix.get(e,t))?n<<1|1:n<<1}readCodewords(){const e=this.readFormatInformation(),t=this.readVersion(),n=At.values.get(e.getDataMask()),r=this.bitMatrix.getHeight();n.unmaskBitMatrix(this.bitMatrix,r);const i=t.buildFunctionPattern();let s=!0;const o=new Uint8Array(t.getTotalCodewords());let a=0,l=0,f=0;for(let x=r-1;x>0;x-=2){x===6&&x--;for(let C=0;C<r;C++){const E=s?r-1-C:C;for(let I=0;I<2;I++)i.get(x-I,E)||(f++,l<<=1,this.bitMatrix.get(x-I,E)&&(l|=1),f===8&&(o[a++]=l,f=0,l=0))}s=!s}if(a!==t.getTotalCodewords())throw new V;return o}remask(){if(this.parsedFormatInfo===null)return;const e=At.values[this.parsedFormatInfo.getDataMask()],t=this.bitMatrix.getHeight();e.unmaskBitMatrix(this.bitMatrix,t)}setMirror(e){this.parsedVersion=null,this.parsedFormatInfo=null,this.isMirror=e}mirror(){const e=this.bitMatrix;for(let t=0,n=e.getWidth();t<n;t++)for(let r=t+1,i=e.getHeight();r<i;r++)e.get(t,r)!==e.get(r,t)&&(e.flip(r,t),e.flip(t,r))}}class fr{constructor(e,t){this.numDataCodewords=e,this.codewords=t}static getDataBlocks(e,t,n){if(e.length!==t.getTotalCodewords())throw new M;const r=t.getECBlocksForLevel(n);let i=0;const s=r.getECBlocks();for(const I of s)i+=I.getCount();const o=new Array(i);let a=0;for(const I of s)for(let y=0;y<I.getCount();y++){const b=I.getDataCodewords(),D=r.getECCodewordsPerBlock()+b;o[a++]=new fr(b,new Uint8Array(D))}const l=o[0].codewords.length;let f=o.length-1;for(;f>=0&&o[f].codewords.length!==l;)f--;f++;const x=l-r.getECCodewordsPerBlock();let C=0;for(let I=0;I<x;I++)for(let y=0;y<a;y++)o[y].codewords[I]=e[C++];for(let I=f;I<a;I++)o[I].codewords[x]=e[C++];const E=o[0].codewords.length;for(let I=x;I<E;I++)for(let y=0;y<a;y++){const b=y<f?I:I+1;o[y].codewords[b]=e[C++]}return o}getNumDataCodewords(){return this.numDataCodewords}getCodewords(){return this.codewords}}var Ct;(function(g){g[g.TERMINATOR=0]="TERMINATOR",g[g.NUMERIC=1]="NUMERIC",g[g.ALPHANUMERIC=2]="ALPHANUMERIC",g[g.STRUCTURED_APPEND=3]="STRUCTURED_APPEND",g[g.BYTE=4]="BYTE",g[g.ECI=5]="ECI",g[g.KANJI=6]="KANJI",g[g.FNC1_FIRST_POSITION=7]="FNC1_FIRST_POSITION",g[g.FNC1_SECOND_POSITION=8]="FNC1_SECOND_POSITION",g[g.HANZI=9]="HANZI"})(Ct||(Ct={}));class Z{constructor(e,t,n,r){this.value=e,this.stringValue=t,this.characterCountBitsForVersions=n,this.bits=r,Z.FOR_BITS.set(r,this),Z.FOR_VALUE.set(e,this)}static forBits(e){const t=Z.FOR_BITS.get(e);if(t===void 0)throw new M;return t}getCharacterCountBits(e){const t=e.getVersionNumber();let n;return t<=9?n=0:t<=26?n=1:n=2,this.characterCountBitsForVersions[n]}getValue(){return this.value}getBits(){return this.bits}equals(e){if(!(e instanceof Z))return!1;const t=e;return this.value===t.value}toString(){return this.stringValue}}Z.FOR_BITS=new Map,Z.FOR_VALUE=new Map,Z.TERMINATOR=new Z(Ct.TERMINATOR,"TERMINATOR",Int32Array.from([0,0,0]),0),Z.NUMERIC=new Z(Ct.NUMERIC,"NUMERIC",Int32Array.from([10,12,14]),1),Z.ALPHANUMERIC=new Z(Ct.ALPHANUMERIC,"ALPHANUMERIC",Int32Array.from([9,11,13]),2),Z.STRUCTURED_APPEND=new Z(Ct.STRUCTURED_APPEND,"STRUCTURED_APPEND",Int32Array.from([0,0,0]),3),Z.BYTE=new Z(Ct.BYTE,"BYTE",Int32Array.from([8,16,16]),4),Z.ECI=new Z(Ct.ECI,"ECI",Int32Array.from([0,0,0]),7),Z.KANJI=new Z(Ct.KANJI,"KANJI",Int32Array.from([8,10,12]),8),Z.FNC1_FIRST_POSITION=new Z(Ct.FNC1_FIRST_POSITION,"FNC1_FIRST_POSITION",Int32Array.from([0,0,0]),5),Z.FNC1_SECOND_POSITION=new Z(Ct.FNC1_SECOND_POSITION,"FNC1_SECOND_POSITION",Int32Array.from([0,0,0]),9),Z.HANZI=new Z(Ct.HANZI,"HANZI",Int32Array.from([8,10,12]),13);class Oe{static decode(e,t,n,r){const i=new hr(e);let s=new me;const o=new Array;let a=-1,l=-1;try{let f=null,x=!1,C;do{if(i.available()<4)C=Z.TERMINATOR;else{const E=i.readBits(4);C=Z.forBits(E)}switch(C){case Z.TERMINATOR:break;case Z.FNC1_FIRST_POSITION:case Z.FNC1_SECOND_POSITION:x=!0;break;case Z.STRUCTURED_APPEND:if(i.available()<16)throw new V;a=i.readBits(8),l=i.readBits(8);break;case Z.ECI:const E=Oe.parseECIValue(i);if(f=U.getCharacterSetECIByValue(E),f===null)throw new V;break;case Z.HANZI:const I=i.readBits(4),y=i.readBits(C.getCharacterCountBits(t));I===Oe.GB2312_SUBSET&&Oe.decodeHanziSegment(i,s,y);break;default:const b=i.readBits(C.getCharacterCountBits(t));switch(C){case Z.NUMERIC:Oe.decodeNumericSegment(i,s,b);break;case Z.ALPHANUMERIC:Oe.decodeAlphanumericSegment(i,s,b,x);break;case Z.BYTE:Oe.decodeByteSegment(i,s,b,f,o,r);break;case Z.KANJI:Oe.decodeKanjiSegment(i,s,b);break;default:throw new V}break}}while(C!==Z.TERMINATOR)}catch{throw new V}return new mn(e,s.toString(),o.length===0?null:o,n===null?null:n.toString(),a,l)}static decodeHanziSegment(e,t,n){if(n*13>e.available())throw new V;const r=new Uint8Array(2*n);let i=0;for(;n>0;){const s=e.readBits(13);let o=s/96<<8&4294967295|s%96;o<959?o+=41377:o+=42657,r[i]=o>>8&255,r[i+1]=o&255,i+=2,n--}try{t.append(st.decode(r,$.GB2312))}catch(s){throw new V(s)}}static decodeKanjiSegment(e,t,n){if(n*13>e.available())throw new V;const r=new Uint8Array(2*n);let i=0;for(;n>0;){const s=e.readBits(13);let o=s/192<<8&4294967295|s%192;o<7936?o+=33088:o+=49472,r[i]=o>>8,r[i+1]=o,i+=2,n--}try{t.append(st.decode(r,$.SHIFT_JIS))}catch(s){throw new V(s)}}static decodeByteSegment(e,t,n,r,i,s){if(8*n>e.available())throw new V;const o=new Uint8Array(n);for(let l=0;l<n;l++)o[l]=e.readBits(8);let a;r===null?a=$.guessEncoding(o,s):a=r.getName();try{t.append(st.decode(o,a))}catch(l){throw new V(l)}i.push(o)}static toAlphaNumericChar(e){if(e>=Oe.ALPHANUMERIC_CHARS.length)throw new V;return Oe.ALPHANUMERIC_CHARS[e]}static decodeAlphanumericSegment(e,t,n,r){const i=t.length();for(;n>1;){if(e.available()<11)throw new V;const s=e.readBits(11);t.append(Oe.toAlphaNumericChar(Math.floor(s/45))),t.append(Oe.toAlphaNumericChar(s%45)),n-=2}if(n===1){if(e.available()<6)throw new V;t.append(Oe.toAlphaNumericChar(e.readBits(6)))}if(r)for(let s=i;s<t.length();s++)t.charAt(s)==="%"&&(s<t.length()-1&&t.charAt(s+1)==="%"?t.deleteCharAt(s+1):t.setCharAt(s,String.fromCharCode(29)))}static decodeNumericSegment(e,t,n){for(;n>=3;){if(e.available()<10)throw new V;const r=e.readBits(10);if(r>=1e3)throw new V;t.append(Oe.toAlphaNumericChar(Math.floor(r/100))),t.append(Oe.toAlphaNumericChar(Math.floor(r/10)%10)),t.append(Oe.toAlphaNumericChar(r%10)),n-=3}if(n===2){if(e.available()<7)throw new V;const r=e.readBits(7);if(r>=100)throw new V;t.append(Oe.toAlphaNumericChar(Math.floor(r/10))),t.append(Oe.toAlphaNumericChar(r%10))}else if(n===1){if(e.available()<4)throw new V;const r=e.readBits(4);if(r>=10)throw new V;t.append(Oe.toAlphaNumericChar(r))}}static parseECIValue(e){const t=e.readBits(8);if((t&128)===0)return t&127;if((t&192)===128){const n=e.readBits(8);return(t&63)<<8&4294967295|n}if((t&224)===192){const n=e.readBits(16);return(t&31)<<16&4294967295|n}throw new V}}Oe.ALPHANUMERIC_CHARS="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",Oe.GB2312_SUBSET=1;class kr{constructor(e){this.mirrored=e}isMirrored(){return this.mirrored}applyMirroredCorrection(e){if(!this.mirrored||e===null||e.length<3)return;const t=e[0];e[0]=e[2],e[2]=t}}class Li{constructor(){this.rsDecoder=new En(fe.QR_CODE_FIELD_256)}decodeBooleanArray(e,t){return this.decodeBitMatrix(He.parseFromBooleanArray(e),t)}decodeBitMatrix(e,t){const n=new vi(e);let r=null;try{return this.decodeBitMatrixParser(n,t)}catch(i){r=i}try{n.remask(),n.setMirror(!0),n.readVersion(),n.readFormatInformation(),n.mirror();const i=this.decodeBitMatrixParser(n,t);return i.setOther(new kr(!0)),i}catch(i){throw r!==null?r:i}}decodeBitMatrixParser(e,t){const n=e.readVersion(),r=e.readFormatInformation().getErrorCorrectionLevel(),i=e.readCodewords(),s=fr.getDataBlocks(i,n,r);let o=0;for(const f of s)o+=f.getNumDataCodewords();const a=new Uint8Array(o);let l=0;for(const f of s){const x=f.getCodewords(),C=f.getNumDataCodewords();this.correctErrors(x,C);for(let E=0;E<C;E++)a[l++]=x[E]}return Oe.decode(a,n,r,t)}correctErrors(e,t){const n=new Int32Array(e);try{this.rsDecoder.decode(n,e.length-t)}catch{throw new P}for(let r=0;r<t;r++)e[r]=n[r]}}class dr extends G{constructor(e,t,n){super(e,t),this.estimatedModuleSize=n}aboutEquals(e,t,n){if(Math.abs(t-this.getY())<=e&&Math.abs(n-this.getX())<=e){const r=Math.abs(e-this.estimatedModuleSize);return r<=1||r<=this.estimatedModuleSize}return!1}combineEstimate(e,t,n){const r=(this.getX()+t)/2,i=(this.getY()+e)/2,s=(this.estimatedModuleSize+n)/2;return new dr(r,i,s)}}class zn{constructor(e,t,n,r,i,s,o){this.image=e,this.startX=t,this.startY=n,this.width=r,this.height=i,this.moduleSize=s,this.resultPointCallback=o,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(3)}find(){const e=this.startX,t=this.height,n=this.width,r=e+n,i=this.startY+t/2,s=new Int32Array(3),o=this.image;for(let a=0;a<t;a++){const l=i+((a&1)===0?Math.floor((a+1)/2):-Math.floor((a+1)/2));s[0]=0,s[1]=0,s[2]=0;let f=e;for(;f<r&&!o.get(f,l);)f++;let x=0;for(;f<r;){if(o.get(f,l))if(x===1)s[1]++;else if(x===2){if(this.foundPatternCross(s)){const C=this.handlePossibleCenter(s,l,f);if(C!==null)return C}s[0]=s[2],s[1]=1,s[2]=0,x=1}else s[++x]++;else x===1&&x++,s[x]++;f++}if(this.foundPatternCross(s)){const C=this.handlePossibleCenter(s,l,r);if(C!==null)return C}}if(this.possibleCenters.length!==0)return this.possibleCenters[0];throw new v}static centerFromEnd(e,t){return t-e[2]-e[1]/2}foundPatternCross(e){const t=this.moduleSize,n=t/2;for(let r=0;r<3;r++)if(Math.abs(t-e[r])>=n)return!1;return!0}crossCheckVertical(e,t,n,r){const i=this.image,s=i.getHeight(),o=this.crossCheckStateCount;o[0]=0,o[1]=0,o[2]=0;let a=e;for(;a>=0&&i.get(t,a)&&o[1]<=n;)o[1]++,a--;if(a<0||o[1]>n)return NaN;for(;a>=0&&!i.get(t,a)&&o[0]<=n;)o[0]++,a--;if(o[0]>n)return NaN;for(a=e+1;a<s&&i.get(t,a)&&o[1]<=n;)o[1]++,a++;if(a===s||o[1]>n)return NaN;for(;a<s&&!i.get(t,a)&&o[2]<=n;)o[2]++,a++;if(o[2]>n)return NaN;const l=o[0]+o[1]+o[2];return 5*Math.abs(l-r)>=2*r?NaN:this.foundPatternCross(o)?zn.centerFromEnd(o,a):NaN}handlePossibleCenter(e,t,n){const r=e[0]+e[1]+e[2],i=zn.centerFromEnd(e,n),s=this.crossCheckVertical(t,i,2*e[1],r);if(!isNaN(s)){const o=(e[0]+e[1]+e[2])/3;for(const l of this.possibleCenters)if(l.aboutEquals(o,s,i))return l.combineEstimate(s,i,o);const a=new dr(i,s,o);this.possibleCenters.push(a),this.resultPointCallback!==null&&this.resultPointCallback!==void 0&&this.resultPointCallback.foundPossibleResultPoint(a)}return null}}class gr extends G{constructor(e,t,n,r){super(e,t),this.estimatedModuleSize=n,this.count=r,r===void 0&&(this.count=1)}getEstimatedModuleSize(){return this.estimatedModuleSize}getCount(){return this.count}aboutEquals(e,t,n){if(Math.abs(t-this.getY())<=e&&Math.abs(n-this.getX())<=e){const r=Math.abs(e-this.estimatedModuleSize);return r<=1||r<=this.estimatedModuleSize}return!1}combineEstimate(e,t,n){const r=this.count+1,i=(this.count*this.getX()+t)/r,s=(this.count*this.getY()+e)/r,o=(this.count*this.estimatedModuleSize+n)/r;return new gr(i,s,o,r)}}class Bi{constructor(e){this.bottomLeft=e[0],this.topLeft=e[1],this.topRight=e[2]}getBottomLeft(){return this.bottomLeft}getTopLeft(){return this.topLeft}getTopRight(){return this.topRight}}class We{constructor(e,t){this.image=e,this.resultPointCallback=t,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(5),this.resultPointCallback=t}getImage(){return this.image}getPossibleCenters(){return this.possibleCenters}find(e){const t=e!=null&&e.get(Ce.TRY_HARDER)!==void 0,n=e!=null&&e.get(Ce.PURE_BARCODE)!==void 0,r=this.image,i=r.getHeight(),s=r.getWidth();let o=Math.floor(3*i/(4*We.MAX_MODULES));(o<We.MIN_SKIP||t)&&(o=We.MIN_SKIP);let a=!1;const l=new Int32Array(5);for(let x=o-1;x<i&&!a;x+=o){l[0]=0,l[1]=0,l[2]=0,l[3]=0,l[4]=0;let C=0;for(let E=0;E<s;E++)if(r.get(E,x))(C&1)===1&&C++,l[C]++;else if((C&1)===0)if(C===4)if(We.foundPatternCross(l)){if(this.handlePossibleCenter(l,x,E,n)===!0)if(o=2,this.hasSkipped===!0)a=this.haveMultiplyConfirmedCenters();else{const y=this.findRowSkip();y>l[2]&&(x+=y-l[2]-o,E=s-1)}else{l[0]=l[2],l[1]=l[3],l[2]=l[4],l[3]=1,l[4]=0,C=3;continue}C=0,l[0]=0,l[1]=0,l[2]=0,l[3]=0,l[4]=0}else l[0]=l[2],l[1]=l[3],l[2]=l[4],l[3]=1,l[4]=0,C=3;else l[++C]++;else l[C]++;We.foundPatternCross(l)&&this.handlePossibleCenter(l,x,s,n)===!0&&(o=l[0],this.hasSkipped&&(a=this.haveMultiplyConfirmedCenters()))}const f=this.selectBestPatterns();return G.orderBestPatterns(f),new Bi(f)}static centerFromEnd(e,t){return t-e[4]-e[3]-e[2]/2}static foundPatternCross(e){let t=0;for(let i=0;i<5;i++){const s=e[i];if(s===0)return!1;t+=s}if(t<7)return!1;const n=t/7,r=n/2;return Math.abs(n-e[0])<r&&Math.abs(n-e[1])<r&&Math.abs(3*n-e[2])<3*r&&Math.abs(n-e[3])<r&&Math.abs(n-e[4])<r}getCrossCheckStateCount(){const e=this.crossCheckStateCount;return e[0]=0,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e}crossCheckDiagonal(e,t,n,r){const i=this.getCrossCheckStateCount();let s=0;const o=this.image;for(;e>=s&&t>=s&&o.get(t-s,e-s);)i[2]++,s++;if(e<s||t<s)return!1;for(;e>=s&&t>=s&&!o.get(t-s,e-s)&&i[1]<=n;)i[1]++,s++;if(e<s||t<s||i[1]>n)return!1;for(;e>=s&&t>=s&&o.get(t-s,e-s)&&i[0]<=n;)i[0]++,s++;if(i[0]>n)return!1;const a=o.getHeight(),l=o.getWidth();for(s=1;e+s<a&&t+s<l&&o.get(t+s,e+s);)i[2]++,s++;if(e+s>=a||t+s>=l)return!1;for(;e+s<a&&t+s<l&&!o.get(t+s,e+s)&&i[3]<n;)i[3]++,s++;if(e+s>=a||t+s>=l||i[3]>=n)return!1;for(;e+s<a&&t+s<l&&o.get(t+s,e+s)&&i[4]<n;)i[4]++,s++;if(i[4]>=n)return!1;const f=i[0]+i[1]+i[2]+i[3]+i[4];return Math.abs(f-r)<2*r&&We.foundPatternCross(i)}crossCheckVertical(e,t,n,r){const i=this.image,s=i.getHeight(),o=this.getCrossCheckStateCount();let a=e;for(;a>=0&&i.get(t,a);)o[2]++,a--;if(a<0)return NaN;for(;a>=0&&!i.get(t,a)&&o[1]<=n;)o[1]++,a--;if(a<0||o[1]>n)return NaN;for(;a>=0&&i.get(t,a)&&o[0]<=n;)o[0]++,a--;if(o[0]>n)return NaN;for(a=e+1;a<s&&i.get(t,a);)o[2]++,a++;if(a===s)return NaN;for(;a<s&&!i.get(t,a)&&o[3]<n;)o[3]++,a++;if(a===s||o[3]>=n)return NaN;for(;a<s&&i.get(t,a)&&o[4]<n;)o[4]++,a++;if(o[4]>=n)return NaN;const l=o[0]+o[1]+o[2]+o[3]+o[4];return 5*Math.abs(l-r)>=2*r?NaN:We.foundPatternCross(o)?We.centerFromEnd(o,a):NaN}crossCheckHorizontal(e,t,n,r){const i=this.image,s=i.getWidth(),o=this.getCrossCheckStateCount();let a=e;for(;a>=0&&i.get(a,t);)o[2]++,a--;if(a<0)return NaN;for(;a>=0&&!i.get(a,t)&&o[1]<=n;)o[1]++,a--;if(a<0||o[1]>n)return NaN;for(;a>=0&&i.get(a,t)&&o[0]<=n;)o[0]++,a--;if(o[0]>n)return NaN;for(a=e+1;a<s&&i.get(a,t);)o[2]++,a++;if(a===s)return NaN;for(;a<s&&!i.get(a,t)&&o[3]<n;)o[3]++,a++;if(a===s||o[3]>=n)return NaN;for(;a<s&&i.get(a,t)&&o[4]<n;)o[4]++,a++;if(o[4]>=n)return NaN;const l=o[0]+o[1]+o[2]+o[3]+o[4];return 5*Math.abs(l-r)>=r?NaN:We.foundPatternCross(o)?We.centerFromEnd(o,a):NaN}handlePossibleCenter(e,t,n,r){const i=e[0]+e[1]+e[2]+e[3]+e[4];let s=We.centerFromEnd(e,n),o=this.crossCheckVertical(t,Math.floor(s),e[2],i);if(!isNaN(o)&&(s=this.crossCheckHorizontal(Math.floor(s),Math.floor(o),e[2],i),!isNaN(s)&&(!r||this.crossCheckDiagonal(Math.floor(o),Math.floor(s),e[2],i)))){const a=i/7;let l=!1;const f=this.possibleCenters;for(let x=0,C=f.length;x<C;x++){const E=f[x];if(E.aboutEquals(a,o,s)){f[x]=E.combineEstimate(o,s,a),l=!0;break}}if(!l){const x=new gr(s,o,a);f.push(x),this.resultPointCallback!==null&&this.resultPointCallback!==void 0&&this.resultPointCallback.foundPossibleResultPoint(x)}return!0}return!1}findRowSkip(){if(this.possibleCenters.length<=1)return 0;let t=null;for(const n of this.possibleCenters)if(n.getCount()>=We.CENTER_QUORUM)if(t==null)t=n;else return this.hasSkipped=!0,Math.floor((Math.abs(t.getX()-n.getX())-Math.abs(t.getY()-n.getY()))/2);return 0}haveMultiplyConfirmedCenters(){let e=0,t=0;const n=this.possibleCenters.length;for(const s of this.possibleCenters)s.getCount()>=We.CENTER_QUORUM&&(e++,t+=s.getEstimatedModuleSize());if(e<3)return!1;const r=t/n;let i=0;for(const s of this.possibleCenters)i+=Math.abs(s.getEstimatedModuleSize()-r);return i<=.05*t}selectBestPatterns(){const e=this.possibleCenters.length;if(e<3)throw new v;const t=this.possibleCenters;let n;if(e>3){let r=0,i=0;for(const a of this.possibleCenters){const l=a.getEstimatedModuleSize();r+=l,i+=l*l}n=r/e;let s=Math.sqrt(i/e-n*n);t.sort((a,l)=>{const f=Math.abs(l.getEstimatedModuleSize()-n),x=Math.abs(a.getEstimatedModuleSize()-n);return f<x?-1:f>x?1:0});const o=Math.max(.2*n,s);for(let a=0;a<t.length&&t.length>3;a++){const l=t[a];Math.abs(l.getEstimatedModuleSize()-n)>o&&(t.splice(a,1),a--)}}if(t.length>3){let r=0;for(const i of t)r+=i.getEstimatedModuleSize();n=r/t.length,t.sort((i,s)=>{if(s.getCount()===i.getCount()){const o=Math.abs(s.getEstimatedModuleSize()-n),a=Math.abs(i.getEstimatedModuleSize()-n);return o<a?1:o>a?-1:0}else return s.getCount()-i.getCount()}),t.splice(3)}return[t[0],t[1],t[2]]}}We.CENTER_QUORUM=2,We.MIN_SKIP=3,We.MAX_MODULES=57;class yn{constructor(e){this.image=e}getImage(){return this.image}getResultPointCallback(){return this.resultPointCallback}detect(e){this.resultPointCallback=e==null?null:e.get(Ce.NEED_RESULT_POINT_CALLBACK);const n=new We(this.image,this.resultPointCallback).find(e);return this.processFinderPatternInfo(n)}processFinderPatternInfo(e){const t=e.getTopLeft(),n=e.getTopRight(),r=e.getBottomLeft(),i=this.calculateModuleSize(t,n,r);if(i<1)throw new v("No pattern found in proccess finder.");const s=yn.computeDimension(t,n,r,i),o=Y.getProvisionalVersionForDimension(s),a=o.getDimensionForVersion()-7;let l=null;if(o.getAlignmentPatternCenters().length>0){const E=n.getX()-t.getX()+r.getX(),I=n.getY()-t.getY()+r.getY(),y=1-3/a,b=Math.floor(t.getX()+y*(E-t.getX())),D=Math.floor(t.getY()+y*(I-t.getY()));for(let F=4;F<=16;F<<=1)try{l=this.findAlignmentInRegion(i,b,D,F);break}catch(k){if(!(k instanceof v))throw k}}const f=yn.createTransform(t,n,r,l,s),x=yn.sampleGrid(this.image,f,s);let C;return l===null?C=[r,t,n]:C=[r,t,n,l],new Fn(x,C)}static createTransform(e,t,n,r,i){const s=i-3.5;let o,a,l,f;return r!==null?(o=r.getX(),a=r.getY(),l=s-3,f=l):(o=t.getX()-e.getX()+n.getX(),a=t.getY()-e.getY()+n.getY(),l=s,f=s),Et.quadrilateralToQuadrilateral(3.5,3.5,s,3.5,l,f,3.5,s,e.getX(),e.getY(),t.getX(),t.getY(),o,a,n.getX(),n.getY())}static sampleGrid(e,t,n){return Vt.getInstance().sampleGridWithTransform(e,n,n,t)}static computeDimension(e,t,n,r){const i=ae.round(G.distance(e,t)/r),s=ae.round(G.distance(e,n)/r);let o=Math.floor((i+s)/2)+7;switch(o&3){case 0:o++;break;case 2:o--;break;case 3:throw new v("Dimensions could be not found.")}return o}calculateModuleSize(e,t,n){return(this.calculateModuleSizeOneWay(e,t)+this.calculateModuleSizeOneWay(e,n))/2}calculateModuleSizeOneWay(e,t){const n=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(e.getX()),Math.floor(e.getY()),Math.floor(t.getX()),Math.floor(t.getY())),r=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(t.getX()),Math.floor(t.getY()),Math.floor(e.getX()),Math.floor(e.getY()));return isNaN(n)?r/7:isNaN(r)?n/7:(n+r)/14}sizeOfBlackWhiteBlackRunBothWays(e,t,n,r){let i=this.sizeOfBlackWhiteBlackRun(e,t,n,r),s=1,o=e-(n-e);o<0?(s=e/(e-o),o=0):o>=this.image.getWidth()&&(s=(this.image.getWidth()-1-e)/(o-e),o=this.image.getWidth()-1);let a=Math.floor(t-(r-t)*s);return s=1,a<0?(s=t/(t-a),a=0):a>=this.image.getHeight()&&(s=(this.image.getHeight()-1-t)/(a-t),a=this.image.getHeight()-1),o=Math.floor(e+(o-e)*s),i+=this.sizeOfBlackWhiteBlackRun(e,t,o,a),i-1}sizeOfBlackWhiteBlackRun(e,t,n,r){const i=Math.abs(r-t)>Math.abs(n-e);if(i){let E=e;e=t,t=E,E=n,n=r,r=E}const s=Math.abs(n-e),o=Math.abs(r-t);let a=-s/2;const l=e<n?1:-1,f=t<r?1:-1;let x=0;const C=n+l;for(let E=e,I=t;E!==C;E+=l){const y=i?I:E,b=i?E:I;if(x===1===this.image.get(y,b)){if(x===2)return ae.distance(E,I,e,t);x++}if(a+=o,a>0){if(I===r)break;I+=f,a-=s}}return x===2?ae.distance(n+l,r,e,t):NaN}findAlignmentInRegion(e,t,n,r){const i=Math.floor(r*e),s=Math.max(0,t-i),o=Math.min(this.image.getWidth()-1,t+i);if(o-s<e*3)throw new v("Alignment top exceeds estimated module size.");const a=Math.max(0,n-i),l=Math.min(this.image.getHeight()-1,n+i);if(l-a<e*3)throw new v("Alignment bottom exceeds estimated module size.");return new zn(this.image,s,a,o-s,l-a,e,this.resultPointCallback).find()}}class Gt{constructor(){this.decoder=new Li}getDecoder(){return this.decoder}decode(e,t){let n,r;if(t!=null&&t.get(Ce.PURE_BARCODE)!==void 0){const a=Gt.extractPureBits(e.getBlackMatrix());n=this.decoder.decodeBitMatrix(a,t),r=Gt.NO_POINTS}else{const a=new yn(e.getBlackMatrix()).detect(t);n=this.decoder.decodeBitMatrix(a.getBits(),t),r=a.getPoints()}n.getOther()instanceof kr&&n.getOther().applyMirroredCorrection(r);const i=new tt(n.getText(),n.getRawBytes(),void 0,r,Q.QR_CODE,void 0),s=n.getByteSegments();s!==null&&i.putMetadata(je.BYTE_SEGMENTS,s);const o=n.getECLevel();return o!==null&&i.putMetadata(je.ERROR_CORRECTION_LEVEL,o),n.hasStructuredAppend()&&(i.putMetadata(je.STRUCTURED_APPEND_SEQUENCE,n.getStructuredAppendSequenceNumber()),i.putMetadata(je.STRUCTURED_APPEND_PARITY,n.getStructuredAppendParity())),i}reset(){}static extractPureBits(e){const t=e.getTopLeftOnBit(),n=e.getBottomRightOnBit();if(t===null||n===null)throw new v;const r=this.moduleSize(t,e);let i=t[1],s=n[1],o=t[0],a=n[0];if(o>=a||i>=s)throw new v;if(s-i!==a-o&&(a=o+(s-i),a>=e.getWidth()))throw new v;const l=Math.round((a-o+1)/r),f=Math.round((s-i+1)/r);if(l<=0||f<=0)throw new v;if(f!==l)throw new v;const x=Math.floor(r/2);i+=x,o+=x;const C=o+Math.floor((l-1)*r)-a;if(C>0){if(C>x)throw new v;o-=C}const E=i+Math.floor((f-1)*r)-s;if(E>0){if(E>x)throw new v;i-=E}const I=new He(l,f);for(let y=0;y<f;y++){const b=i+Math.floor(y*r);for(let D=0;D<l;D++)e.get(o+Math.floor(D*r),b)&&I.set(D,y)}return I}static moduleSize(e,t){const n=t.getHeight(),r=t.getWidth();let i=e[0],s=e[1],o=!0,a=0;for(;i<r&&s<n;){if(o!==t.get(i,s)){if(++a===5)break;o=!o}i++,s++}if(i===r||s===n)throw new v;return(i-e[0])/7}}Gt.NO_POINTS=new Array;class K{PDF417Common(){}static getBitCountSum(e){return ae.sum(e)}static toIntArray(e){if(e==null||!e.length)return K.EMPTY_INT_ARRAY;const t=new Int32Array(e.length);let n=0;for(const r of e)t[n++]=r;return t}static getCodeword(e){const t=he.binarySearch(K.SYMBOL_TABLE,e&262143);return t<0?-1:(K.CODEWORD_TABLE[t]-1)%K.NUMBER_OF_CODEWORDS}}K.NUMBER_OF_CODEWORDS=929,K.MAX_CODEWORDS_IN_BARCODE=K.NUMBER_OF_CODEWORDS-1,K.MIN_ROWS_IN_BARCODE=3,K.MAX_ROWS_IN_BARCODE=90,K.MODULES_IN_CODEWORD=17,K.MODULES_IN_STOP_PATTERN=18,K.BARS_IN_MODULE=8,K.EMPTY_INT_ARRAY=new Int32Array([]),K.SYMBOL_TABLE=Int32Array.from([66142,66170,66206,66236,66290,66292,66350,66382,66396,66454,66470,66476,66594,66600,66614,66626,66628,66632,66640,66654,66662,66668,66682,66690,66718,66720,66748,66758,66776,66798,66802,66804,66820,66824,66832,66846,66848,66876,66880,66936,66950,66956,66968,66992,67006,67022,67036,67042,67044,67048,67062,67118,67150,67164,67214,67228,67256,67294,67322,67350,67366,67372,67398,67404,67416,67438,67474,67476,67490,67492,67496,67510,67618,67624,67650,67656,67664,67678,67686,67692,67706,67714,67716,67728,67742,67744,67772,67782,67788,67800,67822,67826,67828,67842,67848,67870,67872,67900,67904,67960,67974,67992,68016,68030,68046,68060,68066,68068,68072,68086,68104,68112,68126,68128,68156,68160,68216,68336,68358,68364,68376,68400,68414,68448,68476,68494,68508,68536,68546,68548,68552,68560,68574,68582,68588,68654,68686,68700,68706,68708,68712,68726,68750,68764,68792,68802,68804,68808,68816,68830,68838,68844,68858,68878,68892,68920,68976,68990,68994,68996,69e3,69008,69022,69024,69052,69062,69068,69080,69102,69106,69108,69142,69158,69164,69190,69208,69230,69254,69260,69272,69296,69310,69326,69340,69386,69394,69396,69410,69416,69430,69442,69444,69448,69456,69470,69478,69484,69554,69556,69666,69672,69698,69704,69712,69726,69754,69762,69764,69776,69790,69792,69820,69830,69836,69848,69870,69874,69876,69890,69918,69920,69948,69952,70008,70022,70040,70064,70078,70094,70108,70114,70116,70120,70134,70152,70174,70176,70264,70384,70412,70448,70462,70496,70524,70542,70556,70584,70594,70600,70608,70622,70630,70636,70664,70672,70686,70688,70716,70720,70776,70896,71136,71180,71192,71216,71230,71264,71292,71360,71416,71452,71480,71536,71550,71554,71556,71560,71568,71582,71584,71612,71622,71628,71640,71662,71726,71732,71758,71772,71778,71780,71784,71798,71822,71836,71864,71874,71880,71888,71902,71910,71916,71930,71950,71964,71992,72048,72062,72066,72068,72080,72094,72096,72124,72134,72140,72152,72174,72178,72180,72206,72220,72248,72304,72318,72416,72444,72456,72464,72478,72480,72508,72512,72568,72588,72600,72624,72638,72654,72668,72674,72676,72680,72694,72726,72742,72748,72774,72780,72792,72814,72838,72856,72880,72894,72910,72924,72930,72932,72936,72950,72966,72972,72984,73008,73022,73056,73084,73102,73116,73144,73156,73160,73168,73182,73190,73196,73210,73226,73234,73236,73250,73252,73256,73270,73282,73284,73296,73310,73318,73324,73346,73348,73352,73360,73374,73376,73404,73414,73420,73432,73454,73498,73518,73522,73524,73550,73564,73570,73572,73576,73590,73800,73822,73858,73860,73872,73886,73888,73916,73944,73970,73972,73992,74014,74016,74044,74048,74104,74118,74136,74160,74174,74210,74212,74216,74230,74244,74256,74270,74272,74360,74480,74502,74508,74544,74558,74592,74620,74638,74652,74680,74690,74696,74704,74726,74732,74782,74784,74812,74992,75232,75288,75326,75360,75388,75456,75512,75576,75632,75646,75650,75652,75664,75678,75680,75708,75718,75724,75736,75758,75808,75836,75840,75896,76016,76256,76736,76824,76848,76862,76896,76924,76992,77048,77296,77340,77368,77424,77438,77536,77564,77572,77576,77584,77600,77628,77632,77688,77702,77708,77720,77744,77758,77774,77788,77870,77902,77916,77922,77928,77966,77980,78008,78018,78024,78032,78046,78060,78074,78094,78136,78192,78206,78210,78212,78224,78238,78240,78268,78278,78284,78296,78322,78324,78350,78364,78448,78462,78560,78588,78600,78622,78624,78652,78656,78712,78726,78744,78768,78782,78798,78812,78818,78820,78824,78838,78862,78876,78904,78960,78974,79072,79100,79296,79352,79368,79376,79390,79392,79420,79424,79480,79600,79628,79640,79664,79678,79712,79740,79772,79800,79810,79812,79816,79824,79838,79846,79852,79894,79910,79916,79942,79948,79960,79982,79988,80006,80024,80048,80062,80078,80092,80098,80100,80104,80134,80140,80176,80190,80224,80252,80270,80284,80312,80328,80336,80350,80358,80364,80378,80390,80396,80408,80432,80446,80480,80508,80576,80632,80654,80668,80696,80752,80766,80776,80784,80798,80800,80828,80844,80856,80878,80882,80884,80914,80916,80930,80932,80936,80950,80962,80968,80976,80990,80998,81004,81026,81028,81040,81054,81056,81084,81094,81100,81112,81134,81154,81156,81160,81168,81182,81184,81212,81216,81272,81286,81292,81304,81328,81342,81358,81372,81380,81384,81398,81434,81454,81458,81460,81486,81500,81506,81508,81512,81526,81550,81564,81592,81602,81604,81608,81616,81630,81638,81644,81702,81708,81722,81734,81740,81752,81774,81778,81780,82050,82078,82080,82108,82180,82184,82192,82206,82208,82236,82240,82296,82316,82328,82352,82366,82402,82404,82408,82440,82448,82462,82464,82492,82496,82552,82672,82694,82700,82712,82736,82750,82784,82812,82830,82882,82884,82888,82896,82918,82924,82952,82960,82974,82976,83004,83008,83064,83184,83424,83468,83480,83504,83518,83552,83580,83648,83704,83740,83768,83824,83838,83842,83844,83848,83856,83872,83900,83910,83916,83928,83950,83984,84e3,84028,84032,84088,84208,84448,84928,85040,85054,85088,85116,85184,85240,85488,85560,85616,85630,85728,85756,85764,85768,85776,85790,85792,85820,85824,85880,85894,85900,85912,85936,85966,85980,86048,86080,86136,86256,86496,86976,88160,88188,88256,88312,88560,89056,89200,89214,89312,89340,89536,89592,89608,89616,89632,89664,89720,89840,89868,89880,89904,89952,89980,89998,90012,90040,90190,90204,90254,90268,90296,90306,90308,90312,90334,90382,90396,90424,90480,90494,90500,90504,90512,90526,90528,90556,90566,90572,90584,90610,90612,90638,90652,90680,90736,90750,90848,90876,90884,90888,90896,90910,90912,90940,90944,91e3,91014,91020,91032,91056,91070,91086,91100,91106,91108,91112,91126,91150,91164,91192,91248,91262,91360,91388,91584,91640,91664,91678,91680,91708,91712,91768,91888,91928,91952,91966,92e3,92028,92046,92060,92088,92098,92100,92104,92112,92126,92134,92140,92188,92216,92272,92384,92412,92608,92664,93168,93200,93214,93216,93244,93248,93304,93424,93664,93720,93744,93758,93792,93820,93888,93944,93980,94008,94064,94078,94084,94088,94096,94110,94112,94140,94150,94156,94168,94246,94252,94278,94284,94296,94318,94342,94348,94360,94384,94398,94414,94428,94440,94470,94476,94488,94512,94526,94560,94588,94606,94620,94648,94658,94660,94664,94672,94686,94694,94700,94714,94726,94732,94744,94768,94782,94816,94844,94912,94968,94990,95004,95032,95088,95102,95112,95120,95134,95136,95164,95180,95192,95214,95218,95220,95244,95256,95280,95294,95328,95356,95424,95480,95728,95758,95772,95800,95856,95870,95968,95996,96008,96016,96030,96032,96060,96064,96120,96152,96176,96190,96220,96226,96228,96232,96290,96292,96296,96310,96322,96324,96328,96336,96350,96358,96364,96386,96388,96392,96400,96414,96416,96444,96454,96460,96472,96494,96498,96500,96514,96516,96520,96528,96542,96544,96572,96576,96632,96646,96652,96664,96688,96702,96718,96732,96738,96740,96744,96758,96772,96776,96784,96798,96800,96828,96832,96888,97008,97030,97036,97048,97072,97086,97120,97148,97166,97180,97208,97220,97224,97232,97246,97254,97260,97326,97330,97332,97358,97372,97378,97380,97384,97398,97422,97436,97464,97474,97476,97480,97488,97502,97510,97516,97550,97564,97592,97648,97666,97668,97672,97680,97694,97696,97724,97734,97740,97752,97774,97830,97836,97850,97862,97868,97880,97902,97906,97908,97926,97932,97944,97968,97998,98012,98018,98020,98024,98038,98618,98674,98676,98838,98854,98874,98892,98904,98926,98930,98932,98968,99006,99042,99044,99048,99062,99166,99194,99246,99286,99350,99366,99372,99386,99398,99416,99438,99442,99444,99462,99504,99518,99534,99548,99554,99556,99560,99574,99590,99596,99608,99632,99646,99680,99708,99726,99740,99768,99778,99780,99784,99792,99806,99814,99820,99834,99858,99860,99874,99880,99894,99906,99920,99934,99962,99970,99972,99976,99984,99998,1e5,100028,100038,100044,100056,100078,100082,100084,100142,100174,100188,100246,100262,100268,100306,100308,100390,100396,100410,100422,100428,100440,100462,100466,100468,100486,100504,100528,100542,100558,100572,100578,100580,100584,100598,100620,100656,100670,100704,100732,100750,100792,100802,100808,100816,100830,100838,100844,100858,100888,100912,100926,100960,100988,101056,101112,101148,101176,101232,101246,101250,101252,101256,101264,101278,101280,101308,101318,101324,101336,101358,101362,101364,101410,101412,101416,101430,101442,101448,101456,101470,101478,101498,101506,101508,101520,101534,101536,101564,101580,101618,101620,101636,101640,101648,101662,101664,101692,101696,101752,101766,101784,101838,101858,101860,101864,101934,101938,101940,101966,101980,101986,101988,101992,102030,102044,102072,102082,102084,102088,102096,102138,102166,102182,102188,102214,102220,102232,102254,102282,102290,102292,102306,102308,102312,102326,102444,102458,102470,102476,102488,102514,102516,102534,102552,102576,102590,102606,102620,102626,102632,102646,102662,102668,102704,102718,102752,102780,102798,102812,102840,102850,102856,102864,102878,102886,102892,102906,102936,102974,103008,103036,103104,103160,103224,103280,103294,103298,103300,103312,103326,103328,103356,103366,103372,103384,103406,103410,103412,103472,103486,103520,103548,103616,103672,103920,103992,104048,104062,104160,104188,104194,104196,104200,104208,104224,104252,104256,104312,104326,104332,104344,104368,104382,104398,104412,104418,104420,104424,104482,104484,104514,104520,104528,104542,104550,104570,104578,104580,104592,104606,104608,104636,104652,104690,104692,104706,104712,104734,104736,104764,104768,104824,104838,104856,104910,104930,104932,104936,104968,104976,104990,104992,105020,105024,105080,105200,105240,105278,105312,105372,105410,105412,105416,105424,105446,105518,105524,105550,105564,105570,105572,105576,105614,105628,105656,105666,105672,105680,105702,105722,105742,105756,105784,105840,105854,105858,105860,105864,105872,105888,105932,105970,105972,106006,106022,106028,106054,106060,106072,106100,106118,106124,106136,106160,106174,106190,106210,106212,106216,106250,106258,106260,106274,106276,106280,106306,106308,106312,106320,106334,106348,106394,106414,106418,106420,106566,106572,106610,106612,106630,106636,106648,106672,106686,106722,106724,106728,106742,106758,106764,106776,106800,106814,106848,106876,106894,106908,106936,106946,106948,106952,106960,106974,106982,106988,107032,107056,107070,107104,107132,107200,107256,107292,107320,107376,107390,107394,107396,107400,107408,107422,107424,107452,107462,107468,107480,107502,107506,107508,107544,107568,107582,107616,107644,107712,107768,108016,108060,108088,108144,108158,108256,108284,108290,108292,108296,108304,108318,108320,108348,108352,108408,108422,108428,108440,108464,108478,108494,108508,108514,108516,108520,108592,108640,108668,108736,108792,109040,109536,109680,109694,109792,109820,110016,110072,110084,110088,110096,110112,110140,110144,110200,110320,110342,110348,110360,110384,110398,110432,110460,110478,110492,110520,110532,110536,110544,110558,110658,110686,110714,110722,110724,110728,110736,110750,110752,110780,110796,110834,110836,110850,110852,110856,110864,110878,110880,110908,110912,110968,110982,111e3,111054,111074,111076,111080,111108,111112,111120,111134,111136,111164,111168,111224,111344,111372,111422,111456,111516,111554,111556,111560,111568,111590,111632,111646,111648,111676,111680,111736,111856,112096,112152,112224,112252,112320,112440,112514,112516,112520,112528,112542,112544,112588,112686,112718,112732,112782,112796,112824,112834,112836,112840,112848,112870,112890,112910,112924,112952,113008,113022,113026,113028,113032,113040,113054,113056,113100,113138,113140,113166,113180,113208,113264,113278,113376,113404,113416,113424,113440,113468,113472,113560,113614,113634,113636,113640,113686,113702,113708,113734,113740,113752,113778,113780,113798,113804,113816,113840,113854,113870,113890,113892,113896,113926,113932,113944,113968,113982,114016,114044,114076,114114,114116,114120,114128,114150,114170,114194,114196,114210,114212,114216,114242,114244,114248,114256,114270,114278,114306,114308,114312,114320,114334,114336,114364,114380,114420,114458,114478,114482,114484,114510,114524,114530,114532,114536,114842,114866,114868,114970,114994,114996,115042,115044,115048,115062,115130,115226,115250,115252,115278,115292,115298,115300,115304,115318,115342,115394,115396,115400,115408,115422,115430,115436,115450,115478,115494,115514,115526,115532,115570,115572,115738,115758,115762,115764,115790,115804,115810,115812,115816,115830,115854,115868,115896,115906,115912,115920,115934,115942,115948,115962,115996,116024,116080,116094,116098,116100,116104,116112,116126,116128,116156,116166,116172,116184,116206,116210,116212,116246,116262,116268,116282,116294,116300,116312,116334,116338,116340,116358,116364,116376,116400,116414,116430,116444,116450,116452,116456,116498,116500,116514,116520,116534,116546,116548,116552,116560,116574,116582,116588,116602,116654,116694,116714,116762,116782,116786,116788,116814,116828,116834,116836,116840,116854,116878,116892,116920,116930,116936,116944,116958,116966,116972,116986,117006,117048,117104,117118,117122,117124,117136,117150,117152,117180,117190,117196,117208,117230,117234,117236,117304,117360,117374,117472,117500,117506,117508,117512,117520,117536,117564,117568,117624,117638,117644,117656,117680,117694,117710,117724,117730,117732,117736,117750,117782,117798,117804,117818,117830,117848,117874,117876,117894,117936,117950,117966,117986,117988,117992,118022,118028,118040,118064,118078,118112,118140,118172,118210,118212,118216,118224,118238,118246,118266,118306,118312,118338,118352,118366,118374,118394,118402,118404,118408,118416,118430,118432,118460,118476,118514,118516,118574,118578,118580,118606,118620,118626,118628,118632,118678,118694,118700,118730,118738,118740,118830,118834,118836,118862,118876,118882,118884,118888,118902,118926,118940,118968,118978,118980,118984,118992,119006,119014,119020,119034,119068,119096,119152,119166,119170,119172,119176,119184,119198,119200,119228,119238,119244,119256,119278,119282,119284,119324,119352,119408,119422,119520,119548,119554,119556,119560,119568,119582,119584,119612,119616,119672,119686,119692,119704,119728,119742,119758,119772,119778,119780,119784,119798,119920,119934,120032,120060,120256,120312,120324,120328,120336,120352,120384,120440,120560,120582,120588,120600,120624,120638,120672,120700,120718,120732,120760,120770,120772,120776,120784,120798,120806,120812,120870,120876,120890,120902,120908,120920,120946,120948,120966,120972,120984,121008,121022,121038,121058,121060,121064,121078,121100,121112,121136,121150,121184,121212,121244,121282,121284,121288,121296,121318,121338,121356,121368,121392,121406,121440,121468,121536,121592,121656,121730,121732,121736,121744,121758,121760,121804,121842,121844,121890,121922,121924,121928,121936,121950,121958,121978,121986,121988,121992,122e3,122014,122016,122044,122060,122098,122100,122116,122120,122128,122142,122144,122172,122176,122232,122246,122264,122318,122338,122340,122344,122414,122418,122420,122446,122460,122466,122468,122472,122510,122524,122552,122562,122564,122568,122576,122598,122618,122646,122662,122668,122694,122700,122712,122738,122740,122762,122770,122772,122786,122788,122792,123018,123026,123028,123042,123044,123048,123062,123098,123146,123154,123156,123170,123172,123176,123190,123202,123204,123208,123216,123238,123244,123258,123290,123314,123316,123402,123410,123412,123426,123428,123432,123446,123458,123464,123472,123486,123494,123500,123514,123522,123524,123528,123536,123552,123580,123590,123596,123608,123630,123634,123636,123674,123698,123700,123740,123746,123748,123752,123834,123914,123922,123924,123938,123944,123958,123970,123976,123984,123998,124006,124012,124026,124034,124036,124048,124062,124064,124092,124102,124108,124120,124142,124146,124148,124162,124164,124168,124176,124190,124192,124220,124224,124280,124294,124300,124312,124336,124350,124366,124380,124386,124388,124392,124406,124442,124462,124466,124468,124494,124508,124514,124520,124558,124572,124600,124610,124612,124616,124624,124646,124666,124694,124710,124716,124730,124742,124748,124760,124786,124788,124818,124820,124834,124836,124840,124854,124946,124948,124962,124964,124968,124982,124994,124996,125e3,125008,125022,125030,125036,125050,125058,125060,125064,125072,125086,125088,125116,125126,125132,125144,125166,125170,125172,125186,125188,125192,125200,125216,125244,125248,125304,125318,125324,125336,125360,125374,125390,125404,125410,125412,125416,125430,125444,125448,125456,125472,125504,125560,125680,125702,125708,125720,125744,125758,125792,125820,125838,125852,125880,125890,125892,125896,125904,125918,125926,125932,125978,125998,126002,126004,126030,126044,126050,126052,126056,126094,126108,126136,126146,126148,126152,126160,126182,126202,126222,126236,126264,126320,126334,126338,126340,126344,126352,126366,126368,126412,126450,126452,126486,126502,126508,126522,126534,126540,126552,126574,126578,126580,126598,126604,126616,126640,126654,126670,126684,126690,126692,126696,126738,126754,126756,126760,126774,126786,126788,126792,126800,126814,126822,126828,126842,126894,126898,126900,126934,127126,127142,127148,127162,127178,127186,127188,127254,127270,127276,127290,127302,127308,127320,127342,127346,127348,127370,127378,127380,127394,127396,127400,127450,127510,127526,127532,127546,127558,127576,127598,127602,127604,127622,127628,127640,127664,127678,127694,127708,127714,127716,127720,127734,127754,127762,127764,127778,127784,127810,127812,127816,127824,127838,127846,127866,127898,127918,127922,127924,128022,128038,128044,128058,128070,128076,128088,128110,128114,128116,128134,128140,128152,128176,128190,128206,128220,128226,128228,128232,128246,128262,128268,128280,128304,128318,128352,128380,128398,128412,128440,128450,128452,128456,128464,128478,128486,128492,128506,128522,128530,128532,128546,128548,128552,128566,128578,128580,128584,128592,128606,128614,128634,128642,128644,128648,128656,128670,128672,128700,128716,128754,128756,128794,128814,128818,128820,128846,128860,128866,128868,128872,128886,128918,128934,128940,128954,128978,128980,129178,129198,129202,129204,129238,129258,129306,129326,129330,129332,129358,129372,129378,129380,129384,129398,129430,129446,129452,129466,129482,129490,129492,129562,129582,129586,129588,129614,129628,129634,129636,129640,129654,129678,129692,129720,129730,129732,129736,129744,129758,129766,129772,129814,129830,129836,129850,129862,129868,129880,129902,129906,129908,129930,129938,129940,129954,129956,129960,129974,130010]),K.CODEWORD_TABLE=Int32Array.from([2627,1819,2622,2621,1813,1812,2729,2724,2723,2779,2774,2773,902,896,908,868,865,861,859,2511,873,871,1780,835,2493,825,2491,842,837,844,1764,1762,811,810,809,2483,807,2482,806,2480,815,814,813,812,2484,817,816,1745,1744,1742,1746,2655,2637,2635,2626,2625,2623,2628,1820,2752,2739,2737,2728,2727,2725,2730,2785,2783,2778,2777,2775,2780,787,781,747,739,736,2413,754,752,1719,692,689,681,2371,678,2369,700,697,694,703,1688,1686,642,638,2343,631,2341,627,2338,651,646,643,2345,654,652,1652,1650,1647,1654,601,599,2322,596,2321,594,2319,2317,611,610,608,606,2324,603,2323,615,614,612,1617,1616,1614,1612,616,1619,1618,2575,2538,2536,905,901,898,909,2509,2507,2504,870,867,864,860,2512,875,872,1781,2490,2489,2487,2485,1748,836,834,832,830,2494,827,2492,843,841,839,845,1765,1763,2701,2676,2674,2653,2648,2656,2634,2633,2631,2629,1821,2638,2636,2770,2763,2761,2750,2745,2753,2736,2735,2733,2731,1848,2740,2738,2786,2784,591,588,576,569,566,2296,1590,537,534,526,2276,522,2274,545,542,539,548,1572,1570,481,2245,466,2242,462,2239,492,485,482,2249,496,494,1534,1531,1528,1538,413,2196,406,2191,2188,425,419,2202,415,2199,432,430,427,1472,1467,1464,433,1476,1474,368,367,2160,365,2159,362,2157,2155,2152,378,377,375,2166,372,2165,369,2162,383,381,379,2168,1419,1418,1416,1414,385,1411,384,1423,1422,1420,1424,2461,802,2441,2439,790,786,783,794,2409,2406,2403,750,742,738,2414,756,753,1720,2367,2365,2362,2359,1663,693,691,684,2373,680,2370,702,699,696,704,1690,1687,2337,2336,2334,2332,1624,2329,1622,640,637,2344,634,2342,630,2340,650,648,645,2346,655,653,1653,1651,1649,1655,2612,2597,2595,2571,2568,2565,2576,2534,2529,2526,1787,2540,2537,907,904,900,910,2503,2502,2500,2498,1768,2495,1767,2510,2508,2506,869,866,863,2513,876,874,1782,2720,2713,2711,2697,2694,2691,2702,2672,2670,2664,1828,2678,2675,2647,2646,2644,2642,1823,2639,1822,2654,2652,2650,2657,2771,1855,2765,2762,1850,1849,2751,2749,2747,2754,353,2148,344,342,336,2142,332,2140,345,1375,1373,306,2130,299,2128,295,2125,319,314,311,2132,1354,1352,1349,1356,262,257,2101,253,2096,2093,274,273,267,2107,263,2104,280,278,275,1316,1311,1308,1320,1318,2052,202,2050,2044,2040,219,2063,212,2060,208,2055,224,221,2066,1260,1258,1252,231,1248,229,1266,1264,1261,1268,155,1998,153,1996,1994,1991,1988,165,164,2007,162,2006,159,2003,2e3,172,171,169,2012,166,2010,1186,1184,1182,1179,175,1176,173,1192,1191,1189,1187,176,1194,1193,2313,2307,2305,592,589,2294,2292,2289,578,572,568,2297,580,1591,2272,2267,2264,1547,538,536,529,2278,525,2275,547,544,541,1574,1571,2237,2235,2229,1493,2225,1489,478,2247,470,2244,465,2241,493,488,484,2250,498,495,1536,1533,1530,1539,2187,2186,2184,2182,1432,2179,1430,2176,1427,414,412,2197,409,2195,405,2193,2190,426,424,421,2203,418,2201,431,429,1473,1471,1469,1466,434,1477,1475,2478,2472,2470,2459,2457,2454,2462,803,2437,2432,2429,1726,2443,2440,792,789,785,2401,2399,2393,1702,2389,1699,2411,2408,2405,745,741,2415,758,755,1721,2358,2357,2355,2353,1661,2350,1660,2347,1657,2368,2366,2364,2361,1666,690,687,2374,683,2372,701,698,705,1691,1689,2619,2617,2610,2608,2605,2613,2593,2588,2585,1803,2599,2596,2563,2561,2555,1797,2551,1795,2573,2570,2567,2577,2525,2524,2522,2520,1786,2517,1785,2514,1783,2535,2533,2531,2528,1788,2541,2539,906,903,911,2721,1844,2715,2712,1838,1836,2699,2696,2693,2703,1827,1826,1824,2673,2671,2669,2666,1829,2679,2677,1858,1857,2772,1854,1853,1851,1856,2766,2764,143,1987,139,1986,135,133,131,1984,128,1983,125,1981,138,137,136,1985,1133,1132,1130,112,110,1974,107,1973,104,1971,1969,122,121,119,117,1977,114,1976,124,1115,1114,1112,1110,1117,1116,84,83,1953,81,1952,78,1950,1948,1945,94,93,91,1959,88,1958,85,1955,99,97,95,1961,1086,1085,1083,1081,1078,100,1090,1089,1087,1091,49,47,1917,44,1915,1913,1910,1907,59,1926,56,1925,53,1922,1919,66,64,1931,61,1929,1042,1040,1038,71,1035,70,1032,68,1048,1047,1045,1043,1050,1049,12,10,1869,1867,1864,1861,21,1880,19,1877,1874,1871,28,1888,25,1886,22,1883,982,980,977,974,32,30,991,989,987,984,34,995,994,992,2151,2150,2147,2146,2144,356,355,354,2149,2139,2138,2136,2134,1359,343,341,338,2143,335,2141,348,347,346,1376,1374,2124,2123,2121,2119,1326,2116,1324,310,308,305,2131,302,2129,298,2127,320,318,316,313,2133,322,321,1355,1353,1351,1357,2092,2091,2089,2087,1276,2084,1274,2081,1271,259,2102,256,2100,252,2098,2095,272,269,2108,266,2106,281,279,277,1317,1315,1313,1310,282,1321,1319,2039,2037,2035,2032,1203,2029,1200,1197,207,2053,205,2051,201,2049,2046,2043,220,218,2064,215,2062,211,2059,228,226,223,2069,1259,1257,1254,232,1251,230,1267,1265,1263,2316,2315,2312,2311,2309,2314,2304,2303,2301,2299,1593,2308,2306,590,2288,2287,2285,2283,1578,2280,1577,2295,2293,2291,579,577,574,571,2298,582,581,1592,2263,2262,2260,2258,1545,2255,1544,2252,1541,2273,2271,2269,2266,1550,535,532,2279,528,2277,546,543,549,1575,1573,2224,2222,2220,1486,2217,1485,2214,1482,1479,2238,2236,2234,2231,1496,2228,1492,480,477,2248,473,2246,469,2243,490,487,2251,497,1537,1535,1532,2477,2476,2474,2479,2469,2468,2466,2464,1730,2473,2471,2453,2452,2450,2448,1729,2445,1728,2460,2458,2456,2463,805,804,2428,2427,2425,2423,1725,2420,1724,2417,1722,2438,2436,2434,2431,1727,2444,2442,793,791,788,795,2388,2386,2384,1697,2381,1696,2378,1694,1692,2402,2400,2398,2395,1703,2392,1701,2412,2410,2407,751,748,744,2416,759,757,1807,2620,2618,1806,1805,2611,2609,2607,2614,1802,1801,1799,2594,2592,2590,2587,1804,2600,2598,1794,1793,1791,1789,2564,2562,2560,2557,1798,2554,1796,2574,2572,2569,2578,1847,1846,2722,1843,1842,1840,1845,2716,2714,1835,1834,1832,1830,1839,1837,2700,2698,2695,2704,1817,1811,1810,897,862,1777,829,826,838,1760,1758,808,2481,1741,1740,1738,1743,2624,1818,2726,2776,782,740,737,1715,686,679,695,1682,1680,639,628,2339,647,644,1645,1643,1640,1648,602,600,597,595,2320,593,2318,609,607,604,1611,1610,1608,1606,613,1615,1613,2328,926,924,892,886,899,857,850,2505,1778,824,823,821,819,2488,818,2486,833,831,828,840,1761,1759,2649,2632,2630,2746,2734,2732,2782,2781,570,567,1587,531,527,523,540,1566,1564,476,467,463,2240,486,483,1524,1521,1518,1529,411,403,2192,399,2189,423,416,1462,1457,1454,428,1468,1465,2210,366,363,2158,360,2156,357,2153,376,373,370,2163,1410,1409,1407,1405,382,1402,380,1417,1415,1412,1421,2175,2174,777,774,771,784,732,725,722,2404,743,1716,676,674,668,2363,665,2360,685,1684,1681,626,624,622,2335,620,2333,617,2330,641,635,649,1646,1644,1642,2566,928,925,2530,2527,894,891,888,2501,2499,2496,858,856,854,851,1779,2692,2668,2665,2645,2643,2640,2651,2768,2759,2757,2744,2743,2741,2748,352,1382,340,337,333,1371,1369,307,300,296,2126,315,312,1347,1342,1350,261,258,250,2097,246,2094,271,268,264,1306,1301,1298,276,1312,1309,2115,203,2048,195,2045,191,2041,213,209,2056,1246,1244,1238,225,1234,222,1256,1253,1249,1262,2080,2079,154,1997,150,1995,147,1992,1989,163,160,2004,156,2001,1175,1174,1172,1170,1167,170,1164,167,1185,1183,1180,1177,174,1190,1188,2025,2024,2022,587,586,564,559,556,2290,573,1588,520,518,512,2268,508,2265,530,1568,1565,461,457,2233,450,2230,446,2226,479,471,489,1526,1523,1520,397,395,2185,392,2183,389,2180,2177,410,2194,402,422,1463,1461,1459,1456,1470,2455,799,2433,2430,779,776,773,2397,2394,2390,734,728,724,746,1717,2356,2354,2351,2348,1658,677,675,673,670,667,688,1685,1683,2606,2589,2586,2559,2556,2552,927,2523,2521,2518,2515,1784,2532,895,893,890,2718,2709,2707,2689,2687,2684,2663,2662,2660,2658,1825,2667,2769,1852,2760,2758,142,141,1139,1138,134,132,129,126,1982,1129,1128,1126,1131,113,111,108,105,1972,101,1970,120,118,115,1109,1108,1106,1104,123,1113,1111,82,79,1951,75,1949,72,1946,92,89,86,1956,1077,1076,1074,1072,98,1069,96,1084,1082,1079,1088,1968,1967,48,45,1916,42,1914,39,1911,1908,60,57,54,1923,50,1920,1031,1030,1028,1026,67,1023,65,1020,62,1041,1039,1036,1033,69,1046,1044,1944,1943,1941,11,9,1868,7,1865,1862,1859,20,1878,16,1875,13,1872,970,968,966,963,29,960,26,23,983,981,978,975,33,971,31,990,988,985,1906,1904,1902,993,351,2145,1383,331,330,328,326,2137,323,2135,339,1372,1370,294,293,291,289,2122,286,2120,283,2117,309,303,317,1348,1346,1344,245,244,242,2090,239,2088,236,2085,2082,260,2099,249,270,1307,1305,1303,1300,1314,189,2038,186,2036,183,2033,2030,2026,206,198,2047,194,216,1247,1245,1243,1240,227,1237,1255,2310,2302,2300,2286,2284,2281,565,563,561,558,575,1589,2261,2259,2256,2253,1542,521,519,517,514,2270,511,533,1569,1567,2223,2221,2218,2215,1483,2211,1480,459,456,453,2232,449,474,491,1527,1525,1522,2475,2467,2465,2451,2449,2446,801,800,2426,2424,2421,2418,1723,2435,780,778,775,2387,2385,2382,2379,1695,2375,1693,2396,735,733,730,727,749,1718,2616,2615,2604,2603,2601,2584,2583,2581,2579,1800,2591,2550,2549,2547,2545,1792,2542,1790,2558,929,2719,1841,2710,2708,1833,1831,2690,2688,2686,1815,1809,1808,1774,1756,1754,1737,1736,1734,1739,1816,1711,1676,1674,633,629,1638,1636,1633,1641,598,1605,1604,1602,1600,605,1609,1607,2327,887,853,1775,822,820,1757,1755,1584,524,1560,1558,468,464,1514,1511,1508,1519,408,404,400,1452,1447,1444,417,1458,1455,2208,364,361,358,2154,1401,1400,1398,1396,374,1393,371,1408,1406,1403,1413,2173,2172,772,726,723,1712,672,669,666,682,1678,1675,625,623,621,618,2331,636,632,1639,1637,1635,920,918,884,880,889,849,848,847,846,2497,855,852,1776,2641,2742,2787,1380,334,1367,1365,301,297,1340,1338,1335,1343,255,251,247,1296,1291,1288,265,1302,1299,2113,204,196,192,2042,1232,1230,1224,214,1220,210,1242,1239,1235,1250,2077,2075,151,148,1993,144,1990,1163,1162,1160,1158,1155,161,1152,157,1173,1171,1168,1165,168,1181,1178,2021,2020,2018,2023,585,560,557,1585,516,509,1562,1559,458,447,2227,472,1516,1513,1510,398,396,393,390,2181,386,2178,407,1453,1451,1449,1446,420,1460,2209,769,764,720,712,2391,729,1713,664,663,661,659,2352,656,2349,671,1679,1677,2553,922,919,2519,2516,885,883,881,2685,2661,2659,2767,2756,2755,140,1137,1136,130,127,1125,1124,1122,1127,109,106,102,1103,1102,1100,1098,116,1107,1105,1980,80,76,73,1947,1068,1067,1065,1063,90,1060,87,1075,1073,1070,1080,1966,1965,46,43,40,1912,36,1909,1019,1018,1016,1014,58,1011,55,1008,51,1029,1027,1024,1021,63,1037,1034,1940,1939,1937,1942,8,1866,4,1863,1,1860,956,954,952,949,946,17,14,969,967,964,961,27,957,24,979,976,972,1901,1900,1898,1896,986,1905,1903,350,349,1381,329,327,324,1368,1366,292,290,287,284,2118,304,1341,1339,1337,1345,243,240,237,2086,233,2083,254,1297,1295,1293,1290,1304,2114,190,187,184,2034,180,2031,177,2027,199,1233,1231,1229,1226,217,1223,1241,2078,2076,584,555,554,552,550,2282,562,1586,507,506,504,502,2257,499,2254,515,1563,1561,445,443,441,2219,438,2216,435,2212,460,454,475,1517,1515,1512,2447,798,797,2422,2419,770,768,766,2383,2380,2376,721,719,717,714,731,1714,2602,2582,2580,2548,2546,2543,923,921,2717,2706,2705,2683,2682,2680,1771,1752,1750,1733,1732,1731,1735,1814,1707,1670,1668,1631,1629,1626,1634,1599,1598,1596,1594,1603,1601,2326,1772,1753,1751,1581,1554,1552,1504,1501,1498,1509,1442,1437,1434,401,1448,1445,2206,1392,1391,1389,1387,1384,359,1399,1397,1394,1404,2171,2170,1708,1672,1669,619,1632,1630,1628,1773,1378,1363,1361,1333,1328,1336,1286,1281,1278,248,1292,1289,2111,1218,1216,1210,197,1206,193,1228,1225,1221,1236,2073,2071,1151,1150,1148,1146,152,1143,149,1140,145,1161,1159,1156,1153,158,1169,1166,2017,2016,2014,2019,1582,510,1556,1553,452,448,1506,1500,394,391,387,1443,1441,1439,1436,1450,2207,765,716,713,1709,662,660,657,1673,1671,916,914,879,878,877,882,1135,1134,1121,1120,1118,1123,1097,1096,1094,1092,103,1101,1099,1979,1059,1058,1056,1054,77,1051,74,1066,1064,1061,1071,1964,1963,1007,1006,1004,1002,999,41,996,37,1017,1015,1012,1009,52,1025,1022,1936,1935,1933,1938,942,940,938,935,932,5,2,955,953,950,947,18,943,15,965,962,958,1895,1894,1892,1890,973,1899,1897,1379,325,1364,1362,288,285,1334,1332,1330,241,238,234,1287,1285,1283,1280,1294,2112,188,185,181,178,2028,1219,1217,1215,1212,200,1209,1227,2074,2072,583,553,551,1583,505,503,500,513,1557,1555,444,442,439,436,2213,455,451,1507,1505,1502,796,763,762,760,767,711,710,708,706,2377,718,715,1710,2544,917,915,2681,1627,1597,1595,2325,1769,1749,1747,1499,1438,1435,2204,1390,1388,1385,1395,2169,2167,1704,1665,1662,1625,1623,1620,1770,1329,1282,1279,2109,1214,1207,1222,2068,2065,1149,1147,1144,1141,146,1157,1154,2013,2011,2008,2015,1579,1549,1546,1495,1487,1433,1431,1428,1425,388,1440,2205,1705,658,1667,1664,1119,1095,1093,1978,1057,1055,1052,1062,1962,1960,1005,1003,1e3,997,38,1013,1010,1932,1930,1927,1934,941,939,936,933,6,930,3,951,948,944,1889,1887,1884,1881,959,1893,1891,35,1377,1360,1358,1327,1325,1322,1331,1277,1275,1272,1269,235,1284,2110,1205,1204,1201,1198,182,1195,179,1213,2070,2067,1580,501,1551,1548,440,437,1497,1494,1490,1503,761,709,707,1706,913,912,2198,1386,2164,2161,1621,1766,2103,1208,2058,2054,1145,1142,2005,2002,1999,2009,1488,1429,1426,2200,1698,1659,1656,1975,1053,1957,1954,1001,998,1924,1921,1918,1928,937,934,931,1879,1876,1873,1870,945,1885,1882,1323,1273,1270,2105,1202,1199,1196,1211,2061,2057,1576,1543,1540,1484,1481,1478,1491,1700]);class Pi{constructor(e,t){this.bits=e,this.points=t}getBits(){return this.bits}getPoints(){return this.points}}class ne{static detectMultiple(e,t,n){let r=e.getBlackMatrix(),i=ne.detect(n,r);return i.length||(r=r.clone(),r.rotate180(),i=ne.detect(n,r)),new Pi(r,i)}static detect(e,t){const n=new Array;let r=0,i=0,s=!1;for(;r<t.getHeight();){const o=ne.findVertices(t,r,i);if(o[0]==null&&o[3]==null){if(!s)break;s=!1,i=0;for(const a of n)a[1]!=null&&(r=Math.trunc(Math.max(r,a[1].getY()))),a[3]!=null&&(r=Math.max(r,Math.trunc(a[3].getY())));r+=ne.ROW_STEP;continue}if(s=!0,n.push(o),!e)break;o[2]!=null?(i=Math.trunc(o[2].getX()),r=Math.trunc(o[2].getY())):(i=Math.trunc(o[4].getX()),r=Math.trunc(o[4].getY()))}return n}static findVertices(e,t,n){const r=e.getHeight(),i=e.getWidth(),s=new Array(8);return ne.copyToResult(s,ne.findRowsWithPattern(e,r,i,t,n,ne.START_PATTERN),ne.INDEXES_START_PATTERN),s[4]!=null&&(n=Math.trunc(s[4].getX()),t=Math.trunc(s[4].getY())),ne.copyToResult(s,ne.findRowsWithPattern(e,r,i,t,n,ne.STOP_PATTERN),ne.INDEXES_STOP_PATTERN),s}static copyToResult(e,t,n){for(let r=0;r<n.length;r++)e[n[r]]=t[r]}static findRowsWithPattern(e,t,n,r,i,s){const o=new Array(4);let a=!1;const l=new Int32Array(s.length);for(;r<t;r+=ne.ROW_STEP){let x=ne.findGuardPattern(e,i,r,n,!1,s,l);if(x!=null){for(;r>0;){const C=ne.findGuardPattern(e,i,--r,n,!1,s,l);if(C!=null)x=C;else{r++;break}}o[0]=new G(x[0],r),o[1]=new G(x[1],r),a=!0;break}}let f=r+1;if(a){let x=0,C=Int32Array.from([Math.trunc(o[0].getX()),Math.trunc(o[1].getX())]);for(;f<t;f++){const E=ne.findGuardPattern(e,C[0],f,n,!1,s,l);if(E!=null&&Math.abs(C[0]-E[0])<ne.MAX_PATTERN_DRIFT&&Math.abs(C[1]-E[1])<ne.MAX_PATTERN_DRIFT)C=E,x=0;else{if(x>ne.SKIPPED_ROW_COUNT_MAX)break;x++}}f-=x+1,o[2]=new G(C[0],f),o[3]=new G(C[1],f)}return f-r<ne.BARCODE_MIN_HEIGHT&&he.fill(o,null),o}static findGuardPattern(e,t,n,r,i,s,o){he.fillWithin(o,0,o.length,0);let a=t,l=0;for(;e.get(a,n)&&a>0&&l++<ne.MAX_PIXEL_DRIFT;)a--;let f=a,x=0,C=s.length;for(let E=i;f<r;f++)if(e.get(f,n)!==E)o[x]++;else{if(x===C-1){if(ne.patternMatchVariance(o,s,ne.MAX_INDIVIDUAL_VARIANCE)<ne.MAX_AVG_VARIANCE)return new Int32Array([a,f]);a+=o[0]+o[1],W.arraycopy(o,2,o,0,x-1),o[x-1]=0,o[x]=0,x--}else x++;o[x]=1,E=!E}return x===C-1&&ne.patternMatchVariance(o,s,ne.MAX_INDIVIDUAL_VARIANCE)<ne.MAX_AVG_VARIANCE?new Int32Array([a,f-1]):null}static patternMatchVariance(e,t,n){let r=e.length,i=0,s=0;for(let l=0;l<r;l++)i+=e[l],s+=t[l];if(i<s)return 1/0;let o=i/s;n*=o;let a=0;for(let l=0;l<r;l++){let f=e[l],x=t[l]*o,C=f>x?f-x:x-f;if(C>n)return 1/0;a+=C}return a/i}}ne.INDEXES_START_PATTERN=Int32Array.from([0,4,1,5]),ne.INDEXES_STOP_PATTERN=Int32Array.from([6,2,7,3]),ne.MAX_AVG_VARIANCE=.42,ne.MAX_INDIVIDUAL_VARIANCE=.8,ne.START_PATTERN=Int32Array.from([8,1,1,1,1,1,1,3]),ne.STOP_PATTERN=Int32Array.from([7,1,1,3,1,1,1,2,1]),ne.MAX_PIXEL_DRIFT=3,ne.MAX_PATTERN_DRIFT=5,ne.SKIPPED_ROW_COUNT_MAX=25,ne.ROW_STEP=5,ne.BARCODE_MIN_HEIGHT=10;class Ze{constructor(e,t){if(t.length===0)throw new M;this.field=e;let n=t.length;if(n>1&&t[0]===0){let r=1;for(;r<n&&t[r]===0;)r++;r===n?this.coefficients=new Int32Array([0]):(this.coefficients=new Int32Array(n-r),W.arraycopy(t,r,this.coefficients,0,this.coefficients.length))}else this.coefficients=t}getCoefficients(){return this.coefficients}getDegree(){return this.coefficients.length-1}isZero(){return this.coefficients[0]===0}getCoefficient(e){return this.coefficients[this.coefficients.length-1-e]}evaluateAt(e){if(e===0)return this.getCoefficient(0);if(e===1){let r=0;for(let i of this.coefficients)r=this.field.add(r,i);return r}let t=this.coefficients[0],n=this.coefficients.length;for(let r=1;r<n;r++)t=this.field.add(this.field.multiply(e,t),this.coefficients[r]);return t}add(e){if(!this.field.equals(e.field))throw new M("ModulusPolys do not have same ModulusGF field");if(this.isZero())return e;if(e.isZero())return this;let t=this.coefficients,n=e.coefficients;if(t.length>n.length){let s=t;t=n,n=s}let r=new Int32Array(n.length),i=n.length-t.length;W.arraycopy(n,0,r,0,i);for(let s=i;s<n.length;s++)r[s]=this.field.add(t[s-i],n[s]);return new Ze(this.field,r)}subtract(e){if(!this.field.equals(e.field))throw new M("ModulusPolys do not have same ModulusGF field");return e.isZero()?this:this.add(e.negative())}multiply(e){return e instanceof Ze?this.multiplyOther(e):this.multiplyScalar(e)}multiplyOther(e){if(!this.field.equals(e.field))throw new M("ModulusPolys do not have same ModulusGF field");if(this.isZero()||e.isZero())return new Ze(this.field,new Int32Array([0]));let t=this.coefficients,n=t.length,r=e.coefficients,i=r.length,s=new Int32Array(n+i-1);for(let o=0;o<n;o++){let a=t[o];for(let l=0;l<i;l++)s[o+l]=this.field.add(s[o+l],this.field.multiply(a,r[l]))}return new Ze(this.field,s)}negative(){let e=this.coefficients.length,t=new Int32Array(e);for(let n=0;n<e;n++)t[n]=this.field.subtract(0,this.coefficients[n]);return new Ze(this.field,t)}multiplyScalar(e){if(e===0)return new Ze(this.field,new Int32Array([0]));if(e===1)return this;let t=this.coefficients.length,n=new Int32Array(t);for(let r=0;r<t;r++)n[r]=this.field.multiply(this.coefficients[r],e);return new Ze(this.field,n)}multiplyByMonomial(e,t){if(e<0)throw new M;if(t===0)return new Ze(this.field,new Int32Array([0]));let n=this.coefficients.length,r=new Int32Array(n+e);for(let i=0;i<n;i++)r[i]=this.field.multiply(this.coefficients[i],t);return new Ze(this.field,r)}toString(){let e=new me;for(let t=this.getDegree();t>=0;t--){let n=this.getCoefficient(t);n!==0&&(n<0?(e.append(" - "),n=-n):e.length()>0&&e.append(" + "),(t===0||n!==1)&&e.append(n),t!==0&&(t===1?e.append("x"):(e.append("x^"),e.append(t))))}return e.toString()}}class Fi{add(e,t){return(e+t)%this.modulus}subtract(e,t){return(this.modulus+e-t)%this.modulus}exp(e){return this.expTable[e]}log(e){if(e===0)throw new M;return this.logTable[e]}inverse(e){if(e===0)throw new Bn;return this.expTable[this.modulus-this.logTable[e]-1]}multiply(e,t){return e===0||t===0?0:this.expTable[(this.logTable[e]+this.logTable[t])%(this.modulus-1)]}getSize(){return this.modulus}equals(e){return e===this}}class xr extends Fi{constructor(e,t){super(),this.modulus=e,this.expTable=new Int32Array(e),this.logTable=new Int32Array(e);let n=1;for(let r=0;r<e;r++)this.expTable[r]=n,n=n*t%e;for(let r=0;r<e-1;r++)this.logTable[this.expTable[r]]=r;this.zero=new Ze(this,new Int32Array([0])),this.one=new Ze(this,new Int32Array([1]))}getZero(){return this.zero}getOne(){return this.one}buildMonomial(e,t){if(e<0)throw new M;if(t===0)return this.zero;let n=new Int32Array(e+1);return n[0]=t,new Ze(this,n)}}xr.PDF417_GF=new xr(K.NUMBER_OF_CODEWORDS,3);class Ur{constructor(){this.field=xr.PDF417_GF}decode(e,t,n){let r=new Ze(this.field,e),i=new Int32Array(t),s=!1;for(let I=t;I>0;I--){let y=r.evaluateAt(this.field.exp(I));i[t-I]=y,y!==0&&(s=!0)}if(!s)return 0;let o=this.field.getOne();if(n!=null)for(const I of n){let y=this.field.exp(e.length-1-I),b=new Ze(this.field,new Int32Array([this.field.subtract(0,y),1]));o=o.multiply(b)}let a=new Ze(this.field,i),l=this.runEuclideanAlgorithm(this.field.buildMonomial(t,1),a,t),f=l[0],x=l[1],C=this.findErrorLocations(f),E=this.findErrorMagnitudes(x,f,C);for(let I=0;I<C.length;I++){let y=e.length-1-this.field.log(C[I]);if(y<0)throw P.getChecksumInstance();e[y]=this.field.subtract(e[y],E[I])}return C.length}runEuclideanAlgorithm(e,t,n){if(e.getDegree()<t.getDegree()){let C=e;e=t,t=C}let r=e,i=t,s=this.field.getZero(),o=this.field.getOne();for(;i.getDegree()>=Math.round(n/2);){let C=r,E=s;if(r=i,s=o,r.isZero())throw P.getChecksumInstance();i=C;let I=this.field.getZero(),y=r.getCoefficient(r.getDegree()),b=this.field.inverse(y);for(;i.getDegree()>=r.getDegree()&&!i.isZero();){let D=i.getDegree()-r.getDegree(),F=this.field.multiply(i.getCoefficient(i.getDegree()),b);I=I.add(this.field.buildMonomial(D,F)),i=i.subtract(r.multiplyByMonomial(D,F))}o=I.multiply(s).subtract(E).negative()}let a=o.getCoefficient(0);if(a===0)throw P.getChecksumInstance();let l=this.field.inverse(a),f=o.multiply(l),x=i.multiply(l);return[f,x]}findErrorLocations(e){let t=e.getDegree(),n=new Int32Array(t),r=0;for(let i=1;i<this.field.getSize()&&r<t;i++)e.evaluateAt(i)===0&&(n[r]=this.field.inverse(i),r++);if(r!==t)throw P.getChecksumInstance();return n}findErrorMagnitudes(e,t,n){let r=t.getDegree(),i=new Int32Array(r);for(let l=1;l<=r;l++)i[r-l]=this.field.multiply(l,t.getCoefficient(l));let s=new Ze(this.field,i),o=n.length,a=new Int32Array(o);for(let l=0;l<o;l++){let f=this.field.inverse(n[l]),x=this.field.subtract(0,e.evaluateAt(f)),C=this.field.inverse(s.evaluateAt(f));a[l]=this.field.multiply(x,C)}return a}}class $t{constructor(e,t,n,r,i){e instanceof $t?this.constructor_2(e):this.constructor_1(e,t,n,r,i)}constructor_1(e,t,n,r,i){const s=t==null||n==null,o=r==null||i==null;if(s&&o)throw new v;s?(t=new G(0,r.getY()),n=new G(0,i.getY())):o&&(r=new G(e.getWidth()-1,t.getY()),i=new G(e.getWidth()-1,n.getY())),this.image=e,this.topLeft=t,this.bottomLeft=n,this.topRight=r,this.bottomRight=i,this.minX=Math.trunc(Math.min(t.getX(),n.getX())),this.maxX=Math.trunc(Math.max(r.getX(),i.getX())),this.minY=Math.trunc(Math.min(t.getY(),r.getY())),this.maxY=Math.trunc(Math.max(n.getY(),i.getY()))}constructor_2(e){this.image=e.image,this.topLeft=e.getTopLeft(),this.bottomLeft=e.getBottomLeft(),this.topRight=e.getTopRight(),this.bottomRight=e.getBottomRight(),this.minX=e.getMinX(),this.maxX=e.getMaxX(),this.minY=e.getMinY(),this.maxY=e.getMaxY()}static merge(e,t){return e==null?t:t==null?e:new $t(e.image,e.topLeft,e.bottomLeft,t.topRight,t.bottomRight)}addMissingRows(e,t,n){let r=this.topLeft,i=this.bottomLeft,s=this.topRight,o=this.bottomRight;if(e>0){let a=n?this.topLeft:this.topRight,l=Math.trunc(a.getY()-e);l<0&&(l=0);let f=new G(a.getX(),l);n?r=f:s=f}if(t>0){let a=n?this.bottomLeft:this.bottomRight,l=Math.trunc(a.getY()+t);l>=this.image.getHeight()&&(l=this.image.getHeight()-1);let f=new G(a.getX(),l);n?i=f:o=f}return new $t(this.image,r,i,s,o)}getMinX(){return this.minX}getMaxX(){return this.maxX}getMinY(){return this.minY}getMaxY(){return this.maxY}getTopLeft(){return this.topLeft}getTopRight(){return this.topRight}getBottomLeft(){return this.bottomLeft}getBottomRight(){return this.bottomRight}}class ki{constructor(e,t,n,r){this.columnCount=e,this.errorCorrectionLevel=r,this.rowCountUpperPart=t,this.rowCountLowerPart=n,this.rowCount=t+n}getColumnCount(){return this.columnCount}getErrorCorrectionLevel(){return this.errorCorrectionLevel}getRowCount(){return this.rowCount}getRowCountUpperPart(){return this.rowCountUpperPart}getRowCountLowerPart(){return this.rowCountLowerPart}}class Tn{constructor(){this.buffer=""}static form(e,t){let n=-1;function r(s,o,a,l,f,x){if(s==="%%")return"%";if(t[++n]===void 0)return;s=l?parseInt(l.substr(1)):void 0;let C=f?parseInt(f.substr(1)):void 0,E;switch(x){case"s":E=t[n];break;case"c":E=t[n][0];break;case"f":E=parseFloat(t[n]).toFixed(s);break;case"p":E=parseFloat(t[n]).toPrecision(s);break;case"e":E=parseFloat(t[n]).toExponential(s);break;case"x":E=parseInt(t[n]).toString(C||16);break;case"d":E=parseFloat(parseInt(t[n],C||10).toPrecision(s)).toFixed(0);break}E=typeof E=="object"?JSON.stringify(E):(+E).toString(C);let I=parseInt(a),y=a&&a[0]+""=="0"?"0":" ";for(;E.length<I;)E=o!==void 0?E+y:y+E;return E}let i=/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;return e.replace(i,r)}format(e,...t){this.buffer+=Tn.form(e,t)}toString(){return this.buffer}}class Sn{constructor(e){this.boundingBox=new $t(e),this.codewords=new Array(e.getMaxY()-e.getMinY()+1)}getCodewordNearby(e){let t=this.getCodeword(e);if(t!=null)return t;for(let n=1;n<Sn.MAX_NEARBY_DISTANCE;n++){let r=this.imageRowToCodewordIndex(e)-n;if(r>=0&&(t=this.codewords[r],t!=null)||(r=this.imageRowToCodewordIndex(e)+n,r<this.codewords.length&&(t=this.codewords[r],t!=null)))return t}return null}imageRowToCodewordIndex(e){return e-this.boundingBox.getMinY()}setCodeword(e,t){this.codewords[this.imageRowToCodewordIndex(e)]=t}getCodeword(e){return this.codewords[this.imageRowToCodewordIndex(e)]}getBoundingBox(){return this.boundingBox}getCodewords(){return this.codewords}toString(){const e=new Tn;let t=0;for(const n of this.codewords){if(n==null){e.format("%3d:    |   %n",t++);continue}e.format("%3d: %3d|%3d%n",t++,n.getRowNumber(),n.getValue())}return e.toString()}}Sn.MAX_NEARBY_DISTANCE=5;class bn{constructor(){this.values=new Map}setValue(e){e=Math.trunc(e);let t=this.values.get(e);t==null&&(t=0),t++,this.values.set(e,t)}getValue(){let e=-1,t=new Array;for(const[n,r]of this.values.entries()){const i={getKey:()=>n,getValue:()=>r};i.getValue()>e?(e=i.getValue(),t=[],t.push(i.getKey())):i.getValue()===e&&t.push(i.getKey())}return K.toIntArray(t)}getConfidence(e){return this.values.get(e)}}class Vr extends Sn{constructor(e,t){super(e),this._isLeft=t}setRowNumbers(){for(let e of this.getCodewords())e!=null&&e.setRowNumberAsRowIndicatorColumn()}adjustCompleteIndicatorColumnRowNumbers(e){let t=this.getCodewords();this.setRowNumbers(),this.removeIncorrectCodewords(t,e);let n=this.getBoundingBox(),r=this._isLeft?n.getTopLeft():n.getTopRight(),i=this._isLeft?n.getBottomLeft():n.getBottomRight(),s=this.imageRowToCodewordIndex(Math.trunc(r.getY())),o=this.imageRowToCodewordIndex(Math.trunc(i.getY())),a=-1,l=1,f=0;for(let x=s;x<o;x++){if(t[x]==null)continue;let C=t[x],E=C.getRowNumber()-a;if(E===0)f++;else if(E===1)l=Math.max(l,f),f=1,a=C.getRowNumber();else if(E<0||C.getRowNumber()>=e.getRowCount()||E>x)t[x]=null;else{let I;l>2?I=(l-2)*E:I=E;let y=I>=x;for(let b=1;b<=I&&!y;b++)y=t[x-b]!=null;y?t[x]=null:(a=C.getRowNumber(),f=1)}}}getRowHeights(){let e=this.getBarcodeMetadata();if(e==null)return null;this.adjustIncompleteIndicatorColumnRowNumbers(e);let t=new Int32Array(e.getRowCount());for(let n of this.getCodewords())if(n!=null){let r=n.getRowNumber();if(r>=t.length)continue;t[r]++}return t}adjustIncompleteIndicatorColumnRowNumbers(e){let t=this.getBoundingBox(),n=this._isLeft?t.getTopLeft():t.getTopRight(),r=this._isLeft?t.getBottomLeft():t.getBottomRight(),i=this.imageRowToCodewordIndex(Math.trunc(n.getY())),s=this.imageRowToCodewordIndex(Math.trunc(r.getY())),o=this.getCodewords(),a=-1;for(let l=i;l<s;l++){if(o[l]==null)continue;let f=o[l];f.setRowNumberAsRowIndicatorColumn();let x=f.getRowNumber()-a;x===0||(x===1?a=f.getRowNumber():f.getRowNumber()>=e.getRowCount()?o[l]=null:a=f.getRowNumber())}}getBarcodeMetadata(){let e=this.getCodewords(),t=new bn,n=new bn,r=new bn,i=new bn;for(let o of e){if(o==null)continue;o.setRowNumberAsRowIndicatorColumn();let a=o.getValue()%30,l=o.getRowNumber();switch(this._isLeft||(l+=2),l%3){case 0:n.setValue(a*3+1);break;case 1:i.setValue(a/3),r.setValue(a%3);break;case 2:t.setValue(a+1);break}}if(t.getValue().length===0||n.getValue().length===0||r.getValue().length===0||i.getValue().length===0||t.getValue()[0]<1||n.getValue()[0]+r.getValue()[0]<K.MIN_ROWS_IN_BARCODE||n.getValue()[0]+r.getValue()[0]>K.MAX_ROWS_IN_BARCODE)return null;let s=new ki(t.getValue()[0],n.getValue()[0],r.getValue()[0],i.getValue()[0]);return this.removeIncorrectCodewords(e,s),s}removeIncorrectCodewords(e,t){for(let n=0;n<e.length;n++){let r=e[n];if(e[n]==null)continue;let i=r.getValue()%30,s=r.getRowNumber();if(s>t.getRowCount()){e[n]=null;continue}switch(this._isLeft||(s+=2),s%3){case 0:i*3+1!==t.getRowCountUpperPart()&&(e[n]=null);break;case 1:(Math.trunc(i/3)!==t.getErrorCorrectionLevel()||i%3!==t.getRowCountLowerPart())&&(e[n]=null);break;case 2:i+1!==t.getColumnCount()&&(e[n]=null);break}}}isLeft(){return this._isLeft}toString(){return"IsLeft: "+this._isLeft+`
`+super.toString()}}class Nn{constructor(e,t){this.ADJUST_ROW_NUMBER_SKIP=2,this.barcodeMetadata=e,this.barcodeColumnCount=e.getColumnCount(),this.boundingBox=t,this.detectionResultColumns=new Array(this.barcodeColumnCount+2)}getDetectionResultColumns(){this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[0]),this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[this.barcodeColumnCount+1]);let e=K.MAX_CODEWORDS_IN_BARCODE,t;do t=e,e=this.adjustRowNumbersAndGetCount();while(e>0&&e<t);return this.detectionResultColumns}adjustIndicatorColumnRowNumbers(e){e!=null&&e.adjustCompleteIndicatorColumnRowNumbers(this.barcodeMetadata)}adjustRowNumbersAndGetCount(){let e=this.adjustRowNumbersByRow();if(e===0)return 0;for(let t=1;t<this.barcodeColumnCount+1;t++){let n=this.detectionResultColumns[t].getCodewords();for(let r=0;r<n.length;r++)n[r]!=null&&(n[r].hasValidRowNumber()||this.adjustRowNumbers(t,r,n))}return e}adjustRowNumbersByRow(){return this.adjustRowNumbersFromBothRI(),this.adjustRowNumbersFromLRI()+this.adjustRowNumbersFromRRI()}adjustRowNumbersFromBothRI(){if(this.detectionResultColumns[0]==null||this.detectionResultColumns[this.barcodeColumnCount+1]==null)return;let e=this.detectionResultColumns[0].getCodewords(),t=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords();for(let n=0;n<e.length;n++)if(e[n]!=null&&t[n]!=null&&e[n].getRowNumber()===t[n].getRowNumber())for(let r=1;r<=this.barcodeColumnCount;r++){let i=this.detectionResultColumns[r].getCodewords()[n];i!=null&&(i.setRowNumber(e[n].getRowNumber()),i.hasValidRowNumber()||(this.detectionResultColumns[r].getCodewords()[n]=null))}}adjustRowNumbersFromRRI(){if(this.detectionResultColumns[this.barcodeColumnCount+1]==null)return 0;let e=0,t=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords();for(let n=0;n<t.length;n++){if(t[n]==null)continue;let r=t[n].getRowNumber(),i=0;for(let s=this.barcodeColumnCount+1;s>0&&i<this.ADJUST_ROW_NUMBER_SKIP;s--){let o=this.detectionResultColumns[s].getCodewords()[n];o!=null&&(i=Nn.adjustRowNumberIfValid(r,i,o),o.hasValidRowNumber()||e++)}}return e}adjustRowNumbersFromLRI(){if(this.detectionResultColumns[0]==null)return 0;let e=0,t=this.detectionResultColumns[0].getCodewords();for(let n=0;n<t.length;n++){if(t[n]==null)continue;let r=t[n].getRowNumber(),i=0;for(let s=1;s<this.barcodeColumnCount+1&&i<this.ADJUST_ROW_NUMBER_SKIP;s++){let o=this.detectionResultColumns[s].getCodewords()[n];o!=null&&(i=Nn.adjustRowNumberIfValid(r,i,o),o.hasValidRowNumber()||e++)}}return e}static adjustRowNumberIfValid(e,t,n){return n==null||n.hasValidRowNumber()||(n.isValidRowNumber(e)?(n.setRowNumber(e),t=0):++t),t}adjustRowNumbers(e,t,n){if(!this.detectionResultColumns[e-1])return;let r=n[t],i=this.detectionResultColumns[e-1].getCodewords(),s=i;this.detectionResultColumns[e+1]!=null&&(s=this.detectionResultColumns[e+1].getCodewords());let o=new Array(14);o[2]=i[t],o[3]=s[t],t>0&&(o[0]=n[t-1],o[4]=i[t-1],o[5]=s[t-1]),t>1&&(o[8]=n[t-2],o[10]=i[t-2],o[11]=s[t-2]),t<n.length-1&&(o[1]=n[t+1],o[6]=i[t+1],o[7]=s[t+1]),t<n.length-2&&(o[9]=n[t+2],o[12]=i[t+2],o[13]=s[t+2]);for(let a of o)if(Nn.adjustRowNumber(r,a))return}static adjustRowNumber(e,t){return t==null?!1:t.hasValidRowNumber()&&t.getBucket()===e.getBucket()?(e.setRowNumber(t.getRowNumber()),!0):!1}getBarcodeColumnCount(){return this.barcodeColumnCount}getBarcodeRowCount(){return this.barcodeMetadata.getRowCount()}getBarcodeECLevel(){return this.barcodeMetadata.getErrorCorrectionLevel()}setBoundingBox(e){this.boundingBox=e}getBoundingBox(){return this.boundingBox}setDetectionResultColumn(e,t){this.detectionResultColumns[e]=t}getDetectionResultColumn(e){return this.detectionResultColumns[e]}toString(){let e=this.detectionResultColumns[0];e==null&&(e=this.detectionResultColumns[this.barcodeColumnCount+1]);let t=new Tn;for(let n=0;n<e.getCodewords().length;n++){t.format("CW %3d:",n);for(let r=0;r<this.barcodeColumnCount+2;r++){if(this.detectionResultColumns[r]==null){t.format("    |   ");continue}let i=this.detectionResultColumns[r].getCodewords()[n];if(i==null){t.format("    |   ");continue}t.format(" %3d|%3d",i.getRowNumber(),i.getValue())}t.format("%n")}return t.toString()}}class Mn{constructor(e,t,n,r){this.rowNumber=Mn.BARCODE_ROW_UNKNOWN,this.startX=Math.trunc(e),this.endX=Math.trunc(t),this.bucket=Math.trunc(n),this.value=Math.trunc(r)}hasValidRowNumber(){return this.isValidRowNumber(this.rowNumber)}isValidRowNumber(e){return e!==Mn.BARCODE_ROW_UNKNOWN&&this.bucket===e%3*3}setRowNumberAsRowIndicatorColumn(){this.rowNumber=Math.trunc(Math.trunc(this.value/30)*3+Math.trunc(this.bucket/3))}getWidth(){return this.endX-this.startX}getStartX(){return this.startX}getEndX(){return this.endX}getBucket(){return this.bucket}getValue(){return this.value}getRowNumber(){return this.rowNumber}setRowNumber(e){this.rowNumber=e}toString(){return this.rowNumber+"|"+this.value}}Mn.BARCODE_ROW_UNKNOWN=-1;class ct{static initialize(){for(let e=0;e<K.SYMBOL_TABLE.length;e++){let t=K.SYMBOL_TABLE[e],n=t&1;for(let r=0;r<K.BARS_IN_MODULE;r++){let i=0;for(;(t&1)===n;)i+=1,t>>=1;n=t&1,ct.RATIOS_TABLE[e]||(ct.RATIOS_TABLE[e]=new Array(K.BARS_IN_MODULE)),ct.RATIOS_TABLE[e][K.BARS_IN_MODULE-r-1]=Math.fround(i/K.MODULES_IN_CODEWORD)}}this.bSymbolTableReady=!0}static getDecodedValue(e){let t=ct.getDecodedCodewordValue(ct.sampleBitCounts(e));return t!==-1?t:ct.getClosestDecodedValue(e)}static sampleBitCounts(e){let t=ae.sum(e),n=new Int32Array(K.BARS_IN_MODULE),r=0,i=0;for(let s=0;s<K.MODULES_IN_CODEWORD;s++){let o=t/(2*K.MODULES_IN_CODEWORD)+s*t/K.MODULES_IN_CODEWORD;i+e[r]<=o&&(i+=e[r],r++),n[r]++}return n}static getDecodedCodewordValue(e){let t=ct.getBitValue(e);return K.getCodeword(t)===-1?-1:t}static getBitValue(e){let t=0;for(let n=0;n<e.length;n++)for(let r=0;r<e[n];r++)t=t<<1|(n%2===0?1:0);return Math.trunc(t)}static getClosestDecodedValue(e){let t=ae.sum(e),n=new Array(K.BARS_IN_MODULE);if(t>1)for(let s=0;s<n.length;s++)n[s]=Math.fround(e[s]/t);let r=Pn.MAX_VALUE,i=-1;this.bSymbolTableReady||ct.initialize();for(let s=0;s<ct.RATIOS_TABLE.length;s++){let o=0,a=ct.RATIOS_TABLE[s];for(let l=0;l<K.BARS_IN_MODULE;l++){let f=Math.fround(a[l]-n[l]);if(o+=Math.fround(f*f),o>=r)break}o<r&&(r=o,i=K.SYMBOL_TABLE[s])}return i}}ct.bSymbolTableReady=!1,ct.RATIOS_TABLE=new Array(K.SYMBOL_TABLE.length).map(g=>new Array(K.BARS_IN_MODULE));class Hr{constructor(){this.segmentCount=-1,this.fileSize=-1,this.timestamp=-1,this.checksum=-1}getSegmentIndex(){return this.segmentIndex}setSegmentIndex(e){this.segmentIndex=e}getFileId(){return this.fileId}setFileId(e){this.fileId=e}getOptionalData(){return this.optionalData}setOptionalData(e){this.optionalData=e}isLastSegment(){return this.lastSegment}setLastSegment(e){this.lastSegment=e}getSegmentCount(){return this.segmentCount}setSegmentCount(e){this.segmentCount=e}getSender(){return this.sender||null}setSender(e){this.sender=e}getAddressee(){return this.addressee||null}setAddressee(e){this.addressee=e}getFileName(){return this.fileName}setFileName(e){this.fileName=e}getFileSize(){return this.fileSize}setFileSize(e){this.fileSize=e}getChecksum(){return this.checksum}setChecksum(e){this.checksum=e}getTimestamp(){return this.timestamp}setTimestamp(e){this.timestamp=e}}class zr{static parseLong(e,t=void 0){return parseInt(e,t)}}class jr extends A{}jr.kind="NullPointerException";class Ui{writeBytes(e){this.writeBytesOffset(e,0,e.length)}writeBytesOffset(e,t,n){if(e==null)throw new jr;if(t<0||t>e.length||n<0||t+n>e.length||t+n<0)throw new Se;if(n===0)return;for(let r=0;r<n;r++)this.write(e[t+r])}flush(){}close(){}}class Vi extends A{}class Hi extends Ui{constructor(e=32){if(super(),this.count=0,e<0)throw new M("Negative initial size: "+e);this.buf=new Uint8Array(e)}ensureCapacity(e){e-this.buf.length>0&&this.grow(e)}grow(e){let n=this.buf.length<<1;if(n-e<0&&(n=e),n<0){if(e<0)throw new Vi;n=J.MAX_VALUE}this.buf=he.copyOfUint8Array(this.buf,n)}write(e){this.ensureCapacity(this.count+1),this.buf[this.count]=e,this.count+=1}writeBytesOffset(e,t,n){if(t<0||t>e.length||n<0||t+n-e.length>0)throw new Se;this.ensureCapacity(this.count+n),W.arraycopy(e,t,this.buf,this.count,n),this.count+=n}writeTo(e){e.writeBytesOffset(this.buf,0,this.count)}reset(){this.count=0}toByteArray(){return he.copyOfUint8Array(this.buf,this.count)}size(){return this.count}toString(e){return e?typeof e=="string"?this.toString_string(e):this.toString_number(e):this.toString_void()}toString_void(){return new String(this.buf).toString()}toString_string(e){return new String(this.buf).toString()}toString_number(e){return new String(this.buf).toString()}close(){}}var pe;(function(g){g[g.ALPHA=0]="ALPHA",g[g.LOWER=1]="LOWER",g[g.MIXED=2]="MIXED",g[g.PUNCT=3]="PUNCT",g[g.ALPHA_SHIFT=4]="ALPHA_SHIFT",g[g.PUNCT_SHIFT=5]="PUNCT_SHIFT"})(pe||(pe={}));function Gr(){if(typeof window!="undefined")return window.BigInt||null;if(typeof Dn!="undefined")return Dn.BigInt||null;if(typeof self!="undefined")return self.BigInt||null;throw new Error("Can't search globals for BigInt!")}let jn;function Wt(g){if(typeof jn=="undefined"&&(jn=Gr()),jn===null)throw new Error("BigInt is not supported!");return jn(g)}function zi(){let g=[];g[0]=Wt(1);let e=Wt(900);g[1]=e;for(let t=2;t<16;t++)g[t]=g[t-1]*e;return g}class O{static decode(e,t){let n=new me(""),r=U.ISO8859_1;n.enableDecoding(r);let i=1,s=e[i++],o=new Hr;for(;i<e[0];){switch(s){case O.TEXT_COMPACTION_MODE_LATCH:i=O.textCompaction(e,i,n);break;case O.BYTE_COMPACTION_MODE_LATCH:case O.BYTE_COMPACTION_MODE_LATCH_6:i=O.byteCompaction(s,e,r,i,n);break;case O.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(e[i++]);break;case O.NUMERIC_COMPACTION_MODE_LATCH:i=O.numericCompaction(e,i,n);break;case O.ECI_CHARSET:U.getCharacterSetECIByValue(e[i++]);break;case O.ECI_GENERAL_PURPOSE:i+=2;break;case O.ECI_USER_DEFINED:i++;break;case O.BEGIN_MACRO_PDF417_CONTROL_BLOCK:i=O.decodeMacroBlock(e,i,o);break;case O.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case O.MACRO_PDF417_TERMINATOR:throw new V;default:i--,i=O.textCompaction(e,i,n);break}if(i<e.length)s=e[i++];else throw V.getFormatInstance()}if(n.length()===0)throw V.getFormatInstance();let a=new mn(null,n.toString(),null,t);return a.setOther(o),a}static decodeMacroBlock(e,t,n){if(t+O.NUMBER_OF_SEQUENCE_CODEWORDS>e[0])throw V.getFormatInstance();let r=new Int32Array(O.NUMBER_OF_SEQUENCE_CODEWORDS);for(let o=0;o<O.NUMBER_OF_SEQUENCE_CODEWORDS;o++,t++)r[o]=e[t];n.setSegmentIndex(J.parseInt(O.decodeBase900toBase10(r,O.NUMBER_OF_SEQUENCE_CODEWORDS)));let i=new me;t=O.textCompaction(e,t,i),n.setFileId(i.toString());let s=-1;for(e[t]===O.BEGIN_MACRO_PDF417_OPTIONAL_FIELD&&(s=t+1);t<e[0];)switch(e[t]){case O.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:switch(t++,e[t]){case O.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME:let o=new me;t=O.textCompaction(e,t+1,o),n.setFileName(o.toString());break;case O.MACRO_PDF417_OPTIONAL_FIELD_SENDER:let a=new me;t=O.textCompaction(e,t+1,a),n.setSender(a.toString());break;case O.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE:let l=new me;t=O.textCompaction(e,t+1,l),n.setAddressee(l.toString());break;case O.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT:let f=new me;t=O.numericCompaction(e,t+1,f),n.setSegmentCount(J.parseInt(f.toString()));break;case O.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP:let x=new me;t=O.numericCompaction(e,t+1,x),n.setTimestamp(zr.parseLong(x.toString()));break;case O.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM:let C=new me;t=O.numericCompaction(e,t+1,C),n.setChecksum(J.parseInt(C.toString()));break;case O.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE:let E=new me;t=O.numericCompaction(e,t+1,E),n.setFileSize(zr.parseLong(E.toString()));break;default:throw V.getFormatInstance()}break;case O.MACRO_PDF417_TERMINATOR:t++,n.setLastSegment(!0);break;default:throw V.getFormatInstance()}if(s!==-1){let o=t-s;n.isLastSegment()&&o--,n.setOptionalData(he.copyOfRange(e,s,s+o))}return t}static textCompaction(e,t,n){let r=new Int32Array((e[0]-t)*2),i=new Int32Array((e[0]-t)*2),s=0,o=!1;for(;t<e[0]&&!o;){let a=e[t++];if(a<O.TEXT_COMPACTION_MODE_LATCH)r[s]=a/30,r[s+1]=a%30,s+=2;else switch(a){case O.TEXT_COMPACTION_MODE_LATCH:r[s++]=O.TEXT_COMPACTION_MODE_LATCH;break;case O.BYTE_COMPACTION_MODE_LATCH:case O.BYTE_COMPACTION_MODE_LATCH_6:case O.NUMERIC_COMPACTION_MODE_LATCH:case O.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case O.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case O.MACRO_PDF417_TERMINATOR:t--,o=!0;break;case O.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:r[s]=O.MODE_SHIFT_TO_BYTE_COMPACTION_MODE,a=e[t++],i[s]=a,s++;break}}return O.decodeTextCompaction(r,i,s,n),t}static decodeTextCompaction(e,t,n,r){let i=pe.ALPHA,s=pe.ALPHA,o=0;for(;o<n;){let a=e[o],l="";switch(i){case pe.ALPHA:if(a<26)l=String.fromCharCode(65+a);else switch(a){case 26:l=" ";break;case O.LL:i=pe.LOWER;break;case O.ML:i=pe.MIXED;break;case O.PS:s=i,i=pe.PUNCT_SHIFT;break;case O.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:r.append(t[o]);break;case O.TEXT_COMPACTION_MODE_LATCH:i=pe.ALPHA;break}break;case pe.LOWER:if(a<26)l=String.fromCharCode(97+a);else switch(a){case 26:l=" ";break;case O.AS:s=i,i=pe.ALPHA_SHIFT;break;case O.ML:i=pe.MIXED;break;case O.PS:s=i,i=pe.PUNCT_SHIFT;break;case O.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:r.append(t[o]);break;case O.TEXT_COMPACTION_MODE_LATCH:i=pe.ALPHA;break}break;case pe.MIXED:if(a<O.PL)l=O.MIXED_CHARS[a];else switch(a){case O.PL:i=pe.PUNCT;break;case 26:l=" ";break;case O.LL:i=pe.LOWER;break;case O.AL:i=pe.ALPHA;break;case O.PS:s=i,i=pe.PUNCT_SHIFT;break;case O.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:r.append(t[o]);break;case O.TEXT_COMPACTION_MODE_LATCH:i=pe.ALPHA;break}break;case pe.PUNCT:if(a<O.PAL)l=O.PUNCT_CHARS[a];else switch(a){case O.PAL:i=pe.ALPHA;break;case O.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:r.append(t[o]);break;case O.TEXT_COMPACTION_MODE_LATCH:i=pe.ALPHA;break}break;case pe.ALPHA_SHIFT:if(i=s,a<26)l=String.fromCharCode(65+a);else switch(a){case 26:l=" ";break;case O.TEXT_COMPACTION_MODE_LATCH:i=pe.ALPHA;break}break;case pe.PUNCT_SHIFT:if(i=s,a<O.PAL)l=O.PUNCT_CHARS[a];else switch(a){case O.PAL:i=pe.ALPHA;break;case O.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:r.append(t[o]);break;case O.TEXT_COMPACTION_MODE_LATCH:i=pe.ALPHA;break}break}l!==""&&r.append(l),o++}}static byteCompaction(e,t,n,r,i){let s=new Hi,o=0,a=0,l=!1;switch(e){case O.BYTE_COMPACTION_MODE_LATCH:let f=new Int32Array(6),x=t[r++];for(;r<t[0]&&!l;)switch(f[o++]=x,a=900*a+x,x=t[r++],x){case O.TEXT_COMPACTION_MODE_LATCH:case O.BYTE_COMPACTION_MODE_LATCH:case O.NUMERIC_COMPACTION_MODE_LATCH:case O.BYTE_COMPACTION_MODE_LATCH_6:case O.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case O.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case O.MACRO_PDF417_TERMINATOR:r--,l=!0;break;default:if(o%5===0&&o>0){for(let C=0;C<6;++C)s.write(Number(Wt(a)>>Wt(8*(5-C))));a=0,o=0}break}r===t[0]&&x<O.TEXT_COMPACTION_MODE_LATCH&&(f[o++]=x);for(let C=0;C<o;C++)s.write(f[C]);break;case O.BYTE_COMPACTION_MODE_LATCH_6:for(;r<t[0]&&!l;){let C=t[r++];if(C<O.TEXT_COMPACTION_MODE_LATCH)o++,a=900*a+C;else switch(C){case O.TEXT_COMPACTION_MODE_LATCH:case O.BYTE_COMPACTION_MODE_LATCH:case O.NUMERIC_COMPACTION_MODE_LATCH:case O.BYTE_COMPACTION_MODE_LATCH_6:case O.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case O.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case O.MACRO_PDF417_TERMINATOR:r--,l=!0;break}if(o%5===0&&o>0){for(let E=0;E<6;++E)s.write(Number(Wt(a)>>Wt(8*(5-E))));a=0,o=0}}break}return i.append(st.decode(s.toByteArray(),n)),r}static numericCompaction(e,t,n){let r=0,i=!1,s=new Int32Array(O.MAX_NUMERIC_CODEWORDS);for(;t<e[0]&&!i;){let o=e[t++];if(t===e[0]&&(i=!0),o<O.TEXT_COMPACTION_MODE_LATCH)s[r]=o,r++;else switch(o){case O.TEXT_COMPACTION_MODE_LATCH:case O.BYTE_COMPACTION_MODE_LATCH:case O.BYTE_COMPACTION_MODE_LATCH_6:case O.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case O.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case O.MACRO_PDF417_TERMINATOR:t--,i=!0;break}(r%O.MAX_NUMERIC_CODEWORDS===0||o===O.NUMERIC_COMPACTION_MODE_LATCH||i)&&r>0&&(n.append(O.decodeBase900toBase10(s,r)),r=0)}return t}static decodeBase900toBase10(e,t){let n=Wt(0);for(let i=0;i<t;i++)n+=O.EXP900[t-i-1]*Wt(e[i]);let r=n.toString();if(r.charAt(0)!=="1")throw new V;return r.substring(1)}}O.TEXT_COMPACTION_MODE_LATCH=900,O.BYTE_COMPACTION_MODE_LATCH=901,O.NUMERIC_COMPACTION_MODE_LATCH=902,O.BYTE_COMPACTION_MODE_LATCH_6=924,O.ECI_USER_DEFINED=925,O.ECI_GENERAL_PURPOSE=926,O.ECI_CHARSET=927,O.BEGIN_MACRO_PDF417_CONTROL_BLOCK=928,O.BEGIN_MACRO_PDF417_OPTIONAL_FIELD=923,O.MACRO_PDF417_TERMINATOR=922,O.MODE_SHIFT_TO_BYTE_COMPACTION_MODE=913,O.MAX_NUMERIC_CODEWORDS=15,O.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME=0,O.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT=1,O.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP=2,O.MACRO_PDF417_OPTIONAL_FIELD_SENDER=3,O.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE=4,O.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE=5,O.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM=6,O.PL=25,O.LL=27,O.AS=27,O.ML=28,O.AL=28,O.PS=29,O.PAL=29,O.PUNCT_CHARS=`;<>@[\\]_\`~!\r	,:
-.$/"|*()?{}'`,O.MIXED_CHARS="0123456789&\r	,:#-.$/+%*=^",O.EXP900=Gr()?zi():[],O.NUMBER_OF_SEQUENCE_CODEWORDS=2;class re{constructor(){}static decode(e,t,n,r,i,s,o){let a=new $t(e,t,n,r,i),l=null,f=null,x;for(let I=!0;;I=!1){if(t!=null&&(l=re.getRowIndicatorColumn(e,a,t,!0,s,o)),r!=null&&(f=re.getRowIndicatorColumn(e,a,r,!1,s,o)),x=re.merge(l,f),x==null)throw v.getNotFoundInstance();let y=x.getBoundingBox();if(I&&y!=null&&(y.getMinY()<a.getMinY()||y.getMaxY()>a.getMaxY()))a=y;else break}x.setBoundingBox(a);let C=x.getBarcodeColumnCount()+1;x.setDetectionResultColumn(0,l),x.setDetectionResultColumn(C,f);let E=l!=null;for(let I=1;I<=C;I++){let y=E?I:C-I;if(x.getDetectionResultColumn(y)!==void 0)continue;let b;y===0||y===C?b=new Vr(a,y===0):b=new Sn(a),x.setDetectionResultColumn(y,b);let D=-1,F=D;for(let k=a.getMinY();k<=a.getMaxY();k++){if(D=re.getStartColumn(x,y,k,E),D<0||D>a.getMaxX()){if(F===-1)continue;D=F}let B=re.detectCodeword(e,a.getMinX(),a.getMaxX(),E,D,k,s,o);B!=null&&(b.setCodeword(k,B),F=D,s=Math.min(s,B.getWidth()),o=Math.max(o,B.getWidth()))}}return re.createDecoderResult(x)}static merge(e,t){if(e==null&&t==null)return null;let n=re.getBarcodeMetadata(e,t);if(n==null)return null;let r=$t.merge(re.adjustBoundingBox(e),re.adjustBoundingBox(t));return new Nn(n,r)}static adjustBoundingBox(e){if(e==null)return null;let t=e.getRowHeights();if(t==null)return null;let n=re.getMax(t),r=0;for(let o of t)if(r+=n-o,o>0)break;let i=e.getCodewords();for(let o=0;r>0&&i[o]==null;o++)r--;let s=0;for(let o=t.length-1;o>=0&&(s+=n-t[o],!(t[o]>0));o--);for(let o=i.length-1;s>0&&i[o]==null;o--)s--;return e.getBoundingBox().addMissingRows(r,s,e.isLeft())}static getMax(e){let t=-1;for(let n of e)t=Math.max(t,n);return t}static getBarcodeMetadata(e,t){let n;if(e==null||(n=e.getBarcodeMetadata())==null)return t==null?null:t.getBarcodeMetadata();let r;return t==null||(r=t.getBarcodeMetadata())==null?n:n.getColumnCount()!==r.getColumnCount()&&n.getErrorCorrectionLevel()!==r.getErrorCorrectionLevel()&&n.getRowCount()!==r.getRowCount()?null:n}static getRowIndicatorColumn(e,t,n,r,i,s){let o=new Vr(t,r);for(let a=0;a<2;a++){let l=a===0?1:-1,f=Math.trunc(Math.trunc(n.getX()));for(let x=Math.trunc(Math.trunc(n.getY()));x<=t.getMaxY()&&x>=t.getMinY();x+=l){let C=re.detectCodeword(e,0,e.getWidth(),r,f,x,i,s);C!=null&&(o.setCodeword(x,C),r?f=C.getStartX():f=C.getEndX())}}return o}static adjustCodewordCount(e,t){let n=t[0][1],r=n.getValue(),i=e.getBarcodeColumnCount()*e.getBarcodeRowCount()-re.getNumberOfECCodeWords(e.getBarcodeECLevel());if(r.length===0){if(i<1||i>K.MAX_CODEWORDS_IN_BARCODE)throw v.getNotFoundInstance();n.setValue(i)}else r[0]!==i&&n.setValue(i)}static createDecoderResult(e){let t=re.createBarcodeMatrix(e);re.adjustCodewordCount(e,t);let n=new Array,r=new Int32Array(e.getBarcodeRowCount()*e.getBarcodeColumnCount()),i=[],s=new Array;for(let a=0;a<e.getBarcodeRowCount();a++)for(let l=0;l<e.getBarcodeColumnCount();l++){let f=t[a][l+1].getValue(),x=a*e.getBarcodeColumnCount()+l;f.length===0?n.push(x):f.length===1?r[x]=f[0]:(s.push(x),i.push(f))}let o=new Array(i.length);for(let a=0;a<o.length;a++)o[a]=i[a];return re.createDecoderResultFromAmbiguousValues(e.getBarcodeECLevel(),r,K.toIntArray(n),K.toIntArray(s),o)}static createDecoderResultFromAmbiguousValues(e,t,n,r,i){let s=new Int32Array(r.length),o=100;for(;o-- >0;){for(let a=0;a<s.length;a++)t[r[a]]=i[a][s[a]];try{return re.decodeCodewords(t,e,n)}catch(a){if(!(a instanceof P))throw a}if(s.length===0)throw P.getChecksumInstance();for(let a=0;a<s.length;a++)if(s[a]<i[a].length-1){s[a]++;break}else if(s[a]=0,a===s.length-1)throw P.getChecksumInstance()}throw P.getChecksumInstance()}static createBarcodeMatrix(e){let t=Array.from({length:e.getBarcodeRowCount()},()=>new Array(e.getBarcodeColumnCount()+2));for(let r=0;r<t.length;r++)for(let i=0;i<t[r].length;i++)t[r][i]=new bn;let n=0;for(let r of e.getDetectionResultColumns()){if(r!=null){for(let i of r.getCodewords())if(i!=null){let s=i.getRowNumber();if(s>=0){if(s>=t.length)continue;t[s][n].setValue(i.getValue())}}}n++}return t}static isValidBarcodeColumn(e,t){return t>=0&&t<=e.getBarcodeColumnCount()+1}static getStartColumn(e,t,n,r){let i=r?1:-1,s=null;if(re.isValidBarcodeColumn(e,t-i)&&(s=e.getDetectionResultColumn(t-i).getCodeword(n)),s!=null)return r?s.getEndX():s.getStartX();if(s=e.getDetectionResultColumn(t).getCodewordNearby(n),s!=null)return r?s.getStartX():s.getEndX();if(re.isValidBarcodeColumn(e,t-i)&&(s=e.getDetectionResultColumn(t-i).getCodewordNearby(n)),s!=null)return r?s.getEndX():s.getStartX();let o=0;for(;re.isValidBarcodeColumn(e,t-i);){t-=i;for(let a of e.getDetectionResultColumn(t).getCodewords())if(a!=null)return(r?a.getEndX():a.getStartX())+i*o*(a.getEndX()-a.getStartX());o++}return r?e.getBoundingBox().getMinX():e.getBoundingBox().getMaxX()}static detectCodeword(e,t,n,r,i,s,o,a){i=re.adjustCodewordStartColumn(e,t,n,r,i,s);let l=re.getModuleBitCount(e,t,n,r,i,s);if(l==null)return null;let f,x=ae.sum(l);if(r)f=i+x;else{for(let I=0;I<l.length/2;I++){let y=l[I];l[I]=l[l.length-1-I],l[l.length-1-I]=y}f=i,i=f-x}if(!re.checkCodewordSkew(x,o,a))return null;let C=ct.getDecodedValue(l),E=K.getCodeword(C);return E===-1?null:new Mn(i,f,re.getCodewordBucketNumber(C),E)}static getModuleBitCount(e,t,n,r,i,s){let o=i,a=new Int32Array(8),l=0,f=r?1:-1,x=r;for(;(r?o<n:o>=t)&&l<a.length;)e.get(o,s)===x?(a[l]++,o+=f):(l++,x=!x);return l===a.length||o===(r?n:t)&&l===a.length-1?a:null}static getNumberOfECCodeWords(e){return 2<<e}static adjustCodewordStartColumn(e,t,n,r,i,s){let o=i,a=r?-1:1;for(let l=0;l<2;l++){for(;(r?o>=t:o<n)&&r===e.get(o,s);){if(Math.abs(i-o)>re.CODEWORD_SKEW_SIZE)return i;o+=a}a=-a,r=!r}return o}static checkCodewordSkew(e,t,n){return t-re.CODEWORD_SKEW_SIZE<=e&&e<=n+re.CODEWORD_SKEW_SIZE}static decodeCodewords(e,t,n){if(e.length===0)throw V.getFormatInstance();let r=1<<t+1,i=re.correctErrors(e,n,r);re.verifyCodewordCount(e,r);let s=O.decode(e,""+t);return s.setErrorsCorrected(i),s.setErasures(n.length),s}static correctErrors(e,t,n){if(t!=null&&t.length>n/2+re.MAX_ERRORS||n<0||n>re.MAX_EC_CODEWORDS)throw P.getChecksumInstance();return re.errorCorrection.decode(e,n,t)}static verifyCodewordCount(e,t){if(e.length<4)throw V.getFormatInstance();let n=e[0];if(n>e.length)throw V.getFormatInstance();if(n===0)if(t<e.length)e[0]=e.length-t;else throw V.getFormatInstance()}static getBitCountForCodeword(e){let t=new Int32Array(8),n=0,r=t.length-1;for(;!((e&1)!==n&&(n=e&1,r--,r<0));)t[r]++,e>>=1;return t}static getCodewordBucketNumber(e){return e instanceof Int32Array?this.getCodewordBucketNumber_Int32Array(e):this.getCodewordBucketNumber_number(e)}static getCodewordBucketNumber_number(e){return re.getCodewordBucketNumber(re.getBitCountForCodeword(e))}static getCodewordBucketNumber_Int32Array(e){return(e[0]-e[2]+e[4]-e[6]+9)%9}static toString(e){let t=new Tn;for(let n=0;n<e.length;n++){t.format("Row %2d: ",n);for(let r=0;r<e[n].length;r++){let i=e[n][r];i.getValue().length===0?t.format("        ",null):t.format("%4d(%2d)",i.getValue()[0],i.getConfidence(i.getValue()[0]))}t.format("%n")}return t.toString()}}re.CODEWORD_SKEW_SIZE=2,re.MAX_ERRORS=3,re.MAX_EC_CODEWORDS=512,re.errorCorrection=new Ur;class Qe{decode(e,t=null){let n=Qe.decode(e,t,!1);if(n==null||n.length===0||n[0]==null)throw v.getNotFoundInstance();return n[0]}decodeMultiple(e,t=null){try{return Qe.decode(e,t,!0)}catch(n){throw n instanceof V||n instanceof P?v.getNotFoundInstance():n}}static decode(e,t,n){const r=new Array,i=ne.detectMultiple(e,t,n);for(const s of i.getPoints()){const o=re.decode(i.getBits(),s[4],s[5],s[6],s[7],Qe.getMinCodewordWidth(s),Qe.getMaxCodewordWidth(s)),a=new tt(o.getText(),o.getRawBytes(),void 0,s,Q.PDF_417);a.putMetadata(je.ERROR_CORRECTION_LEVEL,o.getECLevel());const l=o.getOther();l!=null&&a.putMetadata(je.PDF417_EXTRA_METADATA,l),r.push(a)}return r.map(s=>s)}static getMaxWidth(e,t){return e==null||t==null?0:Math.trunc(Math.abs(e.getX()-t.getX()))}static getMinWidth(e,t){return e==null||t==null?J.MAX_VALUE:Math.trunc(Math.abs(e.getX()-t.getX()))}static getMaxCodewordWidth(e){return Math.floor(Math.max(Math.max(Qe.getMaxWidth(e[0],e[4]),Qe.getMaxWidth(e[6],e[2])*K.MODULES_IN_CODEWORD/K.MODULES_IN_STOP_PATTERN),Math.max(Qe.getMaxWidth(e[1],e[5]),Qe.getMaxWidth(e[7],e[3])*K.MODULES_IN_CODEWORD/K.MODULES_IN_STOP_PATTERN)))}static getMinCodewordWidth(e){return Math.floor(Math.min(Math.min(Qe.getMinWidth(e[0],e[4]),Qe.getMinWidth(e[6],e[2])*K.MODULES_IN_CODEWORD/K.MODULES_IN_STOP_PATTERN),Math.min(Qe.getMinWidth(e[1],e[5]),Qe.getMinWidth(e[7],e[3])*K.MODULES_IN_CODEWORD/K.MODULES_IN_STOP_PATTERN)))}reset(){}}class Gn extends A{}Gn.kind="ReaderException";class Wr{constructor(e,t){this.verbose=e===!0,t&&this.setHints(t)}decode(e,t){return t&&this.setHints(t),this.decodeInternal(e)}decodeWithState(e){return(this.readers===null||this.readers===void 0)&&this.setHints(null),this.decodeInternal(e)}setHints(e){this.hints=e;const t=!w(e)&&e.get(Ce.TRY_HARDER)===!0,n=w(e)?null:e.get(Ce.POSSIBLE_FORMATS),r=new Array;if(!w(n)){const i=n.some(s=>s===Q.UPC_A||s===Q.UPC_E||s===Q.EAN_13||s===Q.EAN_8||s===Q.CODABAR||s===Q.CODE_39||s===Q.CODE_93||s===Q.CODE_128||s===Q.ITF||s===Q.RSS_14||s===Q.RSS_EXPANDED);i&&!t&&r.push(new cn(e,this.verbose)),n.includes(Q.QR_CODE)&&r.push(new Gt),n.includes(Q.DATA_MATRIX)&&r.push(new jt),n.includes(Q.AZTEC)&&r.push(new kn),n.includes(Q.PDF_417)&&r.push(new Qe),i&&t&&r.push(new cn(e,this.verbose))}r.length===0&&(t||r.push(new cn(e,this.verbose)),r.push(new Gt),r.push(new jt),r.push(new kn),r.push(new Qe),t&&r.push(new cn(e,this.verbose))),this.readers=r}reset(){if(this.readers!==null)for(const e of this.readers)e.reset()}decodeInternal(e){if(this.readers===null)throw new Gn("No readers where selected, nothing can be read.");for(const t of this.readers)try{return t.decode(e,this.hints)}catch(n){if(n instanceof Gn)continue}throw new v("No MultiFormat Readers were able to detect the code.")}}class ji extends Qt{constructor(e=null,t=500){const n=new Wr;n.setHints(e),super(n,t)}decodeBitmap(e){return this.reader.decodeWithState(e)}}class Gi extends Qt{constructor(e=500){super(new Qe,e)}}class Wi extends Qt{constructor(e=500){super(new Gt,e)}}var wr;(function(g){g[g.ERROR_CORRECTION=0]="ERROR_CORRECTION",g[g.CHARACTER_SET=1]="CHARACTER_SET",g[g.DATA_MATRIX_SHAPE=2]="DATA_MATRIX_SHAPE",g[g.MIN_SIZE=3]="MIN_SIZE",g[g.MAX_SIZE=4]="MAX_SIZE",g[g.MARGIN=5]="MARGIN",g[g.PDF417_COMPACT=6]="PDF417_COMPACT",g[g.PDF417_COMPACTION=7]="PDF417_COMPACTION",g[g.PDF417_DIMENSIONS=8]="PDF417_DIMENSIONS",g[g.AZTEC_LAYERS=9]="AZTEC_LAYERS",g[g.QR_VERSION=10]="QR_VERSION"})(wr||(wr={}));var Ve=wr;class Ar{constructor(e){this.field=e,this.cachedGenerators=[],this.cachedGenerators.push(new ot(e,Int32Array.from([1])))}buildGenerator(e){const t=this.cachedGenerators;if(e>=t.length){let n=t[t.length-1];const r=this.field;for(let i=t.length;i<=e;i++){const s=n.multiply(new ot(r,Int32Array.from([1,r.exp(i-1+r.getGeneratorBase())])));t.push(s),n=s}}return t[e]}encode(e,t){if(t===0)throw new M("No error correction bytes");const n=e.length-t;if(n<=0)throw new M("No data bytes provided");const r=this.buildGenerator(t),i=new Int32Array(n);W.arraycopy(e,0,i,0,n);let s=new ot(this.field,i);s=s.multiplyByMonomial(t,1);const a=s.divide(r)[1].getCoefficients(),l=t-a.length;for(let f=0;f<l;f++)e[n+f]=0;W.arraycopy(a,0,e,n+l,a.length)}}class Le{constructor(){}static applyMaskPenaltyRule1(e){return Le.applyMaskPenaltyRule1Internal(e,!0)+Le.applyMaskPenaltyRule1Internal(e,!1)}static applyMaskPenaltyRule2(e){let t=0;const n=e.getArray(),r=e.getWidth(),i=e.getHeight();for(let s=0;s<i-1;s++){const o=n[s];for(let a=0;a<r-1;a++){const l=o[a];l===o[a+1]&&l===n[s+1][a]&&l===n[s+1][a+1]&&t++}}return Le.N2*t}static applyMaskPenaltyRule3(e){let t=0;const n=e.getArray(),r=e.getWidth(),i=e.getHeight();for(let s=0;s<i;s++)for(let o=0;o<r;o++){const a=n[s];o+6<r&&a[o]===1&&a[o+1]===0&&a[o+2]===1&&a[o+3]===1&&a[o+4]===1&&a[o+5]===0&&a[o+6]===1&&(Le.isWhiteHorizontal(a,o-4,o)||Le.isWhiteHorizontal(a,o+7,o+11))&&t++,s+6<i&&n[s][o]===1&&n[s+1][o]===0&&n[s+2][o]===1&&n[s+3][o]===1&&n[s+4][o]===1&&n[s+5][o]===0&&n[s+6][o]===1&&(Le.isWhiteVertical(n,o,s-4,s)||Le.isWhiteVertical(n,o,s+7,s+11))&&t++}return t*Le.N3}static isWhiteHorizontal(e,t,n){t=Math.max(t,0),n=Math.min(n,e.length);for(let r=t;r<n;r++)if(e[r]===1)return!1;return!0}static isWhiteVertical(e,t,n,r){n=Math.max(n,0),r=Math.min(r,e.length);for(let i=n;i<r;i++)if(e[i][t]===1)return!1;return!0}static applyMaskPenaltyRule4(e){let t=0;const n=e.getArray(),r=e.getWidth(),i=e.getHeight();for(let a=0;a<i;a++){const l=n[a];for(let f=0;f<r;f++)l[f]===1&&t++}const s=e.getHeight()*e.getWidth();return Math.floor(Math.abs(t*2-s)*10/s)*Le.N4}static getDataMaskBit(e,t,n){let r,i;switch(e){case 0:r=n+t&1;break;case 1:r=n&1;break;case 2:r=t%3;break;case 3:r=(n+t)%3;break;case 4:r=Math.floor(n/2)+Math.floor(t/3)&1;break;case 5:i=n*t,r=(i&1)+i%3;break;case 6:i=n*t,r=(i&1)+i%3&1;break;case 7:i=n*t,r=i%3+(n+t&1)&1;break;default:throw new M("Invalid mask pattern: "+e)}return r===0}static applyMaskPenaltyRule1Internal(e,t){let n=0;const r=t?e.getHeight():e.getWidth(),i=t?e.getWidth():e.getHeight(),s=e.getArray();for(let o=0;o<r;o++){let a=0,l=-1;for(let f=0;f<i;f++){const x=t?s[o][f]:s[f][o];x===l?a++:(a>=5&&(n+=Le.N1+(a-5)),a=1,l=x)}a>=5&&(n+=Le.N1+(a-5))}return n}}Le.N1=3,Le.N2=3,Le.N3=40,Le.N4=10;class Wn{constructor(e,t){this.width=e,this.height=t;const n=new Array(t);for(let r=0;r!==t;r++)n[r]=new Uint8Array(e);this.bytes=n}getHeight(){return this.height}getWidth(){return this.width}get(e,t){return this.bytes[t][e]}getArray(){return this.bytes}setNumber(e,t,n){this.bytes[t][e]=n}setBoolean(e,t,n){this.bytes[t][e]=n?1:0}clear(e){for(const t of this.bytes)he.fill(t,e)}equals(e){if(!(e instanceof Wn))return!1;const t=e;if(this.width!==t.width||this.height!==t.height)return!1;for(let n=0,r=this.height;n<r;++n){const i=this.bytes[n],s=t.bytes[n];for(let o=0,a=this.width;o<a;++o)if(i[o]!==s[o])return!1}return!0}toString(){const e=new me;for(let t=0,n=this.height;t<n;++t){const r=this.bytes[t];for(let i=0,s=this.width;i<s;++i)switch(r[i]){case 0:e.append(" 0");break;case 1:e.append(" 1");break;default:e.append("  ");break}e.append(`
`)}return e.toString()}}class en{constructor(){this.maskPattern=-1}getMode(){return this.mode}getECLevel(){return this.ecLevel}getVersion(){return this.version}getMaskPattern(){return this.maskPattern}getMatrix(){return this.matrix}toString(){const e=new me;return e.append(`<<
`),e.append(" mode: "),e.append(this.mode?this.mode.toString():"null"),e.append(`
 ecLevel: `),e.append(this.ecLevel?this.ecLevel.toString():"null"),e.append(`
 version: `),e.append(this.version?this.version.toString():"null"),e.append(`
 maskPattern: `),e.append(this.maskPattern.toString()),this.matrix?(e.append(`
 matrix:
`),e.append(this.matrix.toString())):e.append(`
 matrix: null
`),e.append(`>>
`),e.toString()}setMode(e){this.mode=e}setECLevel(e){this.ecLevel=e}setVersion(e){this.version=e}setMaskPattern(e){this.maskPattern=e}setMatrix(e){this.matrix=e}static isValidMaskPattern(e){return e>=0&&e<en.NUM_MASK_PATTERNS}}en.NUM_MASK_PATTERNS=8;class Ie extends A{}Ie.kind="WriterException";class q{constructor(){}static clearMatrix(e){e.clear(255)}static buildMatrix(e,t,n,r,i){q.clearMatrix(i),q.embedBasicPatterns(n,i),q.embedTypeInfo(t,r,i),q.maybeEmbedVersionInfo(n,i),q.embedDataBits(e,r,i)}static embedBasicPatterns(e,t){q.embedPositionDetectionPatternsAndSeparators(t),q.embedDarkDotAtLeftBottomCorner(t),q.maybeEmbedPositionAdjustmentPatterns(e,t),q.embedTimingPatterns(t)}static embedTypeInfo(e,t,n){const r=new ue;q.makeTypeInfoBits(e,t,r);for(let i=0,s=r.getSize();i<s;++i){const o=r.get(r.getSize()-1-i),a=q.TYPE_INFO_COORDINATES[i],l=a[0],f=a[1];if(n.setBoolean(l,f,o),i<8){const x=n.getWidth()-i-1,C=8;n.setBoolean(x,C,o)}else{const C=n.getHeight()-7+(i-8);n.setBoolean(8,C,o)}}}static maybeEmbedVersionInfo(e,t){if(e.getVersionNumber()<7)return;const n=new ue;q.makeVersionInfoBits(e,n);let r=6*3-1;for(let i=0;i<6;++i)for(let s=0;s<3;++s){const o=n.get(r);r--,t.setBoolean(i,t.getHeight()-11+s,o),t.setBoolean(t.getHeight()-11+s,i,o)}}static embedDataBits(e,t,n){let r=0,i=-1,s=n.getWidth()-1,o=n.getHeight()-1;for(;s>0;){for(s===6&&(s-=1);o>=0&&o<n.getHeight();){for(let a=0;a<2;++a){const l=s-a;if(!q.isEmpty(n.get(l,o)))continue;let f;r<e.getSize()?(f=e.get(r),++r):f=!1,t!==255&&Le.getDataMaskBit(t,l,o)&&(f=!f),n.setBoolean(l,o,f)}o+=i}i=-i,o+=i,s-=2}if(r!==e.getSize())throw new Ie("Not all bits consumed: "+r+"/"+e.getSize())}static findMSBSet(e){return 32-J.numberOfLeadingZeros(e)}static calculateBCHCode(e,t){if(t===0)throw new M("0 polynomial");const n=q.findMSBSet(t);for(e<<=n-1;q.findMSBSet(e)>=n;)e^=t<<q.findMSBSet(e)-n;return e}static makeTypeInfoBits(e,t,n){if(!en.isValidMaskPattern(t))throw new Ie("Invalid mask pattern");const r=e.getBits()<<3|t;n.appendBits(r,5);const i=q.calculateBCHCode(r,q.TYPE_INFO_POLY);n.appendBits(i,10);const s=new ue;if(s.appendBits(q.TYPE_INFO_MASK_PATTERN,15),n.xor(s),n.getSize()!==15)throw new Ie("should not happen but we got: "+n.getSize())}static makeVersionInfoBits(e,t){t.appendBits(e.getVersionNumber(),6);const n=q.calculateBCHCode(e.getVersionNumber(),q.VERSION_INFO_POLY);if(t.appendBits(n,12),t.getSize()!==18)throw new Ie("should not happen but we got: "+t.getSize())}static isEmpty(e){return e===255}static embedTimingPatterns(e){for(let t=8;t<e.getWidth()-8;++t){const n=(t+1)%2;q.isEmpty(e.get(t,6))&&e.setNumber(t,6,n),q.isEmpty(e.get(6,t))&&e.setNumber(6,t,n)}}static embedDarkDotAtLeftBottomCorner(e){if(e.get(8,e.getHeight()-8)===0)throw new Ie;e.setNumber(8,e.getHeight()-8,1)}static embedHorizontalSeparationPattern(e,t,n){for(let r=0;r<8;++r){if(!q.isEmpty(n.get(e+r,t)))throw new Ie;n.setNumber(e+r,t,0)}}static embedVerticalSeparationPattern(e,t,n){for(let r=0;r<7;++r){if(!q.isEmpty(n.get(e,t+r)))throw new Ie;n.setNumber(e,t+r,0)}}static embedPositionAdjustmentPattern(e,t,n){for(let r=0;r<5;++r){const i=q.POSITION_ADJUSTMENT_PATTERN[r];for(let s=0;s<5;++s)n.setNumber(e+s,t+r,i[s])}}static embedPositionDetectionPattern(e,t,n){for(let r=0;r<7;++r){const i=q.POSITION_DETECTION_PATTERN[r];for(let s=0;s<7;++s)n.setNumber(e+s,t+r,i[s])}}static embedPositionDetectionPatternsAndSeparators(e){const t=q.POSITION_DETECTION_PATTERN[0].length;q.embedPositionDetectionPattern(0,0,e),q.embedPositionDetectionPattern(e.getWidth()-t,0,e),q.embedPositionDetectionPattern(0,e.getWidth()-t,e);const n=8;q.embedHorizontalSeparationPattern(0,n-1,e),q.embedHorizontalSeparationPattern(e.getWidth()-n,n-1,e),q.embedHorizontalSeparationPattern(0,e.getWidth()-n,e);const r=7;q.embedVerticalSeparationPattern(r,0,e),q.embedVerticalSeparationPattern(e.getHeight()-r-1,0,e),q.embedVerticalSeparationPattern(r,e.getHeight()-r,e)}static maybeEmbedPositionAdjustmentPatterns(e,t){if(e.getVersionNumber()<2)return;const n=e.getVersionNumber()-1,r=q.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[n];for(let i=0,s=r.length;i!==s;i++){const o=r[i];if(o>=0)for(let a=0;a!==s;a++){const l=r[a];l>=0&&q.isEmpty(t.get(l,o))&&q.embedPositionAdjustmentPattern(l-2,o-2,t)}}}}q.POSITION_DETECTION_PATTERN=Array.from([Int32Array.from([1,1,1,1,1,1,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,1,1,1,1,1,1])]),q.POSITION_ADJUSTMENT_PATTERN=Array.from([Int32Array.from([1,1,1,1,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,0,1,0,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,1,1,1,1])]),q.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE=Array.from([Int32Array.from([-1,-1,-1,-1,-1,-1,-1]),Int32Array.from([6,18,-1,-1,-1,-1,-1]),Int32Array.from([6,22,-1,-1,-1,-1,-1]),Int32Array.from([6,26,-1,-1,-1,-1,-1]),Int32Array.from([6,30,-1,-1,-1,-1,-1]),Int32Array.from([6,34,-1,-1,-1,-1,-1]),Int32Array.from([6,22,38,-1,-1,-1,-1]),Int32Array.from([6,24,42,-1,-1,-1,-1]),Int32Array.from([6,26,46,-1,-1,-1,-1]),Int32Array.from([6,28,50,-1,-1,-1,-1]),Int32Array.from([6,30,54,-1,-1,-1,-1]),Int32Array.from([6,32,58,-1,-1,-1,-1]),Int32Array.from([6,34,62,-1,-1,-1,-1]),Int32Array.from([6,26,46,66,-1,-1,-1]),Int32Array.from([6,26,48,70,-1,-1,-1]),Int32Array.from([6,26,50,74,-1,-1,-1]),Int32Array.from([6,30,54,78,-1,-1,-1]),Int32Array.from([6,30,56,82,-1,-1,-1]),Int32Array.from([6,30,58,86,-1,-1,-1]),Int32Array.from([6,34,62,90,-1,-1,-1]),Int32Array.from([6,28,50,72,94,-1,-1]),Int32Array.from([6,26,50,74,98,-1,-1]),Int32Array.from([6,30,54,78,102,-1,-1]),Int32Array.from([6,28,54,80,106,-1,-1]),Int32Array.from([6,32,58,84,110,-1,-1]),Int32Array.from([6,30,58,86,114,-1,-1]),Int32Array.from([6,34,62,90,118,-1,-1]),Int32Array.from([6,26,50,74,98,122,-1]),Int32Array.from([6,30,54,78,102,126,-1]),Int32Array.from([6,26,52,78,104,130,-1]),Int32Array.from([6,30,56,82,108,134,-1]),Int32Array.from([6,34,60,86,112,138,-1]),Int32Array.from([6,30,58,86,114,142,-1]),Int32Array.from([6,34,62,90,118,146,-1]),Int32Array.from([6,30,54,78,102,126,150]),Int32Array.from([6,24,50,76,102,128,154]),Int32Array.from([6,28,54,80,106,132,158]),Int32Array.from([6,32,58,84,110,136,162]),Int32Array.from([6,26,54,82,110,138,166]),Int32Array.from([6,30,58,86,114,142,170])]),q.TYPE_INFO_COORDINATES=Array.from([Int32Array.from([8,0]),Int32Array.from([8,1]),Int32Array.from([8,2]),Int32Array.from([8,3]),Int32Array.from([8,4]),Int32Array.from([8,5]),Int32Array.from([8,7]),Int32Array.from([8,8]),Int32Array.from([7,8]),Int32Array.from([5,8]),Int32Array.from([4,8]),Int32Array.from([3,8]),Int32Array.from([2,8]),Int32Array.from([1,8]),Int32Array.from([0,8])]),q.VERSION_INFO_POLY=7973,q.TYPE_INFO_POLY=1335,q.TYPE_INFO_MASK_PATTERN=21522;class Yi{constructor(e,t){this.dataBytes=e,this.errorCorrectionBytes=t}getDataBytes(){return this.dataBytes}getErrorCorrectionBytes(){return this.errorCorrectionBytes}}class Me{constructor(){}static calculateMaskPenalty(e){return Le.applyMaskPenaltyRule1(e)+Le.applyMaskPenaltyRule2(e)+Le.applyMaskPenaltyRule3(e)+Le.applyMaskPenaltyRule4(e)}static encode(e,t,n=null){let r=Me.DEFAULT_BYTE_MODE_ENCODING;const i=n!==null&&n.get(Ve.CHARACTER_SET)!==void 0;i&&(r=n.get(Ve.CHARACTER_SET).toString());const s=this.chooseMode(e,r),o=new ue;if(s===Z.BYTE&&(i||Me.DEFAULT_BYTE_MODE_ENCODING!==r)){const k=U.getCharacterSetECIByName(r);k!==void 0&&this.appendECI(k,o)}this.appendModeInfo(s,o);const a=new ue;this.appendBytes(e,s,a,r);let l;if(n!==null&&n.get(Ve.QR_VERSION)!==void 0){const k=Number.parseInt(n.get(Ve.QR_VERSION).toString(),10);l=Y.getVersionForNumber(k);const B=this.calculateBitsNeeded(s,o,a,l);if(!this.willFit(B,l,t))throw new Ie("Data too big for requested version")}else l=this.recommendVersion(t,s,o,a);const f=new ue;f.appendBitArray(o);const x=s===Z.BYTE?a.getSizeInBytes():e.length;this.appendLengthInfo(x,l,s,f),f.appendBitArray(a);const C=l.getECBlocksForLevel(t),E=l.getTotalCodewords()-C.getTotalECCodewords();this.terminateBits(E,f);const I=this.interleaveWithECBytes(f,l.getTotalCodewords(),E,C.getNumBlocks()),y=new en;y.setECLevel(t),y.setMode(s),y.setVersion(l);const b=l.getDimensionForVersion(),D=new Wn(b,b),F=this.chooseMaskPattern(I,t,l,D);return y.setMaskPattern(F),q.buildMatrix(I,t,l,F,D),y.setMatrix(D),y}static recommendVersion(e,t,n,r){const i=this.calculateBitsNeeded(t,n,r,Y.getVersionForNumber(1)),s=this.chooseVersion(i,e),o=this.calculateBitsNeeded(t,n,r,s);return this.chooseVersion(o,e)}static calculateBitsNeeded(e,t,n,r){return t.getSize()+e.getCharacterCountBits(r)+n.getSize()}static getAlphanumericCode(e){return e<Me.ALPHANUMERIC_TABLE.length?Me.ALPHANUMERIC_TABLE[e]:-1}static chooseMode(e,t=null){if(U.SJIS.getName()===t&&this.isOnlyDoubleByteKanji(e))return Z.KANJI;let n=!1,r=!1;for(let i=0,s=e.length;i<s;++i){const o=e.charAt(i);if(Me.isDigit(o))n=!0;else if(this.getAlphanumericCode(o.charCodeAt(0))!==-1)r=!0;else return Z.BYTE}return r?Z.ALPHANUMERIC:n?Z.NUMERIC:Z.BYTE}static isOnlyDoubleByteKanji(e){let t;try{t=st.encode(e,U.SJIS)}catch{return!1}const n=t.length;if(n%2!==0)return!1;for(let r=0;r<n;r+=2){const i=t[r]&255;if((i<129||i>159)&&(i<224||i>235))return!1}return!0}static chooseMaskPattern(e,t,n,r){let i=Number.MAX_SAFE_INTEGER,s=-1;for(let o=0;o<en.NUM_MASK_PATTERNS;o++){q.buildMatrix(e,t,n,o,r);let a=this.calculateMaskPenalty(r);a<i&&(i=a,s=o)}return s}static chooseVersion(e,t){for(let n=1;n<=40;n++){const r=Y.getVersionForNumber(n);if(Me.willFit(e,r,t))return r}throw new Ie("Data too big")}static willFit(e,t,n){const r=t.getTotalCodewords(),s=t.getECBlocksForLevel(n).getTotalECCodewords(),o=r-s,a=(e+7)/8;return o>=a}static terminateBits(e,t){const n=e*8;if(t.getSize()>n)throw new Ie("data bits cannot fit in the QR Code"+t.getSize()+" > "+n);for(let s=0;s<4&&t.getSize()<n;++s)t.appendBit(!1);const r=t.getSize()&7;if(r>0)for(let s=r;s<8;s++)t.appendBit(!1);const i=e-t.getSizeInBytes();for(let s=0;s<i;++s)t.appendBits((s&1)===0?236:17,8);if(t.getSize()!==n)throw new Ie("Bits size does not equal capacity")}static getNumDataBytesAndNumECBytesForBlockID(e,t,n,r,i,s){if(r>=n)throw new Ie("Block ID too large");const o=e%n,a=n-o,l=Math.floor(e/n),f=l+1,x=Math.floor(t/n),C=x+1,E=l-x,I=f-C;if(E!==I)throw new Ie("EC bytes mismatch");if(n!==a+o)throw new Ie("RS blocks mismatch");if(e!==(x+E)*a+(C+I)*o)throw new Ie("Total bytes mismatch");r<a?(i[0]=x,s[0]=E):(i[0]=C,s[0]=I)}static interleaveWithECBytes(e,t,n,r){if(e.getSizeInBytes()!==n)throw new Ie("Number of bits and data bytes does not match");let i=0,s=0,o=0;const a=new Array;for(let f=0;f<r;++f){const x=new Int32Array(1),C=new Int32Array(1);Me.getNumDataBytesAndNumECBytesForBlockID(t,n,r,f,x,C);const E=x[0],I=new Uint8Array(E);e.toBytes(8*i,I,0,E);const y=Me.generateECBytes(I,C[0]);a.push(new Yi(I,y)),s=Math.max(s,E),o=Math.max(o,y.length),i+=x[0]}if(n!==i)throw new Ie("Data bytes does not match offset");const l=new ue;for(let f=0;f<s;++f)for(const x of a){const C=x.getDataBytes();f<C.length&&l.appendBits(C[f],8)}for(let f=0;f<o;++f)for(const x of a){const C=x.getErrorCorrectionBytes();f<C.length&&l.appendBits(C[f],8)}if(t!==l.getSizeInBytes())throw new Ie("Interleaving error: "+t+" and "+l.getSizeInBytes()+" differ.");return l}static generateECBytes(e,t){const n=e.length,r=new Int32Array(n+t);for(let s=0;s<n;s++)r[s]=e[s]&255;new Ar(fe.QR_CODE_FIELD_256).encode(r,t);const i=new Uint8Array(t);for(let s=0;s<t;s++)i[s]=r[n+s];return i}static appendModeInfo(e,t){t.appendBits(e.getBits(),4)}static appendLengthInfo(e,t,n,r){const i=n.getCharacterCountBits(t);if(e>=1<<i)throw new Ie(e+" is bigger than "+((1<<i)-1));r.appendBits(e,i)}static appendBytes(e,t,n,r){switch(t){case Z.NUMERIC:Me.appendNumericBytes(e,n);break;case Z.ALPHANUMERIC:Me.appendAlphanumericBytes(e,n);break;case Z.BYTE:Me.append8BitBytes(e,n,r);break;case Z.KANJI:Me.appendKanjiBytes(e,n);break;default:throw new Ie("Invalid mode: "+t)}}static getDigit(e){return e.charCodeAt(0)-48}static isDigit(e){const t=Me.getDigit(e);return t>=0&&t<=9}static appendNumericBytes(e,t){const n=e.length;let r=0;for(;r<n;){const i=Me.getDigit(e.charAt(r));if(r+2<n){const s=Me.getDigit(e.charAt(r+1)),o=Me.getDigit(e.charAt(r+2));t.appendBits(i*100+s*10+o,10),r+=3}else if(r+1<n){const s=Me.getDigit(e.charAt(r+1));t.appendBits(i*10+s,7),r+=2}else t.appendBits(i,4),r++}}static appendAlphanumericBytes(e,t){const n=e.length;let r=0;for(;r<n;){const i=Me.getAlphanumericCode(e.charCodeAt(r));if(i===-1)throw new Ie;if(r+1<n){const s=Me.getAlphanumericCode(e.charCodeAt(r+1));if(s===-1)throw new Ie;t.appendBits(i*45+s,11),r+=2}else t.appendBits(i,6),r++}}static append8BitBytes(e,t,n){let r;try{r=st.encode(e,n)}catch(i){throw new Ie(i)}for(let i=0,s=r.length;i!==s;i++){const o=r[i];t.appendBits(o,8)}}static appendKanjiBytes(e,t){let n;try{n=st.encode(e,U.SJIS)}catch(i){throw new Ie(i)}const r=n.length;for(let i=0;i<r;i+=2){const s=n[i]&255,o=n[i+1]&255,a=s<<8&4294967295|o;let l=-1;if(a>=33088&&a<=40956?l=a-33088:a>=57408&&a<=60351&&(l=a-49472),l===-1)throw new Ie("Invalid byte sequence");const f=(l>>8)*192+(l&255);t.appendBits(f,13)}}static appendECI(e,t){t.appendBits(Z.ECI.getBits(),4),t.appendBits(e.getValue(),8)}}Me.ALPHANUMERIC_TABLE=Int32Array.from([-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,36,-1,-1,-1,37,38,-1,-1,-1,-1,39,40,-1,41,42,43,0,1,2,3,4,5,6,7,8,9,44,-1,-1,-1,-1,-1,-1,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,-1,-1,-1,-1,-1]),Me.DEFAULT_BYTE_MODE_ENCODING=U.UTF8.getName();class tn{write(e,t,n,r=null){if(e.length===0)throw new M("Found empty contents");if(t<0||n<0)throw new M("Requested dimensions are too small: "+t+"x"+n);let i=Ne.L,s=tn.QUIET_ZONE_SIZE;r!==null&&(r.get(Ve.ERROR_CORRECTION)!==void 0&&(i=Ne.fromString(r.get(Ve.ERROR_CORRECTION).toString())),r.get(Ve.MARGIN)!==void 0&&(s=Number.parseInt(r.get(Ve.MARGIN).toString(),10)));const o=Me.encode(e,i,r);return this.renderResult(o,t,n,s)}writeToDom(e,t,n,r,i=null){typeof e=="string"&&(e=document.querySelector(e));const s=this.write(t,n,r,i);e&&e.appendChild(s)}renderResult(e,t,n,r){const i=e.getMatrix();if(i===null)throw new St;const s=i.getWidth(),o=i.getHeight(),a=s+r*2,l=o+r*2,f=Math.max(t,a),x=Math.max(n,l),C=Math.min(Math.floor(f/a),Math.floor(x/l)),E=Math.floor((f-s*C)/2),I=Math.floor((x-o*C)/2),y=this.createSVGElement(f,x);for(let b=0,D=I;b<o;b++,D+=C)for(let F=0,k=E;F<s;F++,k+=C)if(i.get(F,b)===1){const B=this.createSvgRectElement(k,D,C,C);y.appendChild(B)}return y}createSVGElement(e,t){const n=document.createElementNS(tn.SVG_NS,"svg");return n.setAttributeNS(null,"height",e.toString()),n.setAttributeNS(null,"width",t.toString()),n}createSvgRectElement(e,t,n,r){const i=document.createElementNS(tn.SVG_NS,"rect");return i.setAttributeNS(null,"x",e.toString()),i.setAttributeNS(null,"y",t.toString()),i.setAttributeNS(null,"height",n.toString()),i.setAttributeNS(null,"width",r.toString()),i.setAttributeNS(null,"fill","#000000"),i}}tn.QUIET_ZONE_SIZE=4,tn.SVG_NS="http://www.w3.org/2000/svg";class hn{encode(e,t,n,r,i){if(e.length===0)throw new M("Found empty contents");if(t!==Q.QR_CODE)throw new M("Can only encode QR_CODE, but got "+t);if(n<0||r<0)throw new M(`Requested dimensions are too small: ${n}x${r}`);let s=Ne.L,o=hn.QUIET_ZONE_SIZE;i!==null&&(i.get(Ve.ERROR_CORRECTION)!==void 0&&(s=Ne.fromString(i.get(Ve.ERROR_CORRECTION).toString())),i.get(Ve.MARGIN)!==void 0&&(o=Number.parseInt(i.get(Ve.MARGIN).toString(),10)));const a=Me.encode(e,s,i);return hn.renderResult(a,n,r,o)}static renderResult(e,t,n,r){const i=e.getMatrix();if(i===null)throw new St;const s=i.getWidth(),o=i.getHeight(),a=s+r*2,l=o+r*2,f=Math.max(t,a),x=Math.max(n,l),C=Math.min(Math.floor(f/a),Math.floor(x/l)),E=Math.floor((f-s*C)/2),I=Math.floor((x-o*C)/2),y=new He(f,x);for(let b=0,D=I;b<o;b++,D+=C)for(let F=0,k=E;F<s;F++,k+=C)i.get(F,b)===1&&y.setRegion(k,D,C,C);return y}}hn.QUIET_ZONE_SIZE=4;class Xi{encode(e,t,n,r,i){let s;switch(t){case Q.QR_CODE:s=new hn;break;default:throw new M("No encoder available for format "+t)}return s.encode(e,t,n,r,i)}}class _t extends Cn{constructor(e,t,n,r,i,s,o,a){if(super(s,o),this.yuvData=e,this.dataWidth=t,this.dataHeight=n,this.left=r,this.top=i,r+s>t||i+o>n)throw new M("Crop rectangle does not fit within image data.");a&&this.reverseHorizontal(s,o)}getRow(e,t){if(e<0||e>=this.getHeight())throw new M("Requested row is outside the image: "+e);const n=this.getWidth();(t==null||t.length<n)&&(t=new Uint8ClampedArray(n));const r=(e+this.top)*this.dataWidth+this.left;return W.arraycopy(this.yuvData,r,t,0,n),t}getMatrix(){const e=this.getWidth(),t=this.getHeight();if(e===this.dataWidth&&t===this.dataHeight)return this.yuvData;const n=e*t,r=new Uint8ClampedArray(n);let i=this.top*this.dataWidth+this.left;if(e===this.dataWidth)return W.arraycopy(this.yuvData,i,r,0,n),r;for(let s=0;s<t;s++){const o=s*e;W.arraycopy(this.yuvData,i,r,o,e),i+=this.dataWidth}return r}isCropSupported(){return!0}crop(e,t,n,r){return new _t(this.yuvData,this.dataWidth,this.dataHeight,this.left+e,this.top+t,n,r,!1)}renderThumbnail(){const e=this.getWidth()/_t.THUMBNAIL_SCALE_FACTOR,t=this.getHeight()/_t.THUMBNAIL_SCALE_FACTOR,n=new Int32Array(e*t),r=this.yuvData;let i=this.top*this.dataWidth+this.left;for(let s=0;s<t;s++){const o=s*e;for(let a=0;a<e;a++){const l=r[i+a*_t.THUMBNAIL_SCALE_FACTOR]&255;n[o+a]=4278190080|l*65793}i+=this.dataWidth*_t.THUMBNAIL_SCALE_FACTOR}return n}getThumbnailWidth(){return this.getWidth()/_t.THUMBNAIL_SCALE_FACTOR}getThumbnailHeight(){return this.getHeight()/_t.THUMBNAIL_SCALE_FACTOR}reverseHorizontal(e,t){const n=this.yuvData;for(let r=0,i=this.top*this.dataWidth+this.left;r<t;r++,i+=this.dataWidth){const s=i+e/2;for(let o=i,a=i+e-1;o<s;o++,a--){const l=n[o];n[o]=n[a],n[a]=l}}}invert(){return new kt(this)}}_t.THUMBNAIL_SCALE_FACTOR=2;class Cr extends Cn{constructor(e,t,n,r,i,s,o){if(super(t,n),this.dataWidth=r,this.dataHeight=i,this.left=s,this.top=o,e.BYTES_PER_ELEMENT===4){const a=t*n,l=new Uint8ClampedArray(a);for(let f=0;f<a;f++){const x=e[f],C=x>>16&255,E=x>>7&510,I=x&255;l[f]=(C+E+I)/4&255}this.luminances=l}else this.luminances=e;if(r===void 0&&(this.dataWidth=t),i===void 0&&(this.dataHeight=n),s===void 0&&(this.left=0),o===void 0&&(this.top=0),this.left+t>this.dataWidth||this.top+n>this.dataHeight)throw new M("Crop rectangle does not fit within image data.")}getRow(e,t){if(e<0||e>=this.getHeight())throw new M("Requested row is outside the image: "+e);const n=this.getWidth();(t==null||t.length<n)&&(t=new Uint8ClampedArray(n));const r=(e+this.top)*this.dataWidth+this.left;return W.arraycopy(this.luminances,r,t,0,n),t}getMatrix(){const e=this.getWidth(),t=this.getHeight();if(e===this.dataWidth&&t===this.dataHeight)return this.luminances;const n=e*t,r=new Uint8ClampedArray(n);let i=this.top*this.dataWidth+this.left;if(e===this.dataWidth)return W.arraycopy(this.luminances,i,r,0,n),r;for(let s=0;s<t;s++){const o=s*e;W.arraycopy(this.luminances,i,r,o,e),i+=this.dataWidth}return r}isCropSupported(){return!0}crop(e,t,n,r){return new Cr(this.luminances,n,r,this.dataWidth,this.dataHeight,this.left+e,this.top+t)}invert(){return new kt(this)}}class Yr extends U{static forName(e){return this.getCharacterSetECIByName(e)}}class mr{}mr.ISO_8859_1=U.ISO8859_1;class Xr{isCompact(){return this.compact}setCompact(e){this.compact=e}getSize(){return this.size}setSize(e){this.size=e}getLayers(){return this.layers}setLayers(e){this.layers=e}getCodeWords(){return this.codeWords}setCodeWords(e){this.codeWords=e}getMatrix(){return this.matrix}setMatrix(e){this.matrix=e}}class Zr{static singletonList(e){return[e]}static min(e,t){return e.sort(t)[0]}}class Zi{constructor(e){this.previous=e}getPrevious(){return this.previous}}class fn extends Zi{constructor(e,t,n){super(e),this.value=t,this.bitCount=n}appendTo(e,t){e.appendBits(this.value,this.bitCount)}add(e,t){return new fn(this,e,t)}addBinaryShift(e,t){return console.warn("addBinaryShift on SimpleToken, this simply returns a copy of this token"),new fn(this,e,t)}toString(){let e=this.value&(1<<this.bitCount)-1;return e|=1<<this.bitCount,"<"+J.toBinaryString(e|1<<this.bitCount).substring(1)+">"}}class pr extends fn{constructor(e,t,n){super(e,0,0),this.binaryShiftStart=t,this.binaryShiftByteCount=n}appendTo(e,t){for(let n=0;n<this.binaryShiftByteCount;n++)(n===0||n===31&&this.binaryShiftByteCount<=62)&&(e.appendBits(31,5),this.binaryShiftByteCount>62?e.appendBits(this.binaryShiftByteCount-31,16):n===0?e.appendBits(Math.min(this.binaryShiftByteCount,31),5):e.appendBits(this.binaryShiftByteCount-31,5)),e.appendBits(t[this.binaryShiftStart+n],8)}addBinaryShift(e,t){return new pr(this,e,t)}toString(){return"<"+this.binaryShiftStart+"::"+(this.binaryShiftStart+this.binaryShiftByteCount-1)+">"}}function Qi(g,e,t){return new pr(g,e,t)}function _n(g,e,t){return new fn(g,e,t)}const qi=["UPPER","LOWER","DIGIT","MIXED","PUNCT"],Yt=0,Yn=1,mt=2,Qr=3,Ot=4,Ki=new fn(null,0,0),Er=[Int32Array.from([0,(5<<16)+28,(5<<16)+30,(5<<16)+29,(10<<16)+(29<<5)+30]),Int32Array.from([(9<<16)+(30<<4)+14,0,(5<<16)+30,(5<<16)+29,(10<<16)+(29<<5)+30]),Int32Array.from([(4<<16)+14,(9<<16)+(14<<5)+28,0,(9<<16)+(14<<5)+29,(14<<16)+(14<<10)+(29<<5)+30]),Int32Array.from([(5<<16)+29,(5<<16)+28,(10<<16)+(29<<5)+30,0,(5<<16)+30]),Int32Array.from([(5<<16)+31,(10<<16)+(31<<5)+28,(10<<16)+(31<<5)+30,(10<<16)+(31<<5)+29,0])];function Ji(g){for(let e of g)he.fill(e,-1);return g[Yt][Ot]=0,g[Yn][Ot]=0,g[Yn][Yt]=28,g[Qr][Ot]=0,g[mt][Ot]=0,g[mt][Yt]=15,g}const qr=Ji(he.createInt32Array(6,6));class Dt{constructor(e,t,n,r){this.token=e,this.mode=t,this.binaryShiftByteCount=n,this.bitCount=r}getMode(){return this.mode}getToken(){return this.token}getBinaryShiftByteCount(){return this.binaryShiftByteCount}getBitCount(){return this.bitCount}latchAndAppend(e,t){let n=this.bitCount,r=this.token;if(e!==this.mode){let s=Er[this.mode][e];r=_n(r,s&65535,s>>16),n+=s>>16}let i=e===mt?4:5;return r=_n(r,t,i),new Dt(r,e,0,n+i)}shiftAndAppend(e,t){let n=this.token,r=this.mode===mt?4:5;return n=_n(n,qr[this.mode][e],r),n=_n(n,t,5),new Dt(n,this.mode,0,this.bitCount+r+5)}addBinaryShiftChar(e){let t=this.token,n=this.mode,r=this.bitCount;if(this.mode===Ot||this.mode===mt){let o=Er[n][Yt];t=_n(t,o&65535,o>>16),r+=o>>16,n=Yt}let i=this.binaryShiftByteCount===0||this.binaryShiftByteCount===31?18:this.binaryShiftByteCount===62?9:8,s=new Dt(t,n,this.binaryShiftByteCount+1,r+i);return s.binaryShiftByteCount===2047+31&&(s=s.endBinaryShift(e+1)),s}endBinaryShift(e){if(this.binaryShiftByteCount===0)return this;let t=this.token;return t=Qi(t,e-this.binaryShiftByteCount,this.binaryShiftByteCount),new Dt(t,this.mode,0,this.bitCount)}isBetterThanOrEqualTo(e){let t=this.bitCount+(Er[this.mode][e.mode]>>16);return this.binaryShiftByteCount<e.binaryShiftByteCount?t+=Dt.calculateBinaryShiftCost(e)-Dt.calculateBinaryShiftCost(this):this.binaryShiftByteCount>e.binaryShiftByteCount&&e.binaryShiftByteCount>0&&(t+=10),t<=e.bitCount}toBitArray(e){let t=[];for(let r=this.endBinaryShift(e.length).token;r!==null;r=r.getPrevious())t.unshift(r);let n=new ue;for(const r of t)r.appendTo(n,e);return n}toString(){return $.format("%s bits=%d bytes=%d",qi[this.mode],this.bitCount,this.binaryShiftByteCount)}static calculateBinaryShiftCost(e){return e.binaryShiftByteCount>62?21:e.binaryShiftByteCount>31?20:e.binaryShiftByteCount>0?10:0}}Dt.INITIAL_STATE=new Dt(Ki,Yt,0,0);function $i(g){const e=$.getCharCode(" "),t=$.getCharCode("."),n=$.getCharCode(",");g[Yt][e]=1;const r=$.getCharCode("Z"),i=$.getCharCode("A");for(let C=i;C<=r;C++)g[Yt][C]=C-i+2;g[Yn][e]=1;const s=$.getCharCode("z"),o=$.getCharCode("a");for(let C=o;C<=s;C++)g[Yn][C]=C-o+2;g[mt][e]=1;const a=$.getCharCode("9"),l=$.getCharCode("0");for(let C=l;C<=a;C++)g[mt][C]=C-l+2;g[mt][n]=12,g[mt][t]=13;const f=["\0"," ","","","","","","","\x07","\b","	",`
`,"\v","\f","\r","\x1B","","","","","@","\\","^","_","`","|","~","\x7F"];for(let C=0;C<f.length;C++)g[Qr][$.getCharCode(f[C])]=C;const x=["\0","\r","\0","\0","\0","\0","!","'","#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}"];for(let C=0;C<x.length;C++)$.getCharCode(x[C])>0&&(g[Ot][$.getCharCode(x[C])]=C);return g}const Ir=$i(he.createInt32Array(5,256));class On{constructor(e){this.text=e}encode(){const e=$.getCharCode(" "),t=$.getCharCode(`
`);let n=Zr.singletonList(Dt.INITIAL_STATE);for(let i=0;i<this.text.length;i++){let s,o=i+1<this.text.length?this.text[i+1]:0;switch(this.text[i]){case $.getCharCode("\r"):s=o===t?2:0;break;case $.getCharCode("."):s=o===e?3:0;break;case $.getCharCode(","):s=o===e?4:0;break;case $.getCharCode(":"):s=o===e?5:0;break;default:s=0}s>0?(n=On.updateStateListForPair(n,i,s),i++):n=this.updateStateListForChar(n,i)}return Zr.min(n,(i,s)=>i.getBitCount()-s.getBitCount()).toBitArray(this.text)}updateStateListForChar(e,t){const n=[];for(let r of e)this.updateStateForChar(r,t,n);return On.simplifyStates(n)}updateStateForChar(e,t,n){let r=this.text[t]&255,i=Ir[e.getMode()][r]>0,s=null;for(let o=0;o<=Ot;o++){let a=Ir[o][r];if(a>0){if(s==null&&(s=e.endBinaryShift(t)),!i||o===e.getMode()||o===mt){const l=s.latchAndAppend(o,a);n.push(l)}if(!i&&qr[e.getMode()][o]>=0){const l=s.shiftAndAppend(o,a);n.push(l)}}}if(e.getBinaryShiftByteCount()>0||Ir[e.getMode()][r]===0){let o=e.addBinaryShiftChar(t);n.push(o)}}static updateStateListForPair(e,t,n){const r=[];for(let i of e)this.updateStateForPair(i,t,n,r);return this.simplifyStates(r)}static updateStateForPair(e,t,n,r){let i=e.endBinaryShift(t);if(r.push(i.latchAndAppend(Ot,n)),e.getMode()!==Ot&&r.push(i.shiftAndAppend(Ot,n)),n===3||n===4){let s=i.latchAndAppend(mt,16-n).latchAndAppend(mt,1);r.push(s)}if(e.getBinaryShiftByteCount()>0){let s=e.addBinaryShiftChar(t).addBinaryShiftChar(t+1);r.push(s)}}static simplifyStates(e){let t=[];for(const n of e){let r=!0;for(const i of t){if(i.isBetterThanOrEqualTo(n)){r=!1;break}n.isBetterThanOrEqualTo(i)&&(t=t.filter(s=>s!==i))}r&&t.push(n)}return t}}class le{constructor(){}static encodeBytes(e){return le.encode(e,le.DEFAULT_EC_PERCENT,le.DEFAULT_AZTEC_LAYERS)}static encode(e,t,n){let r=new On(e).encode(),i=J.truncDivision(r.getSize()*t,100)+11,s=r.getSize()+i,o,a,l,f,x;if(n!==le.DEFAULT_AZTEC_LAYERS){if(o=n<0,a=Math.abs(n),a>(o?le.MAX_NB_BITS_COMPACT:le.MAX_NB_BITS))throw new M($.format("Illegal value %s for layers",n));l=le.totalBitsInLayer(a,o),f=le.WORD_SIZE[a];let B=l-l%f;if(x=le.stuffBits(r,f),x.getSize()+i>B)throw new M("Data to large for user specified layer");if(o&&x.getSize()>f*64)throw new M("Data to large for user specified layer")}else{f=0,x=null;for(let B=0;;B++){if(B>le.MAX_NB_BITS)throw new M("Data too large for an Aztec code");if(o=B<=3,a=o?B+1:B,l=le.totalBitsInLayer(a,o),s>l)continue;(x==null||f!==le.WORD_SIZE[a])&&(f=le.WORD_SIZE[a],x=le.stuffBits(r,f));let se=l-l%f;if(!(o&&x.getSize()>f*64)&&x.getSize()+i<=se)break}}let C=le.generateCheckWords(x,l,f),E=x.getSize()/f,I=le.generateModeMessage(o,a,E),y=(o?11:14)+a*4,b=new Int32Array(y),D;if(o){D=y;for(let B=0;B<b.length;B++)b[B]=B}else{D=y+1+2*J.truncDivision(J.truncDivision(y,2)-1,15);let B=J.truncDivision(y,2),se=J.truncDivision(D,2);for(let ee=0;ee<B;ee++){let ut=ee+J.truncDivision(ee,15);b[B-ee-1]=se-ut-1,b[B+ee]=se+ut+1}}let F=new He(D);for(let B=0,se=0;B<a;B++){let ee=(a-B)*4+(o?9:12);for(let ut=0;ut<ee;ut++){let yt=ut*2;for(let ht=0;ht<2;ht++)C.get(se+yt+ht)&&F.set(b[B*2+ht],b[B*2+ut]),C.get(se+ee*2+yt+ht)&&F.set(b[B*2+ut],b[y-1-B*2-ht]),C.get(se+ee*4+yt+ht)&&F.set(b[y-1-B*2-ht],b[y-1-B*2-ut]),C.get(se+ee*6+yt+ht)&&F.set(b[y-1-B*2-ut],b[B*2+ht])}se+=ee*8}if(le.drawModeMessage(F,o,D,I),o)le.drawBullsEye(F,J.truncDivision(D,2),5);else{le.drawBullsEye(F,J.truncDivision(D,2),7);for(let B=0,se=0;B<J.truncDivision(y,2)-1;B+=15,se+=16)for(let ee=J.truncDivision(D,2)&1;ee<D;ee+=2)F.set(J.truncDivision(D,2)-se,ee),F.set(J.truncDivision(D,2)+se,ee),F.set(ee,J.truncDivision(D,2)-se),F.set(ee,J.truncDivision(D,2)+se)}let k=new Xr;return k.setCompact(o),k.setSize(D),k.setLayers(a),k.setCodeWords(E),k.setMatrix(F),k}static drawBullsEye(e,t,n){for(let r=0;r<n;r+=2)for(let i=t-r;i<=t+r;i++)e.set(i,t-r),e.set(i,t+r),e.set(t-r,i),e.set(t+r,i);e.set(t-n,t-n),e.set(t-n+1,t-n),e.set(t-n,t-n+1),e.set(t+n,t-n),e.set(t+n,t-n+1),e.set(t+n,t+n-1)}static generateModeMessage(e,t,n){let r=new ue;return e?(r.appendBits(t-1,2),r.appendBits(n-1,6),r=le.generateCheckWords(r,28,4)):(r.appendBits(t-1,5),r.appendBits(n-1,11),r=le.generateCheckWords(r,40,4)),r}static drawModeMessage(e,t,n,r){let i=J.truncDivision(n,2);if(t)for(let s=0;s<7;s++){let o=i-3+s;r.get(s)&&e.set(o,i-5),r.get(s+7)&&e.set(i+5,o),r.get(20-s)&&e.set(o,i+5),r.get(27-s)&&e.set(i-5,o)}else for(let s=0;s<10;s++){let o=i-5+s+J.truncDivision(s,5);r.get(s)&&e.set(o,i-7),r.get(s+10)&&e.set(i+7,o),r.get(29-s)&&e.set(o,i+7),r.get(39-s)&&e.set(i-7,o)}}static generateCheckWords(e,t,n){let r=e.getSize()/n,i=new Ar(le.getGF(n)),s=J.truncDivision(t,n),o=le.bitsToWords(e,n,s);i.encode(o,s-r);let a=t%n,l=new ue;l.appendBits(0,a);for(const f of Array.from(o))l.appendBits(f,n);return l}static bitsToWords(e,t,n){let r=new Int32Array(n),i,s;for(i=0,s=e.getSize()/t;i<s;i++){let o=0;for(let a=0;a<t;a++)o|=e.get(i*t+a)?1<<t-a-1:0;r[i]=o}return r}static getGF(e){switch(e){case 4:return fe.AZTEC_PARAM;case 6:return fe.AZTEC_DATA_6;case 8:return fe.AZTEC_DATA_8;case 10:return fe.AZTEC_DATA_10;case 12:return fe.AZTEC_DATA_12;default:throw new M("Unsupported word size "+e)}}static stuffBits(e,t){let n=new ue,r=e.getSize(),i=(1<<t)-2;for(let s=0;s<r;s+=t){let o=0;for(let a=0;a<t;a++)(s+a>=r||e.get(s+a))&&(o|=1<<t-1-a);(o&i)===i?(n.appendBits(o&i,t),s--):(o&i)===0?(n.appendBits(o|1,t),s--):n.appendBits(o,t)}return n}static totalBitsInLayer(e,t){return((t?88:112)+16*e)*e}}le.DEFAULT_EC_PERCENT=33,le.DEFAULT_AZTEC_LAYERS=0,le.MAX_NB_BITS=32,le.MAX_NB_BITS_COMPACT=4,le.WORD_SIZE=Int32Array.from([4,6,6,8,8,8,8,8,8,10,10,10,10,10,10,10,10,10,10,10,10,10,10,12,12,12,12,12,12,12,12,12,12]);class Xn{encode(e,t,n,r){return this.encodeWithHints(e,t,n,r,null)}encodeWithHints(e,t,n,r,i){let s=mr.ISO_8859_1,o=le.DEFAULT_EC_PERCENT,a=le.DEFAULT_AZTEC_LAYERS;return i!=null&&(i.has(Ve.CHARACTER_SET)&&(s=Yr.forName(i.get(Ve.CHARACTER_SET).toString())),i.has(Ve.ERROR_CORRECTION)&&(o=J.parseInt(i.get(Ve.ERROR_CORRECTION).toString())),i.has(Ve.AZTEC_LAYERS)&&(a=J.parseInt(i.get(Ve.AZTEC_LAYERS).toString()))),Xn.encodeLayers(e,t,n,r,s,o,a)}static encodeLayers(e,t,n,r,i,s,o){if(t!==Q.AZTEC)throw new M("Can only encode AZTEC, but got "+t);let a=le.encode($.getBytes(e,i),s,o);return Xn.renderResult(a,n,r)}static renderResult(e,t,n){let r=e.getMatrix();if(r==null)throw new St;let i=r.getWidth(),s=r.getHeight(),o=Math.max(t,i),a=Math.max(n,s),l=Math.min(o/i,a/s),f=(o-i*l)/2,x=(a-s*l)/2,C=new He(o,a);for(let E=0,I=x;E<s;E++,I+=l)for(let y=0,b=f;y<i;y++,b+=l)r.get(y,E)&&C.setRegion(b,I,l,l);return C}}c.AbstractExpandedDecoder=ar,c.ArgumentException=S,c.ArithmeticException=Bn,c.AztecCode=Xr,c.AztecCodeReader=kn,c.AztecCodeWriter=Xn,c.AztecDecoder=be,c.AztecDetector=vr,c.AztecDetectorResult=Dr,c.AztecEncoder=le,c.AztecHighLevelEncoder=On,c.AztecPoint=nt,c.BarcodeFormat=Q,c.Binarizer=De,c.BinaryBitmap=X,c.BitArray=ue,c.BitMatrix=He,c.BitSource=hr,c.BrowserAztecCodeReader=Si,c.BrowserBarcodeReader=Oi,c.BrowserCodeReader=Qt,c.BrowserDatamatrixCodeReader=Ri,c.BrowserMultiFormatReader=ji,c.BrowserPDF417Reader=Gi,c.BrowserQRCodeReader=Wi,c.BrowserQRCodeSvgWriter=tn,c.CharacterSetECI=U,c.ChecksumException=P,c.Code128Reader=H,c.Code39Reader=ke,c.DataMatrixDecodedBitStreamParser=zt,c.DataMatrixReader=jt,c.DecodeHintType=Ce,c.DecoderResult=mn,c.DefaultGridSampler=Rr,c.DetectorResult=Fn,c.EAN13Reader=qt,c.EncodeHintType=Ve,c.Exception=A,c.FormatException=V,c.GenericGF=fe,c.GenericGFPoly=ot,c.GlobalHistogramBinarizer=ze,c.GridSampler=rr,c.GridSamplerInstance=Vt,c.HTMLCanvasElementLuminanceSource=Ut,c.HybridBinarizer=te,c.ITFReader=Ee,c.IllegalArgumentException=M,c.IllegalStateException=St,c.InvertedLuminanceSource=kt,c.LuminanceSource=Cn,c.MathUtils=ae,c.MultiFormatOneDReader=cn,c.MultiFormatReader=Wr,c.MultiFormatWriter=Xi,c.NotFoundException=v,c.OneDReader=Fe,c.PDF417DecodedBitStreamParser=O,c.PDF417DecoderErrorCorrection=Ur,c.PDF417Reader=Qe,c.PDF417ResultMetadata=Hr,c.PerspectiveTransform=Et,c.PlanarYUVLuminanceSource=_t,c.QRCodeByteMatrix=Wn,c.QRCodeDataMask=At,c.QRCodeDecodedBitStreamParser=Oe,c.QRCodeDecoderErrorCorrectionLevel=Ne,c.QRCodeDecoderFormatInformation=$e,c.QRCodeEncoder=Me,c.QRCodeEncoderQRCode=en,c.QRCodeMaskUtil=Le,c.QRCodeMatrixUtil=q,c.QRCodeMode=Z,c.QRCodeReader=Gt,c.QRCodeVersion=Y,c.QRCodeWriter=hn,c.RGBLuminanceSource=Cr,c.RSS14Reader=ve,c.RSSExpandedReader=L,c.ReaderException=Gn,c.ReedSolomonDecoder=En,c.ReedSolomonEncoder=Ar,c.ReedSolomonException=sn,c.Result=tt,c.ResultMetadataType=je,c.ResultPoint=G,c.StringUtils=$,c.UnsupportedOperationException=rn,c.VideoInputDevice=Or,c.WhiteRectangleDetector=vt,c.WriterException=Ie,c.ZXingArrays=he,c.ZXingCharset=Yr,c.ZXingInteger=J,c.ZXingStandardCharsets=mr,c.ZXingStringBuilder=me,c.ZXingStringEncoding=st,c.ZXingSystem=W,c.createAbstractExpandedDecoder=Fr,Object.defineProperty(c,"__esModule",{value:!0})})})(Te,Te.exports);var Rs=fs(Te.exports),vs=Cs({__proto__:null,default:Rs},[Te.exports]),ni=function(){function h(u,c,w){if(this.formatMap=new Map([[j.QR_CODE,Te.exports.BarcodeFormat.QR_CODE],[j.AZTEC,Te.exports.BarcodeFormat.AZTEC],[j.CODABAR,Te.exports.BarcodeFormat.CODABAR],[j.CODE_39,Te.exports.BarcodeFormat.CODE_39],[j.CODE_93,Te.exports.BarcodeFormat.CODE_93],[j.CODE_128,Te.exports.BarcodeFormat.CODE_128],[j.DATA_MATRIX,Te.exports.BarcodeFormat.DATA_MATRIX],[j.MAXICODE,Te.exports.BarcodeFormat.MAXICODE],[j.ITF,Te.exports.BarcodeFormat.ITF],[j.EAN_13,Te.exports.BarcodeFormat.EAN_13],[j.EAN_8,Te.exports.BarcodeFormat.EAN_8],[j.PDF_417,Te.exports.BarcodeFormat.PDF_417],[j.RSS_14,Te.exports.BarcodeFormat.RSS_14],[j.RSS_EXPANDED,Te.exports.BarcodeFormat.RSS_EXPANDED],[j.UPC_A,Te.exports.BarcodeFormat.UPC_A],[j.UPC_E,Te.exports.BarcodeFormat.UPC_E],[j.UPC_EAN_EXTENSION,Te.exports.BarcodeFormat.UPC_EAN_EXTENSION]]),this.reverseFormatMap=this.createReverseFormatMap(),!vs)throw"Use html5qrcode.min.js without edit, ZXing not found.";this.verbose=c,this.logger=w;var m=this.createZXingFormats(u),d=new Map;d.set(Te.exports.DecodeHintType.POSSIBLE_FORMATS,m),d.set(Te.exports.DecodeHintType.TRY_HARDER,!1),this.hints=d}return h.prototype.decodeAsync=function(u){var c=this;return new Promise(function(w,m){try{w(c.decode(u))}catch(d){m(d)}})},h.prototype.decode=function(u){var c=new Te.exports.MultiFormatReader(this.verbose,this.hints),w=new Te.exports.HTMLCanvasElementLuminanceSource(u),m=new Te.exports.BinaryBitmap(new Te.exports.HybridBinarizer(w)),d=c.decode(m);return{text:d.text,format:Ci.create(this.toHtml5QrcodeSupportedFormats(d.format)),debugData:this.createDebugData()}},h.prototype.createReverseFormatMap=function(){var u=new Map;return this.formatMap.forEach(function(c,w,m){u.set(c,w)}),u},h.prototype.toHtml5QrcodeSupportedFormats=function(u){if(!this.reverseFormatMap.has(u))throw"reverseFormatMap doesn't have ".concat(u);return this.reverseFormatMap.get(u)},h.prototype.createZXingFormats=function(u){for(var c=[],w=0,m=u;w<m.length;w++){var d=m[w];this.formatMap.has(d)?c.push(this.formatMap.get(d)):this.logger.logError("".concat(d," is not supported by")+"ZXingHtml5QrcodeShim")}return c},h.prototype.createDebugData=function(){return{decoderName:"zxing-js"}},h}(),Ls=globalThis&&globalThis.__awaiter||function(h,u,c,w){function m(d){return d instanceof c?d:new c(function(T){T(d)})}return new(c||(c=Promise))(function(d,T){function R(S){try{A(w.next(S))}catch(M){T(M)}}function _(S){try{A(w.throw(S))}catch(M){T(M)}}function A(S){S.done?d(S.value):m(S.value).then(R,_)}A((w=w.apply(h,u||[])).next())})},Bs=globalThis&&globalThis.__generator||function(h,u){var c={label:0,sent:function(){if(d[0]&1)throw d[1];return d[1]},trys:[],ops:[]},w,m,d,T;return T={next:R(0),throw:R(1),return:R(2)},typeof Symbol=="function"&&(T[Symbol.iterator]=function(){return this}),T;function R(A){return function(S){return _([A,S])}}function _(A){if(w)throw new TypeError("Generator is already executing.");for(;T&&(T=0,A[0]&&(c=0)),c;)try{if(w=1,m&&(d=A[0]&2?m.return:A[0]?m.throw||((d=m.return)&&d.call(m),0):m.next)&&!(d=d.call(m,A[1])).done)return d;switch(m=0,d&&(A=[A[0]&2,d.value]),A[0]){case 0:case 1:d=A;break;case 4:return c.label++,{value:A[1],done:!1};case 5:c.label++,m=A[1],A=[0];continue;case 7:A=c.ops.pop(),c.trys.pop();continue;default:if(d=c.trys,!(d=d.length>0&&d[d.length-1])&&(A[0]===6||A[0]===2)){c=0;continue}if(A[0]===3&&(!d||A[1]>d[0]&&A[1]<d[3])){c.label=A[1];break}if(A[0]===6&&c.label<d[1]){c.label=d[1],d=A;break}if(d&&c.label<d[2]){c.label=d[2],c.ops.push(A);break}d[2]&&c.ops.pop(),c.trys.pop();continue}A=u.call(h,c)}catch(S){A=[6,S],m=0}finally{w=d=0}if(A[0]&5)throw A[1];return{value:A[0]?A[1]:void 0,done:!0}}},ri=function(){function h(u,c,w){if(this.formatMap=new Map([[j.QR_CODE,"qr_code"],[j.AZTEC,"aztec"],[j.CODABAR,"codabar"],[j.CODE_39,"code_39"],[j.CODE_93,"code_93"],[j.CODE_128,"code_128"],[j.DATA_MATRIX,"data_matrix"],[j.ITF,"itf"],[j.EAN_13,"ean_13"],[j.EAN_8,"ean_8"],[j.PDF_417,"pdf417"],[j.UPC_A,"upc_a"],[j.UPC_E,"upc_e"]]),this.reverseFormatMap=this.createReverseFormatMap(),!h.isSupported())throw"Use html5qrcode.min.js without edit, Use BarcodeDetectorDelegate only if it isSupported();";this.verbose=c,this.logger=w;var m=this.createBarcodeDetectorFormats(u);if(this.detector=new BarcodeDetector(m),!this.detector)throw"BarcodeDetector detector not supported"}return h.isSupported=function(){if(!("BarcodeDetector"in window))return!1;var u=new BarcodeDetector({formats:["qr_code"]});return typeof u!="undefined"},h.prototype.decodeAsync=function(u){return Ls(this,void 0,void 0,function(){var c,w;return Bs(this,function(m){switch(m.label){case 0:return[4,this.detector.detect(u)];case 1:if(c=m.sent(),!c||c.length===0)throw"No barcode or QR code detected.";return w=this.selectLargestBarcode(c),[2,{text:w.rawValue,format:Ci.create(this.toHtml5QrcodeSupportedFormats(w.format)),debugData:this.createDebugData()}]}})})},h.prototype.selectLargestBarcode=function(u){for(var c=null,w=0,m=0,d=u;m<d.length;m++){var T=d[m],R=T.boundingBox.width*T.boundingBox.height;R>w&&(w=R,c=T)}if(!c)throw"No largest barcode found";return c},h.prototype.createBarcodeDetectorFormats=function(u){for(var c=[],w=0,m=u;w<m.length;w++){var d=m[w];this.formatMap.has(d)?c.push(this.formatMap.get(d)):this.logger.warn("".concat(d," is not supported by")+"BarcodeDetectorDelegate")}return{formats:c}},h.prototype.toHtml5QrcodeSupportedFormats=function(u){if(!this.reverseFormatMap.has(u))throw"reverseFormatMap doesn't have ".concat(u);return this.reverseFormatMap.get(u)},h.prototype.createReverseFormatMap=function(){var u=new Map;return this.formatMap.forEach(function(c,w,m){u.set(c,w)}),u},h.prototype.createDebugData=function(){return{decoderName:"BarcodeDetector"}},h}(),ii=globalThis&&globalThis.__awaiter||function(h,u,c,w){function m(d){return d instanceof c?d:new c(function(T){T(d)})}return new(c||(c=Promise))(function(d,T){function R(S){try{A(w.next(S))}catch(M){T(M)}}function _(S){try{A(w.throw(S))}catch(M){T(M)}}function A(S){S.done?d(S.value):m(S.value).then(R,_)}A((w=w.apply(h,u||[])).next())})},si=globalThis&&globalThis.__generator||function(h,u){var c={label:0,sent:function(){if(d[0]&1)throw d[1];return d[1]},trys:[],ops:[]},w,m,d,T;return T={next:R(0),throw:R(1),return:R(2)},typeof Symbol=="function"&&(T[Symbol.iterator]=function(){return this}),T;function R(A){return function(S){return _([A,S])}}function _(A){if(w)throw new TypeError("Generator is already executing.");for(;T&&(T=0,A[0]&&(c=0)),c;)try{if(w=1,m&&(d=A[0]&2?m.return:A[0]?m.throw||((d=m.return)&&d.call(m),0):m.next)&&!(d=d.call(m,A[1])).done)return d;switch(m=0,d&&(A=[A[0]&2,d.value]),A[0]){case 0:case 1:d=A;break;case 4:return c.label++,{value:A[1],done:!1};case 5:c.label++,m=A[1],A=[0];continue;case 7:A=c.ops.pop(),c.trys.pop();continue;default:if(d=c.trys,!(d=d.length>0&&d[d.length-1])&&(A[0]===6||A[0]===2)){c=0;continue}if(A[0]===3&&(!d||A[1]>d[0]&&A[1]<d[3])){c.label=A[1];break}if(A[0]===6&&c.label<d[1]){c.label=d[1],d=A;break}if(d&&c.label<d[2]){c.label=d[2],c.ops.push(A);break}d[2]&&c.ops.pop(),c.trys.pop();continue}A=u.call(h,c)}catch(S){A=[6,S],m=0}finally{w=d=0}if(A[0]&5)throw A[1];return{value:A[0]?A[1]:void 0,done:!0}}},Ps=function(){function h(u,c,w,m){this.EXECUTIONS_TO_REPORT_PERFORMANCE=100,this.executions=0,this.executionResults=[],this.wasPrimaryDecoderUsedInLastDecode=!1,this.verbose=w,c&&ri.isSupported()?(this.primaryDecoder=new ri(u,w,m),this.secondaryDecoder=new ni(u,w,m)):this.primaryDecoder=new ni(u,w,m)}return h.prototype.decodeAsync=function(u){return ii(this,void 0,void 0,function(){var c;return si(this,function(w){switch(w.label){case 0:c=performance.now(),w.label=1;case 1:return w.trys.push([1,,3,4]),[4,this.getDecoder().decodeAsync(u)];case 2:return[2,w.sent()];case 3:return this.possiblyLogPerformance(c),[7];case 4:return[2]}})})},h.prototype.decodeRobustlyAsync=function(u){return ii(this,void 0,void 0,function(){var c,w;return si(this,function(m){switch(m.label){case 0:c=performance.now(),m.label=1;case 1:return m.trys.push([1,3,4,5]),[4,this.primaryDecoder.decodeAsync(u)];case 2:return[2,m.sent()];case 3:if(w=m.sent(),this.secondaryDecoder)return[2,this.secondaryDecoder.decodeAsync(u)];throw w;case 4:return this.possiblyLogPerformance(c),[7];case 5:return[2]}})})},h.prototype.getDecoder=function(){return this.secondaryDecoder?this.wasPrimaryDecoderUsedInLastDecode===!1?(this.wasPrimaryDecoderUsedInLastDecode=!0,this.primaryDecoder):(this.wasPrimaryDecoderUsedInLastDecode=!1,this.secondaryDecoder):this.primaryDecoder},h.prototype.possiblyLogPerformance=function(u){if(!!this.verbose){var c=performance.now()-u;this.executionResults.push(c),this.executions++,this.possiblyFlushPerformanceReport()}},h.prototype.possiblyFlushPerformanceReport=function(){if(!(this.executions<this.EXECUTIONS_TO_REPORT_PERFORMANCE)){for(var u=0,c=0,w=this.executionResults;c<w.length;c++){var m=w[c];u+=m}var d=u/this.executionResults.length;console.log("".concat(d," ms for ").concat(this.executionResults.length," last runs.")),this.executions=0,this.executionResults=[]}},h}(),Mr=globalThis&&globalThis.__extends||function(){var h=function(u,c){return h=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(w,m){w.__proto__=m}||function(w,m){for(var d in m)Object.prototype.hasOwnProperty.call(m,d)&&(w[d]=m[d])},h(u,c)};return function(u,c){if(typeof c!="function"&&c!==null)throw new TypeError("Class extends value "+String(c)+" is not a constructor or null");h(u,c);function w(){this.constructor=u}u.prototype=c===null?Object.create(c):(w.prototype=c.prototype,new w)}}(),Jn=globalThis&&globalThis.__awaiter||function(h,u,c,w){function m(d){return d instanceof c?d:new c(function(T){T(d)})}return new(c||(c=Promise))(function(d,T){function R(S){try{A(w.next(S))}catch(M){T(M)}}function _(S){try{A(w.throw(S))}catch(M){T(M)}}function A(S){S.done?d(S.value):m(S.value).then(R,_)}A((w=w.apply(h,u||[])).next())})},$n=globalThis&&globalThis.__generator||function(h,u){var c={label:0,sent:function(){if(d[0]&1)throw d[1];return d[1]},trys:[],ops:[]},w,m,d,T;return T={next:R(0),throw:R(1),return:R(2)},typeof Symbol=="function"&&(T[Symbol.iterator]=function(){return this}),T;function R(A){return function(S){return _([A,S])}}function _(A){if(w)throw new TypeError("Generator is already executing.");for(;T&&(T=0,A[0]&&(c=0)),c;)try{if(w=1,m&&(d=A[0]&2?m.return:A[0]?m.throw||((d=m.return)&&d.call(m),0):m.next)&&!(d=d.call(m,A[1])).done)return d;switch(m=0,d&&(A=[A[0]&2,d.value]),A[0]){case 0:case 1:d=A;break;case 4:return c.label++,{value:A[1],done:!1};case 5:c.label++,m=A[1],A=[0];continue;case 7:A=c.ops.pop(),c.trys.pop();continue;default:if(d=c.trys,!(d=d.length>0&&d[d.length-1])&&(A[0]===6||A[0]===2)){c=0;continue}if(A[0]===3&&(!d||A[1]>d[0]&&A[1]<d[3])){c.label=A[1];break}if(A[0]===6&&c.label<d[1]){c.label=d[1],d=A;break}if(d&&c.label<d[2]){c.label=d[2],c.ops.push(A);break}d[2]&&c.ops.pop(),c.trys.pop();continue}A=u.call(h,c)}catch(S){A=[6,S],m=0}finally{w=d=0}if(A[0]&5)throw A[1];return{value:A[0]?A[1]:void 0,done:!0}}},Ii=function(){function h(u,c){this.name=u,this.track=c}return h.prototype.isSupported=function(){return this.track.getCapabilities?this.name in this.track.getCapabilities():!1},h.prototype.apply=function(u){var c={};c[this.name]=u;var w={advanced:[c]};return this.track.applyConstraints(w)},h.prototype.value=function(){var u=this.track.getSettings();if(this.name in u){var c=u[this.name];return c}return null},h}(),Fs=function(h){Mr(u,h);function u(c,w){return h.call(this,c,w)||this}return u.prototype.min=function(){return this.getCapabilities().min},u.prototype.max=function(){return this.getCapabilities().max},u.prototype.step=function(){return this.getCapabilities().step},u.prototype.apply=function(c){var w={};w[this.name]=c;var m={advanced:[w]};return this.track.applyConstraints(m)},u.prototype.getCapabilities=function(){this.failIfNotSupported();var c=this.track.getCapabilities(),w=c[this.name];return{min:w.min,max:w.max,step:w.step}},u.prototype.failIfNotSupported=function(){if(!this.isSupported())throw new Error("".concat(this.name," capability not supported"))},u}(Ii),ks=function(h){Mr(u,h);function u(c){return h.call(this,"zoom",c)||this}return u}(Fs),Us=function(h){Mr(u,h);function u(c){return h.call(this,"torch",c)||this}return u}(Ii),Vs=function(){function h(u){this.track=u}return h.prototype.zoomFeature=function(){return new ks(this.track)},h.prototype.torchFeature=function(){return new Us(this.track)},h}(),Hs=function(){function h(u,c,w){this.isClosed=!1,this.parentElement=u,this.mediaStream=c,this.callbacks=w,this.surface=this.createVideoElement(this.parentElement.clientWidth),u.append(this.surface)}return h.prototype.createVideoElement=function(u){var c=document.createElement("video");return c.style.width="".concat(u,"px"),c.style.display="block",c.muted=!0,c.setAttribute("muted","true"),c.playsInline=!0,c},h.prototype.setupSurface=function(){var u=this;this.surface.onabort=function(){throw"RenderedCameraImpl video surface onabort() called"},this.surface.onerror=function(){throw"RenderedCameraImpl video surface onerror() called"};var c=function(){var w=u.surface.clientWidth,m=u.surface.clientHeight;u.callbacks.onRenderSurfaceReady(w,m),u.surface.removeEventListener("playing",c)};this.surface.addEventListener("playing",c),this.surface.srcObject=this.mediaStream,this.surface.play()},h.create=function(u,c,w,m){return Jn(this,void 0,void 0,function(){var d,T;return $n(this,function(R){switch(R.label){case 0:return d=new h(u,c,m),w.aspectRatio?(T={aspectRatio:w.aspectRatio},[4,d.getFirstTrackOrFail().applyConstraints(T)]):[3,2];case 1:R.sent(),R.label=2;case 2:return d.setupSurface(),[2,d]}})})},h.prototype.failIfClosed=function(){if(this.isClosed)throw"The RenderedCamera has already been closed."},h.prototype.getFirstTrackOrFail=function(){if(this.failIfClosed(),this.mediaStream.getVideoTracks().length===0)throw"No video tracks found";return this.mediaStream.getVideoTracks()[0]},h.prototype.pause=function(){this.failIfClosed(),this.surface.pause()},h.prototype.resume=function(u){this.failIfClosed();var c=this,w=function(){setTimeout(u,200),c.surface.removeEventListener("playing",w)};this.surface.addEventListener("playing",w),this.surface.play()},h.prototype.isPaused=function(){return this.failIfClosed(),this.surface.paused},h.prototype.getSurface=function(){return this.failIfClosed(),this.surface},h.prototype.getRunningTrackCapabilities=function(){return this.getFirstTrackOrFail().getCapabilities()},h.prototype.getRunningTrackSettings=function(){return this.getFirstTrackOrFail().getSettings()},h.prototype.applyVideoConstraints=function(u){return Jn(this,void 0,void 0,function(){return $n(this,function(c){if("aspectRatio"in u)throw"Changing 'aspectRatio' in run-time is not yet supported.";return[2,this.getFirstTrackOrFail().applyConstraints(u)]})})},h.prototype.close=function(){if(this.isClosed)return Promise.resolve();var u=this;return new Promise(function(c,w){var m=u.mediaStream.getVideoTracks(),d=m.length,T=0;u.mediaStream.getVideoTracks().forEach(function(R){u.mediaStream.removeTrack(R),R.stop(),++T,T>=d&&(u.isClosed=!0,u.parentElement.removeChild(u.surface),c())})})},h.prototype.getCapabilities=function(){return new Vs(this.getFirstTrackOrFail())},h}(),zs=function(){function h(u){this.mediaStream=u}return h.prototype.render=function(u,c,w){return Jn(this,void 0,void 0,function(){return $n(this,function(m){return[2,Hs.create(u,this.mediaStream,c,w)]})})},h.create=function(u){return Jn(this,void 0,void 0,function(){var c,w;return $n(this,function(m){switch(m.label){case 0:if(!navigator.mediaDevices)throw"navigator.mediaDevices not supported";return c={audio:!1,video:u},[4,navigator.mediaDevices.getUserMedia(c)];case 1:return w=m.sent(),[2,new h(w)]}})})},h}(),oi=globalThis&&globalThis.__awaiter||function(h,u,c,w){function m(d){return d instanceof c?d:new c(function(T){T(d)})}return new(c||(c=Promise))(function(d,T){function R(S){try{A(w.next(S))}catch(M){T(M)}}function _(S){try{A(w.throw(S))}catch(M){T(M)}}function A(S){S.done?d(S.value):m(S.value).then(R,_)}A((w=w.apply(h,u||[])).next())})},ai=globalThis&&globalThis.__generator||function(h,u){var c={label:0,sent:function(){if(d[0]&1)throw d[1];return d[1]},trys:[],ops:[]},w,m,d,T;return T={next:R(0),throw:R(1),return:R(2)},typeof Symbol=="function"&&(T[Symbol.iterator]=function(){return this}),T;function R(A){return function(S){return _([A,S])}}function _(A){if(w)throw new TypeError("Generator is already executing.");for(;T&&(T=0,A[0]&&(c=0)),c;)try{if(w=1,m&&(d=A[0]&2?m.return:A[0]?m.throw||((d=m.return)&&d.call(m),0):m.next)&&!(d=d.call(m,A[1])).done)return d;switch(m=0,d&&(A=[A[0]&2,d.value]),A[0]){case 0:case 1:d=A;break;case 4:return c.label++,{value:A[1],done:!1};case 5:c.label++,m=A[1],A=[0];continue;case 7:A=c.ops.pop(),c.trys.pop();continue;default:if(d=c.trys,!(d=d.length>0&&d[d.length-1])&&(A[0]===6||A[0]===2)){c=0;continue}if(A[0]===3&&(!d||A[1]>d[0]&&A[1]<d[3])){c.label=A[1];break}if(A[0]===6&&c.label<d[1]){c.label=d[1],d=A;break}if(d&&c.label<d[2]){c.label=d[2],c.ops.push(A);break}d[2]&&c.ops.pop(),c.trys.pop();continue}A=u.call(h,c)}catch(S){A=[6,S],m=0}finally{w=d=0}if(A[0]&5)throw A[1];return{value:A[0]?A[1]:void 0,done:!0}}},js=function(){function h(){}return h.failIfNotSupported=function(){return oi(this,void 0,void 0,function(){return ai(this,function(u){if(!navigator.mediaDevices)throw"navigator.mediaDevices not supported";return[2,new h]})})},h.prototype.create=function(u){return oi(this,void 0,void 0,function(){return ai(this,function(c){return[2,zs.create(u)]})})},h}(),Gs=globalThis&&globalThis.__awaiter||function(h,u,c,w){function m(d){return d instanceof c?d:new c(function(T){T(d)})}return new(c||(c=Promise))(function(d,T){function R(S){try{A(w.next(S))}catch(M){T(M)}}function _(S){try{A(w.throw(S))}catch(M){T(M)}}function A(S){S.done?d(S.value):m(S.value).then(R,_)}A((w=w.apply(h,u||[])).next())})},Ws=globalThis&&globalThis.__generator||function(h,u){var c={label:0,sent:function(){if(d[0]&1)throw d[1];return d[1]},trys:[],ops:[]},w,m,d,T;return T={next:R(0),throw:R(1),return:R(2)},typeof Symbol=="function"&&(T[Symbol.iterator]=function(){return this}),T;function R(A){return function(S){return _([A,S])}}function _(A){if(w)throw new TypeError("Generator is already executing.");for(;T&&(T=0,A[0]&&(c=0)),c;)try{if(w=1,m&&(d=A[0]&2?m.return:A[0]?m.throw||((d=m.return)&&d.call(m),0):m.next)&&!(d=d.call(m,A[1])).done)return d;switch(m=0,d&&(A=[A[0]&2,d.value]),A[0]){case 0:case 1:d=A;break;case 4:return c.label++,{value:A[1],done:!1};case 5:c.label++,m=A[1],A=[0];continue;case 7:A=c.ops.pop(),c.trys.pop();continue;default:if(d=c.trys,!(d=d.length>0&&d[d.length-1])&&(A[0]===6||A[0]===2)){c=0;continue}if(A[0]===3&&(!d||A[1]>d[0]&&A[1]<d[3])){c.label=A[1];break}if(A[0]===6&&c.label<d[1]){c.label=d[1],d=A;break}if(d&&c.label<d[2]){c.label=d[2],c.ops.push(A);break}d[2]&&c.ops.pop(),c.trys.pop();continue}A=u.call(h,c)}catch(S){A=[6,S],m=0}finally{w=d=0}if(A[0]&5)throw A[1];return{value:A[0]?A[1]:void 0,done:!0}}},Ys=function(){function h(){}return h.retrieve=function(){if(navigator.mediaDevices)return h.getCamerasFromMediaDevices();var u=MediaStreamTrack;return MediaStreamTrack&&u.getSources?h.getCamerasFromMediaStreamTrack():h.rejectWithError()},h.rejectWithError=function(){var u=wn.unableToQuerySupportedDevices();return h.isHttpsOrLocalhost()||(u=wn.insecureContextCameraQueryError()),Promise.reject(u)},h.isHttpsOrLocalhost=function(){if(location.protocol==="https:")return!0;var u=location.host.split(":")[0];return u==="127.0.0.1"||u==="localhost"},h.getCamerasFromMediaDevices=function(){return Gs(this,void 0,void 0,function(){var u,c,w,m,d,T,R;return Ws(this,function(_){switch(_.label){case 0:return u=function(A){for(var S=A.getVideoTracks(),M=0,X=S;M<X.length;M++){var P=X[M];P.enabled=!1,P.stop(),A.removeTrack(P)}},[4,navigator.mediaDevices.getUserMedia({audio:!1,video:!0})];case 1:return c=_.sent(),[4,navigator.mediaDevices.enumerateDevices()];case 2:for(w=_.sent(),m=[],d=0,T=w;d<T.length;d++)R=T[d],R.kind==="videoinput"&&m.push({id:R.deviceId,label:R.label});return u(c),[2,m]}})})},h.getCamerasFromMediaStreamTrack=function(){return new Promise(function(u,c){var w=function(d){for(var T=[],R=0,_=d;R<_.length;R++){var A=_[R];A.kind==="video"&&T.push({id:A.id,label:A.label})}u(T)},m=MediaStreamTrack;m.getSources(w)})},h}(),Be;(function(h){h[h.UNKNOWN=0]="UNKNOWN",h[h.NOT_STARTED=1]="NOT_STARTED",h[h.SCANNING=2]="SCANNING",h[h.PAUSED=3]="PAUSED"})(Be||(Be={}));var Xs=function(){function h(){this.state=Be.NOT_STARTED,this.onGoingTransactionNewState=Be.UNKNOWN}return h.prototype.directTransition=function(u){this.failIfTransitionOngoing(),this.validateTransition(u),this.state=u},h.prototype.startTransition=function(u){return this.failIfTransitionOngoing(),this.validateTransition(u),this.onGoingTransactionNewState=u,this},h.prototype.execute=function(){if(this.onGoingTransactionNewState===Be.UNKNOWN)throw"Transaction is already cancelled, cannot execute().";var u=this.onGoingTransactionNewState;this.onGoingTransactionNewState=Be.UNKNOWN,this.directTransition(u)},h.prototype.cancel=function(){if(this.onGoingTransactionNewState===Be.UNKNOWN)throw"Transaction is already cancelled, cannot cancel().";this.onGoingTransactionNewState=Be.UNKNOWN},h.prototype.getState=function(){return this.state},h.prototype.failIfTransitionOngoing=function(){if(this.onGoingTransactionNewState!==Be.UNKNOWN)throw"Cannot transition to a new state, already under transition"},h.prototype.validateTransition=function(u){switch(this.state){case Be.UNKNOWN:throw"Transition from unknown is not allowed";case Be.NOT_STARTED:this.failIfNewStateIs(u,[Be.PAUSED]);break;case Be.SCANNING:break;case Be.PAUSED:break}},h.prototype.failIfNewStateIs=function(u,c){for(var w=0,m=c;w<m.length;w++){var d=m[w];if(u===d)throw"Cannot transition from ".concat(this.state," to ").concat(u)}},h}(),Zs=function(){function h(u){this.stateManager=u}return h.prototype.startTransition=function(u){return this.stateManager.startTransition(u)},h.prototype.directTransition=function(u){this.stateManager.directTransition(u)},h.prototype.getState=function(){return this.stateManager.getState()},h.prototype.canScanFile=function(){return this.stateManager.getState()===Be.NOT_STARTED},h.prototype.isScanning=function(){return this.stateManager.getState()!==Be.NOT_STARTED},h.prototype.isStrictlyScanning=function(){return this.stateManager.getState()===Be.SCANNING},h.prototype.isPaused=function(){return this.stateManager.getState()===Be.PAUSED},h}(),Qs=function(){function h(){}return h.create=function(){return new Zs(new Xs)},h}(),qs=globalThis&&globalThis.__extends||function(){var h=function(u,c){return h=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(w,m){w.__proto__=m}||function(w,m){for(var d in m)Object.prototype.hasOwnProperty.call(m,d)&&(w[d]=m[d])},h(u,c)};return function(u,c){if(typeof c!="function"&&c!==null)throw new TypeError("Class extends value "+String(c)+" is not a constructor or null");h(u,c);function w(){this.constructor=u}u.prototype=c===null?Object.create(c):(w.prototype=c.prototype,new w)}}(),dt=function(h){qs(u,h);function u(){return h!==null&&h.apply(this,arguments)||this}return u.DEFAULT_WIDTH=300,u.DEFAULT_WIDTH_OFFSET=2,u.FILE_SCAN_MIN_HEIGHT=300,u.FILE_SCAN_HIDDEN_CANVAS_PADDING=100,u.MIN_QR_BOX_SIZE=50,u.SHADED_LEFT=1,u.SHADED_RIGHT=2,u.SHADED_TOP=3,u.SHADED_BOTTOM=4,u.SHADED_REGION_ELEMENT_ID="qr-shaded-region",u.VERBOSE=!1,u.BORDER_SHADER_DEFAULT_COLOR="#ffffff",u.BORDER_SHADER_MATCH_COLOR="rgb(90, 193, 56)",u}(Tt),Ks=function(){function h(u,c){this.logger=c,this.fps=dt.SCAN_DEFAULT_FPS,u?(u.fps&&(this.fps=u.fps),this.disableFlip=u.disableFlip===!0,this.qrbox=u.qrbox,this.aspectRatio=u.aspectRatio,this.videoConstraints=u.videoConstraints):this.disableFlip=dt.DEFAULT_DISABLE_FLIP}return h.prototype.isMediaStreamConstraintsValid=function(){return this.videoConstraints?Ei.isMediaStreamConstraintsValid(this.videoConstraints,this.logger):(this.logger.logError("Empty videoConstraints",!0),!1)},h.prototype.isShadedBoxEnabled=function(){return!Rt(this.qrbox)},h.create=function(u,c){return new h(u,c)},h}(),li=function(){function h(u,c){if(this.element=null,this.canvasElement=null,this.scannerPausedUiElement=null,this.hasBorderShaders=null,this.borderShaders=null,this.qrMatch=null,this.renderedCamera=null,this.qrRegion=null,this.context=null,this.lastScanImageFile=null,this.isScanning=!1,!document.getElementById(u))throw"HTML Element with id=".concat(u," not found");this.elementId=u,this.verbose=!1;var w;typeof c=="boolean"?this.verbose=c===!0:c&&(w=c,this.verbose=w.verbose===!0,w.experimentalFeatures),this.logger=new pi(this.verbose),this.qrcode=new Ps(this.getSupportedFormats(c),this.getUseBarCodeDetectorIfSupported(w),this.verbose,this.logger),this.foreverScanTimeout,this.shouldScan=!0,this.stateManagerProxy=Qs.create()}return h.prototype.start=function(u,c,w,m){var d=this;if(!u)throw"cameraIdOrConfig is required";if(!w||typeof w!="function")throw"qrCodeSuccessCallback is required and should be a function.";var T;m?T=m:T=this.verbose?this.logger.log:function(){};var R=Ks.create(c,this.logger);this.clearElement();var _=!1;R.videoConstraints&&(R.isMediaStreamConstraintsValid()?_=!0:this.logger.logError("'videoConstraints' is not valid 'MediaStreamConstraints, it will be ignored.'",!0));var A=_,S=document.getElementById(this.elementId);S.clientWidth?S.clientWidth:dt.DEFAULT_WIDTH,S.style.position="relative",this.shouldScan=!0,this.element=S;var M=this,X=this.stateManagerProxy.startTransition(Be.SCANNING);return new Promise(function(P,De){var W=A?R.videoConstraints:M.createVideoConstraints(u);if(!W){X.cancel(),De("videoConstraints should be defined");return}var Se={};(!A||R.aspectRatio)&&(Se.aspectRatio=R.aspectRatio);var Ke={onRenderSurfaceReady:function(he,J){M.setupUi(he,J,R),M.isScanning=!0,M.foreverScan(R,w,T)}};js.failIfNotSupported().then(function(he){he.create(W).then(function(J){return J.render(d.element,Se,Ke).then(function(ue){M.renderedCamera=ue,X.execute(),P(null)}).catch(function(ue){X.cancel(),De(ue)})}).catch(function(J){X.cancel(),De(wn.errorGettingUserMedia(J))})}).catch(function(he){X.cancel(),De(wn.cameraStreamingNotSupported())})})},h.prototype.pause=function(u){if(!this.stateManagerProxy.isStrictlyScanning())throw"Cannot pause, scanner is not scanning.";this.stateManagerProxy.directTransition(Be.PAUSED),this.showPausedState(),(Rt(u)||u!==!0)&&(u=!1),u&&this.renderedCamera&&this.renderedCamera.pause()},h.prototype.resume=function(){if(!this.stateManagerProxy.isPaused())throw"Cannot result, scanner is not paused.";if(!this.renderedCamera)throw"renderedCamera doesn't exist while trying resume()";var u=this,c=function(){u.stateManagerProxy.directTransition(Be.SCANNING),u.hidePausedState()};if(!this.renderedCamera.isPaused()){c();return}this.renderedCamera.resume(function(){c()})},h.prototype.getState=function(){return this.stateManagerProxy.getState()},h.prototype.stop=function(){var u=this;if(!this.stateManagerProxy.isScanning())throw"Cannot stop, scanner is not running or paused.";var c=this.stateManagerProxy.startTransition(Be.NOT_STARTED);this.shouldScan=!1,this.foreverScanTimeout&&clearTimeout(this.foreverScanTimeout);var w=function(){if(!!u.element){var d=document.getElementById(dt.SHADED_REGION_ELEMENT_ID);d&&u.element.removeChild(d)}},m=this;return this.renderedCamera.close().then(function(){return m.renderedCamera=null,m.element&&(m.element.removeChild(m.canvasElement),m.canvasElement=null),w(),m.qrRegion&&(m.qrRegion=null),m.context&&(m.context=null),c.execute(),m.hidePausedState(),m.isScanning=!1,Promise.resolve()})},h.prototype.scanFile=function(u,c){return this.scanFileV2(u,c).then(function(w){return w.decodedText})},h.prototype.scanFileV2=function(u,c){var w=this;if(!u||!(u instanceof File))throw"imageFile argument is mandatory and should be instance of File. Use 'event.target.files[0]'.";if(Rt(c)&&(c=!0),!this.stateManagerProxy.canScanFile())throw"Cannot start file scan - ongoing camera scan";return new Promise(function(m,d){w.possiblyCloseLastScanImageFile(),w.clearElement(),w.lastScanImageFile=URL.createObjectURL(u);var T=new Image;T.onload=function(){var R=T.width,_=T.height,A=document.getElementById(w.elementId),S=A.clientWidth?A.clientWidth:dt.DEFAULT_WIDTH,M=Math.max(A.clientHeight?A.clientHeight:_,dt.FILE_SCAN_MIN_HEIGHT),X=w.computeCanvasDrawConfig(R,_,S,M);if(c){var P=w.createCanvasElement(S,M,"qr-canvas-visible");P.style.display="inline-block",A.appendChild(P);var De=P.getContext("2d");if(!De)throw"Unable to get 2d context from canvas";De.canvas.width=S,De.canvas.height=M,De.drawImage(T,0,0,R,_,X.x,X.y,X.width,X.height)}var W=dt.FILE_SCAN_HIDDEN_CANVAS_PADDING,Se=Math.max(T.width,X.width),Ke=Math.max(T.height,X.height),he=Se+2*W,J=Ke+2*W,ue=w.createCanvasElement(he,J);A.appendChild(ue);var Ft=ue.getContext("2d");if(!Ft)throw"Unable to get 2d context from canvas";Ft.canvas.width=he,Ft.canvas.height=J,Ft.drawImage(T,0,0,R,_,W,W,Se,Ke);try{w.qrcode.decodeRobustlyAsync(ue).then(function(Ce){m(ei.createFromQrcodeResult(Ce))}).catch(d)}catch(Ce){d("QR code parse error, error = ".concat(Ce))}},T.onerror=d,T.onabort=d,T.onstalled=d,T.onsuspend=d,T.src=URL.createObjectURL(u)})},h.prototype.clear=function(){this.clearElement()},h.getCameras=function(){return Ys.retrieve()},h.prototype.getRunningTrackCapabilities=function(){return this.getRenderedCameraOrFail().getRunningTrackCapabilities()},h.prototype.getRunningTrackSettings=function(){return this.getRenderedCameraOrFail().getRunningTrackSettings()},h.prototype.getRunningTrackCameraCapabilities=function(){return this.getRenderedCameraOrFail().getCapabilities()},h.prototype.applyVideoConstraints=function(u){if(u){if(!Ei.isMediaStreamConstraintsValid(u,this.logger))throw"invalid videoConstaints passed, check logs for more details"}else throw"videoConstaints is required argument.";return this.getRenderedCameraOrFail().applyVideoConstraints(u)},h.prototype.getRenderedCameraOrFail=function(){if(this.renderedCamera==null)throw"Scanning is not in running state, call this API only when QR code scanning using camera is in running state.";return this.renderedCamera},h.prototype.getSupportedFormats=function(u){var c=[j.QR_CODE,j.AZTEC,j.CODABAR,j.CODE_39,j.CODE_93,j.CODE_128,j.DATA_MATRIX,j.MAXICODE,j.ITF,j.EAN_13,j.EAN_8,j.PDF_417,j.RSS_14,j.RSS_EXPANDED,j.UPC_A,j.UPC_E,j.UPC_EAN_EXTENSION];if(!u||typeof u=="boolean"||!u.formatsToSupport)return c;if(!Array.isArray(u.formatsToSupport))throw"configOrVerbosityFlag.formatsToSupport should be undefined or an array.";if(u.formatsToSupport.length===0)throw"Atleast 1 formatsToSupport is needed.";for(var w=[],m=0,d=u.formatsToSupport;m<d.length;m++){var T=d[m];Os(T)?w.push(T):this.logger.warn("Invalid format: ".concat(T," passed in config, ignoring."))}if(w.length===0)throw"None of formatsToSupport match supported values.";return w},h.prototype.getUseBarCodeDetectorIfSupported=function(u){if(Rt(u))return!0;if(!Rt(u.useBarCodeDetectorIfSupported))return u.useBarCodeDetectorIfSupported!==!1;if(Rt(u.experimentalFeatures))return!0;var c=u.experimentalFeatures;return Rt(c.useBarCodeDetectorIfSupported)?!0:c.useBarCodeDetectorIfSupported!==!1},h.prototype.validateQrboxSize=function(u,c,w){var m=this,d=w.qrbox;this.validateQrboxConfig(d);var T=this.toQrdimensions(u,c,d),R=function(A){if(A<dt.MIN_QR_BOX_SIZE)throw"minimum size of 'config.qrbox' dimension value is"+" ".concat(dt.MIN_QR_BOX_SIZE,"px.")},_=function(A){return A>u&&(m.logger.warn("`qrbox.width` or `qrbox` is larger than the width of the root element. The width will be truncated to the width of root element."),A=u),A};R(T.width),R(T.height),T.width=_(T.width)},h.prototype.validateQrboxConfig=function(u){if(typeof u!="number"&&typeof u!="function"&&(u.width===void 0||u.height===void 0))throw"Invalid instance of QrDimensions passed for 'config.qrbox'. Both 'width' and 'height' should be set."},h.prototype.toQrdimensions=function(u,c,w){if(typeof w=="number")return{width:w,height:w};if(typeof w=="function")try{return w(u,c)}catch(m){throw new Error("qrbox config was passed as a function but it failed with unknown error"+m)}return w},h.prototype.setupUi=function(u,c,w){w.isShadedBoxEnabled()&&this.validateQrboxSize(u,c,w);var m=Rt(w.qrbox)?{width:u,height:c}:w.qrbox;this.validateQrboxConfig(m);var d=this.toQrdimensions(u,c,m);d.height>c&&this.logger.warn("[Html5Qrcode] config.qrbox has height that isgreater than the height of the video stream. Shading will be ignored");var T=w.isShadedBoxEnabled()&&d.height<=c,R={x:0,y:0,width:u,height:c},_=T?this.getShadedRegionBounds(u,c,d):R,A=this.createCanvasElement(_.width,_.height),S={willReadFrequently:!0},M=A.getContext("2d",S);M.canvas.width=_.width,M.canvas.height=_.height,this.element.append(A),T&&this.possiblyInsertShadingElement(this.element,u,c,d),this.createScannerPausedUiElement(this.element),this.qrRegion=_,this.context=M,this.canvasElement=A},h.prototype.createScannerPausedUiElement=function(u){var c=document.createElement("div");c.innerText=wn.scannerPaused(),c.style.display="none",c.style.position="absolute",c.style.top="0px",c.style.zIndex="1",c.style.background="rgba(9, 9, 9, 0.46)",c.style.color="#FFECEC",c.style.textAlign="center",c.style.width="100%",u.appendChild(c),this.scannerPausedUiElement=c},h.prototype.scanContext=function(u,c){var w=this;return this.stateManagerProxy.isPaused()?Promise.resolve(!1):this.qrcode.decodeAsync(this.canvasElement).then(function(m){return u(m.text,ei.createFromQrcodeResult(m)),w.possiblyUpdateShaders(!0),!0}).catch(function(m){w.possiblyUpdateShaders(!1);var d=wn.codeParseError(m);return c(d,mi.createFrom(d)),!1})},h.prototype.foreverScan=function(u,c,w){var m=this;if(!!this.shouldScan&&!!this.renderedCamera){var d=this.renderedCamera.getSurface(),T=d.videoWidth/d.clientWidth,R=d.videoHeight/d.clientHeight;if(!this.qrRegion)throw"qrRegion undefined when localMediaStream is ready.";var _=this.qrRegion.width*T,A=this.qrRegion.height*R,S=this.qrRegion.x*T,M=this.qrRegion.y*R;this.context.drawImage(d,S,M,_,A,0,0,this.qrRegion.width,this.qrRegion.height);var X=function(){m.foreverScanTimeout=setTimeout(function(){m.foreverScan(u,c,w)},m.getTimeoutFps(u.fps))};this.scanContext(c,w).then(function(P){!P&&u.disableFlip!==!0?(m.context.translate(m.context.canvas.width,0),m.context.scale(-1,1),m.scanContext(c,w).finally(function(){X()})):X()}).catch(function(P){m.logger.logError("Error happend while scanning context",P),X()})}},h.prototype.createVideoConstraints=function(u){if(typeof u=="string")return{deviceId:{exact:u}};if(typeof u=="object"){var c="facingMode",w="deviceId",m={user:!0,environment:!0},d="exact",T=function(De){if(De in m)return!0;throw"config has invalid 'facingMode' value = "+"'".concat(De,"'")},R=Object.keys(u);if(R.length!==1)throw"'cameraIdOrConfig' object should have exactly 1 key,"+" if passed as an object, found ".concat(R.length," keys");var _=Object.keys(u)[0];if(_!==c&&_!==w)throw"Only '".concat(c,"' and '").concat(w,"' ")+" are supported for 'cameraIdOrConfig'";if(_===c){var A=u.facingMode;if(typeof A=="string"){if(T(A))return{facingMode:A}}else if(typeof A=="object")if(d in A){if(T(A["".concat(d)]))return{facingMode:{exact:A["".concat(d)]}}}else throw"'facingMode' should be string or object with"+" ".concat(d," as key.");else{var S=typeof A;throw"Invalid type of 'facingMode' = ".concat(S)}}else{var M=u.deviceId;if(typeof M=="string")return{deviceId:M};if(typeof M=="object"){if(d in M)return{deviceId:{exact:M["".concat(d)]}};throw"'deviceId' should be string or object with"+" ".concat(d," as key.")}else{var X=typeof M;throw"Invalid type of 'deviceId' = ".concat(X)}}}var P=typeof u;throw"Invalid type of 'cameraIdOrConfig' = ".concat(P)},h.prototype.computeCanvasDrawConfig=function(u,c,w,m){if(u<=w&&c<=m){var d=(w-u)/2,T=(m-c)/2;return{x:d,y:T,width:u,height:c}}else{var R=u,_=c;return u>w&&(c=w/u*c,u=w),c>m&&(u=m/c*u,c=m),this.logger.log("Image downsampled from "+"".concat(R,"X").concat(_)+" to ".concat(u,"X").concat(c,".")),this.computeCanvasDrawConfig(u,c,w,m)}},h.prototype.clearElement=function(){if(this.stateManagerProxy.isScanning())throw"Cannot clear while scan is ongoing, close it first.";var u=document.getElementById(this.elementId);u&&(u.innerHTML="")},h.prototype.possiblyUpdateShaders=function(u){this.qrMatch!==u&&(this.hasBorderShaders&&this.borderShaders&&this.borderShaders.length&&this.borderShaders.forEach(function(c){c.style.backgroundColor=u?dt.BORDER_SHADER_MATCH_COLOR:dt.BORDER_SHADER_DEFAULT_COLOR}),this.qrMatch=u)},h.prototype.possiblyCloseLastScanImageFile=function(){this.lastScanImageFile&&(URL.revokeObjectURL(this.lastScanImageFile),this.lastScanImageFile=null)},h.prototype.createCanvasElement=function(u,c,w){var m=u,d=c,T=document.createElement("canvas");return T.style.width="".concat(m,"px"),T.style.height="".concat(d,"px"),T.style.display="none",T.id=Rt(w)?"qr-canvas":w,T},h.prototype.getShadedRegionBounds=function(u,c,w){if(w.width>u||w.height>c)throw"'config.qrbox' dimensions should not be greater than the dimensions of the root HTML element.";return{x:(u-w.width)/2,y:(c-w.height)/2,width:w.width,height:w.height}},h.prototype.possiblyInsertShadingElement=function(u,c,w,m){if(!(c-m.width<1||w-m.height<1)){var d=document.createElement("div");d.style.position="absolute";var T=(c-m.width)/2,R=(w-m.height)/2;if(d.style.borderLeft="".concat(T,"px solid rgba(0, 0, 0, 0.48)"),d.style.borderRight="".concat(T,"px solid rgba(0, 0, 0, 0.48)"),d.style.borderTop="".concat(R,"px solid rgba(0, 0, 0, 0.48)"),d.style.borderBottom="".concat(R,"px solid rgba(0, 0, 0, 0.48)"),d.style.boxSizing="border-box",d.style.top="0px",d.style.bottom="0px",d.style.left="0px",d.style.right="0px",d.id="".concat(dt.SHADED_REGION_ELEMENT_ID),c-m.width<11||w-m.height<11)this.hasBorderShaders=!1;else{var _=5,A=40;this.insertShaderBorders(d,A,_,-_,null,0,!0),this.insertShaderBorders(d,A,_,-_,null,0,!1),this.insertShaderBorders(d,A,_,null,-_,0,!0),this.insertShaderBorders(d,A,_,null,-_,0,!1),this.insertShaderBorders(d,_,A+_,-_,null,-_,!0),this.insertShaderBorders(d,_,A+_,null,-_,-_,!0),this.insertShaderBorders(d,_,A+_,-_,null,-_,!1),this.insertShaderBorders(d,_,A+_,null,-_,-_,!1),this.hasBorderShaders=!0}u.append(d)}},h.prototype.insertShaderBorders=function(u,c,w,m,d,T,R){var _=document.createElement("div");_.style.position="absolute",_.style.backgroundColor=dt.BORDER_SHADER_DEFAULT_COLOR,_.style.width="".concat(c,"px"),_.style.height="".concat(w,"px"),m!==null&&(_.style.top="".concat(m,"px")),d!==null&&(_.style.bottom="".concat(d,"px")),R?_.style.left="".concat(T,"px"):_.style.right="".concat(T,"px"),this.borderShaders||(this.borderShaders=[]),this.borderShaders.push(_),u.appendChild(_)},h.prototype.showPausedState=function(){if(!this.scannerPausedUiElement)throw"[internal error] scanner paused UI element not found";this.scannerPausedUiElement.style.display="block"},h.prototype.hidePausedState=function(){if(!this.scannerPausedUiElement)throw"[internal error] scanner paused UI element not found";this.scannerPausedUiElement.style.display="none"},h.prototype.getTimeoutFps=function(u){return 1e3/u},h}(),_r="data:image/svg+xml;base64,",Js=_r+"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",$s=_r+"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1OS4wMTggNTkuMDE4IiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA1OS4wMTggNTkuMDE4IiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBkPSJtNTguNzQxIDU0LjgwOS01Ljk2OS02LjI0NGExMC43NCAxMC43NCAwIDAgMCAyLjgyLTcuMjVjMC01Ljk1My00Ljg0My0xMC43OTYtMTAuNzk2LTEwLjc5NlMzNCAzNS4zNjEgMzQgNDEuMzE0IDM4Ljg0MyA1Mi4xMSA0NC43OTYgNTIuMTFjMi40NDEgMCA0LjY4OC0uODI0IDYuNDk5LTIuMTk2bDYuMDAxIDYuMjc3YS45OTguOTk4IDAgMCAwIDEuNDE0LjAzMiAxIDEgMCAwIDAgLjAzMS0xLjQxNHpNMzYgNDEuMzE0YzAtNC44NSAzLjk0Ni04Ljc5NiA4Ljc5Ni04Ljc5NnM4Ljc5NiAzLjk0NiA4Ljc5NiA4Ljc5Ni0zLjk0NiA4Ljc5Ni04Ljc5NiA4Ljc5NlMzNiA0Ni4xNjQgMzYgNDEuMzE0ek0xMC40MzEgMTYuMDg4YzAgMy4wNyAyLjQ5OCA1LjU2OCA1LjU2OSA1LjU2OHM1LjU2OS0yLjQ5OCA1LjU2OS01LjU2OGMwLTMuMDcxLTIuNDk4LTUuNTY5LTUuNTY5LTUuNTY5cy01LjU2OSAyLjQ5OC01LjU2OSA1LjU2OXptOS4xMzggMGMwIDEuOTY4LTEuNjAyIDMuNTY4LTMuNTY5IDMuNTY4cy0zLjU2OS0xLjYwMS0zLjU2OS0zLjU2OCAxLjYwMi0zLjU2OSAzLjU2OS0zLjU2OSAzLjU2OSAxLjYwMSAzLjU2OSAzLjU2OXoiLz48cGF0aCBkPSJtMzAuODgyIDI4Ljk4NyA5LjE4LTEwLjA1NCAxMS4yNjIgMTAuMzIzYTEgMSAwIDAgMCAxLjM1MS0xLjQ3NWwtMTItMTFhMSAxIDAgMCAwLTEuNDE0LjA2M2wtOS43OTQgMTAuNzI3LTQuNzQzLTQuNzQzYTEuMDAzIDEuMDAzIDAgMCAwLTEuMzY4LS4wNDRMNi4zMzkgMzcuNzY4YTEgMSAwIDEgMCAxLjMyMiAxLjUwMWwxNi4zMTMtMTQuMzYyIDcuMzE5IDcuMzE4YS45OTkuOTk5IDAgMSAwIDEuNDE0LTEuNDE0bC0xLjgyNS0xLjgyNHoiLz48cGF0aCBkPSJNMzAgNDYuNTE4SDJ2LTQyaDU0djI4YTEgMSAwIDEgMCAyIDB2LTI5YTEgMSAwIDAgMC0xLTFIMWExIDEgMCAwIDAtMSAxdjQ0YTEgMSAwIDAgMCAxIDFoMjlhMSAxIDAgMSAwIDAtMnoiLz48L3N2Zz4=",ci=_r+"PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NjAgNDYwIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA0NjAgNDYwIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBkPSJNMjMwIDBDMTAyLjk3NSAwIDAgMTAyLjk3NSAwIDIzMHMxMDIuOTc1IDIzMCAyMzAgMjMwIDIzMC0xMDIuOTc0IDIzMC0yMzBTMzU3LjAyNSAwIDIzMCAwem0zOC4zMzMgMzc3LjM2YzAgOC42NzYtNy4wMzQgMTUuNzEtMTUuNzEgMTUuNzFoLTQzLjEwMWMtOC42NzYgMC0xNS43MS03LjAzNC0xNS43MS0xNS43MVYyMDIuNDc3YzAtOC42NzYgNy4wMzMtMTUuNzEgMTUuNzEtMTUuNzFoNDMuMTAxYzguNjc2IDAgMTUuNzEgNy4wMzMgMTUuNzEgMTUuNzFWMzc3LjM2ek0yMzAgMTU3Yy0yMS41MzkgMC0zOS0xNy40NjEtMzktMzlzMTcuNDYxLTM5IDM5LTM5IDM5IDE3LjQ2MSAzOSAzOS0xNy40NjEgMzktMzkgMzl6Ii8+PC9zdmc+",eo="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAQgAAAEIBarqQRAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAE1SURBVDiNfdI7S0NBEAXgLya1otFgpbYSbISAgpXYi6CmiH9KCAiChaVga6OiWPgfRDQ+0itaGVNosXtluWwcuMzePfM4M3sq8lbHBubwg1dc4m1E/J/N4ghDPOIsfk/4xiEao5KX0McFljN4C9d4QTPXuY99jP3DsIoDPGM6BY5i5yI5R7O4q+ImFkJY2DCh3cAH2klyB+9J1xUMMAG7eCh1a+Mr+k48b5diXrFVwwLuS+BJ9MfR7+G0FHOHhTHhnXNWS87VDF4pcnfQK4Ep7XScNLmPTZgURNKKYENYWDpzW1BhscS1WHS8CDgURFJQrWcoF3c13KKbgg1BYQfy8xZWEzTTw1QZbAoKu8FqJnktdu5hcVSHmchiILzzuaDQvjBzV2m8yohCE1jHfPx/xhU+y4G/D75ELlRJsSYAAAAASUVORK5CYII=",ui=function(){function h(){}return h.createDefault=function(){return{hasPermission:!1,lastUsedCameraId:null}},h}(),to=function(){function h(){this.data=ui.createDefault();var u=localStorage.getItem(h.LOCAL_STORAGE_KEY);u?this.data=JSON.parse(u):this.reset()}return h.prototype.hasCameraPermissions=function(){return this.data.hasPermission},h.prototype.getLastUsedCameraId=function(){return this.data.lastUsedCameraId},h.prototype.setHasPermission=function(u){this.data.hasPermission=u,this.flush()},h.prototype.setLastUsedCameraId=function(u){this.data.lastUsedCameraId=u,this.flush()},h.prototype.resetLastUsedCameraId=function(){this.data.lastUsedCameraId=null,this.flush()},h.prototype.reset=function(){this.data=ui.createDefault(),this.flush()},h.prototype.flush=function(){localStorage.setItem(h.LOCAL_STORAGE_KEY,JSON.stringify(this.data))},h.LOCAL_STORAGE_KEY="HTML5_QRCODE_DATA",h}(),no=function(){function h(){this.infoDiv=document.createElement("div")}return h.prototype.renderInto=function(u){this.infoDiv.style.position="absolute",this.infoDiv.style.top="10px",this.infoDiv.style.right="10px",this.infoDiv.style.zIndex="2",this.infoDiv.style.display="none",this.infoDiv.style.padding="5pt",this.infoDiv.style.border="1px solid #171717",this.infoDiv.style.fontSize="10pt",this.infoDiv.style.background="rgb(0 0 0 / 69%)",this.infoDiv.style.borderRadius="5px",this.infoDiv.style.textAlign="center",this.infoDiv.style.fontWeight="400",this.infoDiv.style.color="white",this.infoDiv.innerText=ti.poweredBy();var c=document.createElement("a");c.innerText="ScanApp",c.href="https://scanapp.org",c.target="new",c.style.color="white",this.infoDiv.appendChild(c);var w=document.createElement("br"),m=document.createElement("br");this.infoDiv.appendChild(w),this.infoDiv.appendChild(m);var d=document.createElement("a");d.innerText=ti.reportIssues(),d.href="https://github.com/mebjas/html5-qrcode/issues",d.target="new",d.style.color="white",this.infoDiv.appendChild(d),u.appendChild(this.infoDiv)},h.prototype.show=function(){this.infoDiv.style.display="block"},h.prototype.hide=function(){this.infoDiv.style.display="none"},h}(),ro=function(){function h(u,c){this.isShowingInfoIcon=!0,this.onTapIn=u,this.onTapOut=c,this.infoIcon=document.createElement("img")}return h.prototype.renderInto=function(u){var c=this;this.infoIcon.alt="Info icon",this.infoIcon.src=ci,this.infoIcon.style.position="absolute",this.infoIcon.style.top="4px",this.infoIcon.style.right="4px",this.infoIcon.style.opacity="0.6",this.infoIcon.style.cursor="pointer",this.infoIcon.style.zIndex="2",this.infoIcon.style.width="16px",this.infoIcon.style.height="16px",this.infoIcon.onmouseover=function(w){return c.onHoverIn()},this.infoIcon.onmouseout=function(w){return c.onHoverOut()},this.infoIcon.onclick=function(w){return c.onClick()},u.appendChild(this.infoIcon)},h.prototype.onHoverIn=function(){this.isShowingInfoIcon&&(this.infoIcon.style.opacity="1")},h.prototype.onHoverOut=function(){this.isShowingInfoIcon&&(this.infoIcon.style.opacity="0.6")},h.prototype.onClick=function(){this.isShowingInfoIcon?(this.isShowingInfoIcon=!1,this.onTapIn(),this.infoIcon.src=eo,this.infoIcon.style.opacity="1"):(this.isShowingInfoIcon=!0,this.onTapOut(),this.infoIcon.src=ci,this.infoIcon.style.opacity="0.6")},h}(),io=function(){function h(){var u=this;this.infoDiv=new no,this.infoIcon=new ro(function(){u.infoDiv.show()},function(){u.infoDiv.hide()})}return h.prototype.renderInto=function(u){this.infoDiv.renderInto(u),this.infoIcon.renderInto(u)},h}(),so=globalThis&&globalThis.__awaiter||function(h,u,c,w){function m(d){return d instanceof c?d:new c(function(T){T(d)})}return new(c||(c=Promise))(function(d,T){function R(S){try{A(w.next(S))}catch(M){T(M)}}function _(S){try{A(w.throw(S))}catch(M){T(M)}}function A(S){S.done?d(S.value):m(S.value).then(R,_)}A((w=w.apply(h,u||[])).next())})},oo=globalThis&&globalThis.__generator||function(h,u){var c={label:0,sent:function(){if(d[0]&1)throw d[1];return d[1]},trys:[],ops:[]},w,m,d,T;return T={next:R(0),throw:R(1),return:R(2)},typeof Symbol=="function"&&(T[Symbol.iterator]=function(){return this}),T;function R(A){return function(S){return _([A,S])}}function _(A){if(w)throw new TypeError("Generator is already executing.");for(;T&&(T=0,A[0]&&(c=0)),c;)try{if(w=1,m&&(d=A[0]&2?m.return:A[0]?m.throw||((d=m.return)&&d.call(m),0):m.next)&&!(d=d.call(m,A[1])).done)return d;switch(m=0,d&&(A=[A[0]&2,d.value]),A[0]){case 0:case 1:d=A;break;case 4:return c.label++,{value:A[1],done:!1};case 5:c.label++,m=A[1],A=[0];continue;case 7:A=c.ops.pop(),c.trys.pop();continue;default:if(d=c.trys,!(d=d.length>0&&d[d.length-1])&&(A[0]===6||A[0]===2)){c=0;continue}if(A[0]===3&&(!d||A[1]>d[0]&&A[1]<d[3])){c.label=A[1];break}if(A[0]===6&&c.label<d[1]){c.label=d[1],d=A;break}if(d&&c.label<d[2]){c.label=d[2],c.ops.push(A);break}d[2]&&c.ops.pop(),c.trys.pop();continue}A=u.call(h,c)}catch(S){A=[6,S],m=0}finally{w=d=0}if(A[0]&5)throw A[1];return{value:A[0]?A[1]:void 0,done:!0}}},hi=function(){function h(){}return h.hasPermissions=function(){return so(this,void 0,void 0,function(){var u,c,w,m;return oo(this,function(d){switch(d.label){case 0:return[4,navigator.mediaDevices.enumerateDevices()];case 1:for(u=d.sent(),c=0,w=u;c<w.length;c++)if(m=w[c],m.kind==="videoinput"&&m.label)return[2,!0];return[2,!1]}})})},h}(),Xt=function(){function h(u){this.supportedScanTypes=this.validateAndReturnScanTypes(u)}return h.prototype.getDefaultScanType=function(){return this.supportedScanTypes[0]},h.prototype.hasMoreThanOneScanType=function(){return this.supportedScanTypes.length>1},h.prototype.isCameraScanRequired=function(){for(var u=0,c=this.supportedScanTypes;u<c.length;u++){var w=c[u];if(h.isCameraScanType(w))return!0}return!1},h.isCameraScanType=function(u){return u===Zt.SCAN_TYPE_CAMERA},h.isFileScanType=function(u){return u===Zt.SCAN_TYPE_FILE},h.prototype.validateAndReturnScanTypes=function(u){if(!u||u.length===0)return Tt.DEFAULT_SUPPORTED_SCAN_TYPE;var c=Tt.DEFAULT_SUPPORTED_SCAN_TYPE.length;if(u.length>c)throw"Max ".concat(c," values expected for ")+"supportedScanTypes";for(var w=0,m=u;w<m.length;w++){var d=m[w];if(!Tt.DEFAULT_SUPPORTED_SCAN_TYPE.includes(d))throw"Unsupported scan type ".concat(d)}return u},h}(),xt=function(){function h(){}return h.ALL_ELEMENT_CLASS="html5-qrcode-element",h.CAMERA_PERMISSION_BUTTON_ID="html5-qrcode-button-camera-permission",h.CAMERA_START_BUTTON_ID="html5-qrcode-button-camera-start",h.CAMERA_STOP_BUTTON_ID="html5-qrcode-button-camera-stop",h.TORCH_BUTTON_ID="html5-qrcode-button-torch",h.CAMERA_SELECTION_SELECT_ID="html5-qrcode-select-camera",h.FILE_SELECTION_BUTTON_ID="html5-qrcode-button-file-selection",h.ZOOM_SLIDER_ID="html5-qrcode-input-range-zoom",h.SCAN_TYPE_CHANGE_ANCHOR_ID="html5-qrcode-anchor-scan-type-change",h.TORCH_BUTTON_CLASS_TORCH_ON="html5-qrcode-button-torch-on",h.TORCH_BUTTON_CLASS_TORCH_OFF="html5-qrcode-button-torch-off",h}(),Pt=function(){function h(){}return h.createElement=function(u,c){var w=document.createElement(u);return w.id=c,w.classList.add(xt.ALL_ELEMENT_CLASS),u==="button"&&w.setAttribute("type","button"),w},h}(),yi=globalThis&&globalThis.__awaiter||function(h,u,c,w){function m(d){return d instanceof c?d:new c(function(T){T(d)})}return new(c||(c=Promise))(function(d,T){function R(S){try{A(w.next(S))}catch(M){T(M)}}function _(S){try{A(w.throw(S))}catch(M){T(M)}}function A(S){S.done?d(S.value):m(S.value).then(R,_)}A((w=w.apply(h,u||[])).next())})},Ti=globalThis&&globalThis.__generator||function(h,u){var c={label:0,sent:function(){if(d[0]&1)throw d[1];return d[1]},trys:[],ops:[]},w,m,d,T;return T={next:R(0),throw:R(1),return:R(2)},typeof Symbol=="function"&&(T[Symbol.iterator]=function(){return this}),T;function R(A){return function(S){return _([A,S])}}function _(A){if(w)throw new TypeError("Generator is already executing.");for(;T&&(T=0,A[0]&&(c=0)),c;)try{if(w=1,m&&(d=A[0]&2?m.return:A[0]?m.throw||((d=m.return)&&d.call(m),0):m.next)&&!(d=d.call(m,A[1])).done)return d;switch(m=0,d&&(A=[A[0]&2,d.value]),A[0]){case 0:case 1:d=A;break;case 4:return c.label++,{value:A[1],done:!1};case 5:c.label++,m=A[1],A=[0];continue;case 7:A=c.ops.pop(),c.trys.pop();continue;default:if(d=c.trys,!(d=d.length>0&&d[d.length-1])&&(A[0]===6||A[0]===2)){c=0;continue}if(A[0]===3&&(!d||A[1]>d[0]&&A[1]<d[3])){c.label=A[1];break}if(A[0]===6&&c.label<d[1]){c.label=d[1],d=A;break}if(d&&c.label<d[2]){c.label=d[2],c.ops.push(A);break}d[2]&&c.ops.pop(),c.trys.pop();continue}A=u.call(h,c)}catch(S){A=[6,S],m=0}finally{w=d=0}if(A[0]&5)throw A[1];return{value:A[0]?A[1]:void 0,done:!0}}},fi=function(){function h(u,c,w){this.isTorchOn=!1,this.torchCapability=u,this.buttonController=c,this.onTorchActionFailureCallback=w}return h.prototype.isTorchEnabled=function(){return this.isTorchOn},h.prototype.flipState=function(){return yi(this,void 0,void 0,function(){var u,c;return Ti(this,function(w){switch(w.label){case 0:this.buttonController.disable(),u=!this.isTorchOn,w.label=1;case 1:return w.trys.push([1,3,,4]),[4,this.torchCapability.apply(u)];case 2:return w.sent(),this.updateUiBasedOnLatestSettings(this.torchCapability.value(),u),[3,4];case 3:return c=w.sent(),this.propagateFailure(u,c),this.buttonController.enable(),[3,4];case 4:return[2]}})})},h.prototype.updateUiBasedOnLatestSettings=function(u,c){u===c?(this.buttonController.setText(c?Ae.torchOffButton():Ae.torchOnButton()),this.isTorchOn=c):this.propagateFailure(c),this.buttonController.enable()},h.prototype.propagateFailure=function(u,c){var w=u?Ae.torchOnFailedMessage():Ae.torchOffFailedMessage();c&&(w+="; Error = "+c),this.onTorchActionFailureCallback(w)},h.prototype.reset=function(){this.isTorchOn=!1},h}(),ao=function(){function h(u,c){this.onTorchActionFailureCallback=c,this.torchButton=Pt.createElement("button",xt.TORCH_BUTTON_ID),this.torchController=new fi(u,this,c)}return h.prototype.render=function(u,c){var w=this;this.torchButton.innerText=Ae.torchOnButton(),this.torchButton.style.display=c.display,this.torchButton.style.marginLeft=c.marginLeft;var m=this;this.torchButton.addEventListener("click",function(d){return yi(w,void 0,void 0,function(){return Ti(this,function(T){switch(T.label){case 0:return[4,m.torchController.flipState()];case 1:return T.sent(),m.torchController.isTorchEnabled()?(m.torchButton.classList.remove(xt.TORCH_BUTTON_CLASS_TORCH_OFF),m.torchButton.classList.add(xt.TORCH_BUTTON_CLASS_TORCH_ON)):(m.torchButton.classList.remove(xt.TORCH_BUTTON_CLASS_TORCH_ON),m.torchButton.classList.add(xt.TORCH_BUTTON_CLASS_TORCH_OFF)),[2]}})})}),u.appendChild(this.torchButton)},h.prototype.updateTorchCapability=function(u){this.torchController=new fi(u,this,this.onTorchActionFailureCallback)},h.prototype.getTorchButton=function(){return this.torchButton},h.prototype.hide=function(){this.torchButton.style.display="none"},h.prototype.show=function(){this.torchButton.style.display="inline-block"},h.prototype.disable=function(){this.torchButton.disabled=!0},h.prototype.enable=function(){this.torchButton.disabled=!1},h.prototype.setText=function(u){this.torchButton.innerText=u},h.prototype.reset=function(){this.torchButton.innerText=Ae.torchOnButton(),this.torchController.reset()},h.create=function(u,c,w,m){var d=new h(c,m);return d.render(u,w),d},h}(),lo=function(){function h(u,c,w){this.fileBasedScanRegion=this.createFileBasedScanRegion(),this.fileBasedScanRegion.style.display=c?"block":"none",u.appendChild(this.fileBasedScanRegion);var m=document.createElement("label");m.setAttribute("for",this.getFileScanInputId()),m.style.display="inline-block",this.fileBasedScanRegion.appendChild(m),this.fileSelectionButton=Pt.createElement("button",xt.FILE_SELECTION_BUTTON_ID),this.setInitialValueToButton(),this.fileSelectionButton.addEventListener("click",function(R){m.click()}),m.append(this.fileSelectionButton),this.fileScanInput=Pt.createElement("input",this.getFileScanInputId()),this.fileScanInput.type="file",this.fileScanInput.accept="image/*",this.fileScanInput.style.display="none",m.appendChild(this.fileScanInput);var d=this;this.fileScanInput.addEventListener("change",function(R){if(!(R==null||R.target==null)){var _=R.target;if(!(_.files&&_.files.length===0)){var A=_.files,S=A[0],M=S.name;d.setImageNameToButton(M),w(S)}}});var T=this.createDragAndDropMessage();this.fileBasedScanRegion.appendChild(T),this.fileBasedScanRegion.addEventListener("dragenter",function(R){d.fileBasedScanRegion.style.border=d.fileBasedScanRegionActiveBorder(),R.stopPropagation(),R.preventDefault()}),this.fileBasedScanRegion.addEventListener("dragleave",function(R){d.fileBasedScanRegion.style.border=d.fileBasedScanRegionDefaultBorder(),R.stopPropagation(),R.preventDefault()}),this.fileBasedScanRegion.addEventListener("dragover",function(R){d.fileBasedScanRegion.style.border=d.fileBasedScanRegionActiveBorder(),R.stopPropagation(),R.preventDefault()}),this.fileBasedScanRegion.addEventListener("drop",function(R){R.stopPropagation(),R.preventDefault(),d.fileBasedScanRegion.style.border=d.fileBasedScanRegionDefaultBorder();var _=R.dataTransfer;if(_){var A=_.files;if(!A||A.length===0)return;for(var S=!1,M=0;M<A.length;++M){var X=A.item(M);if(!!X){var P=/image.*/;if(!!X.type.match(P)){S=!0;var De=X.name;d.setImageNameToButton(De),w(X),T.innerText=Ae.dragAndDropMessage();break}}}S||(T.innerText=Ae.dragAndDropMessageOnlyImages())}})}return h.prototype.hide=function(){this.fileBasedScanRegion.style.display="none",this.fileScanInput.disabled=!0},h.prototype.show=function(){this.fileBasedScanRegion.style.display="block",this.fileScanInput.disabled=!1},h.prototype.isShowing=function(){return this.fileBasedScanRegion.style.display==="block"},h.prototype.resetValue=function(){this.fileScanInput.value="",this.setInitialValueToButton()},h.prototype.createFileBasedScanRegion=function(){var u=document.createElement("div");return u.style.textAlign="center",u.style.margin="auto",u.style.width="80%",u.style.maxWidth="600px",u.style.border=this.fileBasedScanRegionDefaultBorder(),u.style.padding="10px",u.style.marginBottom="10px",u},h.prototype.fileBasedScanRegionDefaultBorder=function(){return"6px dashed #ebebeb"},h.prototype.fileBasedScanRegionActiveBorder=function(){return"6px dashed rgb(153 151 151)"},h.prototype.createDragAndDropMessage=function(){var u=document.createElement("div");return u.innerText=Ae.dragAndDropMessage(),u.style.fontWeight="400",u},h.prototype.setImageNameToButton=function(u){var c=20;if(u.length>c){var w=u.substring(0,8),m=u.length,d=u.substring(m-8,m);u="".concat(w,"....").concat(d)}var T=Ae.fileSelectionChooseAnother()+" - "+u;this.fileSelectionButton.innerText=T},h.prototype.setInitialValueToButton=function(){var u=Ae.fileSelectionChooseImage()+" - "+Ae.fileSelectionNoImageSelected();this.fileSelectionButton.innerText=u},h.prototype.getFileScanInputId=function(){return"html5-qrcode-private-filescan-input"},h.create=function(u,c,w){var m=new h(u,c,w);return m},h}(),co=function(){function h(u){this.selectElement=Pt.createElement("select",xt.CAMERA_SELECTION_SELECT_ID),this.cameras=u,this.options=[]}return h.prototype.render=function(u){var c=document.createElement("span");c.style.marginRight="10px";var w=this.cameras.length;if(w===0)throw new Error("No cameras found");if(w===1)c.style.display="none";else{var m=Ae.selectCamera();c.innerText="".concat(m," (").concat(this.cameras.length,")  ")}for(var d=1,T=0,R=this.cameras;T<R.length;T++){var _=R[T],A=_.id,S=_.label==null?A:_.label;(!S||S==="")&&(S=[Ae.anonymousCameraPrefix(),d++].join(" "));var M=document.createElement("option");M.value=A,M.innerText=S,this.options.push(M),this.selectElement.appendChild(M)}c.appendChild(this.selectElement),u.appendChild(c)},h.prototype.disable=function(){this.selectElement.disabled=!0},h.prototype.isDisabled=function(){return this.selectElement.disabled===!0},h.prototype.enable=function(){this.selectElement.disabled=!1},h.prototype.getValue=function(){return this.selectElement.value},h.prototype.hasValue=function(u){for(var c=0,w=this.options;c<w.length;c++){var m=w[c];if(m.value===u)return!0}return!1},h.prototype.setValue=function(u){if(!this.hasValue(u))throw new Error("".concat(u," is not present in the camera list."));this.selectElement.value=u},h.prototype.hasSingleItem=function(){return this.cameras.length===1},h.prototype.numCameras=function(){return this.cameras.length},h.create=function(u,c){var w=new h(c);return w.render(u),w},h}(),uo=function(){function h(){this.onChangeCallback=null,this.zoomElementContainer=document.createElement("div"),this.rangeInput=Pt.createElement("input",xt.ZOOM_SLIDER_ID),this.rangeInput.type="range",this.rangeText=document.createElement("span"),this.rangeInput.min="1",this.rangeInput.max="5",this.rangeInput.value="1",this.rangeInput.step="0.1"}return h.prototype.render=function(u,c){this.zoomElementContainer.style.display=c?"block":"none",this.zoomElementContainer.style.padding="5px 10px",this.zoomElementContainer.style.textAlign="center",u.appendChild(this.zoomElementContainer),this.rangeInput.style.display="inline-block",this.rangeInput.style.width="50%",this.rangeInput.style.height="5px",this.rangeInput.style.background="#d3d3d3",this.rangeInput.style.outline="none",this.rangeInput.style.opacity="0.7";var w=Ae.zoom();this.rangeText.innerText="".concat(this.rangeInput.value,"x ").concat(w),this.rangeText.style.marginRight="10px";var m=this;this.rangeInput.addEventListener("input",function(){return m.onValueChange()}),this.rangeInput.addEventListener("change",function(){return m.onValueChange()}),this.zoomElementContainer.appendChild(this.rangeInput),this.zoomElementContainer.appendChild(this.rangeText)},h.prototype.onValueChange=function(){var u=Ae.zoom();this.rangeText.innerText="".concat(this.rangeInput.value,"x ").concat(u),this.onChangeCallback&&this.onChangeCallback(parseFloat(this.rangeInput.value))},h.prototype.setValues=function(u,c,w,m){this.rangeInput.min=u.toString(),this.rangeInput.max=c.toString(),this.rangeInput.step=m.toString(),this.rangeInput.value=w.toString(),this.onValueChange()},h.prototype.show=function(){this.zoomElementContainer.style.display="block"},h.prototype.hide=function(){this.zoomElementContainer.style.display="none"},h.prototype.setOnCameraZoomValueChangeCallback=function(u){this.onChangeCallback=u},h.prototype.removeOnCameraZoomValueChangeCallback=function(){this.onChangeCallback=null},h.create=function(u,c){var w=new h;return w.render(u,c),w},h}(),gt;(function(h){h[h.STATUS_DEFAULT=0]="STATUS_DEFAULT",h[h.STATUS_SUCCESS=1]="STATUS_SUCCESS",h[h.STATUS_WARNING=2]="STATUS_WARNING",h[h.STATUS_REQUESTING_PERMISSION=3]="STATUS_REQUESTING_PERMISSION"})(gt||(gt={}));function ho(h){return{fps:h.fps,qrbox:h.qrbox,aspectRatio:h.aspectRatio,disableFlip:h.disableFlip,videoConstraints:h.videoConstraints}}function fo(h,u){return{formatsToSupport:h.formatsToSupport,useBarCodeDetectorIfSupported:h.useBarCodeDetectorIfSupported,experimentalFeatures:h.experimentalFeatures,verbose:u}}var go=function(){function h(u,c,w){if(this.lastMatchFound=null,this.cameraScanImage=null,this.fileScanImage=null,this.fileSelectionUi=null,this.elementId=u,this.config=this.createConfig(c),this.verbose=w===!0,!document.getElementById(u))throw"HTML Element with id=".concat(u," not found");this.scanTypeSelector=new Xt(this.config.supportedScanTypes),this.currentScanType=this.scanTypeSelector.getDefaultScanType(),this.sectionSwapAllowed=!0,this.logger=new pi(this.verbose),this.persistedDataManager=new to,c.rememberLastUsedCamera!==!0&&this.persistedDataManager.reset()}return h.prototype.render=function(u,c){var w=this;this.lastMatchFound=null,this.qrCodeSuccessCallback=function(d,T){if(u)u(d,T);else{if(w.lastMatchFound===d)return;w.lastMatchFound=d,w.setHeaderMessage(Ae.lastMatch(d),gt.STATUS_SUCCESS)}},this.qrCodeErrorCallback=function(d,T){c&&c(d,T)};var m=document.getElementById(this.elementId);if(!m)throw"HTML Element with id=".concat(this.elementId," not found");m.innerHTML="",this.createBasicLayout(m),this.html5Qrcode=new li(this.getScanRegionId(),fo(this.config,this.verbose))},h.prototype.pause=function(u){(Rt(u)||u!==!0)&&(u=!1),this.getHtml5QrcodeOrFail().pause(u)},h.prototype.resume=function(){this.getHtml5QrcodeOrFail().resume()},h.prototype.getState=function(){return this.getHtml5QrcodeOrFail().getState()},h.prototype.clear=function(){var u=this,c=function(){var w=document.getElementById(u.elementId);w&&(w.innerHTML="",u.resetBasicLayout(w))};return this.html5Qrcode?new Promise(function(w,m){if(!u.html5Qrcode){w();return}u.html5Qrcode.isScanning?u.html5Qrcode.stop().then(function(d){if(!u.html5Qrcode){w();return}u.html5Qrcode.clear(),c(),w()}).catch(function(d){u.verbose&&u.logger.logError("Unable to stop qrcode scanner",d),m(d)}):(u.html5Qrcode.clear(),c(),w())}):Promise.resolve()},h.prototype.getRunningTrackCapabilities=function(){return this.getHtml5QrcodeOrFail().getRunningTrackCapabilities()},h.prototype.getRunningTrackSettings=function(){return this.getHtml5QrcodeOrFail().getRunningTrackSettings()},h.prototype.applyVideoConstraints=function(u){return this.getHtml5QrcodeOrFail().applyVideoConstraints(u)},h.prototype.getHtml5QrcodeOrFail=function(){if(!this.html5Qrcode)throw"Code scanner not initialized.";return this.html5Qrcode},h.prototype.createConfig=function(u){return u?(u.fps||(u.fps=Tt.SCAN_DEFAULT_FPS),u.rememberLastUsedCamera!==!Tt.DEFAULT_REMEMBER_LAST_CAMERA_USED&&(u.rememberLastUsedCamera=Tt.DEFAULT_REMEMBER_LAST_CAMERA_USED),u.supportedScanTypes||(u.supportedScanTypes=Tt.DEFAULT_SUPPORTED_SCAN_TYPE),u):{fps:Tt.SCAN_DEFAULT_FPS,rememberLastUsedCamera:Tt.DEFAULT_REMEMBER_LAST_CAMERA_USED,supportedScanTypes:Tt.DEFAULT_SUPPORTED_SCAN_TYPE}},h.prototype.createBasicLayout=function(u){u.style.position="relative",u.style.padding="0px",u.style.border="1px solid silver",this.createHeader(u);var c=document.createElement("div"),w=this.getScanRegionId();c.id=w,c.style.width="100%",c.style.minHeight="100px",c.style.textAlign="center",u.appendChild(c),Xt.isCameraScanType(this.currentScanType)?this.insertCameraScanImageToScanRegion():this.insertFileScanImageToScanRegion();var m=document.createElement("div"),d=this.getDashboardId();m.id=d,m.style.width="100%",u.appendChild(m),this.setupInitialDashboard(m)},h.prototype.resetBasicLayout=function(u){u.style.border="none"},h.prototype.setupInitialDashboard=function(u){this.createSection(u),this.createSectionControlPanel(),this.scanTypeSelector.hasMoreThanOneScanType()&&this.createSectionSwap()},h.prototype.createHeader=function(u){var c=document.createElement("div");c.style.textAlign="left",c.style.margin="0px",u.appendChild(c);var w=new io;w.renderInto(c);var m=document.createElement("div");m.id=this.getHeaderMessageContainerId(),m.style.display="none",m.style.textAlign="center",m.style.fontSize="14px",m.style.padding="2px 10px",m.style.margin="4px",m.style.borderTop="1px solid #f6f6f6",c.appendChild(m)},h.prototype.createSection=function(u){var c=document.createElement("div");c.id=this.getDashboardSectionId(),c.style.width="100%",c.style.padding="10px 0px 10px 0px",c.style.textAlign="left",u.appendChild(c)},h.prototype.createCameraListUi=function(u,c,w){var m=this;m.showHideScanTypeSwapLink(!1),m.setHeaderMessage(Ae.cameraPermissionRequesting());var d=function(){w||m.createPermissionButton(u,c)};li.getCameras().then(function(T){m.persistedDataManager.setHasPermission(!0),m.showHideScanTypeSwapLink(!0),m.resetHeaderMessage(),T&&T.length>0?(u.removeChild(c),m.renderCameraSelection(T)):(m.setHeaderMessage(Ae.noCameraFound(),gt.STATUS_WARNING),d())}).catch(function(T){m.persistedDataManager.setHasPermission(!1),w?w.disabled=!1:d(),m.setHeaderMessage(T,gt.STATUS_WARNING),m.showHideScanTypeSwapLink(!0)})},h.prototype.createPermissionButton=function(u,c){var w=this,m=Pt.createElement("button",this.getCameraPermissionButtonId());m.innerText=Ae.cameraPermissionTitle(),m.addEventListener("click",function(){m.disabled=!0,w.createCameraListUi(u,c,m)}),c.appendChild(m)},h.prototype.createPermissionsUi=function(u,c){var w=this;if(Xt.isCameraScanType(this.currentScanType)&&this.persistedDataManager.hasCameraPermissions()){hi.hasPermissions().then(function(m){m?w.createCameraListUi(u,c):(w.persistedDataManager.setHasPermission(!1),w.createPermissionButton(u,c))}).catch(function(m){w.persistedDataManager.setHasPermission(!1),w.createPermissionButton(u,c)});return}this.createPermissionButton(u,c)},h.prototype.createSectionControlPanel=function(){var u=document.getElementById(this.getDashboardSectionId()),c=document.createElement("div");u.appendChild(c);var w=document.createElement("div");w.id=this.getDashboardSectionCameraScanRegionId(),w.style.display=Xt.isCameraScanType(this.currentScanType)?"block":"none",c.appendChild(w);var m=document.createElement("div");m.style.textAlign="center",w.appendChild(m),this.scanTypeSelector.isCameraScanRequired()&&this.createPermissionsUi(w,m),this.renderFileScanUi(c)},h.prototype.renderFileScanUi=function(u){var c=Xt.isFileScanType(this.currentScanType),w=this,m=function(d){if(!w.html5Qrcode)throw"html5Qrcode not defined";!Xt.isFileScanType(w.currentScanType)||(w.setHeaderMessage(Ae.loadingImage()),w.html5Qrcode.scanFileV2(d,!0).then(function(T){w.resetHeaderMessage(),w.qrCodeSuccessCallback(T.decodedText,T)}).catch(function(T){w.setHeaderMessage(T,gt.STATUS_WARNING),w.qrCodeErrorCallback(T,mi.createFrom(T))}))};this.fileSelectionUi=lo.create(u,c,m)},h.prototype.renderCameraSelection=function(u){var c=this,w=this,m=document.getElementById(this.getDashboardSectionCameraScanRegionId());m.style.textAlign="center";var d=uo.create(m,!1),T=function(W){var Se=W.zoomFeature();if(!!Se.isSupported()){d.setOnCameraZoomValueChangeCallback(function(he){Se.apply(he)});var Ke=1;c.config.defaultZoomValueIfSupported&&(Ke=c.config.defaultZoomValueIfSupported),Ke=Ds(Ke,Se.min(),Se.max()),d.setValues(Se.min(),Se.max(),Ke,Se.step()),d.show()}},R=co.create(m,u),_=document.createElement("span"),A=Pt.createElement("button",xt.CAMERA_START_BUTTON_ID);A.innerText=Ae.scanButtonStartScanningText(),_.appendChild(A);var S=Pt.createElement("button",xt.CAMERA_STOP_BUTTON_ID);S.innerText=Ae.scanButtonStopScanningText(),S.style.display="none",S.disabled=!0,_.appendChild(S);var M,X=function(W){if(!W.torchFeature().isSupported()){M&&M.hide();return}M?M.updateTorchCapability(W.torchFeature()):M=ao.create(_,W.torchFeature(),{display:"none",marginLeft:"5px"},function(Se){w.setHeaderMessage(Se,gt.STATUS_WARNING)}),M.show()};m.appendChild(_);var P=function(W){W||(A.style.display="none"),A.innerText=Ae.scanButtonStartScanningText(),A.style.opacity="1",A.disabled=!1,W&&(A.style.display="inline-block")};if(A.addEventListener("click",function(W){A.innerText=Ae.scanButtonScanningStarting(),R.disable(),A.disabled=!0,A.style.opacity="0.5",c.scanTypeSelector.hasMoreThanOneScanType()&&w.showHideScanTypeSwapLink(!1),w.resetHeaderMessage();var Se=R.getValue();w.persistedDataManager.setLastUsedCameraId(Se),w.html5Qrcode.start(Se,ho(w.config),w.qrCodeSuccessCallback,w.qrCodeErrorCallback).then(function(Ke){S.disabled=!1,S.style.display="inline-block",P(!1);var he=w.html5Qrcode.getRunningTrackCameraCapabilities();c.config.showTorchButtonIfSupported===!0&&X(he),c.config.showZoomSliderIfSupported===!0&&T(he)}).catch(function(Ke){w.showHideScanTypeSwapLink(!0),R.enable(),P(!0),w.setHeaderMessage(Ke,gt.STATUS_WARNING)})}),R.hasSingleItem()&&A.click(),S.addEventListener("click",function(W){if(!w.html5Qrcode)throw"html5Qrcode not defined";S.disabled=!0,w.html5Qrcode.stop().then(function(Se){c.scanTypeSelector.hasMoreThanOneScanType()&&w.showHideScanTypeSwapLink(!0),R.enable(),A.disabled=!1,S.style.display="none",A.style.display="inline-block",M&&(M.reset(),M.hide()),d.removeOnCameraZoomValueChangeCallback(),d.hide(),w.insertCameraScanImageToScanRegion()}).catch(function(Se){S.disabled=!1,w.setHeaderMessage(Se,gt.STATUS_WARNING)})}),w.persistedDataManager.getLastUsedCameraId()){var De=w.persistedDataManager.getLastUsedCameraId();R.hasValue(De)?(R.setValue(De),A.click()):w.persistedDataManager.resetLastUsedCameraId()}},h.prototype.createSectionSwap=function(){var u=this,c=Ae.textIfCameraScanSelected(),w=Ae.textIfFileScanSelected(),m=document.getElementById(this.getDashboardSectionId()),d=document.createElement("div");d.style.textAlign="center";var T=Pt.createElement("span",this.getDashboardSectionSwapLinkId());T.style.textDecoration="underline",T.style.cursor="pointer",T.innerText=Xt.isCameraScanType(this.currentScanType)?c:w,T.addEventListener("click",function(){if(!u.sectionSwapAllowed){u.verbose&&u.logger.logError("Section swap called when not allowed");return}u.resetHeaderMessage(),u.fileSelectionUi.resetValue(),u.sectionSwapAllowed=!1,Xt.isCameraScanType(u.currentScanType)?(u.clearScanRegion(),u.getCameraScanRegion().style.display="none",u.fileSelectionUi.show(),T.innerText=w,u.currentScanType=Zt.SCAN_TYPE_FILE,u.insertFileScanImageToScanRegion()):(u.clearScanRegion(),u.getCameraScanRegion().style.display="block",u.fileSelectionUi.hide(),T.innerText=c,u.currentScanType=Zt.SCAN_TYPE_CAMERA,u.insertCameraScanImageToScanRegion(),u.startCameraScanIfPermissionExistsOnSwap()),u.sectionSwapAllowed=!0}),d.appendChild(T),m.appendChild(d)},h.prototype.startCameraScanIfPermissionExistsOnSwap=function(){var u=this,c=this;if(this.persistedDataManager.hasCameraPermissions()){hi.hasPermissions().then(function(w){if(w){var m=document.getElementById(c.getCameraPermissionButtonId());if(!m)throw u.logger.logError("Permission button not found, fail;"),"Permission button not found";m.click()}else c.persistedDataManager.setHasPermission(!1)}).catch(function(w){c.persistedDataManager.setHasPermission(!1)});return}},h.prototype.resetHeaderMessage=function(){var u=document.getElementById(this.getHeaderMessageContainerId());u.style.display="none"},h.prototype.setHeaderMessage=function(u,c){c||(c=gt.STATUS_DEFAULT);var w=this.getHeaderMessageDiv();switch(w.innerText=u,w.style.display="block",c){case gt.STATUS_SUCCESS:w.style.background="rgba(106, 175, 80, 0.26)",w.style.color="#477735";break;case gt.STATUS_WARNING:w.style.background="rgba(203, 36, 49, 0.14)",w.style.color="#cb2431";break;case gt.STATUS_DEFAULT:default:w.style.background="rgba(0, 0, 0, 0)",w.style.color="rgb(17, 17, 17)";break}},h.prototype.showHideScanTypeSwapLink=function(u){this.scanTypeSelector.hasMoreThanOneScanType()&&(u!==!0&&(u=!1),this.sectionSwapAllowed=u,this.getDashboardSectionSwapLink().style.display=u?"inline-block":"none")},h.prototype.insertCameraScanImageToScanRegion=function(){var u=this,c=document.getElementById(this.getScanRegionId());if(this.cameraScanImage){c.innerHTML="<br>",c.appendChild(this.cameraScanImage);return}this.cameraScanImage=new Image,this.cameraScanImage.onload=function(w){c.innerHTML="<br>",c.appendChild(u.cameraScanImage)},this.cameraScanImage.width=64,this.cameraScanImage.style.opacity="0.8",this.cameraScanImage.src=Js,this.cameraScanImage.alt=Ae.cameraScanAltText()},h.prototype.insertFileScanImageToScanRegion=function(){var u=this,c=document.getElementById(this.getScanRegionId());if(this.fileScanImage){c.innerHTML="<br>",c.appendChild(this.fileScanImage);return}this.fileScanImage=new Image,this.fileScanImage.onload=function(w){c.innerHTML="<br>",c.appendChild(u.fileScanImage)},this.fileScanImage.width=64,this.fileScanImage.style.opacity="0.8",this.fileScanImage.src=$s,this.fileScanImage.alt=Ae.fileScanAltText()},h.prototype.clearScanRegion=function(){var u=document.getElementById(this.getScanRegionId());u.innerHTML=""},h.prototype.getDashboardSectionId=function(){return"".concat(this.elementId,"__dashboard_section")},h.prototype.getDashboardSectionCameraScanRegionId=function(){return"".concat(this.elementId,"__dashboard_section_csr")},h.prototype.getDashboardSectionSwapLinkId=function(){return xt.SCAN_TYPE_CHANGE_ANCHOR_ID},h.prototype.getScanRegionId=function(){return"".concat(this.elementId,"__scan_region")},h.prototype.getDashboardId=function(){return"".concat(this.elementId,"__dashboard")},h.prototype.getHeaderMessageContainerId=function(){return"".concat(this.elementId,"__header_message")},h.prototype.getCameraPermissionButtonId=function(){return xt.CAMERA_PERMISSION_BUTTON_ID},h.prototype.getCameraScanRegion=function(){return document.getElementById(this.getDashboardSectionCameraScanRegionId())},h.prototype.getDashboardSectionSwapLink=function(){return document.getElementById(this.getDashboardSectionSwapLinkId())},h.prototype.getHeaderMessageDiv=function(){return document.getElementById(this.getHeaderMessageContainerId())},h}();const xo=An({__name:"QrcodeScanner",props:{modelValue:{type:Boolean,required:!0}},emits:["update:modelValue","update:result"],setup(h,{emit:u}){const c=h,w=u,m=Ln({get:()=>c.modelValue,set:S=>w("update:modelValue",S)});Nr(()=>{m.value&&R()}),br(()=>c.modelValue,S=>{S?R():_()});const d=Bt(null),T={fps:10,qrbox:{width:250,height:250},aspectRatio:1,facingMode:"environment"},R=async()=>{try{(await navigator.mediaDevices.getUserMedia({video:!0})).getTracks().forEach(M=>M.stop()),d.value&&d.value.clear(),A()}catch(S){console.error("\u76F8\u6A5F\u521D\u59CB\u5316\u5931\u6557:",S),rs(S)}},_=()=>{var S;(S=d.value)==null||S.clear()},A=()=>{d.value=new go("reader",T,!1),d.value.render(S=>{w("update:modelValue",!1),w("update:result",S),console.log("\u6383\u63CF\u6210\u529F:",S)},()=>{})};return(S,M)=>(pt(),nn(is,{modelValue:m.value,"onUpdate:modelValue":M[1]||(M[1]=X=>m.value=X),persistent:"","no-refocus":"",position:"top",class:"card-dialog"},{default:ce(()=>[ie(ss,{class:"q-mx-sm q-mt-md"},{default:ce(()=>[ie(Kr,null,{default:ce(()=>[ie(hs),ie(Ye,{type:"button",icon:"close",flat:"",round:"",dense:"",onClick:M[0]||(M[0]=X=>m.value=!1)})]),_:1}),ie(Kr,{id:"reader"})]),_:1})]),_:1},8,["modelValue"]))}}),wo={class:"row q-mb-md"},Ao={class:"row q-mb-xl"},Co={class:"row q-mb-md"},mo=An({__name:"UserVerifyForm",props:{modelValue:{type:Boolean}},emits:["update:modelValue","login"],setup(h,{emit:u}){const{t:c}=er(),w=u,m=Bt(""),d=Bt(""),T=Bt(!0),R=Bt(!1),_=async()=>{try{R.value=!0;const M=await os.verifyUser({username:m.value,password:d.value});S(),w("login",M.result),w("update:modelValue",!1)}finally{R.value=!1}},A=()=>{S(),w("update:modelValue",!1)},S=()=>{m.value="",d.value=""};return(M,X)=>M.modelValue?(pt(),nn(ds,{key:0,onSubmit:Zn(_,["prevent"])},{default:ce(()=>[Lt("div",wo,[ie(yr,{type:"text",modelValue:m.value,"onUpdate:modelValue":X[0]||(X[0]=P=>m.value=P),label:xn(c)("account"),"stack-label":"",outlined:"",class:"full-width",required:""},null,8,["modelValue","label"])]),Lt("div",Ao,[ie(yr,{type:T.value?"password":"text",modelValue:d.value,"onUpdate:modelValue":X[2]||(X[2]=P=>d.value=P),label:xn(c)("password"),"stack-label":"",outlined:"",class:"full-width",required:""},{append:ce(()=>[ie(Qn,{name:T.value?"visibility_off":"visibility",class:"cursor-pointer",onClick:X[1]||(X[1]=P=>T.value=!T.value)},null,8,["name"])]),_:1},8,["type","modelValue","label"])]),Lt("div",Co,[ie(Ye,{type:"submit",label:xn(c)("submit"),"no-caps":"",color:"primary",size:"lg",class:"full-width",loading:R.value},null,8,["label","loading"]),ie(Ye,{type:"button",onClick:A,label:xn(c)("cancel"),color:"negative","no-caps":"",size:"lg",class:"full-width q-mt-md",loading:R.value},null,8,["label","loading"])])]),_:1})):qn("",!0)}});const po=An({__name:"LanguageSwitcher",props:{icon:{default:"language"},size:{default:"sm"},btnClass:{default:"q-pa-md"},menuAnchor:{default:"bottom left"},showLabel:{type:Boolean,default:!1},showTooltip:{type:Boolean,default:!0}},emits:["language-changed"],setup(h,{emit:u}){const{t:c,locale:w}=er(),m=[{code:"en-US",label:"English"},{code:"zh-TW",label:"\u4E2D\u6587"}],d=Ln(()=>{const _=m.find(A=>A.code===w.value);return(_==null?void 0:_.label)||"Language"}),T=_=>{w.value=_,localStorage.setItem("user-locale",_),R("language-changed",_)},R=u;return Nr(()=>{const _=localStorage.getItem("user-locale");_&&m.some(A=>A.code===_)&&(w.value=_)}),(_,A)=>(pt(),nn(Ye,{flat:"",icon:_.icon,size:_.size,class:as(_.btnClass),label:_.showLabel?d.value:void 0},{default:ce(()=>[ie(xs,{anchor:_.menuAnchor},{default:ce(()=>[ie(gs,{style:{"min-width":"120px"}},{default:ce(()=>[(pt(),Rn(Tr,null,di(m,S=>ls(ie(wi,{key:S.code,clickable:"",onClick:M=>T(S.code)},{default:ce(()=>[ie(vn,null,{default:ce(()=>[ie(Ai,null,{default:ce(()=>[it(Kn(S.label),1)]),_:2},1024)]),_:2},1024),xn(w)===S.code?(pt(),nn(vn,{key:0,avatar:""},{default:ce(()=>[ie(Qn,{name:"check",color:"primary"})]),_:1})):qn("",!0)]),_:2},1032,["onClick"]),[[As]])),64))]),_:1})]),_:1},8,["anchor"]),_.showTooltip?(pt(),nn(ws,{key:0},{default:ce(()=>[it(Kn(_.$t("changeLanguage")),1)]),_:1})):qn("",!0)]),_:1},8,["icon","size","class","label"]))}});var Eo=xi(po,[["__scopeId","data-v-061cad75"]]),Po=cs(({app:h})=>{h.component("NumberInput",ps),h.component("NumberPad",Ms),h.component("ItemQuantityInput",_s),h.component("TablePagination",us),h.component("QrcodeScanner",xo),h.component("UserVerifyForm",mo),h.component("LanguageSwitcher",Eo)});export{Po as default};
