package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type ClockController struct {
	clockService service.ClockService
}

func NewClockController(r *gin.RouterGroup, clockService service.ClockService) {
	clockController := ClockController{clockService}

	v1 := r.Group("/v1/clocks")
	{
		v1.POST("", clockController.CreateHandler)
		v1.GET("", clockController.FetchHandler)
		v1.GET("/latest", clockController.GetLatestHandler)

		v1.GET("/pairs", clockController.ListClockPairsHandler)
	}
}

func (ctr *ClockController) CreateHandler(c *gin.Context) {
	var payload domain.ClockCreatePayload
	if err := c.ShouldBind(&payload); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	payload.UserAgent = c.Request.UserAgent()
	payload.IPAddress = c.ClientIP()

	_, err := ctr.clockService.CreateClock(c, &payload)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to punch")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *ClockController) FetchHandler(c *gin.Context) {
	req := struct {
		Filter     domain.ClockFilter `form:"filter"`
		Pagination domain.Pagination  `form:"pagination"`
	}{}
	c.ShouldBind(&req)

	clocks, err := ctr.clockService.FetchClocks(c, &req.Filter, &req.Pagination)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to get data")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"data":       clocks,
		"pagination": req.Pagination,
	})
}

func (ctr *ClockController) GetLatestHandler(c *gin.Context) {
	userUUID := c.Query("user_uuid")
	if userUUID == "" {
		utils.HandleError(c, http.StatusBadRequest, nil, service.ErrUserNotFound.Error())
		return
	}

	clock, err := ctr.clockService.GetLatest(c, userUUID)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to get data")
		return
	}

	utils.HandleSuccess(c, clock)
}

func (ctr *ClockController) ListClockPairsHandler(c *gin.Context) {
	req := struct {
		Filter     domain.ClockPairFilter `form:"filter"`
		Pagination domain.Pagination      `form:"pagination"`
	}{}

	c.ShouldBind(&req)

	paris, err := ctr.clockService.ListClockPairs(c, &req.Filter, &req.Pagination)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to get data")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"data":       paris,
		"pagination": req.Pagination,
	})
}
