package domain

import "time"

type WcProduct struct {
	ID               int64        `json:"id"`
	Name             string       `json:"name"`
	Slug             string       `json:"slug"`
	Description      string       `json:"description"`
	ShortDescription string       `json:"short_description"`
	Sku              string       `json:"sku"`
	Price            float64      `json:"price"`
	RegularPrice     float64      `json:"regular_price"`
	SalePrice        float64      `json:"sale_price"`
	StockQuantity    int          `json:"stock_quantity"`
	StockStatus      string       `json:"stock_status"`
	Weight           float64      `json:"weight"`
	Length           float64      `json:"length"`
	Width            float64      `json:"width"`
	Height           float64      `json:"height"`
	Categories       []WcCategory `json:"categories,omitempty" gorm:"-"`
	Status           string       `json:"status"`
	CreatedAt        time.Time    `json:"created_at"`
	UpdatedAt        time.Time    `json:"updated_at"`
}

func (WcProduct) TableName() string {
	return ""
}

type WcProductFilter struct {
	CategoryID uint
	Keyword    string
	MinPrice   float64
	MaxPrice   float64
	Status     string
	Page       int
	PageSize   int
}
