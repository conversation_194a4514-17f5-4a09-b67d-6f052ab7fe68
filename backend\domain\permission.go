package domain

import "time"

/*
	基本權限類型：
		view_page_* - 允許查看指定頁面
		create_* - 允許新增資料
		update_* - 允許修改資料
		delete_* - 允許刪除資料
		export_* - 允許匯出資料
		approve_* - 允許核准操作
*/

const (
	PermissionViewPageAdmin        PermissionCode = "view_page_admin"        // 進入管理員頁面權限
	PermissionViewPageUser         PermissionCode = "view_page_user"         // 進入使用者管理頁面權限
	PermissionCreateUser           PermissionCode = "create_user"            // 新增使用者權限
	PermissionUpdateUser           PermissionCode = "update_user"            // 修改使用者權限
	PermissionDeleteUser           PermissionCode = "delete_user"            // 刪除使用者權限
	PermissionUpdateUserPermission PermissionCode = "update_user_permission" // 修改使用者權限權限
	PermissionViewPageProduct      PermissionCode = "view_page_product"      // 進入商品管理頁面權限
	PermissionCreateProduct        PermissionCode = "create_product"         // 新增商品權限
	PermissionUpdateProduct        PermissionCode = "update_product"         // 修改商品權限
	PermissionDeleteProduct        PermissionCode = "delete_product"         // 刪除商品權限
	PermissionViewPageOrder        PermissionCode = "view_page_order"        // 進入訂單管理頁面權限
	PermissionCreateOrder          PermissionCode = "create_order"           // 新增訂單
	PermissionUpdateOrder          PermissionCode = "update_order"           // 修改訂單權限
	PermissionDeleteOrder          PermissionCode = "delete_order"           // 刪除訂單權限
	PermissionCheckoutOrder        PermissionCode = "checkout_order"         // 結帳
	PermissionVoidOrder            PermissionCode = "void_order"             // 作廢訂單權限
)

type PermissionCode string

type Permission struct {
	ID        uint           `json:"id"`
	Code      PermissionCode `json:"code"`
	IsActive  bool           `json:"-"`
	CreatedAt time.Time      `json:"-"`

	// Relationships
	Groups []UserGroup `gorm:"many2many:group_permissions" json:"groups,omitempty"`
	Users  []User      `gorm:"many2many:user_permissions" json:"users,omitempty"`
}

type PermissionFilter struct {
	GroupID int64 `form:"group_id"`
	UserID  int64 `form:"user_id"`
}

type PermissionResponse struct {
	Permissions      []Permission `json:"permissions"`
	GroupPermissions []Permission `json:"group_permissions,omitempty"`
}

// user group permission
type GroupPermission struct {
	ID           uint
	GroupID      int64 // user group id
	PermissionID uint
	CreatedAt    time.Time
	CreatedBy    int64
}

type GroupPermissionUpdatePayload struct {
	GroupID         int64
	PermissionCodes []PermissionCode
	UpdatedBy       string // user uuid
}

type UserPermission struct {
	ID           uint
	UserID       int64
	PermissionID uint `gorm:"not null"`
	IsGranted    bool `gorm:"not null;default:true"`
	CreatedAt    time.Time
	CreatedBy    int64
	UpdatedAt    time.Time
}
