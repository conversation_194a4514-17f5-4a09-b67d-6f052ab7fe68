package repository

import (
	"context"
	"cx/domain"

	"gorm.io/gorm"
)

type StocktakingRepository interface {
	Create(ctx context.Context, stocktakingData *domain.CreateStocktakingRequest) error

	CreateItem(ctx context.Context, item *domain.InventoryCountItemRequest) error
}

type stocktakingRepository struct {
	db *gorm.DB
}

func NewStocktakingRepository(db *gorm.DB) StocktakingRepository {
	return &stocktakingRepository{
		db: db,
	}
}

func (r *stocktakingRepository) Create(ctx context.Context, stocktakingData *domain.CreateStocktakingRequest) error {
	tx := r.db.WithContext(ctx)

	// 創建盤點單
	if err := tx.Create(stocktakingData).Error; err != nil {
		return err
	}

	return nil
}

func (r *stocktakingRepository) CreateItem(ctx context.Context, item *domain.InventoryCountItemRequest) error {
	tx := r.db.WithContext(ctx)

	// 創建盤點明細
	if err := tx.Create(item).Error; err != nil {
		return err
	}

	return nil
}
