import { apiWrapper } from '@/boot/axios';
import { CreateResponse } from './modules/response';
import { CustomerInfo } from './customer';
import { Pagination } from '@/types';

export interface XeroOrderSync {
  uuid: string;
  order_uuid: string;
  xero_invoice_id?: string;
  xero_invoice_no?: string;
  sync_status: 'pending' | 'syncing' | 'success' | 'failed' | 'voided';
  error_message?: string;
  last_sync_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Order {
  uuid: string;
  customer_uuid?: string;
  customer_name?: string;
  customer?: CustomerInfo;
  order_no: string;
  order_items: OrderItem[];
  item_qty: number;
  rebate: number;
  discount: number;
  subtotal: number;
  service_fee: number;
  tax: number;
  tax_id: string;
  shipping_fee: number;
  total_discount: number;
  total: number;
  pay_type: string; // 'cash' | 'credit' | 'paypal'
  status: string; // 'pending' | 'processing' | 'completed' | 'cancelled' | 'void'
  notes: string;
  void_reason: string;
  order_at: string;
  xero_sync?: XeroOrderSync;
}

export interface OrderItem {
  uuid: string;
  product: {
    uuid: string;
    name: string;
  };
  quantity: number;
  price: number;
  regular_price: number; // 原價
  rebate: number;
  discount: number;
  is_free: boolean;
  total: number;
}

export interface OrderFilter {
  search?: string;
  customer_uuid?: string;
  status?: string[];
  exclude_status?: string[];
  start_date?: string;
  end_date?: string;
}

// woocommerce order
export interface WCOrdersResponse {
  data: WCOrderInfo[];
  pagination: Pagination;
}

export interface WCOrderFilter {
  search?: string;
  status?: string;
  date_from?: string;
  date_to?: string;
}

export interface WCOrderResponse {
  data: WCOrder;
}

export interface WCOrderInfo {
  id: number;
  date_created: string;
  status: string;
  total: number;
  payment_method_title: string;
  customer_email: string;
  customer_name: string;
  shipping_method: string;
  shipping_method_title: string;
}

export interface WCXeroOrderSync {
  uuid: string;
  wc_order_id: number;
  xero_invoice_id?: string;
  xero_invoice_no?: string;
  sync_status: 'pending' | 'syncing' | 'success' | 'failed' | 'voided';
  error_message?: string;
  last_sync_at?: string;
  created_at: string;
  updated_at: string;
}

export interface WCOrder {
  id: number;
  order_number: string;
  status: string;
  currency: string;
  customer_note: string;
  // billing
  billing_first_name: string;
  billing_last_name: string;
  billing_company: string;
  billing_address_1: string;
  billing_address_2: string;
  billing_city: string;
  billing_state: string;
  billing_postcode: string;
  billing_country: string;
  billing_email: string;
  billing_phone: string;
  // shipping
  shipping_first_name: string;
  shipping_last_name: string;
  shipping_company: string;
  shipping_address_1: string;
  shipping_address_2: string;
  shipping_city: string;
  shipping_state: string;
  shipping_postcode: string;
  shipping_country: string;
  shipping_method: string;
  shipping_method_title: string;
  payment_method: string;
  payment_method_title: string;
  transaction_id: string;
  date_created: string;
  date_modified: string;
  discount_total: number;
  discount_tax: number;
  shipping_total: number;
  cart_tax: number;
  total: number;
  total_tax: number;
  line_items: WCOrderItem[];
  xero_sync?: WCXeroOrderSync;
}

export interface WCOrderItem {
  name: string;
  quantity: number;
  total: number;
}

export interface WCOrderBilling {
  first_name: string;
  last_name: string;
  company?: string;
  address_1?: string;
  address_2?: string;
  city?: string;
  state?: string;
  postcode?: string;
  country?: string;
  email?: string;
  phone?: string;
}

export const OrderApi = {
  fetch: ({
    filter,
    pagination,
  }: {
    filter?: OrderFilter;
    pagination?: Pagination;
  }) =>
    apiWrapper.get<{
      data: Order[];
      pagination: Pagination;
    }>('/v1/orders', {
      params: {
        ...filter,
        ...pagination,
      },
    }),
  get: (uuid: string) => apiWrapper.get<Order>(`/v1/orders/${uuid}`),
  create: () => apiWrapper.post<CreateResponse>('/v1/orders'), // Create a new order, 僅回傳 uuid
  updateNotes: (uuid: string, notes: string) =>
    apiWrapper.patch(`/v1/orders/${uuid}/notes`, {
      notes: notes,
    }),
  voidOrder: (uuid: string, reason: string, otherReason?: string) =>
    apiWrapper.post(`/v1/orders/${uuid}`, {
      reason: reason,
      otherReason: otherReason,
    }),
  deleteOrder: (uuid: string) => apiWrapper.delete(`/v1/orders/${uuid}`),
  checkout: (order: Order) =>
    apiWrapper.post(`/v1/orders/${order.uuid}/checkout`, order),

  // wordpress
  listWCHistory: ({
    filter,
    pagination,
  }: {
    filter?: WCOrderFilter;
    pagination?: Pagination;
  } = {}) =>
    apiWrapper.get<WCOrdersResponse>('/v1/wc-orders/history', {
      params: {
        ...filter,
        ...pagination,
      },
    }),
  wcFetchPending: () =>
    apiWrapper.get<WCOrdersResponse>('/v1/wc-orders/pending'),
  getWCOrder: (id: number) =>
    apiWrapper.get<WCOrder>(`/v1/wc-orders/${id}`),

  updateWCOrderStatus: (id: number, status: string) =>
    apiWrapper.patch<{
      success: boolean;
      message: string;
    }>(`/v1/wc-orders/${id}/status`, { status }),

  updateWCOrderCustomerNote: (id: number, customerNote: string) =>
    apiWrapper.patch<{
      success: boolean;
      message: string;
    }>(`/v1/wc-orders/${id}/customer-note`, { customer_note: customerNote }),

  // WooCommerce 訂單 Xero 同步
  syncWCOrderToXero: (id: number) =>
    apiWrapper.post<{ success: boolean; message: string; invoice: any }>(`/v1/wc-orders/${id}/sync-xero`),
};
