# WooCommerce 訂單 Xero 同步狀態更新

## 修改概述
根據業務需求，Xero 同步應該在付款後就可以進行，而不是只有在訂單完成時。修改了前端和後端的狀態檢查邏輯。

## 修改內容

### 1. 前端修改 (`WCOrderDetailDialog.vue`)

#### 新增 `canSyncToXero` 計算屬性
```javascript
// 檢查訂單是否可以同步到 Xero（付款後的狀態）
const canSyncToXero = computed(() => {
  const status = wcOrder.value?.status;
  return !!(status && ['wc-processing', 'wc-packing', 'wc-shipping', 'wc-completed'].includes(status));
});
```

#### 更新模板條件
```vue
<!-- 修改前 -->
<q-item class="col-12" v-if="wcOrder?.status === 'wc-completed'">

<!-- 修改後 -->
<q-item class="col-12" v-if="canSyncToXero">
```

#### 更新 `canPrintInvoice` 計算屬性
```javascript
// 修改前
const canPrintInvoice = computed(() => {
  return !!(
    wcOrder.value?.xero_sync?.xero_invoice_id &&
    wcOrder.value?.xero_sync?.sync_status === 'success' &&
    wcOrder.value?.status === 'wc-completed'
  );
});

// 修改後
const canPrintInvoice = computed(() => {
  return !!(
    wcOrder.value?.xero_sync?.xero_invoice_id &&
    wcOrder.value?.xero_sync?.sync_status === 'success' &&
    canSyncToXero.value
  );
});
```

### 2. 後端修改 (`xero_service.go`)

#### 更新狀態檢查邏輯
```go
// 修改前
if order.Status != "wc-completed" {
    return nil, fmt.Errorf("order status is not completed: %s", order.Status)
}

// 修改後
allowedStatuses := []string{"wc-processing", "wc-packing", "wc-shipping", "wc-completed"}
statusAllowed := false
for _, status := range allowedStatuses {
    if string(order.Status) == status {
        statusAllowed = true
        break
    }
}
if !statusAllowed {
    return nil, fmt.Errorf("order status not allowed for sync: %s (allowed: %v)", order.Status, allowedStatuses)
}
```

## 支援的訂單狀態

### 可以同步到 Xero 的狀態
- `wc-processing` - 處理中（付款已完成）
- `wc-packing` - 包裝中
- `wc-shipping` - 運送中
- `wc-completed` - 已完成

### 不可同步的狀態
- `wc-pending` - 待付款
- `wc-on-hold` - 暫停
- `wc-cancelled` - 已取消
- `wc-refunded` - 已退款
- `wc-failed` - 失敗

## 功能行為變更

### 1. Xero 同步狀態顯示
- **修改前**：只有 `wc-completed` 狀態的訂單才顯示 Xero 同步狀態
- **修改後**：所有付款後的狀態（processing, packing, shipping, completed）都顯示 Xero 同步狀態

### 2. 同步按鈕可用性
- **修改前**：只有 `wc-completed` 狀態的訂單才可以同步
- **修改後**：所有付款後的狀態都可以同步

### 3. 發票列印功能
- **修改前**：只有 `wc-completed` 且已同步成功的訂單才可以列印
- **修改後**：所有付款後且已同步成功的訂單都可以列印

### 4. 發票發送功能
- **保持不變**：已同步成功且未取消的訂單都可以發送 Email

## 業務邏輯說明

### 為什麼付款後就可以同步？
1. **發票生成時機**：客戶付款後，商家就需要開立發票
2. **會計需求**：付款完成後就應該記錄到會計系統中
3. **庫存管理**：處理中的訂單已經需要預留庫存
4. **客戶服務**：客戶付款後就可以提供發票

### 狀態流程
```
wc-pending (待付款)
    ↓ 付款完成
wc-processing (處理中) ← 可以同步到 Xero
    ↓
wc-packing (包裝中) ← 可以同步到 Xero
    ↓
wc-shipping (運送中) ← 可以同步到 Xero
    ↓
wc-completed (已完成) ← 可以同步到 Xero
```

## 測試建議

### 1. 前端測試
- 測試不同狀態的訂單是否正確顯示同步狀態
- 測試同步按鈕在不同狀態下的可用性
- 測試列印按鈕在不同狀態下的可用性

### 2. 後端測試
- 測試不同狀態的訂單同步 API 調用
- 測試錯誤狀態的訂單是否正確拒絕同步
- 測試錯誤訊息是否清楚說明允許的狀態

### 3. 業務流程測試
- 創建一個 `wc-processing` 狀態的訂單
- 嘗試同步到 Xero
- 驗證同步成功後可以列印和發送 Email
- 測試狀態變更後的行為

## 注意事項

1. **向後兼容**：修改保持了對現有 `wc-completed` 狀態的支援
2. **錯誤處理**：後端會明確說明哪些狀態是允許的
3. **用戶體驗**：前端會根據狀態動態顯示可用的操作
4. **數據一致性**：同步邏輯確保只有付款後的訂單才會同步到 Xero

修改完成後，用戶現在可以在訂單付款後立即同步到 Xero，提高了工作效率！
