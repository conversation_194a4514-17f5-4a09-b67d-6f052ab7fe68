import{f as _}from"./date.6d29930c.js";import{r as y}from"./index.9477d5a3.js";function h(){const f=y({uuid:"",customer_uuid:"",customer_name:"",order_no:"",order_items:[],item_qty:0,rebate:0,discount:0,subtotal:0,service_fee:0,tax:0,tax_id:"",shipping_fee:0,total_discount:0,total:0,pay_type:"cash",status:"pending",notes:"",void_reason:"",order_at:_(new Date,"YYYY/MM/DD")}),p=[{label:"cash",value:"cash",icon:"payments"},{label:"creditCard",value:"credit",icon:"credit_card"},{label:"Paypal",value:"paypal",icon:"paypal"}],d=e=>({...f.value,...e}),b=e=>e.order_items?e.order_items.reduce((t,r)=>t+r.quantity,0):0,m=2,o=e=>isNaN(e)?0:parseFloat(e.toFixed(m)),c=e=>{const t=e.order_items.reduce((r,s)=>r+i(s),0);return o(t)},i=e=>{if(!e||e.is_free)return 0;const t=l(e);return o(t-n(t,e.rebate)-u(t,e.rebate,e.discount))},l=e=>o(e.quantity*e.price),n=(e,t)=>t<=0?0:t>=e?e:o(t),u=(e,t,r)=>{if(r<=0)return 0;let s=e-n(e,t);return r>=100||(s=(e-n(e,t))*(r*.01)),o(s)},a=e=>{const t=c(e);let r=t-n(t,e.rebate)-u(t,e.rebate,e.discount);return r<0&&(r=0),o(r)};return{payTypes:p,newOrder:d,totalQuantity:b,getSubtotal:c,getItemSubtotal:l,getItemTotal:i,getRebatePrice:n,getPercentageOff:u,grandTotal:a,checkout:e=>{e.order_at=_(e.order_at,"YYYY-MM-DD HH:mm"),e.subtotal=c(e),e.total_discount=n(e.subtotal,e.rebate)+u(e.subtotal,e.rebate,e.discount),e.total=a(e)}}}export{h as u};
