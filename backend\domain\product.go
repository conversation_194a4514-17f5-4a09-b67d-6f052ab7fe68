package domain

import (
	"time"

	"gorm.io/gorm"
)

type Product struct {
	ID               int64           `gorm:"primaryKey" json:"-"`
	WcID             int64           `json:"-"`
	UUID             string          `gorm:"<-:create" json:"uuid"`
	CategoryID       int64           `json:"category_id"`
	Category         ProductCategory `gorm:"foreignKey:CategoryID" json:"category"`
	Barcode          string          `json:"barcode"`
	Name             string          `json:"name"`
	Unit             string          `json:"unit"` // 單位
	Price            float64         `json:"price"`
	SalePrice        float64         `json:"sale_price"`
	Cost             float64         `json:"cost"`
	StockQuantity    int             `gorm:"<-:update" json:"stock_quantity"` // 庫存數量
	MinStockQuantity int             `json:"min_stock_quantity"`              // 最低庫存數量
	Description      string          `json:"description"`
	ShortDescription string          `json:"short_description"`
	Images           []ProductImage  `gorm:"foreignKey:ProductID" json:"images"`
	Suppliers        []Customer      `gorm:"many2many:product_suppliers;foreignKey:ID;joinForeignKey:ProductID;References:ID;joinReferences:CustomerID" json:"suppliers"`
	IsActive         bool            `gorm:"default:true" json:"is_active"`
	IsFlexiblePrice  bool            `gorm:"default:false" json:"is_flexible_price"` // 是否為彈性金額產品
	CreatedAt        time.Time       `gorm:"<-:create" json:"-"`
	UpdatedAt        time.Time       `json:"-"`
	DeletedAt        gorm.DeletedAt  `gorm:"index" json:"-"`
}

type ProductImage struct {
	ID        int64     `gorm:"primaryKey" json:"-"`
	WcID      int64     `json:"-"`
	UUID      string    `gorm:"<-:create" json:"uuid"`
	ProductID int64     `json:"-"`
	ImagePath string    `json:"image_path"`
	SortOrder int       `json:"sort_order"` // 用於排序的欄位
	CreatedAt time.Time `gorm:"<-:create" json:"-"`
	UpdatedAt time.Time `json:"-"`
}

type ProductSupplier struct {
	ProductID  int64 `gorm:"primaryKey"`
	CustomerID int64 `gorm:"primaryKey"`
}

type ProductInfo struct {
	ID              int64           `json:"-"`
	UUID            string          `json:"uuid"`
	Name            string          `json:"name"`
	CategoryID      int64           `json:"-"`
	Category        ProductCategory `gorm:"foreignKey:CategoryID" json:"category"`
	Barcode         string          `json:"barcode"`
	Unit            string          `json:"unit"`
	IsFlexiblePrice bool            `json:"is_flexible_price"` // 是否為彈性金額產品
}

func (ProductInfo) TableName() string {
	return "products"
}

type ProductFilter struct {
	Search     string `form:"search"`
	CategoryID int64  `form:"category_id"`
	Name       string `form:"name"`
	Barcode    string `form:"barcode"`
}

type ProductUpdatePayload struct {
	WcID             int64    `json:"wc_id"`
	CategoryID       int64    `json:"category_id"`
	Barcode          *string  `json:"barcode"`
	Name             string   `json:"name"`
	Unit             *string  `json:"unit"`
	Price            *float64 `json:"price"`
	SalePrice        *float64 `json:"sale_price"`
	Cost             *float64 `json:"cost"`
	StockQuantity    *int     `json:"stock_quantity"`
	MinStockQuantity *int     `json:"min_stock_quantity"`
	Description      *string  `json:"description"`
	ShortDescription *string  `json:"short_description"`
	IsActive         *bool    `json:"is_active"`
	IsFlexiblePrice  *bool    `json:"is_flexible_price"` // 是否為彈性金額產品
}
