package controller

import (
	"cx/domain"
	"cx/service"
	"cx/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type ProductController struct {
	productService service.ProductService
}

func NewProductController(r *gin.RouterGroup, productService service.ProductService) {
	productController := ProductController{productService}

	v1 := r.Group("/v1/products")
	{
		v1.GET("/:id", productController.GetByUUIDHandler)
		v1.GET("", productController.ListProductsHandler)
		v1.POST("", productController.CreateHandler)
		v1.PUT("/:id", productController.UpdateHandler)
		v1.PATCH(":id/status", productController.UpdateStatusHandler)
		v1.DELETE("/:id", productController.DeleteHandler)

		v1.POST("/:id/images", productController.UploadImageHandler)
		v1.PATCH("/images/sort-order", productController.UpdateImageSortOrderHandler)
		v1.DELETE("/:id/images/:image_id", productController.DeleteImageHandler)

		v1.POST("/:id/suppliers", productController.AddSupplierHandler)
		v1.DELETE("/:id/suppliers/:supplier_id", productController.RemoveSupplierHandler)

		v1.POST("/:id/wc/sync-images", productController.SyncWcProductWithImagesHandler)
	}
}

func (ctr *ProductController) ListProductsHandler(c *gin.Context) {
	req := struct {
		Filter     domain.ProductFilter `json:"filter"`
		Pagination domain.Pagination    `json:"pagination"`
	}{}

	c.ShouldBindQuery(&req)

	products, err := ctr.productService.ListProducts(c, &req.Filter, &req.Pagination)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to fetch products")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"data":       products,
		"pagination": req.Pagination,
	})
}

func (ctr *ProductController) GetByUUIDHandler(c *gin.Context) {
	id := c.Param("id")

	product, err := ctr.productService.GetByUUID(c, id)
	if err != nil {
		utils.HandleError(c, http.StatusNotFound, err, "Product not found")
		return
	}

	utils.HandleSuccess(c, product)
}

func (ctr *ProductController) CreateHandler(c *gin.Context) {
	var product domain.Product
	if err := c.ShouldBind(&product); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	err := ctr.productService.Create(c, &product)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to create product")
		return
	}

	utils.HandleSuccess(c, product)
}

func (ctr *ProductController) UpdateHandler(c *gin.Context) {
	id := c.Param("id")

	var product domain.ProductUpdatePayload
	if err := c.ShouldBind(&product); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	err := ctr.productService.Update(c, id, &product)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update product")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *ProductController) UpdateStatusHandler(c *gin.Context) {
	req := struct {
		IsActive *bool `form:"is_active" json:"is_active" binding:"required"`
	}{}

	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	id := c.Param("id")

	err := ctr.productService.UpdateStatus(c, id, *req.IsActive)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *ProductController) DeleteHandler(c *gin.Context) {
	id := c.Param("id")

	err := ctr.productService.Delete(c, id)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to delete product")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *ProductController) UploadImageHandler(c *gin.Context) {
	productID := c.Param("id") // uuid

	image, err := c.FormFile("file")
	if err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	err = ctr.productService.UploadImage(c, productID, image)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to upload image")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *ProductController) UpdateImageSortOrderHandler(c *gin.Context) {
	var req struct {
		Images []domain.ProductImage `form:"images" json:"images" binding:"required"`
	}

	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request")
		return
	}

	err := ctr.productService.UpdateImageSortOrder(c, req.Images)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to update image sort order")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *ProductController) DeleteImageHandler(c *gin.Context) {
	imageID := c.Param("image_id") // uuid

	err := ctr.productService.DeleteImage(c, imageID)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to delete image")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *ProductController) AddSupplierHandler(c *gin.Context) {
	productUUID := c.Param("id")

	req := struct {
		SupplierUUID string `json:"supplier_uuid"`
	}{}

	if err := c.ShouldBind(&req); err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to add supplier")
		return
	}

	err := ctr.productService.AddSupplier(c, productUUID, req.SupplierUUID)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to add supplier")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *ProductController) RemoveSupplierHandler(c *gin.Context) {
	productUUID := c.Param("id")
	supplierUUID := c.Param("supplier_id") // uuid

	err := ctr.productService.RemoveSupplier(c, productUUID, supplierUUID)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to remove supplier")
		return
	}

	utils.HandleSuccess(c, nil)
}

func (ctr *ProductController) SyncWcProductWithImagesHandler(c *gin.Context) {
	productUUID := c.Param("id")

	product, err := ctr.productService.GetByUUID(c, productUUID)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to sync product to online store")
		return
	}

	err = ctr.productService.SyncWcProductWithImages(c, product)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to sync product to online store")
		return
	}

	utils.HandleSuccess(c, nil)
}
