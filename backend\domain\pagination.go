package domain

import (
	"math"

	"gorm.io/gorm"
)

type Pagination struct {
	SortBy      string `form:"sortBy" json:"sortBy"`
	Descending  bool   `form:"descending" json:"descending"`
	Page        int    `form:"page" json:"page"`               // 當前頁碼
	RowsPerPage int    `form:"rowsPerPage" json:"rowsPerPage"` // 每頁顯示的行數
	RowsNumber  int64  `form:"rowsNumber" json:"rowsNumber"`   // 總資料數
}

// Set Offset and Limit to *gorm.DB
func (p *Pagination) SetPaginationToDB(db *gorm.DB, defaultSortBy string, defaultDescending bool) *gorm.DB {
	var (
		offset int
		limit  int
	)

	if p.RowsPerPage <= 0 {
		// 取得所有資料
		limit = -1
		p.RowsPerPage = -1
	} else {
		limit = p.RowsPerPage
		maxPage := math.Ceil(float64(p.RowsNumber) / float64(limit))

		if p.Page > int(maxPage) {
			p.Page = int(maxPage)
		}

		if p.Page <= 0 {
			p.Page = 1
		}

		offset = (p.Page - 1) * limit

		if offset < 0 {
			offset = 0
		}
	}

	if p.SortBy == "" {
		p.SortBy = defaultSortBy
		p.Descending = defaultDescending
	}

	if p.Descending {
		db = db.Order(p.SortBy + " DESC")
	} else {
		db = db.Order(p.SortBy)
	}

	return db.Offset(offset).Limit(limit)
}
