<template>
  <q-item clickable @click="triggerOnClick" :class="{ 'bg-grey-2': isSubmenu }">
    <q-item-section v-if="icon" avatar>
      <q-icon :name="icon" />
    </q-item-section>

    <q-item-section>
      <q-item-label>{{ title }}</q-item-label>
    </q-item-section>
  </q-item>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { BaseMenuLinkProps } from './models/Menu';

const router = useRouter();

const props = defineProps<BaseMenuLinkProps>();

function triggerOnClick() {
  if (props.link) {
    router.push(props.link);
  } else {
    props.onClick?.();
  }
}
</script>

<style lang="scss" scoped>
.q-expansion-item__content {
  .q-item__label {
    padding-left: 0.8rem;
  }
}
</style>
