package repository

import (
	"context"
	"cx/domain"

	"gorm.io/gorm"
)

type SystemRepository interface {
	Get(ctx context.Context) (*domain.SystemOption, error)
	Update(ctx context.Context, payload *domain.SystemOptionUpdatePayload) error
}

type systemRepository struct {
	db *gorm.DB
}

func NewSystemRepository(db *gorm.DB) SystemRepository {
	return &systemRepository{db}
}

func (r *systemRepository) Get(ctx context.Context) (*domain.SystemOption, error) {
	res := domain.SystemOption{}

	tx := r.db.WithContext(ctx)
	err := tx.FirstOrCreate(&res).Error

	return &res, err
}

func (r *systemRepository) Update(ctx context.Context, payload *domain.SystemOptionUpdatePayload) error {
	tx := r.db.WithContext(ctx)
	return tx.Updates(payload).Error
}
