<?php
class WCAPI {
    private $db;
    private $config;
    
    public function __construct() {
        // 載入配置文件
        $this->config = require __DIR__ . '/../config/config.php';
        
        // 設定時區
        date_default_timezone_set($this->config['timezone']);
        
        // 資料庫連接
        try {
            $dbConfig = $this->config['database'];
            $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']}";
            $this->db = new PDO(
                $dsn,
                $dbConfig['username'],
                $dbConfig['password'],
                $dbConfig['options']
            );
            $this->db->exec("SET NAMES utf8mb4");
        } catch (PDOException $e) {
            throw new Exception('資料庫連接失敗：' . $e->getMessage());
        }
    }

    // 獲取訂單列表
    public function getOrders() {
        try {
            $query = "
                SELECT 
                    o.id,
                    o.date_created_gmt as date_created,
                    o.status,
                    o.total_amount as total,
                    o.payment_method_title,
                    a.first_name,
                    a.last_name,
                    a.email as customer_email
                FROM wp_wc_orders o
                LEFT JOIN wp_wc_order_addresses a ON o.id = a.order_id AND a.address_type = 'billing'
                WHERE o.type = 'shop_order'
                ORDER BY o.date_created_gmt DESC
            ";
            
            $stmt = $this->db->query($query);
            $orders = $stmt->fetchAll(PDO::FETCH_OBJ);
            
            // 處理訂單數據
            foreach ($orders as &$order) {
                $order->number = $order->id;
                $order->customer_name = trim($order->first_name . ' ' . $order->last_name);
                $order->status = str_replace('wc-', '', $order->status);
            }

            return [
                'success' => true,
                'orders' => $orders,
                'counts' => $this->getOrderCounts($orders)
            ];
        } catch (Exception $e) {
            error_log('Database Error: ' . $e->getMessage());
            throw new Exception('無法獲取訂單數據: ' . $e->getMessage());
        }
    }

    // 獲取訂單詳情
    public function getOrderDetail($orderId) {
        try {
            // 獲取訂單基本信息
            $query = "
                SELECT 
                    o.id,
                    o.date_created_gmt as date_created,
                    o.status,
                    o.total_amount as total,
                    o.payment_method_title,
                    o.customer_note,
                    s.shipping_total,
                    s.tax_total,
                    s.net_total,
                    a.first_name,
                    a.last_name,
                    a.company,
                    a.address_1,
                    a.address_2,
                    a.city,
                    a.state,
                    a.postcode,
                    a.country,
                    a.email,
                    a.phone
                FROM wp_wc_orders o
                LEFT JOIN wp_wc_order_stats s ON o.id = s.order_id
                LEFT JOIN wp_wc_order_addresses a ON o.id = a.order_id AND a.address_type = 'billing'
                WHERE o.id = :orderId AND o.type = 'shop_order'
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute(['orderId' => $orderId]);
            $order = $stmt->fetch(PDO::FETCH_OBJ);

            if (!$order) {
                throw new Exception('訂單不存在');
            }

            // 獲取訂單商品
            $query = "
                SELECT 
                    i.order_item_name as name,
                    m1.meta_value as quantity,
                    m2.meta_value as total
                FROM wp_woocommerce_order_items i
                LEFT JOIN wp_woocommerce_order_itemmeta m1 ON i.order_item_id = m1.order_item_id AND m1.meta_key = '_qty'
                LEFT JOIN wp_woocommerce_order_itemmeta m2 ON i.order_item_id = m2.order_item_id AND m2.meta_key = '_line_total'
                WHERE i.order_id = :orderId AND i.order_item_type = 'line_item'
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute(['orderId' => $orderId]);
            $order->line_items = $stmt->fetchAll(PDO::FETCH_OBJ);

            // 處理訂單狀態
            $order->status = str_replace('wc-', '', $order->status);

            // 構建帳單地址對象
            $order->billing = (object)[
                'first_name' => $order->first_name,
                'last_name' => $order->last_name,
                'company' => $order->company,
                'address_1' => $order->address_1,
                'address_2' => $order->address_2,
                'city' => $order->city,
                'state' => $order->state,
                'postcode' => $order->postcode,
                'country' => $order->country,
                'email' => $order->email,
                'phone' => $order->phone
            ];

            return [
                'success' => true,
                'order' => $order
            ];
        } catch (Exception $e) {
            error_log('Database Error: ' . $e->getMessage());
            throw new Exception('無法獲取訂單詳情: ' . $e->getMessage());
        }
    }

    // 獲取訂單統計
    private function getOrderCounts($orders) {
        $counts = [
            'pending' => 0,
            'processing' => 0,
            'today' => 0
        ];

        $today = date('Y-m-d');
        
        foreach ($orders as $order) {
            if ($order->status === 'pending') {
                $counts['pending']++;
            }
            if ($order->status === 'processing') {
                $counts['processing']++;
            }
            if (date('Y-m-d', strtotime($order->date_created)) === $today) {
                $counts['today']++;
            }
        }

        return $counts;
    }

    // 檢查新訂單
    public function checkNewOrders() {
        try {
            $query = "
                SELECT 
                    o.id,
                    o.date_created_gmt as date_created,
                    o.status,
                    o.total_amount as total,
                    o.payment_method_title,
                    a.first_name,
                    a.last_name,
                    a.email as customer_email
                FROM wp_wc_orders o
                LEFT JOIN wp_wc_order_addresses a ON o.id = a.order_id AND a.address_type = 'billing'
                WHERE o.type = 'shop_order'
                AND o.status IN ('wc-pending', 'wc-processing')
                ORDER BY o.date_created_gmt DESC
                LIMIT 10
            ";
            
            $stmt = $this->db->query($query);
            $orders = $stmt->fetchAll(PDO::FETCH_OBJ);
            
            foreach ($orders as &$order) {
                $order->number = $order->id;
                $order->customer_name = trim($order->first_name . ' ' . $order->last_name);
                $order->status = str_replace('wc-', '', $order->status);
            }

            return [
                'success' => true,
                'orders' => $orders
            ];
        } catch (Exception $e) {
            throw new Exception('檢查新訂單失敗: ' . $e->getMessage());
        }
    }

    // 更新訂單狀態
    public function updateOrderStatus($orderId, $status) {
        try {
            $status = 'wc-' . $status; // 添加 wc- 前綴
            
            $query = "
                UPDATE wp_wc_orders 
                SET status = :status 
                WHERE id = :orderId
            ";
            
            $stmt = $this->db->prepare($query);
            $result = $stmt->execute([
                'status' => $status,
                'orderId' => $orderId
            ]);

            if (!$result) {
                throw new Exception('更新失敗');
            }

            return [
                'success' => true,
                'message' => '訂單狀態已更新'
            ];
        } catch (Exception $e) {
            throw new Exception('更新訂單狀態失敗: ' . $e->getMessage());
        }
    }

    // 獲取歷史訂單
    public function getHistoryOrders() {
        try {
            $query = "
                SELECT 
                    o.id,
                    o.date_created_gmt as date_created,
                    o.status,
                    o.total_amount as total,
                    o.payment_method_title,
                    a.first_name,
                    a.last_name,
                    a.email as customer_email
                FROM wp_wc_orders o
                LEFT JOIN wp_wc_order_addresses a ON o.id = a.order_id AND a.address_type = 'billing'
                WHERE o.type = 'shop_order'
                ORDER BY o.date_created_gmt DESC
                LIMIT 100
            ";
            
            $stmt = $this->db->query($query);
            $orders = $stmt->fetchAll(PDO::FETCH_OBJ);
            
            // 處理訂單數據
            foreach ($orders as &$order) {
                $order->number = $order->id;
                $order->customer_name = trim($order->first_name . ' ' . $order->last_name);
                // 移除 'wc-' 前綴並返回原始狀態
                $order->status = str_replace('wc-', '', $order->status);
                // 添加狀態中文說明
                $order->status_text = $this->getStatusText($order->status);
            }

            return [
                'success' => true,
                'orders' => $orders,
                'total_count' => count($orders)
            ];
        } catch (Exception $e) {
            throw new Exception('獲取訂單失敗: ' . $e->getMessage());
        }
    }

    // 新增一個輔助方法來獲取狀態的中文說明
    private function getStatusText($status) {
        $statusMap = [
            'pending' => '等待付款中',
            'processing' => '處理中',
            'completed' => '完成',
            'on-hold' => '保留',
            'cancelled' => '已取消',
            'refunded' => '已退費',
            'failed' => '失敗'
        ];
        
        return isset($statusMap[$status]) ? $statusMap[$status] : $status;
    }
}

// 處理 API 請求
if (isset($_GET['action']) || isset($_POST['action'])) {
    try {
        $wcapi = new WCAPI();
        
        // 獲取 action，優先使用 POST
        $action = isset($_POST['action']) ? $_POST['action'] : $_GET['action'];
        
        // 獲取請求數據，優先使用 POST
        $requestData = [];
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $requestData = json_decode(file_get_contents('php://input'), true) ?? $_POST;
        } else {
            $requestData = $_GET;
        }
        
        switch ($action) {
            case 'check_new_orders':
                header('Content-Type: application/json');
                echo json_encode($wcapi->checkNewOrders());
                break;
            
            case 'update_status':
                if (!isset($requestData['id']) || !isset($requestData['status'])) {
                    throw new Exception('缺少訂單ID或狀態');
                }
                header('Content-Type: application/json');
                echo json_encode($wcapi->updateOrderStatus($requestData['id'], $requestData['status']));
                break;
            
            case 'get_orders':
                header('Content-Type: application/json');
                echo json_encode($wcapi->getOrders());
                break;
            
            case 'get_order_detail':
                if (!isset($requestData['id'])) {
                    throw new Exception('缺少訂單ID');
                }
                header('Content-Type: application/json');
                echo json_encode($wcapi->getOrderDetail($requestData['id']));
                break;
            
            case 'get_history_orders':
                header('Content-Type: application/json');
                echo json_encode($wcapi->getHistoryOrders());
                break;
            
            default:
                throw new Exception('無效的操作');
        }
    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}
?> 