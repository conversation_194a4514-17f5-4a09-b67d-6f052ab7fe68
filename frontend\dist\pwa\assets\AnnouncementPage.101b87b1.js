import{bh as d,d as y,u as x,r as u,s as Q,o as q,k as w,f as s,b as e,g as k,y as S,i as n,p as V,t as m,q as r,aS as C,aH as A,m as B,z as N,a$ as p}from"./index.b4716878.js";import{Q as z}from"./QEditor.997e2002.js";import{Q as F}from"./QForm.b737edd8.js";import{Q as D}from"./QPage.cc993543.js";import{u as M}from"./use-quasar.59b22ad6.js";import"./QMenu.45353d3c.js";import"./selection.7371c306.js";import"./QTooltip.cf1e9eea.js";import"./QItemSection.a2ef2d56.js";import"./use-fullscreen.12714a56.js";const f={get:()=>d.get("v1/announcements"),update:i=>d.put("v1/announcements",i)},P={class:"row q-mb-md"},$={class:"col-12 text-h6 text-bold text-black"},j={class:"row q-mb-md"},E={class:"col-12 text-subtitle1"},H={class:"col-12"},X=y({__name:"AnnouncementPage",setup(i){const{t}=x(),h=M(),l=u({content:""}),g=[[{label:h.lang.editor.formatting,list:"no-icons",options:["p","h3","h4","h5","h6"]}],["left","center","right","justify"],["bold","italic","underline","strike"],["unordered","ordered"]],o=u(!1),v=async()=>{try{o.value=!0;const a=await f.get();l.value=a.result}catch(a){console.error("Failed to load announcement:",a)}finally{o.value=!1}},_=async()=>{try{o.value=!0,await f.update(l.value),p.create({type:"positive",message:t("success"),position:"top"})}catch(a){console.error("Failed to save announcement:",a),p.create({type:"negative",message:t("failed"),position:"top"})}finally{o.value=!1}};return Q(()=>{v()}),(a,c)=>(q(),w(D,{class:"q-pt-md"},{default:s(()=>[e(N,{flat:"",square:"",bordered:"",class:"q-mx-auto",style:{width:"800px","max-width":"100%"}},{default:s(()=>[e(F,{onSubmit:k(_,["prevent"]),greedy:""},{default:s(()=>[e(S,null,{default:s(()=>[n("div",P,[n("div",$,[V(m(r(t)("announcement.title"))+" ",1),e(C,{color:"black",size:"2px",class:"q-mb-sm"})])]),n("div",j,[n("div",E,m(r(t)("announcement.content")),1),n("div",H,[e(z,{modelValue:l.value.content,"onUpdate:modelValue":c[0]||(c[0]=b=>l.value.content=b),toolbar:g,"min-height":"15rem",placeholder:r(t)("announcement.placeholder")},null,8,["modelValue","placeholder"])])])]),_:1}),e(A,{align:"right",class:"q-px-lg q-py-md"},{default:s(()=>[e(B,{type:"submit",label:r(t)("save"),color:"positive",size:"md",loading:o.value},null,8,["label","loading"])]),_:1})]),_:1})]),_:1})]),_:1}))}});export{X as default};
