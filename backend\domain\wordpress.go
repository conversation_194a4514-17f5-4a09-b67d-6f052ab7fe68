package domain

import "time"

type WpMetaData struct {
	Key   string
	Value string
}

type WpTerms struct {
	TermID    int64  `gorm:"column:term_id;primary_key"`
	Name      string `gorm:"column:name"`
	Slug      string `gorm:"column:slug"`
	TermGroup int64  `gorm:"column:term_group"`
}

type WpTermmeta struct {
	MetaID    int64  `gorm:"column:meta_id;primary_key;auto_increment"`
	TermID    int64  `gorm:"column:term_id"`
	Meta<PERSON>ey   string `gorm:"column:meta_key"`
	MetaValue string `gorm:"column:meta_value"`
}

type WpPosts struct {
	ID           int64     `gorm:"column:ID;primary_key;auto_increment"`
	PostAuthor   int64     `gorm:"column:post_author"`
	PostDate     time.Time `gorm:"column:post_date"`
	PostContent  string    `gorm:"column:post_content"`
	PostTitle    string    `gorm:"column:post_title"`
	PostStatus   string    `gorm:"column:post_status"`
	PostName     string    `gorm:"column:post_name"`
	PostType     string    `gorm:"column:post_type"`
	GUID         string    `gorm:"column:guid"`
	PostMimeType string    `gorm:"column:post_mime_type"`
}
