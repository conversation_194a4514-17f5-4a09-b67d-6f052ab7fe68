package domain

import (
	"time"

	"gorm.io/gorm"
)

type ClockType string

const (
	ClockTypeClockIn  ClockType = "clock_in"
	ClockTypeClockOut ClockType = "clock_out"
)

type Clock struct {
	ID        int64          `gorm:"primaryKey" json:"-"`
	UUID      string         `gorm:"<-:create" json:"uuid"`
	UserID    int64          `gorm:"<-:create" json:"-"`
	Type      ClockType      `gorm:"<-:create" json:"type"` // clock_in, clock_out
	ClockTime time.Time      `json:"clock_time"`
	UserAgent string         `json:"-"`
	IPAddress string         `json:"-"`
	CreatedAt time.Time      `json:"-"`
	UpdatedAt time.Time      `json:"-"`
	DeletedAt gorm.DeletedAt `json:"-"`

	// Relations
	User UserInfo `gorm:"foreignKey:UserID;references:ID" json:"user"`
}

type ClockInfo struct {
	ID        int64  `json:"-"`
	UUID      string `json:"uuid"`
	Type      string `json:"type"`
	ClockTime string `json:"clock_time"`
}

func (ClockInfo) TableName() string {
	return "clocks"
}

type ClockFilter struct {
	UserUUID  string    `form:"user_uuid"`
	Type      ClockType `form:"type"`
	StartDate string    `form:"start_date"`
	EndDate   string    `form:"end_date"`
}

type ClockCreatePayload struct {
	UserUUID  string    `json:"user_uuid"`
	UserID    int64     `json:"-"`
	Type      ClockType `json:"type"`
	ClockTime time.Time `json:"-"`
	UserAgent string    `json:"user_agent"`
	IPAddress string    `json:"ip_address"`
}
